# Migração para Multi-Tenant

Este documento descreve as alterações necessárias para transformar o projeto em uma aplicação multi-tenant.

## Sumário

1. [Propagação do Tenant (TenantResolver e TenantPropagator)](#1-propagacao-do-tenant-tenantresolver-e-tenantpropagator)
2. [Configuração dos Tenants](#2-configuracao-dos-tenants)
3. [Alterações no Código](#3-alteracoes-no-codigo)
4. [Jobs e Handlers](#4-jobs-e-handlers)
5. [Alterações no DynamoDB](#5-alteracoes-no-dynamodb)
6. [Publicação de mensagem SQS](#6-publicacao-de-mensagem-sqs)
7. [Variáveis de Ambiente e Secrets](#7-variaveis-de-ambiente-e-secrets)
8. [Instâncias Single-Tenant](#8-instancias-single-tenant)

## 1. Propagação do Tenant (TenantResolver e TenantPropagator)
- O sistema suporta duas formas de propagação do tenant:

1. **Tenant Resolver do Micronaut**
    - Utiliza o header HTTP `X-TENANT-ID`
    - Configurado automaticamente pelo Micronaut

2. **Tenant Propagator MDC Context**
    - Implementado via `TenantPropagator`
    - Permite propagação programática do tenant

### 1.1 Tenant Resolver do Micronaut
```yaml
micronaut:
  multitenancy:
    propagation:
      enabled: true
    tenantresolver:
      httpheader:
        enabled: true
        header-name: X-TENANT-ID
```
- Este trecho de configuração ativa a propagação do tenant via header HTTP `X-TENANT-ID`, permitindo que o Micronaut resolva automaticamente o tenant atual baseado nesse header. 
- Essa solução resolve as chamadas HTTP de forma transparente, garantindo que o tenant correto seja utilizado em cada requisição.
- Mas ainda existe o débito em propagar o tenant em ambientes sem o contexto HTTP, como em jobs ou handlers SQS. Para esses casos, utilizaremos o `TenantPropagator`.

### 1.2 Tenant Propagator
O `TenantPropagator` é uma classe utilitária que permite propagar o contexto do tenant de forma programática em ambientes onde não temos o contexto HTTP, como em jobs ou handlers SQS. Suas principais características são:

- Implementa dois métodos principais:
  - `executeWithTenant`: para operações síncronas
  - `executeWithTenantWithSuspend`: para operações assíncronas (suspend functions)

- Utiliza o MDC (Mapped Diagnostic Context) para propagar o tenant através do contexto da aplicação
- Garante que o tenant seja propagado corretamente mesmo em operações aninhadas
- Permite executar blocos de código com um tenant específico de forma isolada

Exemplo de uso:
```kotlin
// Em um job ou handler SQS
tenantPropagator.executeWithTenant("tenant-id") {
    // Todo o código dentro deste bloco terá acesso ao tenant correto
    processData()
    callExternalService()
}
```

## 2. Configuração dos Tenants

### 2.1 Estrutura de Configuração (application-tenant.yml)

A configuração multi-tenant requer uma estrutura específica nos arquivos YAML para funcionar corretamente com o `@EachProperty` do Micronaut. Todas as configurações específicas de um tenant devem estar sob o prefixo `tenants.TENANT_NAME`, onde `TENANT_NAME` é o identificador único do tenant (ex: friday, motorola).

Esta estrutura é necessária porque o Micronaut utiliza o `@EachProperty` para criar automaticamente instâncias de `TenantConfiguration` para cada tenant definido. O prefixo `tenants` é usado como namespace para agrupar todas as configurações relacionadas aos tenants, e o nome do tenant é usado como chave para identificar qual configuração pertence a qual tenant.

Exemplo da estrutura necessária:
```yaml
application-friday.yml
tenants:
  FRIDAY:
    blipAuth:
      secret: "secret1"
    waCommCentre:
      enabled: true
      auth:
        clientId: "client1"
        secret: "secret1"

application-motorola.yml
tenants:
  MOTOROLA:
    blipAuth:
      secret: "secret2"
    waCommCentre:
      enabled: true
      auth:
        clientId: "client2"
        secret: "secret2"
```

Esta estrutura permite que o Micronaut crie automaticamente instâncias de `TenantConfiguration` para cada tenant, facilitando o acesso às configurações específicas de cada um através do `TenantService`.

### 2.2 Classes de Configuração
- Todas as configurações carregadas via application agora são encapsuladas em uma classe `TenantConfiguration`, que contém as configurações específicas de cada tenant.
- O `TenantService` é responsável por resolver o tenant atual e fornecer acesso à configuração correspondente através do método `getConfiguration()`.

```kotlin
@EachProperty("tenants")
data class TenantConfiguration @ConfigurationInject constructor(
    @param:Parameter val name: String,
    val integrations: Integrations,
    val communicationCentre: CommunicationCentreConfig,
    val blipAuth: BlipAuthConfig,
    val waCommCentre: WaCommCentreConfig,
    val features: FeaturesConfig,
    // ... outras configurações
)

private val tenantService: TenantService

// Acesso às configurações específicas do tenant
val config = tenantService.getConfiguration()
val blipAuthSecret = config.blipAuth.secret
val waCommCentreConfig = config.waCommCentre
val features = config.features
```

## 3. Alterações no Código

### 3.1 Substituição de @Property
Antes:
```kotlin
@Property(name = "blip-auth.secret") 
private val internalSecret: String
```

Depois:
```kotlin
private val tenantService: TenantService
// ...
val secret = tenantService.getConfiguration().blipAuth.secret
```

### 3.2 HTTP Clients
Antes:
```kotlin
@Client(
    configuration = BillPaymentHttpConfiguration::class,
    value = "\${integrations.billPayment.host}",
)
private val httpClient: RxHttpClient
```

Nesse caso foi criado um factory src/main/kotlin/ai/chatbot/adapters/billPayment/BillPaymentHttpClientFactory.kt criando esse client corretamente para cada tenant

Depois:
```kotlin
private val httpClient: BillPaymentHttpClient
```


### 3.3 Feature Flags
Antes:
```kotlin
@Named("wa-comm-centre") 
private val waCommCentreFeatureFlag: FeatureFlag

TODO: adicionar uso antigo
```

Depois:
```kotlin
private val tenantService: TenantService
// ...
tenantService.getConfiguration().features.waCommCentre
```

## 4. Jobs e Handlers

### 4.1 Jobs

A execução de jobs foi atualizada para suportar o modelo multi-tenant através de duas abordagens principais:

#### 4.1.1 TenantAbstractJob com @EachTenant

A implementação atual utiliza a anotação `@EachTenant` para criar automaticamente uma instância da job para cada tenant configurado. Esta é a abordagem recomendada e mais robusta:

**Características:**
- Cada tenant executa sua job de forma independente e isolada
- Se uma job falhar para um tenant, não afeta a execução nos outros tenants
- O nome da job é automaticamente prefixado com o nome do tenant (`TENANT_NAME_JobName`)
- Utiliza `TenantPropagator` para garantir o contexto correto do tenant durante a execução

**Implementação:**
```kotlin
@EachTenant
@Requirements(Requires(beans = [ChatBotDailyLogService::class]), Requires(notEnv = ["test"]))
open class ChatLogJob(
    private val chatBotDailyLogService: ChatBotDailyLogService,
    private val configuration: TenantConfiguration,
    private val tenantPropagator: TenantPropagator,
) : TenantAbstractJob(
        cron = "0 8 * * *",
        tenantName = configuration.name,
    ) {
    
    override fun execute() {
        tenantPropagator.executeWithTenant(configuration.name) {
            chatBotDailyLogService.conversationsReportByDate(
                LocalDate.now().minusDays(1), 
                forceClassify = true
            )
        }
    }
}
```

**Vantagens:**
- ✅ **Isolamento completo**: Falhas em um tenant não afetam outros
- ✅ **Execução independente**: Cada tenant pode ter horários diferentes se necessário
- ✅ **Nomenclatura clara**: Jobs são nomeadas como `FRIDAY_ChatLogJob`, `MOTOROLA_ChatLogJob`
- ✅ **Configuração automática**: O Micronaut cria as instâncias automaticamente

#### 4.1.2 Jobs Globais (Independentes de Tenant)

Para jobs que processam dados globais ou executam operações que são independentes de tenant, use a implementação tradicional com `AbstractJob`:

```kotlin
@Singleton
@Requirements(Requires(beans = [InteractionWindowService::class]), Requires(notEnv = ["test"]))
open class ExpireInteractionWindowsJob(
    private val interactionWindowService: InteractionWindowService,
) : AbstractJob(cron = "*/10 * * * *") {

    override fun execute() {
        interactionWindowService.expireAllDue()
    }
}
```

**Características:**
- ✅ **Execução única**: Roda uma única vez por instância, independente de quantos tenants existem
- ✅ **Simplicidade**: Não precisa de propagação de tenant ou configurações específicas
- ✅ **Performance**: Ideal para operações que não dependem de contexto de tenant

**Casos de uso:**
- Limpeza de dados compartilhados
- Operações de sistema (logs, métricas)
- Processamento de dados globais
- Jobs de manutenção da aplicação

#### 4.1.3 Recomendações

- **Use `@EachTenant` + `TenantAbstractJob`** para jobs que precisam processar dados específicos de cada tenant
- **Use `AbstractJob`** para jobs globais que são independentes de tenant
- **Sempre use `TenantPropagator.executeWithTenant()`** em jobs tenant-specific para garantir propagação correta do contexto

### 4.2 Handlers SQS
A propagação do tenant nos handlers SQS foi implementada de forma transparente através da classe base `AbstractSQSHandler` e sua extensão `CoroutineAbstractSQSHandler`. Isso significa que:

1. Todos os handlers que herdam de `CoroutineAbstractSQSHandler` já têm a propagação do tenant implementada automaticamente
2. O tenant é extraído dos atributos da mensagem SQS (`X-TENANT-ID`)
3. A propagação é feita usando o `TenantPropagator` antes de processar cada mensagem
4. O contexto do tenant é mantido durante todo o processamento da mensagem, incluindo operações assíncronas

Exemplo de configuração na classe base:
```kotlin
abstract class AbstractSQSHandler(
    private val tenantPropagator: TenantPropagator
) {
    fun processMessages(messages: List<Message>) {
        for (message in messages) {
            val tenantId = message.messageAttributes()["X-TENANT-ID"]?.stringValue()
            tenantPropagator.executeWithTenant(tenantId) {
                tryProcessMessage(message, queueUrl)
            }
        }
    }
}
```

Exemplo de uso em um handler específico:
```kotlin
class MeuHandler(
    tenantPropagator: TenantPropagator,
    amazonSQS: SqsClient,
    configuration: MessageHandlerConfiguration
) : CoroutineAbstractSQSHandler(
    tenantPropagator,
    amazonSQS,
    configuration,
    "minha-fila"
) {
    override fun handleMessage(message: Message): SQSHandlerResponse {
        // O tenant já está propagado automaticamente
        // Pode acessar as configurações específicas do tenant normalmente
        return SQSHandlerResponse(true)
    }
}
```

## 5. Alterações no DynamoDB

### 5.1 Modificações nos Repositories e Entities

Para suportar multi-tenancy no DynamoDB, é necessário fazer alterações significativas na estrutura das chaves e nos repositories. Aqui está um guia detalhado:

### 5.2 **Modificação nas Partition Keys**
```kotlin
// Antes
private fun buildPartitionKey(userId: UserId): String = userId.value

// Depois
private fun buildPartitionKey(userId: UserId): String = userId.value + "#" + tenantService.getTenantName()
```

### 5.3 **Modificação nos Índices**
```kotlin
// Antes
private fun buildIndex1HashKey(date: LocalDate): String = "$HISTORY_DATE_REGISTER#${date.format(dateFormat)}"

// Depois
private fun buildIndex1HashKey(date: LocalDate): String = "$HISTORY_DATE_REGISTER#${tenantService.getTenantName()}#${date.format(dateFormat)}"
```


## 6. Publicação de mensagem SQS

### 6.1 Adição do Header Tenant
Todas as publicações de mensagens nas filas SQS devem incluir o tenantId como header para garantir a propagação correta do contexto do tenant. Isso é necessário porque:

1. Os handlers SQS extraem o tenantId dos headers da mensagem para propagar o contexto
2. Sem o tenantId no header, o processamento da mensagem não terá acesso às configurações corretas do tenant
3. O header deve usar a chave `X-TENANT-ID` para manter consistência com o resto da aplicação

Exemplo de publicação de mensagem:
```kotlin
sqsClient.sendMessage {
    it.queueUrl(queueUrl)
        .messageBody(messageBody)
        .messageAttributes(
            mapOf(
                "X-TENANT-ID" to MessageAttributeValue.builder()
                    .dataType("String")
                    .stringValue(tenantId)
                    .build()
            )
        )
}
```



## 7. Variáveis de Ambiente e Secrets
A mudança na estrutura dos ymls (com o prefixo `tenants.{TENANT_NAME}`) trouxe mudanças na forma como os secrets são gerenciados, mas isso é transparente para o sistema. O que mudou foi:

### 7.1. **No código/application.yml**:
   - O sistema continua vendo apenas o label `FROM_AWS_SECRETS`
   - Não há mudança na forma como o código acessa os secrets
   - O Micronaut continua resolvendo os secrets da mesma forma

### 7.2. **Na infraestrutura (Terraform)**:
   - A mudança real acontece no mapeamento dos secrets no Terraform
   - Antes: Um único secret para a instancia
   - Depois: Secrets específicos por tenant

Exemplo prático:

**Antes (Single-tenant)**:
```hcl
# Terraform
resource "aws_secretsmanager_secret" "whatsapp_token" {
  name = "WHATSAPP_API_TOKEN"
  value = "token-value"
}
```

**Depois (Multi-tenant)**:
```hcl
# Terraform
resource "aws_secretsmanager_secret" "friday_whatsapp_token" {
  name = "TENANTS_FRIDAY_WHATSAPP_API_TOKEN"
  value = "friday-token-value"
}

resource "aws_secretsmanager_secret" "motorola_whatsapp_token" {
  name = "TENANTS_MOTOROLA_WHATSAPP_API_TOKEN"
  value = "motorola-token-value"
}
```

**No código (permanece igual)**:
```yaml
# application-friday.yml
tenants:
  FRIDAY:
    integrations:
      whatsapp:
        apiToken: FROM_AWS_SECRETS

# application-motorola.yml
tenants:
  MOTOROLA:
    integrations:
      whatsapp:
        apiToken: FROM_AWS_SECRETS
```

A principal mudança é que agora, em vez de ter um único secret `WHATSAPP_API_TOKEN`, temos secrets específicos por tenant (`FRIDAY_WHATSAPP_API_TOKEN`, `MOTOROLA_WHATSAPP_API_TOKEN`). O sistema continua vendo apenas `FROM_AWS_SECRETS`, mas o Terraform mapeia esse label para o secret correto de cada tenant.

## 8. Instâncias Single-Tenant

Para casos onde uma instância precisa permanecer single-tenant (ex: ambientes legados ou específicos), o sistema suporta uma configuração especial que faz com que a instância sempre use um tenant específico.

### 8.1 Configuração
No `application-{friday}.yml` da instância, adicione a configuração:

```yaml
single-tenant:
  enabled: true
  tenantId: TENANT_NAME  # ex: FRIDAY, MOTOROLA
```

### 8.2 Comportamento
Quando `single-tenant.enabled` está `true`:

1. O `TenantService` sempre retornará o tenant configurado em `single-tenant.tenantId`
2. Todas as consultas de tenant retornarão o mesmo valor
3. O `TenantPropagator` usará automaticamente o tenant configurado
4. As configurações específicas do tenant continuam sendo carregadas normalmente