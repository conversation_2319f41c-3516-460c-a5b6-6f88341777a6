image: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/openjdk:17-jdk-slim

default:
  tags:
    - friday
include:
  - template: Code-Quality.gitlab-ci.yml
  - template: Workflows/MergeRequest-Pipelines.gitlab-ci.yml
  - template: Security/SAST.gitlab-ci.yml
  - project: "via1/gitlab-ci-environment"
    file: "/envs/all-ai-chatbot.yml"
    ref: "main"

code_quality:
  interruptible: true
  rules:
    - if: '$CODE_QUALITY_DISABLED'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"' # Run code quality job in merge request pipelines
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'      # Run code quality job in pipelines on the master branch (but not in other branch pipelines)
    - if: '$CI_COMMIT_TAG'
      when: never

stages:
  - build
  - test
  - integration_test
  - release
  - release_environments
  - deploy

sast:
  stage: test
  artifacts:
    paths: [ gl-sast-report.json, gl-code-quality-report.json ]
  when: manual
  allow_failure: true

variables:
  CONTAINER_RELEASE_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
  MAVEN_OPTS: "-Dhttps.protocols=TLSv1.2 -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version -DinstallAtEnd=true -DdeployAtEnd=true"
  TASK_DEFINITION_NAME: "ai-chatbot-task"
  CLUSTER_NAME: "ai-chatbot-cluster"
  SERVICE_NAME: "ai-chatbot-service"
  TASK_ROLE: "ai-chatbot-task-role"
  TASK_EXECUTION_ROLE: "ai-chatbot-execution-role"
  RELEASE_TRIGGER_COMMIT_MESSAGE: "/#deploy(stg|modattastg|mepoupestg|motorola|gigu|multitenant(stg)?)/"

before_script:
  - chmod +x mvnw

build:
  stage: build
  interruptible: true
  allow_failure: false
  cache:
    paths:
      - .m2/repository
  script:
    - LOGGER_LEVELS_ROOT=OFF;OPENAI_TOKEN=; ./mvnw $MAVEN_CLI_OPTS test verify -s ci/settings.xml
  artifacts:
    paths:
      - target/ai-chatbot-0.1.jar
  except:
    - tags

chatgpt_integration_test:
  stage: integration_test
  when: manual
  needs: []
  interruptible: true
  allow_failure: true
  cache:
    paths:
      - .m2/repository
  script:
    - TENANTS_FRIDAY_INTEGRATIONS_WHATSAPP_API_TOKEN=$INTEGRATIONS_WHATSAPP_API_TOKEN ./mvnw -batch-mode --errors -s ci/settings.xml --fail-at-end --show-version -Dtest="ai/integration/**" test
  rules:
    - when: manual

release:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  allow_failure: false
  needs: [ build ]
  stage: release
  #  services:
  #    - docker:dind
  script:
    - curl -fsSL -o dd-java-agent.jar 'https://dtdg.co/latest-java-tracer'
    - docker login -u $CI_DEPENDENCY_PROXY_USER -p $CI_DEPENDENCY_PROXY_PASSWORD $CI_DEPENDENCY_PROXY_SERVER
    - docker build --build-arg DOCKER_IMAGE_MIRROR="${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/" --build-arg APP_VERSION=$CI_COMMIT_SHORT_SHA -t $CONTAINER_RELEASE_IMAGE .
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push $CONTAINER_RELEASE_IMAGE

  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ $RELEASE_TRIGGER_COMMIT_MESSAGE
      when: on_success
  tags:
    - friday

.release_environment: &release_environment
  - apk add --update --no-cache curl build-base py-pip python3-dev
  - python3 -V  # Print out python version for debugging.
  - pip install awscli --upgrade --user
  - export PATH=~/.local/bin:$PATH # Required for awscli.
  - aws configure set region $AWS_REGION
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  - docker pull $CONTAINER_RELEASE_IMAGE
  - docker tag $CONTAINER_RELEASE_IMAGE $AWS_ECR_REPOSITORY:$CI_COMMIT_SHORT_SHA
  - $(aws ecr get-login --no-include-email)
  - docker push $AWS_ECR_REPOSITORY:$CI_COMMIT_SHORT_SHA

friday_release_staging:
  image: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:stable
  stage: release_environments
  when: manual
  needs: [ release ]
  allow_failure: true
  environment:
    name: staging
  #  services:
  #    - docker:dind
  script:
    - *release_environment
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ /#deploystg/
      when: always

friday_release_production:
  image: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:stable
  stage: release_environments
  when: manual
  needs: [ release ]
  allow_failure: true
  environment:
    name: production
  #  services:
  #    - docker:dind
  script:
    - *release_environment
  only:
    - main

me_poupe_release_production:
  image: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:stable
  stage: release_environments
  when: manual
  needs: [ release ]
  allow_failure: true
  environment:
    name: me-poupe
  #  services:
  #    - docker:dind
  script:
    - *release_environment
  only:
    - main

.deploy-ecs: &deploy-ecs
  - aws configure set region $AWS_REGION
  - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_DEFINITION_NAME")
  - NEW_CONTAINER_DEFINITION=$(echo $TASK_DEFINITION | python $CI_PROJECT_DIR/ci/update_task_definition_image.py $AWS_ECR_REPOSITORY:$CI_COMMIT_SHORT_SHA)
  - aws ecs register-task-definition --family "${TASK_DEFINITION_NAME}" --container-definitions "${NEW_CONTAINER_DEFINITION}" --requires-compatibilities "FARGATE" --network-mode awsvpc --task-role-arn arn:aws:iam::${AWS_ACCOUNT_ID}:role/${TASK_ROLE} --execution-role-arn arn:aws:iam::${AWS_ACCOUNT_ID}:role/${TASK_EXECUTION_ROLE} --cpu $CPU --memory $MEMORY
  - aws ecs update-service --cluster "${CLUSTER_NAME}" --service "${SERVICE_NAME}"  --task-definition "${TASK_DEFINITION_NAME}" --desired-count $DESIRED_COUNT

friday_deploy_staging:
  stage: deploy
  needs: [ friday_release_staging ]
  image:
    name: amazon/aws-cli:2.27.50
    entrypoint: [ "" ]
  environment:
    name: staging # faz sentido mudar pra friday na config do gitlab
  allow_failure: false
  script:
    - export MEMORY=4096
    - export CPU=1024
    - export DESIRED_COUNT=1
    - *deploy-ecs
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ /#deploystg/
      when: always

friday_deploy_production:
  stage: deploy
  needs: [ friday_release_production ]
  image:
    name: amazon/aws-cli:2.27.50
    entrypoint: [ "" ]
  environment:
    name: production # faz sentido mudar pra friday na config do gitlab
  allow_failure: false
  script:
    - export MEMORY=4096
    - export CPU=1024
    - export DESIRED_COUNT=1
    - *deploy-ecs
  only:
    - main

me_poupe_deploy_production:
  stage: deploy
  needs: [ me_poupe_release_production ]
  image:
    name: amazon/aws-cli:2.27.50
    entrypoint: [ "" ]
  environment:
    name: me-poupe
  variables:
    TASK_DEFINITION_NAME: "me-poupe-ai-chatbot-task"
    CLUSTER_NAME: "me-poupe-bill-payment-cluster"
    SERVICE_NAME: "me-poupe-ai-chatbot-service"
    TASK_ROLE: "me-poupe-ai-chatbot-task-role"
    TASK_EXECUTION_ROLE: "me-poupe-ai-chatbot-execution-role"
  allow_failure: false
  script:
    - export MEMORY=4096
    - export CPU=1024
    - export DESIRED_COUNT=1
    - *deploy-ecs
  only:
    - main