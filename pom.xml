<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>ai.chatbot</groupId>
    <artifactId>ai-chatbot</artifactId>
    <version>0.1</version>
    <packaging>${packaging}</packaging>

    <parent>
        <groupId>io.micronaut.platform</groupId>
        <artifactId>micronaut-parent</artifactId>
        <version>4.2.1</version>
    </parent>
    <properties>
        <packaging>jar</packaging>
        <jdk.version>17</jdk.version>
        <release.version>17</release.version>
        <micronaut.version>4.2.1</micronaut.version>
        <micronaut.aot.enabled>false</micronaut.aot.enabled>
        <micronaut.aot.packageName>ai.chatbot.aot.generated</micronaut.aot.packageName>
        <micronaut.runtime>netty</micronaut.runtime>
        <openai-java.version>0.18.2</openai-java.version>
        <kotlinVersion>1.8.22</kotlinVersion>
        <exec.mainClass>ai.chatbot.ApplicationKt</exec.mainClass>
        <comm-centre.version>2.49</comm-centre.version>
        <apache.lucene.version>8.11.4</apache.lucene.version>
        <kotest.version>5.8.0</kotest.version>
    </properties>

    <repositories>
        <repository>
            <id>gitlab-maven</id>
            <url>https://gitlab.com/api/v4/projects/16013883/packages/maven</url>
        </repository>
        <repository>
            <id>sonatype-snapshots</id>
            <url>https://s01.oss.sonatype.org/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
        <repository>
            <id>central</id>
            <url>https://repo.maven.apache.org/maven2</url>
        </repository>
        <repository>
            <id>dynamodb-local-oregon</id>
            <name>DynamoDB Local Release Repository</name>
            <url>https://s3-us-west-2.amazonaws.com/dynamodb-local/release</url>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-http-server-netty</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut.reactor</groupId>
            <artifactId>micronaut-reactor-http-client</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut.kotlin</groupId>
            <artifactId>micronaut-kotlin-runtime</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-jackson-databind</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-http-client</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut.rxjava2</groupId>
            <artifactId>micronaut-rxjava2</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut.rxjava2</groupId>
            <artifactId>micronaut-rxjava2-http-client</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut.validation</groupId>
            <artifactId>micronaut-validation</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut.cache</groupId>
            <artifactId>micronaut-cache-caffeine</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.theokanning.openai-gpt3-java</groupId>
            <artifactId>service</artifactId>
            <version>${openai-java.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
            <version>${kotlinVersion}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
            <version>${kotlinVersion}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>dynamodb</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>dynamodb-enhanced</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>transcribe</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sqs</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>ses</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-encryption-sdk-java</artifactId>
            <version>2.4.1</version>
            <scope>compile</scope> <!-- Usando a V1, porque o Cognito criptografa utilizando um envelope. e apenas a V1 consegue abrir.  -->
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>kms</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>7.4</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-micronaut</artifactId>
            <version>5.6.0</version>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-dynamodb2</artifactId>
            <version>5.6.0</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.ganadist.sqlite4java</groupId>
            <artifactId>libsqlite4java-osx-arm64</artifactId>
            <type>dylib</type>
            <version>1.0.392</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>DynamoDBLocal</artifactId>
            <version>1.13.2</version>
            <scope>test</scope>
        </dependency>
        <!-- Also required to enable endpoint -->
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-management</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-assertions-core-jvm</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-runner-junit5-jvm</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Explicit version overrides for Kotest transitive dependencies -->
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-assertions-shared-jvm</artifactId>
            <version>${kotest.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-common-jvm</artifactId>
            <version>${kotest.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-assertions-api-jvm</artifactId>
            <version>${kotest.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-framework-api-jvm</artifactId>
            <version>${kotest.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-framework-engine-jvm</artifactId>
            <version>${kotest.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-framework-discovery-jvm</artifactId>
            <version>${kotest.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-extensions-jvm</artifactId>
            <version>${kotest.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-framework-concurrency-jvm</artifactId>
            <version>${kotest.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>io.micronaut.test</groupId>
            <artifactId>micronaut-test-kotest5</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.mockk</groupId>
            <artifactId>mockk-jvm</artifactId>
            <scope>test</scope>
            <version>1.13.5</version>
        </dependency>
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.multitenancy</groupId>
            <artifactId>micronaut-multitenancy</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>1.70</version>
        </dependency>
        <dependency>
            <groupId>com.datadoghq</groupId>
            <artifactId>dd-trace-api</artifactId>
            <version>1.18.3</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.arrow-kt</groupId>
            <artifactId>arrow-core</artifactId>
            <version>1.2.0</version>
        </dependency>
        <dependency>
            <groupId>io.arrow-kt</groupId>
            <artifactId>arrow-fx-coroutines</artifactId>
            <version>1.2.0</version>
        </dependency>
        <dependency>
            <groupId>io.via1</groupId>
            <artifactId>communication-centre</artifactId>
            <version>${comm-centre.version}</version>
        </dependency>


        <!-- adiciona e propaga traceId e spanId nas chamadas http e filas -->
        <dependency>
            <groupId>io.micronaut.tracing</groupId>
            <artifactId>micronaut-tracing-opentelemetry-http</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut.tracing</groupId>
            <artifactId>micronaut-tracing-opentracing</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry.instrumentation</groupId>
            <artifactId>opentelemetry-aws-sdk-2.2</artifactId>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-extension-kotlin</artifactId>
        </dependency>

        <!-- necessario para propagar traceId e spanId nas filas, desligado ate substituir as factories no codigo por config no yml -->
        <!--        <dependency>-->
        <!--            <groupId>io.micronaut.aws</groupId>-->
        <!--            <artifactId>micronaut-aws-sdk-v2</artifactId>-->
        <!--        </dependency>-->

        <!-- exporta o traceId e spanId para log -->
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-exporter-logging</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry.instrumentation</groupId>
            <artifactId>opentelemetry-logback-mdc-1.0</artifactId>
        </dependency>
        <!-- necessario para conseguir desligar o log do opentelemetry que suja o console -->
        <!-- configurar direto no logging.properties dentro do projeto nao funcionou -->
        <!-- OBS: precisa do LevelChangePropagator no logback.xml para nao impactar a performance -->
        <!-- e desligar o log de cada biblioteca que use o java util logging, por exemplo google cloud platform -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jul-to-slf4j</artifactId>
            <version>2.0.9</version>
        </dependency>

        <dependency>
            <groupId>io.micronaut.micrometer</groupId>
            <artifactId>micronaut-micrometer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.micrometer</groupId>
            <artifactId>micronaut-micrometer-registry-statsd</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
            <version>${apache.lucene.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-queryparser</artifactId>
            <version>${apache.lucene.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.18.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-analyzers-phonetic</artifactId>
            <version>${apache.lucene.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.5.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
            <version>2.15.2</version>
        </dependency>
        <!-- ArchUnit para testes de arquitetura -->
        <dependency>
            <groupId>com.tngtech.archunit</groupId>
            <artifactId>archunit</artifactId>
            <version>1.1.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tngtech.archunit</groupId>
            <artifactId>archunit-junit5</artifactId>
            <version>1.1.0</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>io.micronaut.maven</groupId>
                <artifactId>micronaut-maven-plugin</artifactId>
                <configuration>
                    <configFile>aot-${packaging}.properties</configFile>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.10</version>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeScope>test</includeScope>
                            <includeTypes>so,dll,dylib</includeTypes>
                            <outputDirectory>${project.basedir}/native-libs</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <includes>
                        <include>**/*Spec.*</include>
                        <include>**/*Test.*</include>
                    </includes>
                    <excludes>
                        <exclude>ai/integration/**</exclude>
                    </excludes>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter-engine</artifactId>
                        <version>${junit5.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>kotlin-maven-plugin</artifactId>
                <groupId>org.jetbrains.kotlin</groupId>
                <version>${kotlinVersion}</version>
                <configuration>
                    <jvmTarget>${jdk.version}</jvmTarget>
                    <compilerPlugins>
                        <plugin>all-open</plugin>
                    </compilerPlugins>
                    <pluginOptions>
                        <option>all-open:annotation=io.micronaut.aop.Around</option>
                    </pluginOptions>
                </configuration>
                <executions>
                    <execution>
                        <id>kapt</id>
                        <goals>
                            <goal>kapt</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
                            </sourceDirs>
                            <annotationProcessorPaths combine.self="override">
                                <annotationProcessorPath>
                                    <groupId>io.micronaut</groupId>
                                    <artifactId>micronaut-inject-java</artifactId>
                                    <version>${micronaut.core.version}</version>
                                </annotationProcessorPath>
                                <annotationProcessorPath>
                                    <groupId>io.micronaut</groupId>
                                    <artifactId>micronaut-graal</artifactId>
                                    <version>${micronaut.core.version}</version>
                                </annotationProcessorPath>
                                <annotationProcessorPath>
                                    <groupId>io.micronaut.tracing</groupId>
                                    <artifactId>micronaut-tracing-opentelemetry-annotation</artifactId>
                                    <version>${micronaut.tracing.version}</version>
                                </annotationProcessorPath>
                                <annotationProcessorPath>
                                    <groupId>io.micronaut.validation</groupId>
                                    <artifactId>micronaut-validation-processor</artifactId>
                                    <version>${micronaut.validation.version}</version>
                                </annotationProcessorPath>
                            </annotationProcessorPaths>
                            <annotationProcessorArgs>
                                <annotationProcessorArg>micronaut.processing.group=ai.chatbot</annotationProcessorArg>
                                <annotationProcessorArg>micronaut.processing.module=aiChatbot</annotationProcessorArg>
                            </annotationProcessorArgs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-kapt</id>
                        <goals>
                            <goal>test-kapt</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                            </sourceDirs>
                            <annotationProcessorPaths combine.self="override">
                                <annotationProcessorPath>
                                    <groupId>io.micronaut</groupId>
                                    <artifactId>micronaut-inject-java</artifactId>
                                    <version>${micronaut.core.version}</version>
                                </annotationProcessorPath>
                                <annotationProcessorPath>
                                    <groupId>io.micronaut</groupId>
                                    <artifactId>micronaut-graal</artifactId>
                                    <version>${micronaut.core.version}</version>
                                </annotationProcessorPath>
                            </annotationProcessorPaths>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                                <sourceDir>${project.basedir}/target/generated-sources/kapt/test</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlinVersion}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <proc>none</proc>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                </configuration>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>java-compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>java-test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>ktlint</id>
                        <phase>validate</phase>
                        <configuration>
                            <target name="ktlint">
                                <java taskname="ktlint" dir="${basedir}" fork="true" failonerror="true"
                                      classpathref="maven.plugin.classpath" classname="com.pinterest.ktlint.Main">
                                    <!-- Note: the JVM arg below is only required when running ktlint with Java 16+ in format mode. -->
                                    <jvmarg value="--add-opens=java.base/java.lang=ALL-UNNAMED"/>
                                    <!-- see https://pinterest.github.io/ktlint/install/cli/#command-line-usage for more information -->
                                    <arg value="src/**/*.kt"/>
                                </java>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>ktlint-format</id>
                        <configuration>
                            <target name="ktlint">
                                <java taskname="ktlint" dir="${basedir}" fork="true" failonerror="true"
                                      classpathref="maven.plugin.classpath" classname="com.pinterest.ktlint.Main">
                                    <!-- Note: the JVM args below is only required when running ktlint with Java 16+ in format mode -->
                                    <jvmarg value="--add-opens=java.base/java.lang=ALL-UNNAMED"/>
                                    <!-- see https://pinterest.github.io/ktlint/install/cli/#command-line-usage for more information -->
                                    <arg value="-F"/>
                                    <arg value="src/**/*.kt"/>
                                </java>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.pinterest</groupId>
                        <artifactId>ktlint</artifactId>
                        <version>0.48.2</version>
                    </dependency>
                    <!-- additional 3rd party ruleset(s) can be specified here -->
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>
