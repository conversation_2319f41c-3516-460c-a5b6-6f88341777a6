# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an AI-powered chatbot for the Friday financial application, built with Micronaut and Kotlin. The chatbot, known as "Fred", assists users with bill payments, financial tasks, and account management. It integrates with OpenAI GPT-4o for conversational AI, AWS services for infrastructure, and various payment services.

## Tech Stack & Architecture

### Core Framework
- **Micronaut**: Main application framework with dependency injection
- **Kotlin**: Primary programming language (targeting JDK 17)
- **Maven**: Build system and dependency management

### Key Dependencies
- **OpenAI GPT-4o**: Conversational AI integration via openai-gpt3-java library
- **AWS SDK**: DynamoDB, SQS, S3, KMS, Transcribe services
- **Arrow-kt**: Functional programming utilities (Either, functional error handling)
- **Kotest**: Testing framework with MockK for mocking
- **Apache Lucene**: Text search functionality
- **Micronaut Features**: HTTP client/server, validation, caching (Caffeine), RxJava2, OpenTelemetry tracing

### Architecture Patterns
- **Hexagonal Architecture**: Adapters pattern with clear separation between business logic and external services
- **Event-Driven**: SQS message handling for async operations
- **Multi-Environment**: Support for Friday, Me Poupe, Motorola, and Staging environments

## Common Development Commands

### Build & Test
```bash
# Compile the application
make compile
# or
./mvnw test-compile

# Run all tests (excludes integration tests)
./mvnw test

# Clean and package (skip tests)
make package
# or
./mvnw clean package -Dmaven.test.skip=true

# Clean project
make clean
```

### Code Quality
```bash
# Run ktlint (Kotlin linter)
make lint
# or
./mvnw antrun:run@ktlint

# Auto-format code
make format
# or
./mvnw antrun:run@ktlint-format
```

### Running the Application
```bash
# Run Friday environment
make run-friday
# or
MICRONAUT_ENVIRONMENTS=production ./mvnw mn:run

# Run Me Poupe environment
make run-me-poupe
# or
MICRONAUT_ENVIRONMENTS=me-poupe ./mvnw mn:run
```

### Local Development Setup
For ARM-based Macs (M1/M2), DynamoDB Local requires native library setup:
```bash
mvn clean install -DskipTests
cp ./native-libs/libsqlite4java-osx-arm64-1.0.392.dylib ./native-libs/libsqlite4java-osx-1.0.392.dylib
```

### Testing Individual Components
```bash
# Run specific test class
./mvnw test -Dtest=ConversationServiceTest

# Run integration tests (excluded by default)
./mvnw test -Dtest="ai/integration/**"
```

## Code Architecture

### Package Structure
- `ai.chatbot.adapters/`: External service integrations (AWS, OpenAI, payment services)
- `ai.chatbot.app/`: Core business logic and domain models
- `ai.chatbot.app.conversation/`: Conversation management and state handling
- `ai.chatbot.app.prompt/`: AI prompt templates and services
- `ai.chatbot.app.notification/`: Message formatting and delivery
- `ai.chatbot.app.bill/`: Bill payment and management logic
- `ai.chatbot.app.transaction/`: Transaction processing

### Key Components

#### Conversation Flow
- **ConversationService**: Main orchestrator for user interactions
- **ConversationState**: Manages conversation context and user session state
- **ActionsManager**: Processes OpenAI function calls and executes actions
- **InteractionWindow**: Manages 24-hour WhatsApp interaction windows

#### AI Integration
- **OpenAIAdapter**: Handles GPT-4o API communication
- **PromptService**: Manages context-aware prompts for different environments
- **FridayPrompts/MePoupePrompts/MotorolaPrompts**: Environment-specific prompt templates

#### Payment Processing
- **PaymentAdapter**: Interfaces with bill payment services
- **TransactionService**: Manages PIX and Open Finance transactions
- **BillPaymentAdapter**: Integration with backend payment systems

#### Data Storage
- **DynamoDB**: Primary storage for conversation history, user state, and transactions
- **ChatHistoryDbRepository**: Manages conversation persistence
- **TransactionDbRepository**: Handles transaction state management

### Environment Configuration
The application supports multiple environments through Micronaut's environment-specific configuration:
- `application-friday.yml`: Friday app configuration
- `application-me-poupe.yml`: Me Poupe environment
- `application-motorola.yml`: Motorola partnership environment
- `application-staging.yml`: Staging environment settings

### Message Processing
- **SQS Message Handlers**: Async processing of WhatsApp webhooks, notifications, and state updates
- **BlipMessageHandler**: Handles incoming WhatsApp messages via Blip integration
- **WaCommCentreAdapter**: Alternative WhatsApp integration via communication center

### Security & Monitoring
- **KmsAdapter**: AWS KMS integration for encryption/decryption
- **OpenTelemetry**: Distributed tracing and monitoring
- **Datadog**: APM and metrics collection
- **Feature Flags**: Runtime feature toggling

## Development Guidelines

### Testing Strategy
- Unit tests use Kotest with MockK for mocking
- Integration tests are excluded from default build (run separately)
- DynamoDB Local is used for local testing
- Test utilities in `test/kotlin/ai/integration/chatbot/utils/`

### Local Development
- Use ngrok to expose local server for WhatsApp webhook testing
- Point to staging database for realistic testing scenarios
- Integration tests can be run to test GPT-4 conversation flows

### Code Style
- Kotlin code style enforced by ktlint
- Use Arrow-kt's Either for error handling
- Follow hexagonal architecture patterns
- Environment-specific logic uses Micronaut annotations (@Friday, @MePoupe, etc.)

### AI Prompt Development
- Prompts are version-controlled in Kotlin files, not external files
- Context-aware prompts adapt to user state and environment
- Function calling is used extensively for structured AI interactions
- Test prompt changes with integration tests before deployment