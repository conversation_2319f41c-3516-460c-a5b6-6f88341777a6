## AI Chatbot

## Description
Chat assistant para ajudar no dia a dia do app Friday. Ele é capaz de:
- Iniciar uma conversa avisando que o usuário tem contas a pagar
- Iniciar um One-Pix-Pay ou um Pagar com meu banco
- Escolher um subset das contas para pagar

## Funcionamento
- Cron job para checar se há contas a pagar (de uma lista de usuários early access)
- A Job consulta direto no banco de dados do BillPayment
- Envia mensagem WhatsApp para o usuário quando ele tem contas a pagar via template Blip
- Recebe um webhook da Blip quando o usuário responde a mensagem
  - É possível testar o fluxo localmente rodando a aplicando apontando pro banco de staging. Deve-se conectar a aplicação local via ngrok e configurar o webhook da blip pra responder pro link gerado pelo ngrok
- Usa o GPT-4 para analisar o histórico de conversas, tomar as decisões e interagir com o usuário
- O prompt que está sendo usado pode ser encontrado [`nesse link`](https://gitlab.com/via1/ai-chatbot/-/blob/main/src/main/kotlin/ai/friday/app/prompt/Prompts.kt) 
- Grava o histórico de conversas com o GPT na tabela `Friday-AiChatHistory` do DynamoDB
- Marcamos as contas que serão pagas num campo de estado junto ao histórico de conversa
- Geração de código One-Pix-Pay via backend Friday
- Envia mensagens WhatsApp para o usuário via Blip (aproveitando a janela de 24hs da resposta do usuário)

## Testando localmente

Há duas maneiras de testar o fluxo localmente:

- Rodando a aplicação localmente e apontando para o banco de staging. Deve-se conectar a aplicação local via [ngrok](https://ngrok.com/) e configurar o webhook da blip pra responder pro link gerado pelo ngrok.
- Rodando o teste de integração da classe `ConversationService`. Algumas coisas estão mockadas, mas é possível testar o fluxo de conversa com o GPT-4.

#### ARM Based Macs

DynamoDB Local (usado para teste local) usa SQLite4java que utiliza o binário x86 da biblioteca SQLite. Rode no terminal:

> mvn clean install -DskipTests
>
> cp ./native-libs/libsqlite4java-osx-arm64-1.0.392.dylib ./native-libs/libsqlite4java-osx-1.0.392.dylib

## Aprendizados
- Não devemos inserir informações muito longas nos nossos prompts.
  - Como código Pix, Links de Pagamento, etc. Isso acaba gerando timeout no ChatGPT.
- A maior parte das soluções são mudanças no prompt. Um exemplo recente é a questão de fazer ele aprender a separar opções com \n. Por mais que a gente precise adaptar para o WhatsApp, não foi necessário tratar o texto retornado adicionando quebra de linha.

## Backlog
- [x] Criar o terraform para a infra de produção
- [x] Criar um pipeline de CI/CD
- [x] Tirar testes integrados do build (depois colocar como uma job separada)
- [x] Atualizar a data atual no contexto do Prompt
- [x] Utilizar o login msisdn para consultar o AccountId (no lugar do AccountRegister)
- [ ] Tornar o serviço independente do banco de dados do BillPayment
- [ ] Fazer a chamada ao ChatbotController do BillPayment por dentro da VPC e não pela Internet

## Roadmap
- [x] Permitir pagar com meu banco via Open Finance
- [x] Permitir escolher um subset das contas para pagar
- [ ] Implementar os fluxos de último aviso de vencimento do dia e existem contas vencidas ontem (TODO: CONFIRMAR)

## Débitos técnicos
- [ ] Verificar urls no application.yml, pois estão hardcoded
- [ ] Hoje a job que processa as bills a vencer dos usuários faz isso sem enviar pra uma fila. Seria melhor deixar esse processamento assíncrono. Como temos poucos usuários, ainda não há impacto.
- [ ] No fluxo de pagar as contas que estão vencendo, não consideramos o saldo do usuário. Pegamos todas as contas que ele escolheu pagar, somamos os valores e geramos o one pix pay.
- [ ] No fluxo de pagar as contas que estão vencendo, se o usuário tem saldo para pagar as contas que deseja pagar, o chatbot deveria informar e não gerar o código pix
- [ ] No fluxo de pagar com Open Finance, um usuário que nunca fez cashin com uma conta de mesmo CPF não consegue efetuar o pagamento por Open Finance
- [ ] Existe uma vulnerabilidade em como a api do chatbot se comunica com o billPayment. Hoje essa comunicação está aberta.
- [ ] Endpoint que roda a job na mão não tem autenticação.
- [ ] No fluxo de pagar as contas que vencem hoje, caso o usuário opte por pagar mais tarde e alguma já tiver sido paga, da erro. O bot deveria buscar novamente as contas a pagar e listar elas atualizadas.

## Ajuda
- Para gerar o regexp do MessageParser utilizamos esse [prompt](https://chat.openai.com/share/ffb27553-b549-410d-8a00-9fd243b2f047) com o ChatGPT.
