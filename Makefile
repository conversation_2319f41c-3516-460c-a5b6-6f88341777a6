DEFAULT_GOAL = compile

MVN = ./mvnw

.PHONY: compile clean format-cc cc lint format

compile:
	$(MVN) test-compile

package:
	$(MVN) clean package -Dmaven.test.skip=true

clean:
	$(MVN) clean

format-cc: format cc

format-cc-test: format cc
	OPENAI_TOKEN=; $(MVN) test verify

cc:
	$(MVN) clean test-compile

lint:
	$(MVN) antrun:run@ktlint

format:
	$(MVN) antrun:run@ktlint-format

pre-commit-install:
	echo "make format && git add ." > .git/hooks/pre-commit
	chmod +x .git/hooks/pre-commit

run-friday:
	MICRONAUT_ENVIRONMENTS=production $(MVN) mn:run

run-me-poupe:
	MICRONAUT_ENVIRONMENTS=me-poupe $(MVN) mn:run

#MODEL := "gpt-4.1"
MODEL := "gpt-4o"
model-report:
	@bash "script/report.sh" $(MVN) $(MODEL)