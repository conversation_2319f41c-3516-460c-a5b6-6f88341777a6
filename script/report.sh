#!/bin/sh

command -v jq >/dev/null 2>&1 || { echo >&2 "Precisa instalar o 'jq' - https://jqlang.org/"; exit 1; }

MVN=$1
MODEL="in application.yml"
MODEL_PARAM=""

if [ -n "$2" ] ; then
  MODEL=$2
  MODEL_PARAM="-Dopenai.betaModel=$MODEL -Dopenai.model=$MODEL"
fi

LOGBACK_TMP_FILE=$(mktemp)
TESTS_TMP_FILE=$(mktemp)

echo "Running tests for model $MODEL..."
echo "Report log file: $LOGBACK_TMP_FILE"
echo "Tests log file: $TESTS_TMP_FILE"
#$MVN -Dtest="ai/integration/chatbot/MakePaymentIntegrationTest*" $MODEL_PARAM -Dlogback.configurationFile=script/logback.xml -DlogFile=$LOGBACK_TMP_FILE test > $TESTS_TMP_FILE 2>&1
$MVN -Dtest="ai/integration/**" $MODEL_PARAM -Dlogback.configurationFile=script/logback.xml -DlogFile=$LOGBACK_TMP_FILE test > $TESTS_TMP_FILE 2>&1

BUILD_SUCCESS=$(grep -B1 -A4 "BUILD SUCCESS" $TESTS_TMP_FILE)
if [ -n "$BUILD_SUCCESS" ]; then
  grep -B1 -A4 "BUILD SUCCESS" $TESTS_TMP_FILE
else
  grep "Tests run" $TESTS_TMP_FILE
  grep -B1 -A4 "BUILD FAILURE" $TESTS_TMP_FILE
  exit 1
fi

TIME_TAKEN=($(cat $LOGBACK_TMP_FILE | jq .timeTaken))

echo "OpenAi response time for ${#TIME_TAKEN[@]} requests"
echo "Total:" $(printf "%d\n" "${TIME_TAKEN[@]}" | awk '{ sum+=$0 } END { printf("%0.2f segundos", sum/1000) }')
echo "Average:" $(printf "%d\n" "${TIME_TAKEN[@]}"| awk '{ sum += $0 } END { if (NR > 0) printf("%0.2f segundos", sum/1000/NR) }')