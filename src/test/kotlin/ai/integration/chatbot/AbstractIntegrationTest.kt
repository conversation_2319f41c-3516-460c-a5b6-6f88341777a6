package ai.integration.chatbot

import DynamoD<PERSON><PERSON><PERSON>s.setupDynamoDB
import ai.chatbot.adapters.api.TransactionController
import ai.chatbot.adapters.dynamodb.ChatHistoryDbRepository
import ai.chatbot.adapters.dynamodb.ChatHistoryDynamoDAO
import ai.chatbot.adapters.dynamodb.ChatHistoryStateDbRepository
import ai.chatbot.adapters.dynamodb.ChatHistoryStateDynamoDAO
import ai.chatbot.adapters.dynamodb.TransactionDbRepository
import ai.chatbot.adapters.dynamodb.TransactionDynamoDAO
import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.AvailableLimitResponse
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.HistoryStateRepository
import ai.chatbot.app.NotificationService
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.TransactionRepository
import ai.chatbot.app.TransactionService
import ai.chatbot.app.bill.PendingBillsService
import ai.chatbot.app.billView
import ai.chatbot.app.billView2
import ai.chatbot.app.billView3
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.config.TenantServiceImpl
import ai.chatbot.app.conversation.ActionExecutorLocator
import ai.chatbot.app.conversation.ActionsManager
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.ConversationProcessor
import ai.chatbot.app.conversation.HistoryStateType
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.conversation.actions.ActionExecutorsHelpers
import ai.chatbot.app.newActionExecutorLocator
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.FridayNotificationContextTemplatesService
import ai.chatbot.app.notification.TemplateInfoService
import ai.chatbot.app.prompt.FridayPromptService
import ai.chatbot.app.prompt.TenantPromptService
import ai.chatbot.app.transaction.DefaultTransactionService
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.utils.getObjectMapper
import ai.integration.chatbot.utils.ConversationValidator
import ai.integration.chatbot.utils.OpenAITokenCondition
import arrow.core.right
import io.kotest.core.annotation.EnabledIf
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.core.test.TestCase
import io.kotest.core.test.TestScope
import io.kotest.core.test.parents
import io.micronaut.context.ApplicationContext
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

@EnabledIf(OpenAITokenCondition::class)
@MicronautTest(environments = [FRIDAY_ENV, "test"])
abstract class AbstractIntegrationTest(
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
    printConversation: Boolean = false,
    userGroups: List<String> = emptyList(),
    tenantConfiguration: TenantConfiguration,
) : DescribeSpec() {
    protected val tenantService = TenantServiceImpl(
        tenantConfigurations = mapOf(FRIDAY_ENV to tenantConfiguration),
        tenantResolver = mockk(),
        isSingleTenant = true,
        singleTenantId = FRIDAY_ENV,
    )
    private val validator: ConversationValidator = ConversationValidator(openAIAdapter, printConversation)
    private lateinit var dynamoDbEnhancedClient: DynamoDbEnhancedClient
    protected lateinit var historyRepository: HistoryRepository
    protected lateinit var historyStateRepository: HistoryStateRepository
    protected lateinit var conversationHistoryService: ConversationHistoryService
    protected lateinit var pendingBillsService: PendingBillsService
    protected lateinit var baseProcessor: ConversationProcessor
    private lateinit var actionExecutorLocator: ActionExecutorLocator
    private lateinit var actionsManager: ActionsManager
    private lateinit var locator: ActionExecutorLocator
    protected lateinit var transactionService: TransactionService
    protected lateinit var transactionRepository: TransactionRepository
    protected lateinit var buildNotificationService: BuildNotificationService
    protected lateinit var transactionController: TransactionController
    protected lateinit var actionExecutorsHelpers: ActionExecutorsHelpers
    private val templateInfoService: TemplateInfoService = mockk()
    protected lateinit var customNotificationService: CustomNotificationService

    protected val paymentAdapter = mockk<PaymentAdapter>()

    protected val notificationService: NotificationService = mockk(relaxed = true)

    protected val notificationContextTemplatesService = spyk(FridayNotificationContextTemplatesService())

    protected val walletId = WalletId("walletId")
    protected val user =
        User(
            accountId = AccountId(value = "ACCOUNT-ID"),
            id = UserId("*************"),
            name = "João da Silva Sauro",
            accountGroups = userGroups,
            status = AccountStatus.ACTIVE,
        )

    protected val billViews = listOf(billView, billView2, billView3)

    init {
        beforeEach {
            clearAllMocks()

            dynamoDbEnhancedClient = setupDynamoDB()

            historyRepository = ChatHistoryDbRepository(ChatHistoryDynamoDAO(dynamoDbEnhancedClient, applicationContext), tenantService)
            historyStateRepository = ChatHistoryStateDbRepository(ChatHistoryStateDynamoDAO(dynamoDbEnhancedClient, applicationContext), tenantService)

            transactionRepository = TransactionDbRepository(TransactionDynamoDAO(dynamoDbEnhancedClient, applicationContext), tenantService)
            transactionService = spyk(DefaultTransactionService(transactionRepository, paymentAdapter))

            buildNotificationService = spyk(BuildNotificationService(notificationContextTemplatesService, tenantService))

            pendingBillsService = spyk(
                PendingBillsService(
                    historyStateRepository = historyStateRepository,
                    paymentAdapter = paymentAdapter,
                ),
            )

            conversationHistoryService =
                ConversationHistoryService(
                    historyRepository = historyRepository,
                    openAIAdapter = openAIAdapter,
                    paymentAdapter = paymentAdapter,
                    notificationService = notificationService,
                    historyStateRepository = historyStateRepository,
                    pendingBillsService = pendingBillsService,
                    promptService = TenantPromptService(
                        prompts = mapOf("friday" to FridayPromptService()),
                        tenantService = tenantService,
                        multiTenantPromptService = mockk(),
                    ),
                )

            every {
                templateInfoService.getHistoryMessage(any())
            } answers {
                val notification = (firstArg() as ChatbotRawTemplatedNotification)
                "enviado notificação ${notification.configurationKey.value} para o usuário\n" +
                    "com parâmetros ${notification.arguments}"
            }

            customNotificationService = spyk(
                CustomNotificationService(
                    notificationService = notificationService,
                    conversationHistoryService = conversationHistoryService,
                    templateInfoService = templateInfoService,
                    tenantService = tenantService,
                    notificationContextTemplatesService = notificationContextTemplatesService,
                ),
            )

            actionExecutorsHelpers = ActionExecutorsHelpers(
                conversationHistoryService = conversationHistoryService,
                notificationService = notificationService,
                paymentAdapter = paymentAdapter,
                onePixPayInstrumentation = mockk(relaxed = true),
                transactionService = transactionService,
                buildNotificationService = buildNotificationService,
                customNotificationService = customNotificationService,
                notificationContextTemplatesService = notificationContextTemplatesService,
            )

            locator =
                newActionExecutorLocator(
                    notificationService = notificationService,
                    paymentAdapter = paymentAdapter,
                    conversationHistoryService = conversationHistoryService,
                    transactionService = transactionService,
                    openAIAdapter = openAIAdapter,
                    pendingBillsService = pendingBillsService,
                    buildNotificationService = buildNotificationService,
                    notificationContextTemplatesService = notificationContextTemplatesService,
                    actionExecutorsHelpers = actionExecutorsHelpers,
                    customNotificationService = customNotificationService,
                )

            actionExecutorLocator = spyk(locator)

            actionsManager = spyk(ActionsManager(actionExecutorLocator, conversationHistoryService, notificationService))

            baseProcessor =
                ConversationProcessor(
                    actionsManager = actionsManager,
                    conversationHistoryService = conversationHistoryService,
                    notificationService = notificationService,
                    transactionService = transactionService,
                    onePixPayInstrumentation = mockk(relaxed = true),
                    paymentAdapter = paymentAdapter,
                    buildNotificationService = buildNotificationService,
                    notificationContextTemplatesService = notificationContextTemplatesService,
                    eventService = mockk(relaxed = true),
                    customNotificationService = customNotificationService,
                    tenantService = tenantService,
                )

            transactionController = TransactionController(transactionService, conversationHistoryService, notificationService)

            every {
                paymentAdapter.getOpenFinanceBalances(any(), any(), any())
            } returns emptyMap<SweepingParticipantId, Long?>().right()

            every {
                paymentAdapter.validateAvailableLimit(any(), any(), any(), any())
            } returns AvailableLimitResponse.OK.right()

            createUserHistory()
        }

        afterEach {
            if (printConversation) printConversation(it.a)
        }
    }

    protected fun TestScope.validateConversation(expected: String) {
        val messages = historyRepository.findLatest(user.id).messages
        validator.validate(buildFullTestName(testCase), messages, expected)
    }

    private fun buildFullTestName(testCase: TestCase): String {
        return testCase.parents().joinToString(separator = " - ") { it.name.testName } + " - " + testCase.name.testName
    }

    protected fun createUserHistory() {
        historyRepository.create(
            userId = user.id,
            historyStateType = HistoryStateType.BILLS_COMING_DUE,
            initialUserMessage = null,
        )
    }

    data class History(
        val type: MessageType,
        val message: String,
    )

    fun saveHistory(history: List<History>) {
        history.forEach {
            val (type, message) = it
            when (type) {
                MessageType.ASSISTANT -> conversationHistoryService.createAssistantMessage(
                    user.id,
                    message,
                )

                MessageType.USER -> conversationHistoryService.createUserMessage(
                    user.id,
                    message,
                )

                MessageType.SYSTEM -> conversationHistoryService.createSystemMessage(
                    user.id,
                    message,
                )

                MessageType.FUNCTION -> TODO()
                MessageType.REACTION -> TODO()
            }
        }
    }

    protected fun printConversation(testCase: TestCase) {
        val conversationHistory = historyRepository.findLatest(user.id)
        val testName = buildFullTestName(testCase)

        println("--------------------$testName-----------------------")

        conversationHistory.messages.forEach { entry ->

            when (entry.type) {
                MessageType.SYSTEM -> {
                    println("SYSTEM: ${entry.message}")
                }

                MessageType.USER -> {
                    println("Usuário:\n${entry.message}")
                }

                MessageType.ASSISTANT -> {
                    println("Fred:")
                    println(entry.message ?: "-")

                    entry.completionMessage?.let { completion ->
                        println("\n(${completion.entendimento})")
                        completion.acoes.forEach {
                            println("Action: ${getObjectMapper().writeValueAsString(it)}")
                        }
                    }
                }

                else -> {
                    println("-")
                }
            }

            println()
        }

        println("---------------------$testName----------------------")
    }
}