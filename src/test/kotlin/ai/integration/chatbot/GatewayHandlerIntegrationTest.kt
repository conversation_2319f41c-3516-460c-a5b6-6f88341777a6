package ai.integration.chatbot

import ai.chatbot.adapters.billPayment.AccountTO
import ai.chatbot.adapters.billPayment.toBillTO
import ai.chatbot.adapters.messaging.ChatBotNotificationGatewayHandler
import ai.chatbot.adapters.messaging.ChatbotNotificationBuilder
import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.balance
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.Recipient
import ai.chatbot.app.billView
import ai.chatbot.app.billView2
import ai.chatbot.app.billView3
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.InteractionWindowService
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.notification.BillComingDueRegularDetailsTO
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatBotNotificationDetailsTO
import ai.chatbot.app.notification.ChatBotNotificationGatewayTO
import ai.chatbot.app.notification.FridayOnboardingSinglePixNotificationService
import ai.chatbot.app.notification.GenericNotificationContentTO
import ai.chatbot.app.notification.GenericNotificationDetailsTO
import ai.chatbot.app.notification.NotificationTemplate
import ai.chatbot.app.notification.ReceiverTO
import ai.chatbot.app.subscription
import ai.chatbot.app.user.UserActivity
import ai.chatbot.app.user.UserActivityType
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.getObjectMapper
import arrow.core.right
import io.micronaut.context.ApplicationContext
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import jakarta.inject.Named

class GatewayHandlerIntegrationTest(
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
    @Named(FRIDAY_ENV) tenantConfiguration: TenantConfiguration,
) : AbstractIntegrationTest(
    openAIAdapter = openAIAdapter,
    applicationContext = applicationContext,
    printConversation = true,
    tenantConfiguration = tenantConfiguration,
) {
    private val onboardingSinglePixNotificationService = FridayOnboardingSinglePixNotificationService()
    private lateinit var interactionWindowService: InteractionWindowService
    private lateinit var messageHandler: ChatBotNotificationGatewayHandler
    private lateinit var chatbotNotificationBuilder: ChatbotNotificationBuilder

    init {

        beforeEach() {
            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
            coEvery { paymentAdapter.getContacts(any(), any()) } returns emptyList<Contact>().right()
            coEvery { paymentAdapter.getSubscription(user.id) } returns subscription.copy(type = SubscriptionType.IN_APP, fee = null).right()
            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(billViews, walletId = walletId, "walletName")).right()
            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.right()
            coEvery { paymentAdapter.markBillsAsPaid(any(), any(), any()) } returns Unit.right()
            coEvery { paymentAdapter.ignoreBills(any(), any(), any()) } returns Unit.right()
            coEvery { paymentAdapter.getUserActivities(any(), any()) } returns listOf(
                UserActivity(
                    type = UserActivityType.PromotedSweepingAccountOptOut,
                    value = true,
                ),
            ).right()

            chatbotNotificationBuilder = ChatbotNotificationBuilder(
                buildNotificationService = BuildNotificationService(notificationContextTemplatesService, tenantService),
                notificationContextTemplatesService = notificationContextTemplatesService,
                onboardingSinglePixNotificationService = onboardingSinglePixNotificationService,
                customNotificationService = mockk(relaxed = true),
                tenantService = tenantService,
            )

            interactionWindowService = mockk<InteractionWindowService>()
            every { interactionWindowService.checkAndCreate(any()) } returns Unit.right()

            messageHandler =
                ChatBotNotificationGatewayHandler(
                    conversationHistoryService = conversationHistoryService,
                    notificationService = notificationService,
                    queue = "fake_queue",
                    amazonSQS = mockk(),
                    configuration = mockk(),
                    onePixPayInstrumentation = mockk(relaxed = true),
                    interactionWindowService = interactionWindowService,
                    paymentAdapter = mockk { every { getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right() },
                    tenantPropagator = mockk(relaxed = true),
                    tenantService = tenantService,
                    chatbotNotificationBuilder = chatbotNotificationBuilder,
                )
        }

        describe("quando o usuário receber uma notificação que não seja de contas vencendo") {
            beforeEach {
                val detailsBillComingDue = BillComingDueRegularDetailsTO(
                    walletName = "",
                    bills = listOf(billView3.toBillTO(), billView2.toBillTO(), billView.toBillTO()),
                )
                handleMessage(detailsBillComingDue)
            }
            it("não deve enviar mensagem de 'contas mudaram'") {
                val genericDetails = GenericNotificationDetailsTO(
                    notification = GenericNotificationContentTO(
                        notificationId = "foo",
                        receiver = ReceiverTO(
                            msisdn = user.id.value,
                        ),
                        accountId = user.accountId,
                        template = NotificationTemplate("foo"),
                        configurationKey = "configuration-key",
                        parameters = listOf("foo"),
                        quickReplyButtonsWhatsAppParameter = emptyList(),
                    ),
                )
                handleMessage(genericDetails)
                conversationHistoryService.createUserMessage(user.id, "Sim, pagar todas")
                baseProcessor.process(user)
                validateConversation("Não deve informar ao usuário que as contas mudaram")
            }
        }

        describe("quando usuario receber a notificação de contas vencendo") {
            describe("e for somente uma conta de assinatura") {
                it("não deve notificar") {
                    val subscriptionBill = billView2.copy(billId = BillId("12notification-id"), subscriptionFee = true, assignor = "friday", recipient = Recipient("friday"), externalBillId = 0, dueDate = getLocalDate())
                    val details = BillComingDueRegularDetailsTO(
                        walletName = "",
                        bills = listOf(subscriptionBill.toBillTO()),
                    )
                    handleMessage(details)
                    validateConversation("O assistente não deve enviar mensagem")
                }
            }

            describe("e houver uma assinatura e uma conta regular") {
                it("deve notificar") {
                    val subscriptionBill = billView2.copy(billId = BillId("12notification-id"), subscriptionFee = true, assignor = "friday", recipient = Recipient("friday"), externalBillId = 0, dueDate = getLocalDate())
                    val details = BillComingDueRegularDetailsTO(
                        walletName = "",
                        bills = listOf(subscriptionBill.toBillTO(), billView.toBillTO()),
                    )
                    handleMessage(details)
                    validateConversation("Deve ter enviado notificação chatbot-ai-notify-bills-coming-due para o usuário")
                }
            }

            describe("e possuir um estado anterior") {
                beforeEach {
                    val billComingDueState =
                        BillComingDueHistoryState(
                            walletWithBills =
                            WalletWithBills(
                                bills = listOf(billView),
                                walletId = walletId,
                                walletName = "walletName",
                            ),
                            balance = null,
                            internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
                            user = user,
                            contacts = emptyList(),
                            subscription = null,
                        )
                    historyStateRepository.save(user.id, billComingDueState)

                    val details = BillComingDueRegularDetailsTO(
                        walletName = "",
                        bills = listOf(billView3.toBillTO(), billView2.toBillTO(), billView.toBillTO()),
                    )
                    handleMessage(details)
                }
                describe("se o usuário interagir e mudar a ordem das contas") {
                    it("deve perguntar se ele deseja pagar todas ou apenas algumas contas") {

                        conversationHistoryService.createUserMessage(user.id, "pagar algumas")
                        baseProcessor.process(user)
                        conversationHistoryService.createUserMessage(user.id, "1 e 2")
                        baseProcessor.process(user)
                        validateConversation("A ordem das contas na confirmação de pagamento deve ser a mesma da notificação independente do número de contas")
                    }
                }
            }
            describe("quando o usuário não possui um estado anterior") {
                beforeEach {
                    val details = BillComingDueRegularDetailsTO(
                        walletName = "",
                        bills = listOf(billView3.toBillTO(), billView2.toBillTO(), billView.toBillTO()),
                    )
                    handleMessage(details)
                }
                describe("se o usuário interagir e mudar a ordem das contas") {
                    it("deve perguntar se ele deseja pagar todas ou apenas algumas contas") {
                        conversationHistoryService.createUserMessage(user.id, "pagar algumas")
                        baseProcessor.process(user)
                        conversationHistoryService.createUserMessage(user.id, "1 e 2")
                        baseProcessor.process(user)
                        validateConversation("A ordem das contas na confirmação de pagamento deve ser a mesma da notificação")
                    }
                }
            }
        }
    }

    private fun handleMessage(details: ChatBotNotificationDetailsTO) {
        messageHandler.handleMessage(
            mockk {
                every {
                    body()
                } returns
                    getObjectMapper().writeValueAsString(
                        ChatBotNotificationGatewayTO(
                            walletId = "walletId",
                            account = AccountTO(
                                id = user.accountId.value,
                                fullName = "full name",
                                msisdn = user.id.value,
                                accountGroups = emptyList(),
                            ),
                            details = details,
                        ),
                    )
            },
        )
    }
}