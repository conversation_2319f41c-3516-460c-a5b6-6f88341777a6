package ai.integration.chatbot

import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.ManualEntryId
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.balance
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.conversation.InterceptAction
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.subscription
import ai.chatbot.app.tenantConfiguration
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.WalletWithBills
import arrow.core.right
import io.micronaut.context.ApplicationContext
import io.mockk.coEvery
import jakarta.inject.Named

class ManualEntryIntegrationTest(
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
    @Named(FRIDAY_ENV) tenantConfiguration: TenantConfiguration,
) : AbstractIntegrationTest(
    openAIAdapter = openAIAdapter,
    applicationContext = applicationContext,
    printConversation = true,
    userGroups = listOf("ALPHA"),
    tenantConfiguration = tenantConfiguration,
) {

    init {
        beforeEach {
            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
            coEvery { paymentAdapter.getContacts(any(), any()) } returns emptyList<Contact>().right()
            coEvery { paymentAdapter.getSubscription(user.id) } returns subscription.copy(
                type = SubscriptionType.IN_APP,
                fee = null,
            ).right()
            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.right()
            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(
                user,
                WalletWithBills(billViews, walletId = walletId, "walletName"),
            ).right()
            coEvery { paymentAdapter.createManualEntry(any(), any(), any(), any(), any(), any()) } returns ManualEntryId("MANUAL-12345678910").right()
            coEvery { paymentAdapter.removeManualEntry(any(), any()) } returns Unit.right()
        }

        describe("Ao tentar criar um lançamento manual") {
            describe("Quando o usuário não passar as informações completas") {
                it("Deve perguntar título e valor") {
                    conversationHistoryService.createUserMessage(user.id, "Anotar gasto")
                    baseProcessor.process(user)
                    validateConversation("Deve perguntar ao usuário qual foi o gasto e qual o valor.")
                }

                it("Deve perguntar o valor") {
                    conversationHistoryService.createUserMessage(user.id, "Anotar gasto com mercado")
                    baseProcessor.process(user)
                    validateConversation("Deve perguntar ao usuário qual foi o valor")
                }
            }

            describe("Quando o usuário passar as informações completas") {
                listOf(
                    "Anotar gasto de 63 reais com mercado",
                    "Gastei 15,00 com chocolate",
                    "Gasto 100 com cadeira de praia",
                    "Transferi 20 reais pro Alex",
                    "Anota pra mim 25 com documentos",
                ).forEach { message ->
                    it("Deve registrar ao dizer '$message'") {
                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)
                        validateConversation("Deve dizer ao usuário que registrou o gasto")
                    }
                }
            }

            it("Deve registrar quando o usuário disser o título e depois o valor") {
                conversationHistoryService.createUserMessage(user.id, "Anotar gasto com mercado")
                conversationHistoryService.createAssistantMessage(user.id, "Entendi que você quer registrar um gasto para {{TITLE}}. Poderia me informar o valor?")
                conversationHistoryService.createUserMessage(user.id, "85 reais")
                baseProcessor.process(user)
                validateConversation("Deve dizer ao usuário que registrou o gasto")
            }

            it("Deve remover o gasto quando o usuário clicar em 'Desfazer'") {
                conversationHistoryService.createUserMessage(user.id, "Anotar gasto com mercado 77 reais")
                conversationHistoryService.createAssistantMessage(user.id, "Registrei um gasto de R$ 77,00 com Mercado para você.")
                conversationHistoryService.createUserMessage(user.id, "Desfazer")
                baseProcessor.process(user, interceptAction = InterceptAction(type = InterceptMessagePayloadType.REMOVE_MANUAL_ENTRY, payload = "MANUAL-ENTRY-ID"))
                validateConversation("Deve dizer ao usuário que removeu o gasto")
            }
        }

        describe("Ao tentar criar um lembrete") {
            describe("Quando o usuário não passar as informações completas") {
                it("Deve perguntar título e data") {
                    conversationHistoryService.createUserMessage(user.id, "Criar lembrete")
                    baseProcessor.process(user)
                    validateConversation("Deve perguntar ao usuário qual a data e título do lembrete.")
                }

                it("Deve perguntar a data") {
                    conversationHistoryService.createUserMessage(user.id, "Criar lembrete de impostos")
                    baseProcessor.process(user)
                    validateConversation("Deve perguntar ao usuário qual será a data do lembrete")
                }
            }
            describe("Quando o usuário passar uma data no passado") {
                it("Deve pedir para corrigir a data") {
                    conversationHistoryService.createUserMessage(user.id, "Criar lembrete de pagar impostos dia 10/06/2025")
                    baseProcessor.process(user)
                    validateConversation("Deve dizer ao usuário que a data do lembrete deve ser no futuro.")
                }
            }

            describe("Quando o usuário passar as informações completas") {
                listOf(
                    "Criar lembrete de pagar impostos semana que vem",
                    "Lembrete de pagar escolinha no próximo dia 25",
                    "Me lembra de pagar a diarista amanhã 150 reais",
                ).forEach { message ->
                    it("Deve criar lembrete ao dizer '$message'") {
                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)
                        validateConversation("Deve dizer ao usuário que criou um lembrete")
                    }
                }
            }

            it("Deve criar lembrete quando o usuário disser o título e depois a data") {
                conversationHistoryService.createUserMessage(user.id, "Criar lembrete de pagar natação")
                conversationHistoryService.createAssistantMessage(user.id, "Entendi que você quer criar um lembrete para *{{TITLE}}*. Poderia me informar a data que você gostaria de ser lembrado desse pagamento?")
                conversationHistoryService.createUserMessage(user.id, "Daqui a uma semana")
                baseProcessor.process(user)
                validateConversation("Deve dizer ao usuário que criou um lembrete")
            }

            it("Deve remover o lembrete quando o usuário clicar em 'Desfazer'") {
                conversationHistoryService.createUserMessage(user.id, "Criar lembrete de pagar diarista 150 reais amanhã")
                conversationHistoryService.createAssistantMessage(user.id, "Criei um lembrete para você:\n\n*Título*: Diarista\n*Data*: 18/06/2025\n*Valor*: R$ 150,00\n\nVou te lembrar desse pagamento no início do dia!")
                conversationHistoryService.createUserMessage(user.id, "Desfazer")
                baseProcessor.process(user, interceptAction = InterceptAction(type = InterceptMessagePayloadType.REMOVE_MANUAL_ENTRY, payload = "MANUAL-ENTRY-ID"))
                validateConversation("Deve dizer ao usuário que removeu o lembrete")
            }
        }
    }
}