package ai.integration.chatbot

import ai.chatbot.adapters.api.ConfirmTO
import ai.chatbot.adapters.billPayment.AccountTO
import ai.chatbot.adapters.messaging.ChatBotNotificationGatewayHandler
import ai.chatbot.adapters.messaging.ChatbotNotificationBuilder
import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.PixLimitStatus
import ai.chatbot.app.PixValidationResult
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.SweepingParticipant
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.SweepingRequest
import ai.chatbot.app.balance
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.contact.ContactId
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.InteractionWindowService
import ai.chatbot.app.conversation.InterceptAction
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatBotNotificationGatewayTO
import ai.chatbot.app.notification.FridayOnboardingSinglePixNotificationService
import ai.chatbot.app.notification.GenericNotificationContentTO
import ai.chatbot.app.notification.GenericNotificationDetailsTO
import ai.chatbot.app.notification.NotificationTemplate
import ai.chatbot.app.notification.ReceiverTO
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.subscription
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.transaction.TransactionStatus
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.getObjectMapper
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldContainInOrder
import io.kotest.matchers.collections.shouldContainOnly
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.types.shouldBeSameInstanceAs
import io.micronaut.context.ApplicationContext
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyOrder
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import jakarta.inject.Named
import software.amazon.awssdk.services.sqs.model.Message

class PixTransactionIntegrationTest(
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
    @Named(FRIDAY_ENV) tenantConfiguration: TenantConfiguration,
) : AbstractIntegrationTest(
    openAIAdapter = openAIAdapter,
    applicationContext = applicationContext,
    printConversation = true,
    tenantConfiguration = tenantConfiguration,
) {
    private val onboardingSinglePixNotificationService = FridayOnboardingSinglePixNotificationService()

    init {
        beforeEach() {
            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
            coEvery { paymentAdapter.getContacts(any(), any()) } returns emptyList<Contact>().right()
            coEvery { paymentAdapter.getSubscription(user.id) } returns subscription.copy(
                type = SubscriptionType.IN_APP,
                fee = null,
            ).right()
            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.right()
            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(
                user,
                WalletWithBills(billViews, walletId = walletId, "walletName"),
            ).right()
            coEvery { paymentAdapter.pixValidation(match { it.type == PixKeyType.PHONE }, any(), any(), any()) } returns PixValidationResult(
                keyType = PixKeyType.PHONE,
                keyValue = "+5521999999999",
                recipientName = "Recipient",
                recipientInstitution = "Banco Teste",
                recipientDocument = "12345678910",
                pixLimitStatus = PixLimitStatus.AVAILABLE,
            ).right()
            coEvery { paymentAdapter.pixValidation(match { it.type == PixKeyType.CPF }, any(), any(), any()) } returns PixValidationResult(
                keyType = PixKeyType.CPF,
                keyValue = "***********",
                recipientName = "Mário Gomes",
                recipientInstitution = "Banco Teste",
                recipientDocument = "***********",
                pixLimitStatus = PixLimitStatus.AVAILABLE,
            ).right()
            coEvery { paymentAdapter.pixValidation(match { it.type == PixKeyType.CNPJ }, any(), any(), any()) } returns PixValidationResult(
                keyType = PixKeyType.CNPJ,
                keyValue = "56370732000128",
                recipientName = "Empresa Teste",
                recipientInstitution = "Banco Teste",
                recipientDocument = "56370732000128",
                pixLimitStatus = PixLimitStatus.AVAILABLE,
            ).right()
            coEvery { paymentAdapter.pixValidation(match { it.type == PixKeyType.EVP }, any(), any(), any()) } returns PixValidationResult(
                keyType = PixKeyType.EVP,
                keyValue = "7f7f55d9-356c-4439-9333-47e7d8842ae7",
                recipientName = "Mário Gomes",
                recipientInstitution = "Banco Teste",
                recipientDocument = "***********",
                pixLimitStatus = PixLimitStatus.AVAILABLE,
            ).right()
            coEvery { paymentAdapter.pixValidation(match { it.type == PixKeyType.EMAIL }, any(), any(), any()) } returns PixValidationResult(
                keyType = PixKeyType.EMAIL,
                keyValue = "<EMAIL>",
                recipientName = "Mário Gomes",
                recipientInstitution = "Banco Teste",
                recipientDocument = "***********",
                pixLimitStatus = PixLimitStatus.AVAILABLE,
            ).right()
            coEvery { paymentAdapter.validatePixQrCode(any(), any(), any()) } returns PixValidationResult(
                keyType = PixKeyType.EVP,
                keyValue = "7f7f55d9-356c-4439-9333-47e7d8842ae7",
                recipientName = "Recipient",
                recipientInstitution = "Banco Teste",
                recipientDocument = "12345678910",
                pixLimitStatus = PixLimitStatus.AVAILABLE,
                amount = 20_00,
            ).right()
            coEvery { paymentAdapter.validatePixQrCode(any(), 35_00, any()) } returns PixValidationResult(
                keyType = PixKeyType.EVP,
                keyValue = "7f7f55d9-356c-4439-9333-000000000000",
                recipientName = "Jose",
                recipientInstitution = "Banco Naves",
                recipientDocument = "10987654321",
                pixLimitStatus = PixLimitStatus.AVAILABLE,
                amount = 35_00,
            ).right()
            every {
                paymentAdapter.getOpenFinanceBalances(any(), any(), any())
            } returns emptyMap<SweepingParticipantId, Long?>().right()
        }

        describe("Quando o usuário pedir para fazer uma transferência via Pix") {
            it("Deve perguntar qual o valor e a chave do pix") {
                conversationHistoryService.createUserMessage(user.id, "Quero fazer um pix")
                baseProcessor.process(user)
                validateConversation("Deve perguntar ao usuário a chave pix e o valor.")
            }

            it("Deve perguntar qual a chave do pix") {
                conversationHistoryService.createUserMessage(user.id, "Quero fazer um pix de 20 reais")
                baseProcessor.process(user)
                validateConversation("Deve perguntar ao usuário a chave pix ou o contato.")
            }

            describe("Deve perguntar qual o valor do pix") {
                listOf(
                    "<EMAIL>",
                    "<EMAIL>",
                    "56370732000128",
                    "pix para 5-6-3-7-0-7-3-2-0-0-0-1-2-8",
                    "***********",
                    "pix 154-737-48708",
                    "pix Mário",
                ).forEach { content ->
                    it(content) {
                        conversationHistoryService.createUserMessage(user.id, content)
                        baseProcessor.process(user)
                        validateConversation("Deve verificar se foi perguntado ao usuário o valor do pix ou se o usuário gostaria de fazer um pix.")
                    }
                }
            }

            it("Deve perguntar qual o valor do pix se for um copia e cola sem valor") {
                coEvery { paymentAdapter.validatePixQrCode(any(), match { it == 0L }, any()) } returns PaymentAdapterError.PixQrCodeAmountNotFound.left()
                conversationHistoryService.createUserMessage(user.id, "00020101021126360014br.gov.bcb.pix0114+55219999999995204000053039865802BR5923JOAO DAS NEVES6013RIO DE JANEIR62070503***6304284D")
                baseProcessor.process(user)
                validateConversation("Deve perguntar ao usuário o valor do pix.")
            }

            it("Deve realizar o pix com sucesso para um contato") {
                coEvery {
                    paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                } returns Unit.right()

                coEvery { paymentAdapter.getContacts(any(), any()) } returns listOf(
                    Contact(id = ContactId("1"), name = "Mário Gomes", pixKeys = listOf(PixKey(type = PixKeyType.CPF, value = "***********"))),
                    Contact(id = ContactId("2"), name = "Renato Santos", pixKeys = listOf(PixKey(type = PixKeyType.CPF, value = "12355545666"))),
                ).right()

                conversationHistoryService.createUserMessage(
                    user.id,
                    "Olá Felipe! Boa tarde!\n" +
                        "Por gentileza, o senhor poderia me confirmar por favor o depósito do valor de R\$ 20,00 referente à sua consulta on-line com o Dr. Mario, realizada ontem (19/02/2025)?\n" +
                        "Desde já agradeço pela sua atenção! :)\n" +
                        "Fico aguardando seu retorno! :)\n" +
                        "Atenciosamente,\n" +
                        "Mariana",
                )
                baseProcessor.process(user)

                conversationHistoryService.createUserMessage(
                    user.id,
                    "quero fazer o pix acima",
                )
                baseProcessor.process(user)

                conversationHistoryService.createUserMessage(
                    user.id,
                    "Sim",
                )

                val transactions = transactionRepository.find(user.id, TransactionStatus.ACTIVE)
                if (transactions.isEmpty()) {
                    baseProcessor.process(user)
                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Sim",
                    )
                }

                val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId, "2021-10-10T10:00:00Z"))

                val pixKeyV = slot<PixKey>()
                val amountV = slot<Long>()
                val pixKeyT = slot<PixKey>()
                val amountT = slot<Long>()
                val transactionIdT = slot<TransactionId>()
                val sweepingRequest = mutableListOf<SweepingRequest?>()
                val authorizationToken = mutableListOf<String?>()

                coVerify(exactly = 1) {
                    paymentAdapter.pixValidation(capture(pixKeyV), any(), capture(amountV), any())
                    paymentAdapter.pixTransaction(any(), any(), capture(amountT), captureNullable(sweepingRequest), capture(pixKeyT), capture(transactionIdT), captureNullable(authorizationToken), any())
                }

                pixKeyV.captured.value shouldBe "***********"
                pixKeyV.captured.type shouldBe PixKeyType.CPF
                amountV.captured shouldBe 2000L
                pixKeyT.captured.value shouldBe "***********"
                pixKeyT.captured.type shouldBe PixKeyType.CPF
                amountT.captured shouldBe 2000L
                sweepingRequest shouldContainExactly listOf(null)
                authorizationToken shouldContainExactly listOf(null)
                transactionIdT.captured.value shouldBe transactionId.value

                historyRepository.findLatest(user.id).messages.last().message shouldBe "Transação confirmada com sucesso"
            }

            it("Deve realizar o pix com sucesso para um contato depois de ter sido salvo como Usuário Não Registrado com AccountId vazio") {
                coEvery {
                    paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                } returns Unit.right()

                coEvery { paymentAdapter.getContacts(any(), any()) } returns listOf(
                    Contact(id = ContactId("1"), name = "Mário Gomes", pixKeys = listOf(PixKey(type = PixKeyType.CPF, value = "***********"))),
                ).right()

                generateUnregisteredUser(user)

                conversationHistoryService.createUserMessage(
                    user.id,
                    "Pix para o Mário de 20 reais",
                )
                baseProcessor.process(user)

                conversationHistoryService.createUserMessage(
                    user.id,
                    "Sim",
                )

                val transactions = transactionRepository.find(user.id, TransactionStatus.ACTIVE)
                if (transactions.isEmpty()) {
                    baseProcessor.process(user)
                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Sim",
                    )
                }

                val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId, "2021-10-10T10:00:00Z"))

                val amount = slot<Long>()

                coVerify(exactly = 1) {
                    paymentAdapter.pixValidation(any(), any(), capture(amount), any())
                    paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                }

                amount.captured shouldBe 2000L

                historyRepository.findLatest(user.id).messages.last().message shouldBe "Transação confirmada com sucesso"
            }

            describe("Quando o usuário pedir pra fazer um Pix logo após outra transação, deve sempre perguntar o valor do Pix") {
                val copiaEColaSemValor = "00020101021126360014br.gov.bcb.pix0114+55219999999995204000053039865802BR5923JOAO DAS NEVES6013RIO DE JANEIR62070503***6304284D"

                listOf(
                    // Pair("Fazer um pix de 20 para o Mário", "Fazer pix pro Renato"),
                    // Pair("Fazer um pix de 25 reais para ***********", "Fazer um pix pro Renato"),
                    Pair("Pix de 350 para o Mário", "Pix para 12355545666"),
                    // Pair("Fazer um pix de 35 reais pra ***********", "Fazer pix para 12355545666"),
                    // Pair("Fazer pix de 40 reais pro Renato", copiaEColaSemValor),
                ).forEach { content ->
                    it(content.toString()) {
                        coEvery { paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any()) } returns Unit.right()

                        coEvery { paymentAdapter.validatePixQrCode(copiaEColaSemValor, match { it == 0L }, any()) } returns PaymentAdapterError.PixQrCodeAmountNotFound.left()

                        coEvery { paymentAdapter.getContacts(any(), any()) } returns listOf(
                            Contact(id = ContactId("1"), name = "Mário Gomes", pixKeys = listOf(PixKey(type = PixKeyType.CPF, value = "***********"))),
                            Contact(id = ContactId("2"), name = "Renato Santos", pixKeys = listOf(PixKey(type = PixKeyType.CPF, value = "12355545666"))),
                        ).right()

                        conversationHistoryService.createUserMessage(user.id, content.first)
                        baseProcessor.process(user)
                        conversationHistoryService.createUserMessage(user.id, "Sim")

                        val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId, "2021-10-10T10:00:00Z"))

                        conversationHistoryService.createUserMessage(user.id, content.second)
                        baseProcessor.process(user)

                        coVerify(exactly = 1) {
                            paymentAdapter.pixValidation(any(), any(), any(), any())
                            paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                        }

                        validateConversation("Deve perguntar ao usuário o valor do pix quando não estiver explicito na mensagem.")
                    }
                }
            }

            describe("Se o usuário já estava no meio de outro pedido de Pix") {
                it("Deve ser capaz de executar um novo pedido com novo valor") {
                    coEvery { paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any()) } returns Unit.right()
                    coEvery { paymentAdapter.getContacts(any(), any()) } returns listOf(
                        Contact(id = ContactId("1"), name = "Mário Gomes", pixKeys = listOf(PixKey(type = PixKeyType.CPF, value = "***********"))),
                        Contact(id = ContactId("2"), name = "Renato Santos", pixKeys = listOf(PixKey(type = PixKeyType.CPF, value = "12355545666"))),
                    ).right()

                    conversationHistoryService.createUserMessage(user.id, "Pix de 20 reais para Mario")
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(user.id, "Pix pra Renato")
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(user.id, "35")
                    baseProcessor.process(user)

                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id
                    conversationHistoryService.createUserMessage(user.id, "Sim")

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId, "2021-10-10T10:00:00Z"))

                    coVerify(exactly = 2) {
                        paymentAdapter.pixValidation(any(), any(), any(), any())
                    }

                    coVerify(exactly = 1) {
                        paymentAdapter.pixTransaction(any(), any(), 3500, any(), any(), any(), any(), any())
                    }
                }
            }

            describe("Deve realizar o pix com sucesso") {
                describe("Se tiver saldo, o valor estiver ABAIXO do limite do assistente e a chave for CPF") {
                    listOf(
                        "PIX de R\$ 65 para a chave ***********.",
                        "PIX de R\$ 65 para a chave 154.737.487-08.",
                        "PIX de R\$ 65 para a chave 154-737-48708.",
                        "PIX de R\$ 65 para a chave 1-5-4-7-3-7-4-8-7-0-8.",
                    ).forEach { content ->
                        it(content) {
                            coEvery {
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            } returns Unit.right()

                            conversationHistoryService.createUserMessage(
                                user.id,
                                content,
                            )
                            baseProcessor.process(user)

                            conversationHistoryService.createUserMessage(
                                user.id,
                                "Sim",
                            )

                            val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                            baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId, "2021-10-10T10:00:00Z"))

                            val pixKeyV = slot<PixKey>()
                            val amountV = slot<Long>()
                            val pixKeyT = slot<PixKey>()
                            val amountT = slot<Long>()
                            val transactionIdT = slot<TransactionId>()
                            val sweepingRequest = mutableListOf<SweepingRequest?>()
                            val authorizationToken = mutableListOf<String?>()

                            coVerify(exactly = 1) {
                                paymentAdapter.pixValidation(capture(pixKeyV), any(), capture(amountV), any())
                                paymentAdapter.pixTransaction(any(), any(), capture(amountT), captureNullable(sweepingRequest), capture(pixKeyT), capture(transactionIdT), captureNullable(authorizationToken), any())
                            }

                            pixKeyV.captured.value shouldBe "***********"
                            pixKeyV.captured.type shouldBe PixKeyType.CPF
                            amountV.captured shouldBe 6500L
                            pixKeyT.captured.value shouldBe "***********"
                            pixKeyT.captured.type shouldBe PixKeyType.CPF
                            amountT.captured shouldBe 6500L
                            sweepingRequest shouldContainExactly listOf(null)
                            authorizationToken shouldContainExactly listOf(null)
                            transactionIdT.captured.value shouldBe transactionId.value

                            historyRepository.findLatest(user.id).messages.last().message shouldBe "Transação confirmada com sucesso"
                        }
                    }
                }

                describe("Se tiver saldo, o valor estiver ABAIXO do limite do assistente e a chave for CNPJ") {
                    listOf(
                        "PIX de R\$ 65 para a chave 56370732000128.",
                        "PIX de R\$ 65 para a chave 563-707-320-00128.",
                        "PIX de R\$ 65 para a chave 5-6-3-7-0-7-3-2-0-0-0-1-2-8.",
                        "PIX de R\$ 65 para a chave 56.370.732/0001-28.",
                    ).forEach { content ->
                        it(content) {
                            coEvery {
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            } returns Unit.right()

                            conversationHistoryService.createUserMessage(
                                user.id,
                                content,
                            )
                            baseProcessor.process(user)

                            conversationHistoryService.createUserMessage(
                                user.id,
                                "Sim",
                            )

                            val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                            baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId, "2021-10-10T10:00:00Z"))

                            val pixKeyV = slot<PixKey>()
                            val amountV = slot<Long>()
                            val pixKeyT = slot<PixKey>()
                            val amountT = slot<Long>()
                            val transactionIdT = slot<TransactionId>()
                            val sweepingRequest = mutableListOf<SweepingRequest?>()
                            val authorizationToken = mutableListOf<String?>()

                            coVerify(exactly = 1) {
                                paymentAdapter.pixValidation(capture(pixKeyV), any(), capture(amountV), any())
                                paymentAdapter.pixTransaction(any(), any(), capture(amountT), captureNullable(sweepingRequest), capture(pixKeyT), capture(transactionIdT), captureNullable(authorizationToken), any())
                            }

                            pixKeyV.captured.value shouldBe "56370732000128"
                            pixKeyV.captured.type shouldBe PixKeyType.CNPJ
                            amountV.captured shouldBe 6500L
                            pixKeyT.captured.value shouldBe "56370732000128"
                            pixKeyT.captured.type shouldBe PixKeyType.CNPJ
                            amountT.captured shouldBe 6500L
                            sweepingRequest shouldContainExactly listOf(null)
                            authorizationToken shouldContainExactly listOf(null)
                            transactionIdT.captured.value shouldBe transactionId.value

                            historyRepository.findLatest(user.id).messages.last().message shouldBe "Transação confirmada com sucesso"
                        }
                    }
                }

                describe("Se tiver saldo, o valor estiver ABAIXO do limite do assistente e a chave for EMAIL") {
                    listOf(
                        "PIX de R\$ 65 para <NAME_EMAIL>.",
                        "PIX de R\$ 65 para <NAME_EMAIL>.",
                    ).forEach { content ->
                        it(content) {
                            coEvery {
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            } returns Unit.right()

                            conversationHistoryService.createUserMessage(
                                user.id,
                                content,
                            )
                            baseProcessor.process(user)

                            conversationHistoryService.createUserMessage(
                                user.id,
                                "Sim",
                            )

                            val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                            baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId, "2021-10-10T10:00:00Z"))

                            val pixKeyV = slot<PixKey>()
                            val amountV = slot<Long>()
                            val pixKeyT = slot<PixKey>()
                            val amountT = slot<Long>()
                            val transactionIdT = slot<TransactionId>()
                            val sweepingRequest = mutableListOf<SweepingRequest?>()
                            val authorizationToken = mutableListOf<String?>()

                            coVerify(exactly = 1) {
                                paymentAdapter.pixValidation(capture(pixKeyV), any(), capture(amountV), any())
                                paymentAdapter.pixTransaction(any(), any(), capture(amountT), captureNullable(sweepingRequest), capture(pixKeyT), capture(transactionIdT), captureNullable(authorizationToken), any())
                            }

                            val emails = listOf("<EMAIL>", "<EMAIL>")
                            emails.contains(pixKeyV.captured.value) shouldBe true
                            pixKeyV.captured.type shouldBe PixKeyType.EMAIL
                            amountV.captured shouldBe 6500L
                            emails.contains(pixKeyT.captured.value) shouldBe true
                            pixKeyT.captured.type shouldBe PixKeyType.EMAIL
                            amountT.captured shouldBe 6500L
                            sweepingRequest shouldContainExactly listOf(null)
                            authorizationToken shouldContainExactly listOf(null)
                            transactionIdT.captured.value shouldBe transactionId.value

                            historyRepository.findLatest(user.id).messages.last().message shouldBe "Transação confirmada com sucesso"
                        }
                    }
                }

                it("Se tiver saldo, o valor estiver ACIMA do limite do assistente, mas foi autorizado pelo app") {
                    coEvery {
                        paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                    } returns Unit.right()
                    coEvery { paymentAdapter.pixValidation(any(), any(), any(), any()) } returns PixValidationResult(
                        keyType = PixKeyType.PHONE,
                        keyValue = "+5521999999999",
                        recipientName = "Recipient",
                        recipientInstitution = "Banco Teste",
                        recipientDocument = "12345678910",
                        pixLimitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                    ).right()

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Quero fazer um pix de 20 reais para o número 21 99999-9999",
                    )
                    baseProcessor.process(user)

                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                    transactionController.confirmTransaction(transactionId.value, user.id.value, ConfirmTO("token", false, 1))

                    val pixKeyV = slot<PixKey>()
                    val amountV = slot<Long>()
                    val pixKeyT = slot<PixKey>()
                    val amountT = slot<Long>()
                    val transactionIdT = slot<TransactionId>()
                    val sweepingRequest = mutableListOf<SweepingRequest?>()
                    val authorizationToken = mutableListOf<String?>()

                    coVerify {
                        paymentAdapter.pixValidation(capture(pixKeyV), any(), capture(amountV), any())
                        paymentAdapter.pixTransaction(any(), any(), capture(amountT), captureNullable(sweepingRequest), capture(pixKeyT), capture(transactionIdT), captureNullable(authorizationToken), any())
                    }

                    pixKeyV.captured.value shouldBe "+5521999999999"
                    pixKeyV.captured.type shouldBe PixKeyType.PHONE
                    amountV.captured shouldBe 2000L
                    pixKeyT.captured.value shouldBe "+5521999999999"
                    pixKeyT.captured.type shouldBe PixKeyType.PHONE
                    amountT.captured shouldBe 2000L
                    sweepingRequest shouldBe listOf(null)
                    authorizationToken shouldBe listOf("token")
                    transactionIdT.captured.value shouldBe transactionId.value

                    historyRepository.findLatest(user.id).messages.last().message shouldBe "Transação confirmada com sucesso"
                }

                it("Se NÃO tiver saldo, o valor estiver ABAIXO do limite do assistente e tem conta CONECTADA") {
                    coEvery {
                        paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                    } returns Unit.right()

                    coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns listOf(SweepingConsent(SweepingParticipant(SweepingParticipantId("id"), "nome", "shortName"), 1000_00, null)).right()

                    coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 10_00).right()

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Quero fazer um pix de 20 reais para o número 21 99999-9999",
                    )
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Sim",
                    )

                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId, "2021-10-10T10:00:00Z"))

                    val pixKeyV = slot<PixKey>()
                    val amountV = slot<Long>()
                    val pixKeyT = slot<PixKey>()
                    val amountT = slot<Long>()
                    val transactionIdT = slot<TransactionId>()
                    val sweepingRequest = mutableListOf<SweepingRequest?>()
                    val authorizationToken = mutableListOf<String?>()

                    coVerify {
                        paymentAdapter.pixValidation(capture(pixKeyV), any(), capture(amountV), any())
                        paymentAdapter.pixTransaction(any(), any(), capture(amountT), captureNullable(sweepingRequest), capture(pixKeyT), capture(transactionIdT), captureNullable(authorizationToken), any())
                    }

                    pixKeyV.captured.value shouldBe "+5521999999999"
                    pixKeyV.captured.type shouldBe PixKeyType.PHONE
                    amountV.captured shouldBe 2000L
                    pixKeyT.captured.value shouldBe "+5521999999999"
                    pixKeyT.captured.type shouldBe PixKeyType.PHONE
                    amountT.captured shouldBe 2000L
                    sweepingRequest.map { it?.amount } shouldBe listOf(10_00)
                    authorizationToken shouldBe listOf(null)
                    transactionIdT.captured.value shouldBe transactionId.value

                    historyRepository.findLatest(user.id).messages.last().message shouldBe "Transação confirmada com sucesso"
                }

                it("Se NÃO tiver saldo, o valor estiver ACIMA do limite do assistente e tem conta CONECTADA, mas foi autorizado pelo app") {
                    coEvery {
                        paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                    } returns Unit.right()

                    coEvery { paymentAdapter.pixValidation(any(), any(), any(), any()) } returns PixValidationResult(
                        keyType = PixKeyType.PHONE,
                        keyValue = "+5521999999999",
                        recipientName = "Recipient",
                        recipientInstitution = "Banco Teste",
                        recipientDocument = "12345678910",
                        pixLimitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                    ).right()

                    coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns listOf(SweepingConsent(SweepingParticipant(SweepingParticipantId("id"), "nome", "shortName"), 1000_00, null)).right()

                    coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 10_00).right()

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Quero fazer um pix de 20 reais para o número 21 99999-9999",
                    )
                    baseProcessor.process(user)

                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                    transactionController.confirmTransaction(transactionId.value, user.id.value, ConfirmTO("token", false, 1))

                    val pixKeyV = slot<PixKey>()
                    val amountV = slot<Long>()
                    val pixKeyT = slot<PixKey>()
                    val amountT = slot<Long>()
                    val transactionIdT = slot<TransactionId>()
                    val sweepingRequest = mutableListOf<SweepingRequest?>()
                    val authorizationToken = mutableListOf<String?>()

                    coVerify {
                        paymentAdapter.pixValidation(capture(pixKeyV), any(), capture(amountV), any())
                        paymentAdapter.pixTransaction(any(), any(), capture(amountT), captureNullable(sweepingRequest), capture(pixKeyT), capture(transactionIdT), captureNullable(authorizationToken), any())
                    }

                    pixKeyV.captured.value shouldBe "+5521999999999"
                    pixKeyV.captured.type shouldBe PixKeyType.PHONE
                    amountV.captured shouldBe 2000L
                    pixKeyT.captured.value shouldBe "+5521999999999"
                    pixKeyT.captured.type shouldBe PixKeyType.PHONE
                    amountT.captured shouldBe 2000L
                    sweepingRequest.map { it?.amount } shouldBe listOf(10_00)
                    authorizationToken shouldBe listOf("token")
                    transactionIdT.captured.value shouldBe transactionId.value

                    historyRepository.findLatest(user.id).messages.last().message shouldBe "Transação confirmada com sucesso"
                }

                describe("Se receber um pix copia e cola") {
                    listOf(
                        "00020126580014br.gov.bcb.pix01367f7f55d9-356c-4439-9333-47e7d8842ae7520400005303986540510.005802BR5915ALEXANDRE SILVA6014RIO DE JANEIRO61082022029762100506CecApp6304DF14",
                        "00020126940014br.gov.bcb.pix013686b26441-94eb-4523-9e8a-79120d007b940232VENC. 01/04/25 | FAT. 542634446452040000530398654100000057.995802BR5915TIM BRASIL S.A.6009Sao Paulo62170513B0154263444646304C902",
                    ).forEach { qrCode ->
                        it(qrCode) {
                            coEvery {
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            } returns Unit.right()

                            conversationHistoryService.createUserMessage(
                                user.id,
                                qrCode,
                            )
                            baseProcessor.process(user)

                            conversationHistoryService.createUserMessage(
                                user.id,
                                "Sim",
                            )

                            val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id
                            baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId, "2021-10-10T10:00:00Z"))

                            val qrCodeV = slot<String>()
                            val qrCodeT = slot<String>()
                            val transactionIdT = slot<TransactionId>()
                            val sweepingRequest = mutableListOf<SweepingRequest?>()
                            val authorizationToken = mutableListOf<String?>()

                            coVerify {
                                paymentAdapter.validatePixQrCode(capture(qrCodeV), any(), any())
                                paymentAdapter.pixTransaction(any(), any(), any(), captureNullable(sweepingRequest), any(), capture(transactionIdT), captureNullable(authorizationToken), capture(qrCodeT))
                            }

                            qrCodeV.captured shouldBe qrCode
                            qrCodeT.captured shouldBe qrCode
                            sweepingRequest shouldBe listOf(null)
                            authorizationToken shouldBe listOf(null)
                            transactionIdT.captured.value shouldBe transactionId.value

                            historyRepository.findLatest(user.id).messages.last().message shouldBe "Transação confirmada com sucesso"
                        }
                    }
                }
            }

            describe("Se receber um pix copia e cola sem valor") {
                listOf(
                    "00020101021126360014br.gov.bcb.pix0114+55219999999995204000053039865802BR5923JOAO DAS NEVES6013RIO DE JANEIR62070503***6304284D",
                ).forEach { qrCode ->
                    it(qrCode) {
                        coEvery { paymentAdapter.validatePixQrCode(any(), match { it == 0L }, any()) } returns PaymentAdapterError.PixQrCodeAmountNotFound.left()
                        coEvery {
                            paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                        } returns Unit.right()

                        conversationHistoryService.createUserMessage(
                            user.id,
                            qrCode,
                        )
                        baseProcessor.process(user)

                        conversationHistoryService.createUserMessage(
                            user.id,
                            "20 reais",
                        )
                        baseProcessor.process(user)

                        conversationHistoryService.createUserMessage(
                            user.id,
                            "Sim",
                        )

                        val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId, "2021-10-10T10:00:00Z"))

                        val qrCodeV = mutableListOf<String>()
                        val qrCodeT = slot<String>()
                        val amountV = mutableListOf<Long>()
                        val amountT = slot<Long>()
                        val transactionIdT = slot<TransactionId>()
                        val sweepingRequest = mutableListOf<SweepingRequest?>()
                        val authorizationToken = mutableListOf<String?>()

                        coVerify {
                            paymentAdapter.validatePixQrCode(capture(qrCodeV), capture(amountV), any())
                            paymentAdapter.pixTransaction(any(), any(), capture(amountT), captureNullable(sweepingRequest), any(), capture(transactionIdT), captureNullable(authorizationToken), capture(qrCodeT))
                        }

                        qrCodeV shouldContainOnly listOf(qrCode)
                        qrCodeT.captured shouldBe qrCode
                        amountV shouldContainInOrder listOf(0L, 20_00)
                        amountT.captured shouldBe 20_00
                        sweepingRequest shouldBe listOf(null)
                        authorizationToken shouldBe listOf(null)
                        transactionIdT.captured.value shouldBe transactionId.value

                        historyRepository.findLatest(user.id).messages.last().message shouldBe "Transação confirmada com sucesso"
                    }
                }
            }

            describe("Se o usuário já estava no meio de Pix copia e cola sem valor") {
                it("Deve ser capaz de concluir o pix e fazer um novo pix com novo valor") {
                    val copiaEColaSemValor1 = "00020101021126360014br.gov.bcb.pix0114+55219999999995204000053039865802BR5923JOAO DAS NEVES6013RIO DE JANEIR62070503***6304284D"
                    val copiaEColaSemValor2 = "00020101021126360014br.gov.bcb.pix0114+55219888888885204000053039865802BR5923JOSE DAS NAVES6013RIO DE JANEIR62070503***AAA45678"

                    coEvery { paymentAdapter.validatePixQrCode(any(), match { it == 0L }, any()) } returns PaymentAdapterError.PixQrCodeAmountNotFound.left()
                    coEvery { paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any()) } returns Unit.right()

                    conversationHistoryService.createUserMessage(user.id, copiaEColaSemValor1)
                    baseProcessor.process(user)
                    conversationHistoryService.createUserMessage(user.id, "20")
                    baseProcessor.process(user)

                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id
                    conversationHistoryService.createUserMessage(user.id, "Sim")
                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId, "2021-10-10T10:00:00Z"))

                    conversationHistoryService.createUserMessage(user.id, copiaEColaSemValor2)
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(user.id, "35")
                    baseProcessor.process(user)

                    val transactionId2 = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id
                    conversationHistoryService.createUserMessage(user.id, "Sim")
                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId2, "2021-10-10T10:00:00Z"))

                    coVerifyOrder {
                        paymentAdapter.pixTransaction(any(), any(), 2000, any(), any(), transactionId, any(), any())
                        paymentAdapter.pixTransaction(any(), any(), 3500, any(), any(), transactionId2, any(), any())
                    }
                }

                it("Deve ser capaz de executar um novo pix com novo valor") {
                    val copiaEColaSemValor1 = "00020101021126360014br.gov.bcb.pix0114+55219999999995204000053039865802BR5923JOAO DAS NEVES6013RIO DE JANEIR62070503***6304284D"
                    val copiaEColaSemValor2 = "00020101021126360014br.gov.bcb.pix0114+55219888888885204000053039865802BR5923JOSE DAS NAVES6013RIO DE JANEIR62070503***AAA45678"

                    coEvery { paymentAdapter.validatePixQrCode(any(), match { it == 0L }, any()) } returns PaymentAdapterError.PixQrCodeAmountNotFound.left()
                    coEvery { paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any()) } returns Unit.right()

                    conversationHistoryService.createUserMessage(user.id, copiaEColaSemValor1)
                    baseProcessor.process(user)
                    conversationHistoryService.createUserMessage(user.id, "20")
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(user.id, copiaEColaSemValor2)
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(user.id, "35")
                    baseProcessor.process(user)

                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id
                    conversationHistoryService.createUserMessage(user.id, "Sim")

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId, "2021-10-10T10:00:00Z"))

                    coVerify(exactly = 1) {
                        paymentAdapter.pixTransaction(any(), any(), 3500, any(), any(), any(), any(), any())
                    }
                }
            }

            describe("A partir do texto extraído de alguma imagem ou documento") {
                it("Deve ser capaz de identificar e concluir um PIX sem valor") {
                    coEvery { paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any()) } returns Unit.right()

                    conversationHistoryService.createUserMessage(user.id, "Cobrança Gerada\n\nBanco do Brasil\n\nChave: 12345678000196\n\nCarlos Alberto")
                    conversationHistoryService.createSystemMessage(user.id, "A mensagem anterior foi o texto extraído de uma imagem enviada pelo usuário que pode conter uma chave PIX")
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(user.id, "35")
                    baseProcessor.process(user)

                    val transactionId1 = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id
                    conversationHistoryService.createUserMessage(user.id, "Sim")
                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId1, "2021-10-10T10:00:00Z"))

                    coVerifyOrder {
                        paymentAdapter.pixTransaction(any(), any(), 3500, any(), any(), transactionId1, any(), any())
                    }
                }

                it("Deve ser capaz de identificar e concluir um PIX com valor") {
                    coEvery { paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any()) } returns Unit.right()

                    conversationHistoryService.createUserMessage(user.id, "Cobrança Gerada\n\nBanco do Brasil\n\nChave: ***********\n\nCarlos Alberto\n\n20")
                    conversationHistoryService.createSystemMessage(user.id, "A mensagem anterior foi o texto extraído de uma imagem enviada pelo usuário que pode conter uma chave PIX")
                    baseProcessor.process(user)

                    val transactionId1 = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id
                    conversationHistoryService.createUserMessage(user.id, "Sim")
                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId1, "2021-10-10T10:00:00Z"))

                    coVerifyOrder {
                        paymentAdapter.pixTransaction(any(), any(), 2000, any(), any(), transactionId1, any(), any())
                    }
                }
            }

            describe("Não deve realizar o pix") {
                describe("Se tiver saldo, o valor estiver ABAIXO do limite do assistente e o usuário DIGITAR pra NÃO CONFIRMAR os dados do pix apresentados pelo assistente") {
                    listOf(
                        "Não quero pagar",
                        "Não",
                        "Não confirmo",
                        "Dados incorretos",
                    ).forEach { content ->
                        it(content) {
                            coEvery {
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            } returns Unit.right()

                            conversationHistoryService.createUserMessage(
                                user.id,
                                "Quero fazer um pix de 20 reais para o número 21 99999-9999",
                            )
                            baseProcessor.process(user)

                            conversationHistoryService.createUserMessage(
                                user.id,
                                content,
                            )

                            baseProcessor.process(user)

                            val pixKey = mutableListOf<PixKey>()
                            val amount = mutableListOf<Long>()

                            coVerify {
                                paymentAdapter.pixValidation(capture(pixKey), any(), capture(amount), any())
                            }

                            coVerify(exactly = 0) {
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            }

                            pixKey.last().value shouldBe "+5521999999999"
                            pixKey.last().type shouldBe PixKeyType.PHONE
                            amount.last() shouldBe 2000L

                            val assistantMessage = historyRepository.findLatest(user.id).messages.last { it.type == MessageType.ASSISTANT }

                            if (pixKey.size > 1) {
                                assistantMessage.message shouldContain "pix-confirmation"
                                assistantMessage.message shouldContain "+5521999999999"
                                assistantMessage.message shouldContain "R\$ 20,00"
                            } else {
                                if (assistantMessage.message != null) {
                                    validateConversation("Não deve autorizar e finalizar a conversa sem realizar o pix.")
                                } else {
                                    assistantMessage.completionMessage?.acoes?.first()?.name shouldBeSameInstanceAs ActionType.NOOP
                                }
                            }
                        }
                    }
                }

                it("Se tiver saldo, o valor estiver ABAIXO do limite do assistente e o usuário clicar em NÃO CONFIRMAR os dados do pix apresentados pelo assistente") {
                    coEvery {
                        paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                    } returns Unit.right()

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Quero fazer um pix de 20 reais para o número 21 99999-9999",
                    )
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Não",
                    )

                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CANCEL, transactionId, "2021-10-10T10:00:00Z"))

                    val pixKey = slot<PixKey>()
                    val amount = slot<Long>()
                    val transactionIdTSCancelCapture = slot<TransactionId>()

                    coVerify {
                        paymentAdapter.pixValidation(capture(pixKey), any(), capture(amount), any())
                        transactionService.cancel(capture(transactionIdTSCancelCapture), any())
                    }

                    coVerify(exactly = 0) {
                        transactionService.confirm(any(), any(), any())
                        paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                    }

                    pixKey.captured.value shouldBe "+5521999999999"
                    pixKey.captured.type shouldBe PixKeyType.PHONE
                    amount.captured shouldBe 2000L
                    transactionIdTSCancelCapture.captured.value shouldBe transactionId.value

                    historyRepository.findLatest(user.id).messages.last { it.type == MessageType.SYSTEM }.message shouldBe "Transação cancelada"
                }

                it("Se tiver saldo, o valor estiver ACIMA do limite do assistente, mas o usuário clicou em NÃO AUTORIZAR") {
                    coEvery {
                        paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                    } returns Unit.right()
                    coEvery { paymentAdapter.pixValidation(any(), any(), any(), any()) } returns PixValidationResult(
                        keyType = PixKeyType.PHONE,
                        keyValue = "+5521999999999",
                        recipientName = "Recipient",
                        recipientInstitution = "Banco Teste",
                        recipientDocument = "12345678910",
                        pixLimitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                    ).right()

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Quero fazer um pix de 20 reais para o número 21 99999-9999",
                    )
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Não autorizar",
                    )

                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CANCEL, transactionId, "2021-10-10T10:00:00Z"))

                    val pixKey = slot<PixKey>()
                    val amount = slot<Long>()
                    val transactionIdTSCancelCapture = slot<TransactionId>()

                    coVerify {
                        paymentAdapter.pixValidation(capture(pixKey), any(), capture(amount), any())
                        transactionService.cancel(capture(transactionIdTSCancelCapture), any())
                    }

                    coVerify(exactly = 0) {
                        transactionService.confirm(any(), any(), any())
                        paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                    }

                    pixKey.captured.value shouldBe "+5521999999999"
                    pixKey.captured.type shouldBe PixKeyType.PHONE
                    amount.captured shouldBe 2000L
                    transactionIdTSCancelCapture.captured.value shouldBe transactionId.value

                    historyRepository.findLatest(user.id).messages.last { it.type == MessageType.SYSTEM }.message shouldBe "Transação cancelada"
                }

                describe("Se tiver saldo, o valor estiver ACIMA do limite do assistente, mas o usuário clicou em DADOS INCORRETOS ou DIGITOU pra NÃO AUTORIZAR") {
                    listOf(
                        "Não está certo",
                        "Não",
                        "Dados incorretos",
                        "Não autorizar",
                    ).forEach { content ->
                        it(content) {
                            coEvery {
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            } returns Unit.right()

                            coEvery { paymentAdapter.pixValidation(any(), any(), any(), any()) } returns PixValidationResult(
                                keyType = PixKeyType.PHONE,
                                keyValue = "+5521988888888",
                                recipientName = "Recipient",
                                recipientInstitution = "Banco Alpha",
                                recipientDocument = "12374678910",
                                pixLimitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                            ).right() andThen PixValidationResult(
                                keyType = PixKeyType.PHONE,
                                keyValue = "+5521999999999",
                                recipientName = "Recipient",
                                recipientInstitution = "Banco Teste",
                                recipientDocument = "12345678910",
                                pixLimitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                            ).right()

                            conversationHistoryService.createUserMessage(
                                user.id,
                                "Quero fazer um pix de 20 reais para o número 21 99999-9999",
                            )
                            baseProcessor.process(user)

                            conversationHistoryService.createUserMessage(
                                user.id,
                                content,
                            )

                            baseProcessor.process(user)

                            val pixKey = mutableListOf<PixKey>()
                            val amount = mutableListOf<Long>()

                            coVerify {
                                paymentAdapter.pixValidation(capture(pixKey), any(), capture(amount), any())
                            }

                            coVerify(exactly = 0) {
                                transactionService.cancel(any(), any())
                                transactionService.confirm(any(), any(), any())
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            }

                            pixKey.last().value shouldBe "+5521999999999"
                            pixKey.last().type shouldBe PixKeyType.PHONE
                            amount.last() shouldBe 2000L

                            val assistantMessage = historyRepository.findLatest(user.id).messages.last { it.type == MessageType.ASSISTANT }

                            if (pixKey.size > 1) {
                                assistantMessage.message shouldContain "authorize-pix"
                                assistantMessage.message shouldContain "+5521999999999"
                                assistantMessage.message shouldContain "R\$ 20,00"
                            } else {
                                if (assistantMessage.message != null) {
                                    validateConversation("O assistente deve acatar o pedido do usuário e não realizar o pix.")
                                } else {
                                    assistantMessage.completionMessage?.acoes?.first()?.name shouldBeSameInstanceAs ActionType.NOOP
                                }
                            }
                        }
                    }
                }

                it("Se NÃO tiver saldo, o valor estiver ABAIXO do limite do assistente, tem conta CONECTADA, mas o usuário clicou em NÃO AUTORIZAR") {
                    coEvery {
                        paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                    } returns Unit.right()

                    coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns listOf(SweepingConsent(SweepingParticipant(SweepingParticipantId("id"), "nome", "shortName"), 1000_00, null)).right()

                    coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 10_00).right()

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Quero fazer um pix de 20 reais para o número 21 99999-9999",
                    )
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Não autorizar",
                    )

                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CANCEL, transactionId, "2021-10-10T10:00:00Z"))

                    val pixKey = slot<PixKey>()
                    val amount = slot<Long>()
                    val transactionIdTSCancelCapture = slot<TransactionId>()

                    coVerify {
                        paymentAdapter.pixValidation(capture(pixKey), any(), capture(amount), any())
                        transactionService.cancel(capture(transactionIdTSCancelCapture), any())
                    }

                    coVerify(exactly = 0) {
                        transactionService.confirm(any(), any(), any())
                        paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                    }

                    pixKey.captured.value shouldBe "+5521999999999"
                    pixKey.captured.type shouldBe PixKeyType.PHONE
                    amount.captured shouldBe 2000L
                    transactionIdTSCancelCapture.captured.value shouldBe transactionId.value

                    historyRepository.findLatest(user.id).messages.last { it.type == MessageType.SYSTEM }.message shouldBe "Transação cancelada"
                }

                describe("Se NÃO tiver saldo, o valor estiver ABAIXO do limite do assistente, tem conta CONECTADA, mas o usuário clicou em DADOS INCORRETOS ou DIGITOU pra NÃO AUTORIZAR") {
                    listOf(
                        "Não está certo",
                        "Não",
                        "Dados incorretos",
                        "Não autorizar",
                    ).forEach { content ->
                        it(content) {
                            coEvery {
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            } returns Unit.right()

                            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns listOf(SweepingConsent(SweepingParticipant(SweepingParticipantId("id"), "nome", "shortName"), 1000_00, null)).right()

                            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 10_00).right()

                            conversationHistoryService.createUserMessage(
                                user.id,
                                "Quero fazer um pix de 20 reais para o número 21 99999-9999",
                            )
                            baseProcessor.process(user)

                            conversationHistoryService.createUserMessage(
                                user.id,
                                content,
                            )

                            baseProcessor.process(user)

                            val pixKey = mutableListOf<PixKey>()
                            val amount = mutableListOf<Long>()

                            coVerify {
                                paymentAdapter.pixValidation(capture(pixKey), any(), capture(amount), any())
                            }

                            coVerify(exactly = 0) {
                                transactionService.confirm(any(), any(), any())
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            }

                            pixKey.last().value shouldBe "+5521999999999"
                            pixKey.last().type shouldBe PixKeyType.PHONE
                            amount.last() shouldBe 2000L

                            val assistantMessage = historyRepository.findLatest(user.id).messages.last { it.type == MessageType.ASSISTANT }

                            if (pixKey.size > 1) {
                                assistantMessage.message shouldContain "Você confirma os dados abaixo?"
                                assistantMessage.message shouldContain "Esta transferência usará saldo da sua conta conectada"
                                assistantMessage.message shouldContain "+5521999999999"
                                assistantMessage.message shouldContain "R\$ 20,00"
                            } else {
                                if (assistantMessage.message != null) {
                                    validateConversation("Não deve autorizar e finalizar a conversa sem realizar o pix.")
                                } else {
                                    assistantMessage.completionMessage?.acoes?.first()?.name shouldBeSameInstanceAs ActionType.NOOP
                                }
                            }
                        }
                    }
                }

                it("Se NÃO tiver saldo, o valor estiver ACIMA do limite do assistente e tem conta CONECTADA, mas o usuário clicou em NÃO AUTORIZAR") {
                    coEvery {
                        paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                    } returns Unit.right()

                    coEvery { paymentAdapter.pixValidation(any(), any(), any(), any()) } returns PixValidationResult(
                        keyType = PixKeyType.PHONE,
                        keyValue = "+5521999999999",
                        recipientName = "Recipient",
                        recipientInstitution = "Banco Teste",
                        recipientDocument = "12345678910",
                        pixLimitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                    ).right()

                    coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns listOf(SweepingConsent(SweepingParticipant(SweepingParticipantId("id"), "nome", "shortName"), 1000_00, null)).right()

                    coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 10_00).right()

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Quero fazer um pix de 20 reais para o número 21 99999-9999",
                    )
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Não autorizar",
                    )

                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CANCEL, transactionId, "2021-10-10T10:00:00Z"))

                    val pixKey = slot<PixKey>()
                    val amount = slot<Long>()
                    val transactionIdTSCancelCapture = slot<TransactionId>()

                    coVerify {
                        paymentAdapter.pixValidation(capture(pixKey), any(), capture(amount), any())
                        transactionService.cancel(capture(transactionIdTSCancelCapture), any())
                    }

                    coVerify(exactly = 0) {
                        transactionService.confirm(any(), any(), any())
                        paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                    }

                    pixKey.captured.value shouldBe "+5521999999999"
                    pixKey.captured.type shouldBe PixKeyType.PHONE
                    amount.captured shouldBe 2000L
                    transactionIdTSCancelCapture.captured.value shouldBe transactionId.value

                    historyRepository.findLatest(user.id).messages.last { it.type == MessageType.SYSTEM }.message shouldBe "Transação cancelada"
                }

                describe("Se NÃO tiver saldo, o valor estiver ACIMA do limite do assistente e tem conta CONECTADA, mas o usuário clicou em DADOS INCORRETOS ou DIGITOU pra NÃO AUTORIZAR") {
                    listOf(
                        "Não está certo",
                        "Não",
                        "Dados incorretos",
                        "Não autorizar",
                    ).forEach { content ->
                        it(content) {
                            coEvery {
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            } returns Unit.right()

                            coEvery { paymentAdapter.pixValidation(any(), any(), any(), any()) } returns PixValidationResult(
                                keyType = PixKeyType.PHONE,
                                keyValue = "+5521988888888",
                                recipientName = "Recipient",
                                recipientInstitution = "Banco Alpha",
                                recipientDocument = "12378678910",
                                pixLimitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                            ).right() andThen PixValidationResult(
                                keyType = PixKeyType.PHONE,
                                keyValue = "+5521999999999",
                                recipientName = "Recipient",
                                recipientInstitution = "Banco Teste",
                                recipientDocument = "12345678910",
                                pixLimitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                            ).right()

                            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns listOf(SweepingConsent(SweepingParticipant(SweepingParticipantId("id"), "nome", "shortName"), 1000_00, null)).right()

                            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 10_00).right()

                            conversationHistoryService.createUserMessage(
                                user.id,
                                "Quero fazer um pix de 20 reais para o número 21 99999-9999",
                            )
                            baseProcessor.process(user)

                            conversationHistoryService.createUserMessage(
                                user.id,
                                content,
                            )

                            baseProcessor.process(user)

                            val pixKey = mutableListOf<PixKey>()
                            val amount = mutableListOf<Long>()

                            coVerify {
                                paymentAdapter.pixValidation(capture(pixKey), any(), capture(amount), any())
                            }

                            coVerify(exactly = 0) {
                                transactionService.confirm(any(), any(), any())
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            }

                            pixKey.last().value shouldBe "+5521999999999"
                            pixKey.last().type shouldBe PixKeyType.PHONE
                            amount.last() shouldBe 2000L

                            val assistantMessage = historyRepository.findLatest(user.id).messages.last { it.type == MessageType.ASSISTANT }
                            if (pixKey.size > 1) {
                                assistantMessage.message shouldContain "O valor do Pix excede o seu limite transacional por WhatsApp, mas você pode autorizar o pagamento se os dados estiverem corretos"
                                assistantMessage.message shouldContain "Esta transferência usará saldo da sua conta conectada"
                                assistantMessage.message shouldContain "+5521999999999"
                                assistantMessage.message shouldContain "R\$ 20,00"
                            } else {
                                if (assistantMessage.message != null) {
                                    validateConversation("Não deve autorizar e finalizar a conversa sem realizar o pix.")
                                } else {
                                    assistantMessage.completionMessage?.acoes?.first()?.name shouldBeSameInstanceAs ActionType.NOOP
                                }
                            }
                        }
                    }
                }

                it("Se NÃO tiver saldo, o valor estiver ABAIXO do limite do assistente e tem conta CONECTADA sem SALDO") {
                    coEvery {
                        paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                    } returns Unit.right()

                    coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns listOf(SweepingConsent(SweepingParticipant(SweepingParticipantId("id"), "nome", "shortName"), 1_00, null)).right()

                    coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 10_00).right()

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Quero fazer um pix de 20 reais para o número 21 99999-9999",
                    )
                    baseProcessor.process(user)

                    val pixKey = slot<PixKey>()
                    val amount = slot<Long>()

                    coVerify {
                        paymentAdapter.pixValidation(capture(pixKey), any(), capture(amount), any())
                    }

                    coVerify(exactly = 0) {
                        transactionService.confirm(any(), any(), any())
                        paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                    }

                    pixKey.captured.value shouldBe "+5521999999999"
                    pixKey.captured.type shouldBe PixKeyType.PHONE
                    amount.captured shouldBe 2000L

                    historyRepository.findLatest(user.id).messages.last().message shouldBe "O valor do pix solicitado pelo usuário excede o limite configurado na conta conectada."
                }

                describe("Se NÃO tiver saldo e NÃO tem conta CONECTADA") {
                    listOf(
                        "Quero fazer um pix de 18 reais para o número 21 99999-9999",
                        "Pix pra 21999999999 18 reais",
                    ).forEach { content ->
                        it(content) {
                            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
                            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 0).right()

                            conversationHistoryService.createUserMessage(
                                user.id,
                                content,
                            )
                            baseProcessor.process(user)

                            val pixKey = slot<PixKey>()
                            val amount = slot<Long>()

                            coVerify {
                                paymentAdapter.pixValidation(capture(pixKey), any(), capture(amount), any())
                            }

                            coVerify(exactly = 0) {
                                transactionService.create(any(), any(), any(), any())
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            }

                            pixKey.captured.value shouldBe "+5521999999999"
                            pixKey.captured.type shouldBe PixKeyType.PHONE
                            amount.captured shouldBe 1800L

                            historyRepository.findLatest(user.id).messages.last { it.type == MessageType.ASSISTANT }.message shouldBe "Você não tem saldo suficiente para realizar a transação."
                        }
                    }
                }

                describe("Se NÃO tiver saldo e NÃO tem conta CONECTADA, mas com histórico de mensagens") {
                    listOf(
                        Pair(
                            "Pix pra 21999999999 18 reais",
                            listOf(
                                History(MessageType.USER, "Posso fazer um pix? Pode falar, celular? 119... 9475743"),
                                History(MessageType.SYSTEM, "A mensagem anterior foi transcrita do que o usuário enviou"),
                                History(MessageType.ASSISTANT, "Para realizar um Pix, você precisa ter saldo na sua conta Friday. Atualmente, seu saldo é de R\$ 0,00. Você pode adicionar saldo enviando um <NAME_EMAIL>. Por exemplo, se seu CPF fosse 123.456.789-00, você enviaria um <NAME_EMAIL>. Assim que o saldo estiver disponível, você poderá realizar o Pix."),
                            ),
                        ),
                    ).forEach { (message, history) ->
                        it(message) {
                            saveHistory(history)
                            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
                            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 0).right()

                            conversationHistoryService.createUserMessage(
                                user.id,
                                message,
                            )
                            baseProcessor.process(user)

                            val pixKey = slot<PixKey>()
                            val amount = slot<Long>()

                            coVerify {
                                paymentAdapter.pixValidation(capture(pixKey), any(), capture(amount), any())
                            }

                            coVerify(exactly = 0) {
                                transactionService.create(any(), any(), any(), any())
                                paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any())
                            }

                            pixKey.captured.value shouldBe "+5521999999999"
                            pixKey.captured.type shouldBe PixKeyType.PHONE
                            amount.captured shouldBe 1800L

                            historyRepository.findLatest(user.id).messages.last { it.type == MessageType.ASSISTANT }.message shouldBe "Você não tem saldo suficiente para realizar a transação."
                        }
                    }
                }
            }
        }
    }

    private fun generateUnregisteredUser(user: User) {
        // Test the flow start with a mocked notification gateway event
        val interactionWindowService = mockk<InteractionWindowService>(relaxed = true)
        every { interactionWindowService.checkAndCreate(any<ChatBotNotificationGatewayTO>()) } returns Unit.right()
        val messageHandler = ChatBotNotificationGatewayHandler(
            conversationHistoryService = conversationHistoryService,
            notificationService = notificationService,
            queue = "fake_queue",
            amazonSQS = mockk(),
            configuration = mockk(),
            onePixPayInstrumentation = mockk(relaxed = true),
            interactionWindowService = interactionWindowService,
            paymentAdapter = paymentAdapter,
            tenantPropagator = mockk(relaxed = true),
            tenantService = tenantService,
            chatbotNotificationBuilder = ChatbotNotificationBuilder(
                buildNotificationService = BuildNotificationService(notificationContextTemplatesService, tenantService),
                notificationContextTemplatesService = notificationContextTemplatesService,
                onboardingSinglePixNotificationService = onboardingSinglePixNotificationService,
                customNotificationService = mockk(relaxed = true),
                tenantService = tenantService,
            ),
        )

        val genericDetails = GenericNotificationDetailsTO(
            notification = GenericNotificationContentTO(
                notificationId = "test_notification",
                receiver = ReceiverTO(msisdn = user.id.value),
                accountId = user.accountId,
                template = NotificationTemplate("test_template"),
                configurationKey = "configuration-key",
                parameters = listOf(),
                quickReplyButtonsWhatsAppParameter = emptyList(),
            ),
        )

        val messageBody = ChatBotNotificationGatewayTO(
            walletId = "walletId",
            account = AccountTO(
                id = "", // Empty account ID to trigger buildUnregisteredUserAndWallet
                fullName = "Test User",
                msisdn = user.id.value,
                accountGroups = emptyList(),
                status = AccountStatus.NOT_REGISTERED,
                paymentStatus = AccountPaymentStatus.UpToDate,
                subscriptionType = SubscriptionType.IN_APP,
            ),
            details = genericDetails,
        )

        val message = Message.builder()
            .body(getObjectMapper().writeValueAsString(messageBody))
            .build()

        messageHandler.handleMessage(message)
    }
}