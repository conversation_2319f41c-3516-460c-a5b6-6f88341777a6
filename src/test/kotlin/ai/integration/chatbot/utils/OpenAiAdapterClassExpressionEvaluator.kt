package ai.integration.chatbot.utils

import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.boolex.EventEvaluatorBase

class OpenAiAdapterClassExpressionEvaluator : EventEvaluatorBase<ILoggingEvent>() {
    override fun evaluate(event: ILoggingEvent?): <PERSON><PERSON><PERSON> {
        val logger = event?.loggerName
        return logger == "ai.chatbot.adapters.openai.OpenAIAdapter"
    }
}