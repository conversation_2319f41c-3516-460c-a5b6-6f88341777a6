package ai.integration.chatbot.utils

import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.adapters.openai.OpenAIFunctionMessageParser
import ai.chatbot.app.conversation.ChatMessageWrapper
import ai.chatbot.app.utils.includeConversationInPrompt

class ConversationValidator(
    private val openAIAdapter: OpenAIAdapter,
    private val shouldPrint: Boolean = false,
) {
    fun validate(
        testName: String?,
        messages: List<ChatMessageWrapper>,
        expectedOutput: String,
    ) {
        val history = OpenAIFunctionMessageParser().buildChatMessageList(prompt = includeConversationInPrompt(getPrompt(expectedOutput), messages), historyState = null, messages = emptyList(), systemMessageToCompletion = null)
        val response = openAIAdapter.generateCompletion(history = history).choices.first().message

        if (shouldPrint) {
            println(
                """
                Teste: $testName
                ExpectedOutput: $expectedOutput
                Validation: ${response.content}
            """,
            )
        }
        val trimmedResponse = response.content.trim()

        val validExpressions =
            listOf(
                """"atende": "S"""",
                "atendeu a todos os critérios",
                "submissão atende a todos os critérios",
                "a mensagem atende a todos os critérios",
                "a resposta é S",
                "a submissão atende aos critérios.",
            )

        val passed = validExpressions.any { validExpression -> trimmedResponse.contains(validExpression, ignoreCase = true) }
        if (!passed) {
            throw AssertionError(
                """
                |A validação falhou.
                |Teste: $testName
                |Semântica esperada: $expectedOutput
                |Racional do modelo: $response
                """.trimMargin(),
            )
        }
    }

    private fun getPrompt(
        objective: String,
    ): String {
        return """
        Você é um analisador de conversas do chatbot Friday. Você está analisando uma conversa entre um usuário e um assistente virtual chamado Fred. A sua função é analisar toda a conversa e verificar se o assistente Fred atendeu o objetivo, mesmo que não seja na última mensagem. 
        
        Regras gerais:
            - A conversa a ser analisada está na seção 'Conversa';
            - Você deve considerar a conversa como um todo e não apenas as mensagens individuais;
            - Você deve considerar o contexto da conversa e não apenas as palavras-chave;
            - Você deve considerar a intenção do usuário e não apenas as palavras-chave;
            - Você deve considerar o objetivo na seção 'Objetivo' e verificar se o assistente Fred atendeu a esse objetivo;
            - Você deve montar a sua resposta seguindo as orientações na seção 'Resposta';

        [BEGIN - Resposta]
            Conteúdo da sua resposta:
                1 - A explicação do seu racional. 
                2 - Apenas um JSON com a propriedade "atende" e o valor "S" ou "N". Exemplo: {"atende": "S"}.
            
            Regras para elaborar a resposta:
                - Escreva de maneira passo a passo seu raciocínio sobre cada critério para ter certeza de que sua conclusão está correta. 
                - Evite simplesmente declarar as respostas corretas no início e cite frases ou palavras-chave da conversa que justifiquem sua resposta.
                - Se algum objetivo não for atendido, sua resposta deverá ser N. Caso contrário, sua resposta deverá ser S.
                - Não responda como se fosse o assistente e estivesse passando instruções para o usuário.   
        [END - Resposta]
        
        [BEGIN - Conversa]
            {{messages}}
        [END - Conversa]
        
        [BEGIN - Objetivo]
            $objective
        [END - Objetivo]
        """
    }
}