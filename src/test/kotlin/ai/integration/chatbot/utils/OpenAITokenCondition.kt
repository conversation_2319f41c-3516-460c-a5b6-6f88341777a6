package ai.integration.chatbot.utils

import io.kotest.core.annotation.EnabledCondition
import io.kotest.core.spec.Spec
import kotlin.reflect.KClass

class OpenAITokenCondition : EnabledCondition {
    override fun enabled(kclass: KClass<out Spec>): Boolean {
        val token =
            System.getenv("OPENAI_TOKEN") // Para rodar os testes é necesário configurar a variável de ambiente OPENAI_TOKEN
        val ignoreEnv = System.getenv("OPENAI_TOKEN_IGNORE")
        val ignore = ignoreEnv != null && ignoreEnv.toBoolean()
        return !token.isNullOrBlank() && !ignore
    }
}