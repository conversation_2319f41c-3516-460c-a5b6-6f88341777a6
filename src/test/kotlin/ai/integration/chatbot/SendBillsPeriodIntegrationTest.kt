package ai.integration.chatbot

import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.balance
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.Recipient
import ai.chatbot.app.bill.ScheduledInfo
import ai.chatbot.app.billView
import ai.chatbot.app.billView2
import ai.chatbot.app.billView3
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.subscription
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.formattedAmount
import ai.chatbot.app.withGivenDateTime
import ai.integration.chatbot.utils.OpenAITokenCondition
import arrow.core.right
import io.kotest.core.annotation.EnabledIf
import io.kotest.matchers.collections.shouldBeIn
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.micronaut.context.ApplicationContext
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.slot
import io.mockk.verify
import jakarta.inject.Named
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters
import java.util.UUID

@EnabledIf(OpenAITokenCondition::class)
class SendBillsPeriodIntegrationTest(
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
    @Named(FRIDAY_ENV) tenantConfiguration: TenantConfiguration,
) : AbstractIntegrationTest(
    openAIAdapter = openAIAdapter,
    applicationContext = applicationContext,
    printConversation = true,
    userGroups = listOf(),
    tenantConfiguration = tenantConfiguration,
) {
    private val billComingDueState =
        BillComingDueHistoryState(
            walletWithBills =
            WalletWithBills(
                bills = listOf(),
                walletId = walletId,
                walletName = "walletName",
            ),
            balance = null,
            internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
            user = user,
            contacts = emptyList(),
            subscription = null, // TODO: o que vai acontecer quando passar a receber a subscription
        )

    private val billToday = billViews.first().copy(dueDate = getLocalDate(), billId = BillId("billToday"), amountTotal = 1000)
    private val billTomorrow = billViews.first().copy(dueDate = getLocalDate().plusDays(1), billId = BillId("billTomorrow"), amountTotal = 2000)
    private val billNextMonday = billViews.first().copy(dueDate = getLocalDate().with(TemporalAdjusters.next(DayOfWeek.MONDAY)), billId = BillId("billNextMonday"), amountTotal = 4000)
    private val billNextWeek = billViews.first().copy(dueDate = getLocalDate().plusDays(7), billId = BillId("billNextWeek"), amountTotal = 5000)
    private val billThisWeek = billViews.first().copy(dueDate = getLocalDate().with(TemporalAdjusters.next(DayOfWeek.SATURDAY)), billId = BillId("billThisWeek"), amountTotal = 8000)
    private val billThisMonth = billViews.first().copy(dueDate = getLocalDate().with(TemporalAdjusters.lastDayOfMonth()), billId = BillId("billThisMonth"), amountTotal = 6000)
    private val billNextFifteen = billViews.first().copy(dueDate = getLocalDate().plusDays(14), billId = BillId("billNextFifteen"), amountTotal = 7000)

    init {
        beforeEach {
            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
            coEvery { paymentAdapter.getContacts(any(), any()) } returns emptyList<Contact>().right()
            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(billViews, walletId = walletId, "walletName")).right()
            coEvery { paymentAdapter.getSubscription(user.id) } returns subscription.right()
            coEvery {
                paymentAdapter.getBalanceAndForecast(any(), any())
            } returns balance.right()

            historyStateRepository.save(user.id, billComingDueState)
        }

        describe("quando o usuário pedir suas contas") {
            describe("quando o usuário falar sobre as contas de hoje") {
                listOf(
                    "quais minhas contas de hoje?",
                    "quais são as contas pendentes de hoje?",
                    "o que tenho pendente hoje?",
                ).forEach { message ->
                    it("deve listar as contas de hoje - $message") {
                        coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billView3.copy(dueDate = getLocalDate())), walletId, "walletName")).right()
                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)
                        val startPeriod = getLocalDate()
                        val endPeriod = getLocalDate()

                        val startSlot = slot<LocalDate>()
                        val endSlot = slot<LocalDate>()
                        verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                        startSlot.captured shouldBe startPeriod
                        endSlot.captured shouldBe endPeriod
                        coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                        val state = historyStateRepository.findLatest(user.id)
                        state.shouldBeInstanceOf<BillComingDueHistoryState>()
                        with(state) {
                            startDate shouldBe startPeriod
                            endDate shouldBe endPeriod
                        }
                    }
                }
            }

            describe("quando usuário falar sobre contas vencidas") {
                listOf(
                    "quais minhas contas vencidas?",
                    "quais são as contas vencidas?",
                    "o que tenho vencido?",
                    "tenho alguma conta vencida?",
                ).forEach { message ->
                    it("deve listar as contas vencidas - $message") {
                        coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billView2.copy(dueDate = getLocalDate().minusDays(3)), billView3.copy(dueDate = getLocalDate().minusDays(2))), walletId, "walletName")).right()

                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)

                        val yesterday = getLocalDate().minusDays(1)
                        val startPeriod = yesterday.minusWeeks(1)

                        val startSlot = slot<LocalDate>()
                        val endSlot = slot<LocalDate>()
                        verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                        startSlot.captured shouldBe startPeriod
                        endSlot.captured shouldBe yesterday
                        coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                        val state = historyStateRepository.findLatest(user.id)
                        state.shouldBeInstanceOf<BillComingDueHistoryState>()

                        with(state) {
                            startDate shouldBe startPeriod
                            endDate shouldBe yesterday
                        }
                    }
                }
            }

            describe("quando usuário falar sobre as contas do mês") {
                listOf(
                    "quais minhas contas do mês?",
                    "quais são as contas do mês?",
                    "o que tenho para pagar esse mês?",
                ).forEach { message ->
                    it("deve listar as contas do mês - $message") {
                        coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billView2.copy(dueDate = getLocalDate().minusDays(3)), billView, billView3.copy(dueDate = getLocalDate().plusWeeks(1))), walletId, "walletName")).right()

                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)

                        val startOfMonth = getLocalDate().withDayOfMonth(1)
                        val endPeriod = startOfMonth.with(TemporalAdjusters.lastDayOfMonth())

                        val startSlot = slot<LocalDate>()
                        val endSlot = slot<LocalDate>()
                        verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                        startSlot.captured shouldBe startOfMonth
                        endSlot.captured shouldBe endPeriod
                        coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                        val state = historyStateRepository.findLatest(user.id)
                        state.shouldBeInstanceOf<BillComingDueHistoryState>()

                        with(state) {
                            startDate shouldBe startOfMonth
                            endDate shouldBe endPeriod
                        }
                    }
                }

                it("deve listar as contas do mês em mensagens separadas se passar de 1024 caracteres") {
                    coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns
                        UserAndWallet(
                            user = user,
                            wallet = WalletWithBills(
                                bills = buildList {
                                    for (x in 1..30) {
                                        val amount = (100..1000L).random() * x
                                        add(
                                            billView2.copy(
                                                billId = BillId(UUID.randomUUID().toString()),
                                                dueDate = getLocalDate().plusDays((0..29L).random()),
                                                billDescription = "Pagamento pix $x",
                                                recipient = Recipient(name = "Pessoa de Número $x"),
                                                amount = amount,
                                                amountTotal = amount,
                                                externalBillId = x,
                                            ),
                                        )
                                    }
                                },
                                walletId = walletId,
                                walletName = "walletName",
                            ),
                        ).right()

                    conversationHistoryService.createUserMessage(user.id, "quais minhas contas do mês?")
                    baseProcessor.process(user)

                    val startOfMonth = getLocalDate().withDayOfMonth(1)
                    val endPeriod = startOfMonth.with(TemporalAdjusters.lastDayOfMonth())

                    val startSlot = slot<LocalDate>()
                    val endSlot = slot<LocalDate>()
                    verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                    startSlot.captured shouldBe startOfMonth
                    endSlot.captured shouldBe endPeriod
                    coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                    val state = historyStateRepository.findLatest(user.id)
                    state.shouldBeInstanceOf<BillComingDueHistoryState>()

                    with(state) {
                        startDate shouldBe startOfMonth
                        endDate shouldBe endPeriod
                    }

                    verify(exactly = 4) {
                        notificationService.notify(
                            userId = user.id,
                            accountId = user.accountId,
                            message = match { it.contains("Pessoa de Número", true) },
                            quickReplyButtons = any(),
                        )
                    }
                }
            }

            describe("quando o usuário perguntar sobre as contas de amanhã") {
                listOf(
                    "quais minhas contas de amanhã?",
                    "quais são as contas pendentes de amanhã?",
                    "o que tenho pendente amanhã?",
                ).forEach { message ->
                    it("deve listar as contas de amanhã - $message") {
                        coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billView3.copy(dueDate = getLocalDate().plusDays(1))), walletId, "walletName")).right()

                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)

                        val startPeriod = getLocalDate().plusDays(1)
                        val endPeriod = getLocalDate().plusDays(1)

                        val startSlot = slot<LocalDate>()
                        val endSlot = slot<LocalDate>()
                        verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                        startSlot.captured shouldBe startPeriod
                        endSlot.captured shouldBe endPeriod
                        coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                        val state = historyStateRepository.findLatest(user.id)
                        state.shouldBeInstanceOf<BillComingDueHistoryState>()
                        with(state) {
                            startDate shouldBe startPeriod
                            endDate shouldBe endPeriod
                        }
                    }
                }

                describe("e for o último dia de fevereiro") {
                    it("deve listar as contas de 1 de março") {
                        withGivenDateTime(getZonedDateTime().withDayOfMonth(28).withMonth(2).withYear(2025)) {
                            createUserHistory()
                            historyStateRepository.save(
                                user.id,
                                BillComingDueHistoryState(
                                    walletWithBills =
                                    WalletWithBills(
                                        bills = listOf(),
                                        walletId = walletId,
                                        walletName = "walletName",
                                    ),
                                    balance = null,
                                    internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
                                    user = user,
                                    contacts = emptyList(),
                                    subscription = null,
                                ),
                            )
                            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billView3.copy(dueDate = getLocalDate().plusDays(1))), walletId, "walletName")).right()

                            conversationHistoryService.createUserMessage(user.id, "quais minhas contas de amanhã?")
                            baseProcessor.process(user)

                            val startPeriod = getLocalDate().plusDays(1)
                            val endPeriod = getLocalDate().plusDays(1)

                            val startSlot = slot<LocalDate>()
                            val endSlot = slot<LocalDate>()
                            verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                            startSlot.captured shouldBe startPeriod
                            endSlot.captured shouldBe endPeriod
                            coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                            val state = historyStateRepository.findLatest(user.id)
                            state.shouldBeInstanceOf<BillComingDueHistoryState>()
                            with(state) {
                                startDate shouldBe startPeriod
                                endDate shouldBe endPeriod
                            }
                            printConversation(testCase)
                        }
                    }
                }
            }

            describe("quando usuário perguntar sobre contas da semana") {
                listOf(
                    "quais minhas contas da semana?",
                    "quais são as contas pendentes da semana?",
                    "o que tenho pendente essa semana?",
                ).forEach { message ->
                    it("deve listar as contas da semana - $message") {
                        coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billView2.copy(dueDate = getLocalDate().plusDays(1)), billView, billView3.copy(dueDate = getLocalDate().plusDays(2))), walletId, "walletName")).right()

                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)

                        val startOfWeek = getLocalDate().with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY))
                        val endOfWeek = startOfWeek.with(TemporalAdjusters.next(DayOfWeek.SATURDAY))

                        val startSlot = slot<LocalDate>()
                        val endSlot = slot<LocalDate>()
                        verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                        startSlot.captured shouldBe startOfWeek
                        endSlot.captured shouldBe endOfWeek
                        coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                        val state = historyStateRepository.findLatest(user.id)
                        state.shouldBeInstanceOf<BillComingDueHistoryState>()

                        with(state) {
                            startDate shouldBe startOfWeek
                            endDate shouldBe endOfWeek
                        }
                    }
                }
            }

            describe("quando o usuário não tiver contas pendentes") {
                listOf(
                    "quais minhas contas de hoje?",
                    "quais são as contas pendentes do mês?",
                    "o que tenho pendente essa semana?",
                ).forEach { message ->
                    it("deve responder que não tem contas vencendo no período - $message") {
                        coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(), walletId, "walletName")).right()

                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)

                        verify { pendingBillsService.updatePendingBills(any(), any(), any()) }

                        coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                        validateConversation("""Deve informar que o usuário não possui conta vencendo no período.""".trimIndent())
                    }
                }
            }
        }

        describe("quando o chatbot oferecer pra ver as contas") {
            listOf(
                "Você gostaria de ver suas contas?",
                "Conexão Open Finance concluída com sucesso! Agora é possível realizar todos os seus pagamentos e investimentos de forma prática, utilizando o saldo disponível em seu banco, Ao autorizar com um *\"sim\"*, eu transfiro o saldo necessário de lá pra cá e pago suas contas diretamente. Bora ver os pagamentos cadastrados que podem ser pagos com um \"sim\"?",
                "Você quer ver os seus pagamentos?",
            ).forEach { message ->
                it("deve perguntar se o usuário quer ver as contas - $message") {
                    coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billView2.copy(dueDate = getLocalDate().plusDays(1)), billView, billView3.copy(dueDate = getLocalDate().plusDays(2))), walletId, "walletName")).right()

                    conversationHistoryService.createAssistantMessage(user.id, message)
                    conversationHistoryService.createUserMessage(user.id, "sim")

                    baseProcessor.process(user)

                    conversationHistoryService.historyRepository.findLatest(user.id)

                    coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }
                }
            }
        }

        describe("quando o usuário perguntar sobre o valor de contas a pagar") {
            describe("e depois pedir para ver as contas") {
                it("deve mostrar os valores e depois as contas") {
                    coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billToday), walletId, "walletName")).right()

                    conversationHistoryService.createUserMessage(user.id, "quanto tenho pendente pra hoje?")
                    baseProcessor.process(user)

                    val startOfPeriod = getLocalDate()
                    val endOfPeriod = getLocalDate()

                    val startSlot = slot<LocalDate>()
                    val endSlot = slot<LocalDate>()
                    verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                    startSlot.captured shouldBe startOfPeriod
                    endSlot.captured shouldBe endOfPeriod
                    coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                    conversationHistoryService.createUserMessage(user.id, "e quais são essas contas?")
                    baseProcessor.process(user)

                    validateConversation(
                        """
                        Deve informar que o usuário possui ${billToday.amountTotal.formattedAmount()} em aberto.
                        E depois apresentar a lista de contas
                        """.trimIndent(),
                    )
                }
            }

            describe("quando o usuário perguntar o valor das contas de hoje") {
                listOf(
                    "quanto tenho pendente pra hoje?",
                    "quanto tenho de pagamentos pra hoje?",
                    "qual valor de contas pendentes para hoje?",
                ).forEach { message ->
                    it("deve exibir o valor das contas pendentes para hoje - $message") {
                        coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billToday), walletId, "walletName")).right()

                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)

                        val startOfPeriod = getLocalDate()
                        val endOfPeriod = getLocalDate()

                        val startSlot = slot<LocalDate>()
                        val endSlot = slot<LocalDate>()
                        verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                        startSlot.captured shouldBe startOfPeriod
                        endSlot.captured shouldBe endOfPeriod
                        coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                        validateConversation("""Deve informar que o usuário possui ${billToday.amountTotal.formattedAmount()} em aberto.""".trimIndent())
                    }
                }
            }

            describe("quando o usuário perguntar o valor das contas de amanhã") {
                listOf(
                    "quanto tenho pendente pra amanhã?",
                    "quanto tenho de pagamentos pra amanhã?",
                    "qual valor de contas pendentes para amanhã?",
                ).forEach { message ->
                    it("deve exibir o valor das contas pendentes para amanhã - $message") {
                        val billTomorrowScheduled = billTomorrow.copy(billId = BillId("tomorrowScheduled"), scheduledInfo = ScheduledInfo.SCHEDULED, amountTotal = 4000)
                        coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billTomorrow, billTomorrowScheduled), walletId, "walletName")).right()

                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)

                        val startOfPeriod = getLocalDate().plusDays(1)
                        val endOfPeriod = getLocalDate().plusDays(1)

                        val startSlot = slot<LocalDate>()
                        val endSlot = slot<LocalDate>()
                        verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                        startSlot.captured shouldBe startOfPeriod
                        endSlot.captured shouldBe endOfPeriod
                        coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                        validateConversation("""Deve informar que o usuário possui ${billTomorrow.amountTotal.formattedAmount()} em aberto e ${billTomorrowScheduled.amountTotal.formattedAmount()} agendado""".trimIndent())
                    }
                }
            }

            describe("quando o usuário perguntar o valor das contas da semana") {
                listOf(
                    "quanto tenho pendente pra essa semana?",
                    "quanto tenho de pagamentos pra essa semana?",
                    "qual valor de contas pendentes para essa semana?",
                ).forEach { message ->
                    it("deve exibir o valor das contas pendentes para essa semana - $message") {
                        val billThisWeekScheduled = billThisWeek.copy(billId = BillId("thisWeekScheduled"), scheduledInfo = ScheduledInfo.SCHEDULED, amountTotal = 5000)
                        coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billNextWeek, billThisWeekScheduled), walletId, "walletName")).right()

                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)

                        val startOfPeriod = getLocalDate().with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY))
                        val endOfPeriod = getLocalDate().with(TemporalAdjusters.next(DayOfWeek.SATURDAY))

                        val startSlot = slot<LocalDate>()
                        val endSlot = slot<LocalDate>()
                        verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                        startSlot.captured shouldBe startOfPeriod
                        endSlot.captured shouldBe endOfPeriod
                        coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                        validateConversation("""Deve informar que o usuário possui ${billNextWeek.amountTotal.formattedAmount()} em aberto e ${billThisWeekScheduled.amountTotal.formattedAmount()} agendado""".trimIndent())
                    }
                }
            }

            describe("quando o usuário perguntar o valor das contas do mês") {
                listOf(
                    "quanto tenho pendente pra esse mês?",
                    "quanto tenho de pagamentos pra esse mês?",
                    "qual valor de contas pendentes para esse mês?",
                ).forEach { message ->
                    it("deve exibir o valor das contas pendentes para esse mês - $message") {
                        val billThisMonthScheduled = billThisMonth.copy(billId = BillId("thisMonthScheduled"), scheduledInfo = ScheduledInfo.SCHEDULED, amountTotal = 6000)
                        coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billThisMonth, billThisMonthScheduled), walletId, "walletName")).right()

                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)

                        val startOfPeriod = getLocalDate().withDayOfMonth(1)
                        val endOfPeriod = getLocalDate().with(TemporalAdjusters.lastDayOfMonth())

                        val startSlot = slot<LocalDate>()
                        val endSlot = slot<LocalDate>()
                        verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                        startSlot.captured shouldBe startOfPeriod
                        endSlot.captured shouldBe endOfPeriod
                        coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                        validateConversation("""Deve informar que o usuário possui ${billThisMonth.amountTotal.formattedAmount()} em aberto e ${billThisMonthScheduled.amountTotal.formattedAmount()} agendado""".trimIndent())
                    }
                }
            }

            describe("quando o usuário perguntar o valor das contas dos próximos 15 dias") {
                listOf(
                    "quanto tenho pendente pra próxima quinzena?",
                    "quanto tenho de pagamentos nos próximos 15 dias?",
                    "qual valor de contas pendentes para os próximos 15?",
                ).forEach { message ->
                    it("deve exibir o valor das contas pendentes para a próxima quinzena - $message") {
                        val billNextFifteenScheduled = billNextFifteen.copy(billId = BillId("nextFifteenScheduled"), scheduledInfo = ScheduledInfo.SCHEDULED, amountTotal = 7000)
                        coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billNextFifteen, billNextFifteenScheduled), walletId, "walletName")).right()

                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)

                        val startOfPeriod = getLocalDate()
                        val endOfPeriod = getLocalDate().plusDays(14)

                        val startSlot = slot<LocalDate>()
                        val endSlot = slot<LocalDate>()
                        verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                        startSlot.captured shouldBe startOfPeriod
                        endSlot.captured shouldBeIn listOf(endOfPeriod, endOfPeriod.plusDays(1))
                        coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                        validateConversation("""Deve informar que o usuário possui ${billNextFifteen.amountTotal.formattedAmount()} em aberto e ${billNextFifteenScheduled.amountTotal.formattedAmount()} agendado""".trimIndent())
                    }
                }
            }

            describe("quando perguntar o valor das contas da próxima segunda") {
                listOf(
                    "quanto tenho pendente pra próxima segunda?",
                    "quanto tenho de pagamentos pra próxima segunda?",
                    "qual valor de contas pendentes para a próxima segunda?",
                ).forEach { message ->
                    it("deve exibir o valor das contas pendentes para a próxima segunda - $message") {
                        val billNextMondayScheduled = billNextMonday.copy(billId = BillId("nextMondayScheduled"), scheduledInfo = ScheduledInfo.SCHEDULED, amountTotal = 4000)
                        coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billNextMonday, billNextMondayScheduled), walletId, "walletName")).right()

                        conversationHistoryService.createUserMessage(user.id, message)
                        baseProcessor.process(user)

                        val startOfPeriod = getLocalDate().with(TemporalAdjusters.next(DayOfWeek.MONDAY))
                        val endOfPeriod = getLocalDate().with(TemporalAdjusters.next(DayOfWeek.MONDAY))

                        val startSlot = slot<LocalDate>()
                        val endSlot = slot<LocalDate>()
                        verify { pendingBillsService.updatePendingBills(any(), capture(startSlot), capture(endSlot)) }

                        startSlot.captured shouldBe startOfPeriod
                        endSlot.captured shouldBe endOfPeriod
                        coVerify { paymentAdapter.findPendingBills(any(), any(), any()) }

                        validateConversation("""Deve informar que o usuário possui ${billNextMonday.amountTotal.formattedAmount()} em aberto e ${billNextMondayScheduled.amountTotal.formattedAmount()} agendado""".trimIndent())
                    }
                }
            }

            describe("quando pedir pra pagar as contas logo depois de perguntar qual o valor ou quais são as contas") {
                describe("deve ter as contas no contexto e confirmar corretamente") {
                    listOf(
                        "qual o valor das contas que vencem hoje?",
                        "quais são as contas que vencem hoje?",
                    ).forEach { message ->
                        it(message) {
                            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(listOf(billToday), walletId, "walletName")).right()

                            conversationHistoryService.createUserMessage(user.id, message)
                            baseProcessor.process(user)
                            conversationHistoryService.createUserMessage(user.id, "pagar todas as contas que vencem hoje")
                            baseProcessor.process(user)

                            val startOfPeriod = getLocalDate()
                            val endOfPeriod = getLocalDate()

                            val startSlots = mutableListOf<LocalDate>()
                            val endSlots = mutableListOf<LocalDate>()
                            verify(exactly = 2) { pendingBillsService.updatePendingBills(any(), capture(startSlots), capture(endSlots)) }

                            startSlots.forEach { it shouldBe startOfPeriod }
                            endSlots.forEach { it shouldBe endOfPeriod }

                            coVerify(exactly = 2) { paymentAdapter.findPendingBills(any(), any(), any()) }

                            validateConversation("O assistente deve informar que os pagamentos selecionados contém ${billToday.assignor} no valor de ${billToday.amountTotal.formattedAmount()}")
                        }
                    }
                }
            }
        }
    }
}