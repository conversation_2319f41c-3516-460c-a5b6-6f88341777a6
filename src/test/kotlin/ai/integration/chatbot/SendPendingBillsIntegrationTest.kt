package ai.integration.chatbot

import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.BILL_ID_4
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.balance
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.Recipient
import ai.chatbot.app.billView
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.InterceptAction
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.subscription
import ai.chatbot.app.user.UserActivity
import ai.chatbot.app.user.UserActivityType
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.dateFormat
import arrow.core.right
import io.micronaut.context.ApplicationContext
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import jakarta.inject.Named

class SendPendingBillsIntegrationTest(
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
    @Named(FRIDAY_ENV) tenantConfiguration: TenantConfiguration,
) : AbstractIntegrationTest(
    openAIAdapter = openAIAdapter,
    applicationContext = applicationContext,
    printConversation = true,
    tenantConfiguration = tenantConfiguration,
) {
    init {
        beforeEach() {
            clearAllMocks()
            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
            coEvery { paymentAdapter.getContacts(any(), any()) } returns emptyList<Contact>().right()
            coEvery { paymentAdapter.getSubscription(user.id) } returns subscription.copy(type = SubscriptionType.IN_APP, fee = null).right()
            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(billViews, walletId = walletId, "walletName")).right()
            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.right()
            coEvery { paymentAdapter.markBillsAsPaid(any(), any(), any()) } returns Unit.right()
            coEvery { paymentAdapter.ignoreBills(any(), any(), any()) } returns Unit.right()
            coEvery { paymentAdapter.getUserActivities(any(), any()) } returns listOf(
                UserActivity(
                    type = UserActivityType.PromotedSweepingAccountOptOut,
                    value = true,
                ),
            ).right()

            val billComingDueState =
                BillComingDueHistoryState(
                    walletWithBills =
                    WalletWithBills(
                        bills = billViews,
                        walletId = walletId,
                        walletName = "walletName",
                    ),
                    balance = null,
                    internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
                    user = user,
                    contacts = emptyList(),
                    subscription = null,
                )
            historyStateRepository.save(user.id, billComingDueState)
        }

        describe("quando o usuário clicar no botão ver contas") {
            describe("e o botão estiver desatualizado") {
                it("deve enviar as contas do dia da notificação e as contas do dia atual") {
                    val yesterdayBillView = billView.copy(
                        billId = BillId(BILL_ID_4),
                        externalBillId = 4,
                        recipient = Recipient("Jack Bauer"),
                        billDescription = "conta de ontem",
                        amount = 2400,
                        amountTotal = 2400,
                        discount = 0,
                        assignor = "Jack Bauer",
                        dueDate = BrazilZonedDateTimeSupplier.getLocalDate().minusDays(1),
                    )
                    val outdatedBillViews = listOf(yesterdayBillView) + billViews

                    coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(outdatedBillViews, walletId = walletId, "walletName")).right()

                    val text = notificationContextTemplatesService.getBillComingDueLastWarn(user.name, "20:00")

                    conversationHistoryService.createAssistantMessage(user.id, text)

                    conversationHistoryService.createUserMessage(user.id, "Ver contas")

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.LIST_BILLS, null, "2021-10-10"))

                    validateConversation("deve informar que a lista de contas foi atualizada.")
                }

                it("deve informar que o usuário não tem mais contas pendentes caso elas já tenham sido pagas") {
                    coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(
                        user,
                        WalletWithBills(
                            emptyList(),
                            walletId = walletId,
                            "walletName",
                        ),
                    ).right()

                    val text = notificationContextTemplatesService.getBillComingDueLastWarn(user.name, "20:00")

                    conversationHistoryService.createAssistantMessage(user.id, text)

                    conversationHistoryService.createUserMessage(user.id, "Ver contas")

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.LIST_BILLS, null, "2021-10-10"))

                    validateConversation("deve informar que o usuário não possui contas em aberto.")
                }
            }

            describe("e a notificação for do dia atual") {
                it("deve enviar as contas do dia atual") {

                    coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(billViews, walletId = walletId, "walletName")).right()

                    val text = notificationContextTemplatesService.getBillComingDueLastWarn(user.name, "20:00")

                    conversationHistoryService.createAssistantMessage(user.id, text)

                    conversationHistoryService.createUserMessage(user.id, "Ver contas")

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.LIST_BILLS, null, dateFormat.format(getLocalDate())))

                    coVerify { buildNotificationService.buildBillsComingDueUserRequestedNotification(user, any()) }

                    validateConversation("Deve mostrar a lista de contas.")
                }
            }
        }
    }
}