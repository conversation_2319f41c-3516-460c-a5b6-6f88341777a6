package ai.integration.chatbot

import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.BILL_ID
import ai.chatbot.app.CreateBillResult
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.balance
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.BillType
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.InterceptAction
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.media.BoletoInfo
import ai.chatbot.app.subscription
import ai.chatbot.app.transaction.AddBoletoTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.dateFormat
import ai.integration.chatbot.utils.OpenAITokenCondition
import arrow.core.left
import arrow.core.right
import io.kotest.core.annotation.EnabledIf
import io.kotest.matchers.shouldBe
import io.micronaut.context.ApplicationContext
import io.mockk.coEvery
import jakarta.inject.Named
import java.time.LocalDate

@EnabledIf(OpenAITokenCondition::class)
class AddBoletoIntegrationTest(
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
    @Named(FRIDAY_ENV) tenantConfiguration: TenantConfiguration,
) : AbstractIntegrationTest(
    openAIAdapter = openAIAdapter,
    applicationContext = applicationContext,
    printConversation = true,
    userGroups = listOf("ALPHA"),
    tenantConfiguration = tenantConfiguration,
) {

    private val billComingDueState =
        BillComingDueHistoryState(
            walletWithBills =
            WalletWithBills(
                bills = listOf(),
                walletId = walletId,
                walletName = "walletName",
            ),
            balance = null,
            internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
            user = user,
            contacts = emptyList(),
            subscription = null, // TODO: o que vai acontecer quando passar a receber a subscription
        )

    init {
        beforeEach {
            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
            coEvery { paymentAdapter.getContacts(any(), any()) } returns emptyList<Contact>().right()
            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(billViews, walletId = walletId, "walletName")).right()
            coEvery { paymentAdapter.getSubscription(any()) } returns subscription.right()
            coEvery {
                paymentAdapter.getBalanceAndForecast(any(), any())
            } returns balance.right()

            historyStateRepository.save(user.id, billComingDueState)
        }

        describe("quando for adicionar mais de um boleto") {
            lateinit var transaction: Transaction
            beforeEach {
                transaction = transactionService.create(user.id, walletId, AddBoletoTransactionDetails(listOf(BoletoInfo("12345678901234567890123456789012345678901234"), BoletoInfo("12345678901234567890123456789012345678901233"))))
            }
            it("deve informar que irá adicionar os boletos em instantes") {
                coEvery { paymentAdapter.addBill(any(), any(), any(), any()) } returns CreateBillResult(
                    billId = BillId(BILL_ID),
                    billType = BillType.CONCESSIONARIA,
                    assignor = "fake assignor",
                    amount = 1000L,
                    dueDate = LocalDate.now().plusDays(1),
                ).right()

                conversationHistoryService.createUserMessage(user.id, "Sim")
                baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.ADD_BOLETO, transaction.id, getLocalDate().format(dateFormat)))

                validateConversation("Deve informar que irá adicionar os boletos em instantes")
            }
        }

        describe("quando adicionar um boleto") {
            describe("e a transaction não for mais válida") {
                it("deve informar que o botão não é mais válido") {
                    conversationHistoryService.createUserMessage(user.id, "Sim")
                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.ADD_BOLETO, TransactionId("fake"), getLocalDate().format(dateFormat)))
                    historyRepository.findLatest(user.id).messages.last().message shouldBe "Este botão não é mais válido"
                }
            }

            describe("e a transaction existir") {
                lateinit var transaction: Transaction
                beforeEach {
                    transaction = transactionService.create(user.id, walletId, AddBoletoTransactionDetails(listOf(BoletoInfo("12345678901234567890123456789012345678901234"))))
                }
                describe("e o boleto for válido") {
                    it("deve adicionar o boleto com sucesso") {
                        coEvery { paymentAdapter.addBill(any(), any(), any(), any()) } returns CreateBillResult(
                            billId = BillId(BILL_ID),
                            billType = BillType.CONCESSIONARIA,
                            assignor = "fake assignor",
                            amount = 1000L,
                            dueDate = LocalDate.now().plusDays(1),
                        ).right()

                        conversationHistoryService.createUserMessage(user.id, "Sim")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.ADD_BOLETO, transaction.id, getLocalDate().format(dateFormat)))

                        validateConversation("Deve adicionar o boleto com sucesso")
                    }
                }

                describe("e o boleto não for pagável") {
                    it("deve informar que o boleto não pode ser pago") {
                        coEvery { paymentAdapter.addBill(any(), any(), any(), any()) } returns PaymentAdapterError.BillNotPayable.left()

                        conversationHistoryService.createUserMessage(user.id, "Sim")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.ADD_BOLETO, transaction.id, getLocalDate().format(dateFormat)))

                        validateConversation("Deve informar que o boleto não pode ser pago")
                    }
                }

                describe("e o boleto já tiver sido pago") {
                    it("deve informar que o boleto já foi pago") {
                        coEvery { paymentAdapter.addBill(any(), any(), any(), any()) } returns PaymentAdapterError.BillAlreadyPaid.left()

                        conversationHistoryService.createUserMessage(user.id, "Sim")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.ADD_BOLETO, transaction.id, getLocalDate().format(dateFormat)))

                        validateConversation("Deve informar que o boleto já foi pago")
                    }
                }

                describe("e o boleto já tiver sido adicionado") {
                    it("deve informar que o boleto já foi adicionado") {
                        coEvery { paymentAdapter.addBill(any(), any(), any(), any()) } returns PaymentAdapterError.BillAlreadyExists.left()

                        conversationHistoryService.createUserMessage(user.id, "Sim")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.ADD_BOLETO, transaction.id, getLocalDate().format(dateFormat)))

                        validateConversation("Deve informar que o boleto já foi adicionado")
                    }
                }

                describe("e o usuário optar não adicionar") {
                    it("deve informar que o boleto não foi adicionado") {
                        conversationHistoryService.createUserMessage(user.id, "Não")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CANCEL, transaction.id, getLocalDate().format(dateFormat)))

                        historyRepository.findLatest(user.id).messages.last { it.type == MessageType.SYSTEM }.message shouldBe "Transação cancelada"
                    }
                }
            }
        }
    }
}