package ai.integration.chatbot

import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.BILL_ID
import ai.chatbot.app.CreateBillResult
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.SweepingParticipant
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.balance
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.BillType
import ai.chatbot.app.billView
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.InterceptAction
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.media.BoletoInfo
import ai.chatbot.app.notification.TextNotificationConfig
import ai.chatbot.app.subscription
import ai.chatbot.app.transaction.AddBoletoTransactionDetails
import ai.chatbot.app.transaction.ScheduleBillsTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.dateFormat
import ai.integration.chatbot.utils.OpenAITokenCondition
import arrow.core.left
import arrow.core.right
import io.kotest.core.annotation.EnabledIf
import io.kotest.matchers.shouldBe
import io.micronaut.context.ApplicationContext
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.slot
import jakarta.inject.Named
import java.time.LocalDate

@EnabledIf(OpenAITokenCondition::class)
class ScheduleBoletoIntegrationTest(
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
    @Named(FRIDAY_ENV) tenantConfiguration: TenantConfiguration,
) : AbstractIntegrationTest(
    openAIAdapter = openAIAdapter,
    applicationContext = applicationContext,
    printConversation = true,
    userGroups = listOf("ALPHA"),
    tenantConfiguration = tenantConfiguration,
) {

    private val billComingDueState =
        BillComingDueHistoryState(
            walletWithBills =
            WalletWithBills(
                bills = listOf(),
                walletId = walletId,
                walletName = "walletName",
            ),
            balance = null,
            internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
            user = user,
            contacts = emptyList(),
            subscription = null,
        )

    init {
        beforeEach {
            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
            coEvery { paymentAdapter.getContacts(any(), any()) } returns emptyList<Contact>().right()
            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(billViews, walletId = walletId, "walletName")).right()
            coEvery { paymentAdapter.getSubscription(any()) } returns subscription.right()
            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.right()

            historyStateRepository.save(user.id, billComingDueState)
        }

        describe("quando agendar um boleto") {
            describe("e a transaction não for mais válida") {
                it("deve informar que o botão não é mais válido") {
                    conversationHistoryService.createUserMessage(user.id, "Sim")
                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.ADD_BOLETO, TransactionId("fake"), getLocalDate().format(dateFormat)))
                    historyRepository.findLatest(user.id).messages.last().message shouldBe "Este botão não é mais válido"
                }
            }

            describe("e a transaction for válida") {
                lateinit var transaction: Transaction
                beforeEach {
                    transaction = transactionService.create(user.id, walletId, ScheduleBillsTransactionDetails(listOf(BillId(BILL_ID))))
                }

                describe("e o pagamento não estiver mais ativo") {
                    it("deve informar que o pagamento não está mais ativo") {
                        coEvery { paymentAdapter.checkBills(any(), any()) } returns PaymentAdapterError.BillNotActiveError.left()

                        conversationHistoryService.createUserMessage(user.id, "Pagar Agora")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.PAY_BOLETO, transaction.id, getLocalDate().format(dateFormat)))

                        validateConversation("Deve informar que o pagamento não está mais ativo")
                    }
                }

                describe("e não encontrar o pagamento") {
                    it("deve informar que não encontrou o pagamento") {
                        coEvery { paymentAdapter.checkBills(any(), any()) } returns PaymentAdapterError.NotFoundError.left()

                        conversationHistoryService.createUserMessage(user.id, "Pagar Agora")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.PAY_BOLETO, transaction.id, getLocalDate().format(dateFormat)))

                        validateConversation("Deve informar que não encontrou o pagamento")
                    }
                }

                describe("e o boleto ainda não tiver sido adicionado") {
                    lateinit var addBoletoTransaction: Transaction
                    beforeEach {
                        addBoletoTransaction = transactionService.create(user.id, walletId, AddBoletoTransactionDetails(listOf(BoletoInfo("12345678901234567890123456789012345678901234"), BoletoInfo("12345678901234567890123456789012345678901233"))))
                    }
                    describe("se for um boleto válido") {
                        it("deve adicionar o boleto e agendar o pagamento se o usuário tiver saldo suficiente") {
                            coEvery { paymentAdapter.addBill(any(), any(), any(), false) } returns CreateBillResult(
                                billId = BillId(BILL_ID),
                                billType = BillType.CONCESSIONARIA,
                                assignor = "fake assignor",
                                amount = 1000L,
                                dueDate = LocalDate.now().plusDays(1),
                            ).right()
                            coEvery { paymentAdapter.checkBills(any(), any()) } returns listOf(billView).right()
                            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 10000000).right()
                            coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns Unit.right()

                            conversationHistoryService.createUserMessage(user.id, "Pagar Agora")
                            baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.PAY_BOLETO, addBoletoTransaction.id, getLocalDate().format(dateFormat)))

                            coVerify { paymentAdapter.scheduleBills(any(), any(), any(), any(), any(), false) }
                            coVerify { paymentAdapter.addBill(any(), any(), any(), false) }

                            validateConversation("Deve informar que está processando o pagamento")
                        }

                        it("deve enviar a mensagem de sweeping se o usuário não tiver saldo suficiente") {
                            coEvery { paymentAdapter.addBill(any(), any(), any(), false) } returns CreateBillResult(
                                billId = BillId(BILL_ID),
                                billType = BillType.CONCESSIONARIA,
                                assignor = "fake assignor",
                                amount = 2000L,
                                dueDate = LocalDate.now().plusDays(1),
                            ).right()
                            coEvery { paymentAdapter.checkBills(any(), any()) } returns listOf(billView).right()
                            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 1000L).right()
                            coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns Unit.right()
                            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns listOf(SweepingConsent(SweepingParticipant(SweepingParticipantId("id"), "nome", "shortName"), 100000, null)).right()

                            conversationHistoryService.createUserMessage(user.id, "Pagar Agora")
                            baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.PAY_BOLETO, addBoletoTransaction.id, getLocalDate().format(dateFormat)))

                            coVerify(exactly = 0) { paymentAdapter.scheduleBills(any(), any(), any(), any(), any(), any()) }
                            val slot = slot<TextNotificationConfig>()
                            coVerify { customNotificationService.send(user = user, notificationConfig = capture(slot), params = any()) }

                            slot.captured shouldBe customNotificationService.config().scheduleConfirmationSingleSweepingConsent
                            coVerify { paymentAdapter.addBill(any(), any(), any(), false) }
                        }
                    }

                    describe("se for um boleto inválido") {
                        it("deve informar que o boleto não pode ser pago") {
                            coEvery { paymentAdapter.addBill(any(), any(), any(), false) } returns PaymentAdapterError.BillNotPayable.left()

                            conversationHistoryService.createUserMessage(user.id, "Pagar Agora")
                            baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.PAY_BOLETO, addBoletoTransaction.id, getLocalDate().format(dateFormat)))

                            validateConversation("Deve informar que o boleto não pode ser pago")
                        }
                    }
                }

                describe("e o pagamento for válido") {
                    it("deve agendar o pagamento quando tiver saldo suficiente") {
                        coEvery { paymentAdapter.checkBills(any(), any()) } returns listOf(billView).right()
                        coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 10000000).right()
                        coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns Unit.right()

                        conversationHistoryService.createUserMessage(user.id, "Pagar Agora")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.PAY_BOLETO, transaction.id, getLocalDate().format(dateFormat)))

                        coVerify { paymentAdapter.scheduleBills(any(), any(), any(), any(), any(), false) }

                        validateConversation("Deve informar que está processando o pagamento")
                    }

                    it("deve agendar para o vencimento quando solicitado") {
                        coEvery { paymentAdapter.checkBills(any(), any()) } returns listOf(billView).right()
                        coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 10000000).right()
                        coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any(), scheduleToDueDate = true) } returns Unit.right()

                        conversationHistoryService.createUserMessage(user.id, "Pagar no vencimento")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.SCHEDULE_BOLETO, transaction.id, getLocalDate().format(dateFormat)))

                        coVerify { paymentAdapter.scheduleBills(any(), any(), any(), any(), any(), scheduleToDueDate = true) }

                        validateConversation("Deve informar agendou o boleto")
                    }
                }
            }
        }
    }
}