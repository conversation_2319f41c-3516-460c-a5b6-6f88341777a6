package ai.integration.chatbot

import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.BILL_ID_4
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.balance
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.Recipient
import ai.chatbot.app.billView
import ai.chatbot.app.billView2
import ai.chatbot.app.billView3
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.InterceptAction
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.notification.NotificationFormatter
import ai.chatbot.app.subscription
import ai.chatbot.app.transaction.TransactionStatus
import ai.chatbot.app.user.UserActivity
import ai.chatbot.app.user.UserActivityType
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.dateFormat
import ai.integration.chatbot.utils.OpenAITokenCondition
import arrow.core.right
import io.kotest.core.annotation.EnabledIf
import io.kotest.matchers.collections.shouldContainAll
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeSameInstanceAs
import io.micronaut.context.ApplicationContext
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.slot
import jakarta.inject.Named

@EnabledIf(OpenAITokenCondition::class)
class MarkAsPaidIntegrationTest(
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
    @Named(FRIDAY_ENV) tenantConfiguration: TenantConfiguration,
) : AbstractIntegrationTest(
    openAIAdapter = openAIAdapter,
    applicationContext = applicationContext,
    printConversation = true,
    tenantConfiguration = tenantConfiguration,
) {
    init {
        beforeEach() {
            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
            coEvery { paymentAdapter.getContacts(any(), any()) } returns emptyList<Contact>().right()
            coEvery { paymentAdapter.getSubscription(user.id) } returns subscription.copy(type = SubscriptionType.IN_APP, fee = null).right()
            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(billViews, walletId = walletId, "walletName")).right()
            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.right()
            coEvery { paymentAdapter.markBillsAsPaid(any(), any(), any()) } returns Unit.right()
            coEvery { paymentAdapter.ignoreBills(any(), any(), any()) } returns Unit.right()
            coEvery { paymentAdapter.getUserActivities(any(), any()) } returns listOf(
                UserActivity(
                    type = UserActivityType.PromotedSweepingAccountOptOut,
                    value = true,
                ),
            ).right()
        }

        describe("quando o usuário clicar em já paguei de dias anteriores") {
            it("deve informar que a lista de contas foi atualizada") {
                val yesterdayBillView = billView.copy(
                    billId = BillId(BILL_ID_4),
                    externalBillId = 4,
                    recipient = Recipient("Jack Bauer"),
                    billDescription = "conta de ontem",
                    amount = 2400,
                    amountTotal = 2400,
                    discount = 0,
                    assignor = "Jack Bauer",
                    dueDate = getLocalDate().minusDays(1),
                )
                val outdatedBillViews = listOf(yesterdayBillView) + billViews

                coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(outdatedBillViews, walletId = walletId, "walletName")).right()

                val text = notificationContextTemplatesService.getBillComingDueLastWarn(user.name, "20:00")

                conversationHistoryService.createAssistantMessage(user.id, text)

                conversationHistoryService.createUserMessage(user.id, "Já paguei")

                baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID, null, "2021-10-10"))

                validateConversation("deve informar que a lista de contas foi atualizada.")
            }

            describe("e o usuário não tiver contas") {
                it("deve informar que não tem contas") {

                    coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(emptyList(), walletId = walletId, "walletName")).right()

                    val text = notificationContextTemplatesService.getBillComingDueLastWarn(user.name, "20:00")

                    conversationHistoryService.createAssistantMessage(user.id, text)

                    conversationHistoryService.createUserMessage(user.id, "Já paguei")

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID, null, "2021-10-10"))

                    validateConversation("deve informar que não tem contas.")
                }
            }

            describe("e o usuário já possuir um estado no dia atual que não tenha sido atualizado") {
                beforeEach {
                    val billComingDueState =
                        BillComingDueHistoryState(
                            walletWithBills =
                            WalletWithBills(
                                bills = billViews,
                                walletId = walletId,
                                walletName = "walletName",
                            ),
                            balance = null,
                            internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
                            user = user,
                            contacts = emptyList(),
                            subscription = null,
                        )
                    historyStateRepository.save(user.id, billComingDueState)
                }
                it("deve informar que a lista de contas foi atualizada") {
                    coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(billViews, walletId = walletId, "walletName")).right()
                    val text = notificationContextTemplatesService.getBillComingDueLastWarn(user.name, "20:00")

                    conversationHistoryService.createAssistantMessage(user.id, text)

                    conversationHistoryService.createUserMessage(user.id, "Já paguei")

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID, null, "2021-10-10"))

                    validateConversation("deve informar que a lista de contas foi atualizada.")
                }
            }
        }

        describe("Deve marcar como pago") {
            beforeEach {
                val billComingDueState =
                    BillComingDueHistoryState(
                        walletWithBills =
                        WalletWithBills(
                            bills = billViews,
                            walletId = walletId,
                            walletName = "walletName",
                        ),
                        balance = null,
                        internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
                        user = user,
                        contacts = emptyList(),
                        subscription = null,
                    )
                historyStateRepository.save(user.id, billComingDueState)
            }
            it("Quando o usuário informar que já pagou todas as contas") {
                val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                conversationHistoryService.createAssistantMessage(user.id, text)

                conversationHistoryService.createUserMessage(user.id, "Já paguei")
                baseProcessor.process(user)

                val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                conversationHistoryService.createUserMessage(user.id, "Sim")
                baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID, transactionId, "2021-10-10T10:00:00Z"))

                val billIds = slot<List<BillId>>()

                coVerify {
                    paymentAdapter.markBillsAsPaid(any(), capture(billIds), any())
                }

                billIds.captured.size shouldBe 3
                billIds.captured.shouldContainAll(billView.billId, billView2.billId, billView3.billId)

                historyRepository.findLatest(user.id).messages.last().message shouldBe "Ok, marquei as contas como pagas."
            }

            it("Quando o usuário clicar no botão 'já paguei' e tiver mais de uma conta") {
                val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                conversationHistoryService.createAssistantMessage(user.id, text)

                conversationHistoryService.createUserMessage(user.id, "Já paguei")
                baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID, null, dateFormat.format(getLocalDate())))

                val billIds = slot<List<BillId>>()

                coVerify {
                    paymentAdapter.markBillsAsPaid(any(), capture(billIds), any())
                }

                billIds.captured.size shouldBe 3
                billIds.captured.shouldContainAll(billView.billId, billView2.billId, billView3.billId)

                historyRepository.findLatest(user.id).messages.last().message shouldBe "Ok, marquei as contas como pagas."
            }

            it("Quando o usuário informar que quer marcar apenas uma conta pelo índice") {
                val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                conversationHistoryService.createAssistantMessage(user.id, text)

                conversationHistoryService.createUserMessage(user.id, "Já paguei a conta 1")
                baseProcessor.process(user)

                val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                conversationHistoryService.createUserMessage(user.id, "Sim")
                baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID, transactionId, "2021-10-10T10:00:00Z"))

                val billIds = slot<List<BillId>>()

                coVerify {
                    paymentAdapter.markBillsAsPaid(any(), capture(billIds), any())
                }

                billIds.captured.size shouldBe 1
                billIds.captured.shouldContainAll(billView.billId)

                historyRepository.findLatest(user.id).messages.last().message shouldBe "Ok, marquei a conta como paga."
            }

            it("Quando o usuário informar que quer marcar como paga apenas uma pelo nome da conta") {
                val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                conversationHistoryService.createAssistantMessage(user.id, text)

                conversationHistoryService.createUserMessage(user.id, "Já fiz o pix para o joão")
                baseProcessor.process(user)

                val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                conversationHistoryService.createUserMessage(user.id, "Sim")
                baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID, transactionId, "2021-10-10T10:00:00Z"))

                val billIds = slot<List<BillId>>()

                coVerify {
                    paymentAdapter.markBillsAsPaid(any(), capture(billIds), any())
                }

                billIds.captured.size shouldBe 1
                billIds.captured.shouldContainAll(billView3.billId)

                historyRepository.findLatest(user.id).messages.last().message shouldBe "Ok, marquei a conta como paga."
            }

            it("Quando o usuário informar que quer marcar apenas duas contas") {
                val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                conversationHistoryService.createAssistantMessage(user.id, text)

                conversationHistoryService.createUserMessage(user.id, "Já paguei a conta 1 e 3")
                baseProcessor.process(user)

                val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                conversationHistoryService.createUserMessage(user.id, "Sim")
                baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID, transactionId, "2021-10-10T10:00:00Z"))

                val billIds = slot<List<BillId>>()

                coVerify {
                    paymentAdapter.markBillsAsPaid(any(), capture(billIds), any())
                }

                billIds.captured.size shouldBe 2
                billIds.captured.shouldContainAll(billView.billId, billView3.billId)

                historyRepository.findLatest(user.id).messages.last().message shouldBe "Ok, marquei as contas como pagas."
            }

            it("Quando o usuário tentar marcar uma conta que não foi encontrada") {
                val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                conversationHistoryService.createAssistantMessage(user.id, text)

                conversationHistoryService.createUserMessage(user.id, "Já paguei a conta 4")
                baseProcessor.process(user)

                coVerify(exactly = 0) {
                    paymentAdapter.markBillsAsPaid(any(), any(), any())
                }

                validateConversation("Deve informar ao usuário que não encontrou a conta solicitada.")
            }
        }

        describe("Deve ignorar uma conta") {
            beforeEach {
                val billComingDueState =
                    BillComingDueHistoryState(
                        walletWithBills =
                        WalletWithBills(
                            bills = billViews,
                            walletId = walletId,
                            walletName = "walletName",
                        ),
                        balance = null,
                        internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
                        user = user,
                        contacts = emptyList(),
                        subscription = null,
                    )
                historyStateRepository.save(user.id, billComingDueState)
            }
            describe("Quando o usuário pedir para ignorar as contas") {
                listOf(
                    "Ignorar todas",
                    "Remover todas",
                    "Deletar todas",
                ).forEach { content ->
                    it(content) {
                        val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                        val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                        conversationHistoryService.createAssistantMessage(user.id, text)

                        conversationHistoryService.createUserMessage(user.id, content)
                        baseProcessor.process(user)

                        val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                        conversationHistoryService.createUserMessage(user.id, "Sim")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID, transactionId, "2021-10-10T10:00:00Z"))

                        val billIds = slot<List<BillId>>()

                        coVerify {
                            paymentAdapter.ignoreBills(any(), capture(billIds), any())
                        }

                        billIds.captured.size shouldBe 3
                        billIds.captured.shouldContainAll(billView.billId, billView2.billId, billView3.billId)

                        historyRepository.findLatest(user.id).messages.last().message shouldBe "Ok, removi as contas da timeline para você."
                    }
                }
            }

            describe("Quando o usuário informar que quer ignorar apenas uma conta pelo índice") {
                listOf(
                    "Ignorar a conta 1",
                    "Remover a conta 1",
                ).forEach { content ->
                    it(content) {
                        val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                        val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                        conversationHistoryService.createAssistantMessage(user.id, text)

                        conversationHistoryService.createUserMessage(user.id, content)
                        baseProcessor.process(user)

                        val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                        conversationHistoryService.createUserMessage(user.id, "Sim")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID, transactionId, "2021-10-10T10:00:00Z"))

                        val billIds = slot<List<BillId>>()

                        coVerify {
                            paymentAdapter.ignoreBills(any(), capture(billIds), any())
                        }

                        billIds.captured.size shouldBe 1
                        billIds.captured.shouldContainAll(billView.billId)

                        historyRepository.findLatest(user.id).messages.last().message shouldBe "Ok, removi a conta da timeline para você."
                    }
                }
            }

            describe("Quando o usuário informar que quer ignorar apenas uma pelo nome da conta") {
                listOf(
                    Pair("Ignorar a conta de luz", billView.billId),
                    Pair("Remover o pix pro joão", billView3.billId),
                ).forEach { content ->
                    it(content.first) {
                        val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                        val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                        conversationHistoryService.createAssistantMessage(user.id, text)

                        conversationHistoryService.createUserMessage(user.id, content.first)
                        baseProcessor.process(user)

                        val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                        conversationHistoryService.createUserMessage(user.id, "Sim")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID, transactionId, "2021-10-10T10:00:00Z"))

                        val billIds = slot<List<BillId>>()

                        coVerify {
                            paymentAdapter.ignoreBills(any(), capture(billIds), any())
                        }

                        billIds.captured.size shouldBe 1
                        billIds.captured.shouldContainAll(content.second)

                        historyRepository.findLatest(user.id).messages.last().message shouldBe "Ok, removi a conta da timeline para você."
                    }
                }
            }

            describe("Quando o usuário informar que quer ignorar mais de uma conta") {
                listOf(
                    Pair("Ignorar a 1 e a 2", listOf(billView.billId, billView2.billId)),
                    Pair("Remover o pix pro joão e pix pro enzo", listOf(billView2.billId, billView3.billId)),
                ).forEach { content ->
                    it(content.first) {
                        val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                        val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                        conversationHistoryService.createAssistantMessage(user.id, text)

                        conversationHistoryService.createUserMessage(user.id, content.first)
                        baseProcessor.process(user)

                        val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                        conversationHistoryService.createUserMessage(user.id, "Sim")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID, transactionId, "2021-10-10T10:00:00Z"))

                        val billIds = slot<List<BillId>>()

                        coVerify {
                            paymentAdapter.ignoreBills(any(), capture(billIds), any())
                        }

                        billIds.captured.size shouldBe 2
                        billIds.captured.shouldContainAll(content.second)

                        historyRepository.findLatest(user.id).messages.last().message shouldBe "Ok, removi as contas da timeline para você."
                    }
                }
            }

            describe("Quando o usuário tentar ignorar uma conta que não foi encontrada") {
                listOf(
                    "Ignorar a conta 4",
                    "Remover a conta 4",
                ).forEach { content ->
                    it(content) {
                        val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                        val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                        conversationHistoryService.createAssistantMessage(user.id, text)

                        conversationHistoryService.createUserMessage(user.id, content)
                        baseProcessor.process(user)

                        coVerify(exactly = 0) {
                            paymentAdapter.ignoreBills(any(), any(), any())
                        }

                        validateConversation("Deve informar ao usuário que não encontrou a conta solicitada.")
                    }
                }
            }
        }

        describe("Não deve marcar como pago") {
            describe("Quando o usuário clicar no botão para não confirmar a marcação das contas") {
                listOf(
                    "Já paguei as contas 1 e 3",
                    "Ignorar a conta 1",
                ).forEach { content ->
                    it(content) {
                        val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                        val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                        conversationHistoryService.createAssistantMessage(user.id, text)

                        conversationHistoryService.createUserMessage(user.id, content)
                        baseProcessor.process(user)

                        val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                        conversationHistoryService.createUserMessage(user.id, "Não")
                        baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CANCEL, transactionId, "2021-10-10T10:00:00Z"))

                        coVerify(exactly = 0) {
                            paymentAdapter.markBillsAsPaid(any(), any(), any())
                            paymentAdapter.ignoreBills(any(), any(), any())
                        }

                        historyRepository.findLatest(user.id).messages.last().message shouldBe "Entendi. Se precisar de mais alguma coisa, estou à disposição."
                    }
                }
            }

            describe("Quando o usuário responder pra não confirmar a marcação das contas") {
                listOf(
                    "Já paguei as contas 1 e 3",
                    "Ignorar a conta 1",
                ).forEach { content ->
                    it(content) {
                        val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                        val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                        conversationHistoryService.createAssistantMessage(user.id, text)

                        conversationHistoryService.createUserMessage(user.id, content)
                        baseProcessor.process(user)

                        conversationHistoryService.createUserMessage(user.id, "Não quero")
                        baseProcessor.process(user)

                        coVerify(exactly = 0) {
                            paymentAdapter.markBillsAsPaid(any(), any(), any())
                            paymentAdapter.ignoreBills(any(), any(), any())
                        }

                        val assistantMessage = historyRepository.findLatest(user.id).messages.last { it.type == MessageType.ASSISTANT }

                        if (assistantMessage.message != null) {
                            validateConversation("Deve finalizar a conversa sem marcar como pago ou ignorar contas.")
                        } else {
                            assistantMessage.completionMessage?.acoes?.first()?.name shouldBeSameInstanceAs ActionType.NOOP
                        }
                    }
                }
            }
        }
    }
}