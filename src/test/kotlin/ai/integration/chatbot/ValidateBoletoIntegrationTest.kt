package ai.integration.chatbot

import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.BILL_ID
import ai.chatbot.app.CreateBillResult
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.balance
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.BillType
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.InterceptAction
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.media.BoletoInfo
import ai.chatbot.app.subscription
import ai.chatbot.app.transaction.AddBoletoTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.brazilDateFormat
import ai.chatbot.app.utils.dateFormat
import ai.integration.chatbot.utils.OpenAITokenCondition
import arrow.core.left
import arrow.core.right
import io.kotest.core.annotation.EnabledIf
import io.micronaut.context.ApplicationContext
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.slot
import jakarta.inject.Named
import java.time.LocalDate

@EnabledIf(OpenAITokenCondition::class)
class ValidateBoletoIntegrationTest(
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
    @Named(FRIDAY_ENV) tenantConfiguration: TenantConfiguration,
) : AbstractIntegrationTest(
    openAIAdapter = openAIAdapter,
    applicationContext = applicationContext,
    printConversation = true,
    userGroups = listOf("ALPHA"),
    tenantConfiguration = tenantConfiguration,
) {

    private val billComingDueState =
        BillComingDueHistoryState(
            walletWithBills =
            WalletWithBills(
                bills = listOf(),
                walletId = walletId,
                walletName = "walletName",
            ),
            balance = null,
            internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
            user = user,
            contacts = emptyList(),
            subscription = null, // TODO: o que vai acontecer quando passar a receber a subscription
        )

    init {
        beforeEach {
            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
            coEvery { paymentAdapter.getContacts(any(), any()) } returns emptyList<Contact>().right()
            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(billViews, walletId = walletId, "walletName")).right()
            coEvery { paymentAdapter.getSubscription(any()) } returns subscription.right()
            coEvery {
                paymentAdapter.getBalanceAndForecast(any(), any())
            } returns balance.right()

            historyStateRepository.save(user.id, billComingDueState)
        }

        describe("quando interceptar uma lista de boletos por imagem") {
            lateinit var transaction: Transaction
            beforeEach {
                transaction = transactionService.create(
                    user.id,
                    walletId,
                    AddBoletoTransactionDetails(
                        barCodes = listOf(
                            BoletoInfo("34191790010104351004791020150008787820026318", "10/10/2025"),
                            BoletoInfo("34191004791020150008787820026318901790010104351"),
                        ),
                    ),
                )
            }

            it("deve validar os boletos") {
                val date = slot<String>()
                coEvery { paymentAdapter.addBill(user.id, any(), capture(date), dryRun = true) } answers {
                    CreateBillResult(
                        assignor = "Test Company",
                        amount = 1000L,
                        dueDate = LocalDate.now(),
                        billId = BillId(BILL_ID),
                        billType = BillType.FICHA_COMPENSACAO,
                    ).right()
                }

                coEvery { paymentAdapter.addBill(user.id, any(), null, dryRun = true) } returns
                    CreateBillResult(
                        assignor = "Test Company",
                        amount = 1000L,
                        dueDate = LocalDate.now(),
                        billId = BillId(BILL_ID),
                        billType = BillType.FICHA_COMPENSACAO,
                    ).right()

                conversationHistoryService.createUserMessage(user.id, " ")
                baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.VALIDATE_BOLETO, transaction.id, getLocalDate().format(dateFormat)))

                validateConversation(
                    "O assistente deve validar alguns dos boletos e mostrar os detalhes (empresa, valor e data de vencimento) de alguns deles e perguntar se o usuário deseja adicionar todos.",
                )
            }
        }

        describe("quando validar vários boletos") {
            describe("com código de barras de concessionária") {
                it("deve validar com sucesso") {

                    val boleto1 = Pair("846900000023362601622024506150380000003826566469", "10/10/2025")
                    val boleto2 = Pair("846900000023362601622024506150380000003826566470", null)
                    val boleto3 = Pair("84690000002-3 36260162202-4 50615038000-0 00382656647-1", "20/10/2025")

                    val date = slot<String>()
                    coEvery { paymentAdapter.addBill(user.id, any(), capture(date), dryRun = true) } coAnswers {
                        val vencimento = date.captured
                        CreateBillResult(
                            assignor = "Test Company",
                            amount = 1000L,
                            dueDate = LocalDate.parse(vencimento),
                            billId = BillId(BILL_ID),
                            billType = BillType.CONCESSIONARIA,
                        ).right()
                    }

                    coEvery { paymentAdapter.addBill(user.id, any(), null, dryRun = true) } returns
                        CreateBillResult(
                            assignor = "Test Company",
                            amount = 1000L,
                            dueDate = LocalDate.now(),
                            billId = BillId(BILL_ID),
                            billType = BillType.CONCESSIONARIA,
                        ).right()

                    val message = """
                        ${boleto1.first} ${boleto1.second.let { vencimento -> " vencimento em $vencimento" }}
                        ${boleto2.first}
                        ${boleto3.first} ${boleto3.second.let { vencimento -> " vencimento em $vencimento" }}
                    """.trimIndent()

                    conversationHistoryService.createUserMessage(user.id, message)
                    baseProcessor.process(user)

                    // coVerify(exactly = 1) { paymentAdapter.addBill(user.id, barcode, vencimentoDate?.toString(), dryRun = true) }
                    validateConversation(
                        "O assistente deve validar os boletos e mostrar os detalhes (empresa, valor e data de vencimento) para o usuário",
                    )
                }
            }

            it("deve validar múltiplos boletos com sucesso") {
                val barcodes = listOf(
                    "34191790010104351004791020150008787820026318",
                    "34191004791020150008787820026318901790010104351",
                )

                coEvery { paymentAdapter.addBill(user.id, any(), dryRun = true) } returns
                    CreateBillResult(
                        assignor = "Test Company",
                        amount = 1000L,
                        dueDate = LocalDate.now(),
                        billId = BillId(BILL_ID),
                        billType = BillType.FICHA_COMPENSACAO,
                    ).right()

                conversationHistoryService.createUserMessage(user.id, barcodes.joinToString("\n"))
                baseProcessor.process(user)

                coVerify(exactly = 2) { paymentAdapter.addBill(user.id, any(), dryRun = true) }

                validateConversation(
                    "O assistente deve validar alguns dos boletos e mostrar os detalhes (empresa, valor e data de vencimento) de alguns deles e perguntar se o usuário deseja adicionar todos.",
                )
            }

            it("deve validar múltiplos boletos com alguns inválidos") {
                val validBarcode = "34191790010104351004791020150008787820026318"
                val invalidBarcode = "341917900101043510047910201500087878200263189"

                coEvery { paymentAdapter.addBill(user.id, any(), dryRun = true) } returns
                    CreateBillResult(
                        assignor = "Test Company",
                        amount = 1000L,
                        dueDate = LocalDate.now(),
                        billId = BillId(BILL_ID),
                        billType = BillType.FICHA_COMPENSACAO,
                    ).right()

                conversationHistoryService.createUserMessage(
                    user.id,
                    """código de barras:$validBarcode
                    | e código de barras $invalidBarcode
                    """.trimMargin(),
                )
                baseProcessor.process(user)

                coVerify(exactly = 1) { paymentAdapter.addBill(user.id, any(), dryRun = true) }
                validateConversation(
                    "O assistente deve validar o boleto válido e informar erro para o boleto inválido",
                )
            }

            it("deve validar múltiplos boletos com alguns já pagos") {
                val validBarcode = "34191790010104351004791020150008787820026318"
                val paidBarcode = "34191004791020150008787820026318901790010104351"

                coEvery { paymentAdapter.addBill(user.id, any(), dryRun = true) } returns
                    CreateBillResult(
                        assignor = "Test Company",
                        amount = 1000L,
                        dueDate = LocalDate.now(),
                        billId = BillId(BILL_ID),
                        billType = BillType.FICHA_COMPENSACAO,
                    ).right()

                coEvery { paymentAdapter.addBill(user.id, paidBarcode, dryRun = true) } returns
                    PaymentAdapterError.BillAlreadyPaid.left()

                conversationHistoryService.createUserMessage(
                    user.id,
                    """código de barras: $validBarcode
                    |e código de barras: $paidBarcode
                    """.trimMargin(),
                )
                baseProcessor.process(user)

                coVerify(exactly = 2) { paymentAdapter.addBill(user.id, any(), dryRun = true) }
                validateConversation(
                    "O assistente deve validar o boleto válido e informar que o outro boleto já foi pago",
                )
            }

            it("deve validar múltiplos boletos com alguns vencidos") {
                val validBarcode = "34191790010104351004791020150008787820026318345"
                val expiredBarcode = "34191004791020150008787820026318901790010104351"

                coEvery { paymentAdapter.addBill(user.id, validBarcode, dryRun = true) } returns
                    CreateBillResult(
                        assignor = "Test Company",
                        amount = 1000L,
                        dueDate = LocalDate.now(),
                        billId = BillId(BILL_ID),
                        billType = BillType.FICHA_COMPENSACAO,
                    ).right()

                coEvery { paymentAdapter.addBill(user.id, expiredBarcode, dryRun = true) } returns
                    PaymentAdapterError.BillPaymentLimitExpired.left()

                conversationHistoryService.createUserMessage(
                    user.id,
                    """código de barras: $validBarcode
                    |e código de barras:$expiredBarcode
                    """.trimMargin(),
                )
                baseProcessor.process(user)

                coVerify(exactly = 2) { paymentAdapter.addBill(user.id, any(), dryRun = true) }

                validateConversation(
                    "O assistente deve validar o boleto válido e informar que o outro boleto está vencido",
                )
            }
        }

        describe("quando validar um boleto") {
            describe("com código de barras de concessionária") {
                listOf(
                    Pair("846900000023362601622024506150380000003826566469", "20/10/2025"),
                    Pair("84690000002-3 36260162202-4 50615038000-0 00382656647-0", null),
                ).forEach { (barcode, vencimento) ->
                    it(vencimento?.let { "com vencimento na mensagem" } ?: "sem vencimento na mensagem") {
                        val vencimentoDate = vencimento?.let { LocalDate.parse(vencimento, brazilDateFormat) }
                        val codigo = barcode.replace("-", "").replace(" ", "")

                        coEvery { paymentAdapter.addBill(user.id, codigo, vencimentoDate?.toString(), dryRun = true) } returns
                            CreateBillResult(
                                assignor = "Test Company",
                                amount = 1000L,
                                dueDate = vencimentoDate ?: LocalDate.now(),
                                billId = BillId(BILL_ID),
                                billType = BillType.CONCESSIONARIA,
                            ).right()

                        conversationHistoryService.createUserMessage(user.id, barcode + (vencimento?.let { " vencimento em $it" } ?: ""))
                        baseProcessor.process(user)

                        coVerify(exactly = 1) { paymentAdapter.addBill(user.id, codigo.replace("-", "").replace(" ", ""), vencimentoDate?.toString(), dryRun = true) }
                        validateConversation(
                            "O assistente deve validar o boleto e mostrar os detalhes (empresa, valor e data de vencimento) para o usuário",
                        )
                    }
                }
            }

            describe("com código de barras válido (44 dígitos)") {
                it("deve validar com sucesso") {
                    val barcode = "34191790010104351004791020150008787820026318"

                    coEvery { paymentAdapter.addBill(user.id, barcode, dryRun = true) } returns
                        CreateBillResult(
                            assignor = "Test Company",
                            amount = 1000L,
                            dueDate = LocalDate.now(),
                            billId = BillId(BILL_ID),
                            billType = BillType.FICHA_COMPENSACAO,
                        ).right()

                    conversationHistoryService.createUserMessage(user.id, barcode)
                    baseProcessor.process(user)

                    coVerify(exactly = 1) { paymentAdapter.addBill(user.id, barcode, dryRun = true) }
                    validateConversation(
                        "O assistente deve validar o boleto e mostrar os detalhes (empresa, valor e data de vencimento) para o usuário",
                    )
                }
            }

            describe("com linha digitável válida (47 dígitos)") {
                it("deve validar com sucesso") {
                    val digitable = "34191004791020150008787820026318901790010104351"

                    coEvery { paymentAdapter.addBill(user.id, digitable, dryRun = true) } returns
                        CreateBillResult(
                            assignor = "Test Company",
                            amount = 1000L,
                            dueDate = LocalDate.now(),
                            billId = BillId(BILL_ID),
                            billType = BillType.FICHA_COMPENSACAO,
                        )
                            .right()

                    conversationHistoryService.createUserMessage(user.id, digitable)
                    baseProcessor.process(user)

                    coVerify(exactly = 1) { paymentAdapter.addBill(user.id, digitable, dryRun = true) }
                    validateConversation(
                        "O assistente deve validar o boleto e mostrar os detalhes (empresa, valor e data de vencimento) para o usuário",
                    )
                }
            }

            describe("com comprimento inválido") {
                it("deve retornar mensagem de erro") {
                    val invalidCode = "12345"

                    coEvery { paymentAdapter.addBill(user.id, any(), dryRun = true) } returns
                        PaymentAdapterError.UnableToValidateBill.left()

                    conversationHistoryService.createUserMessage(user.id, "Adicione este boleto pra mim $invalidCode")
                    baseProcessor.process(user)

                    coVerify(exactly = 0) { paymentAdapter.addBill(any(), any(), any()) }
                    validateConversation(
                        "O assistente deve informar que o código do boleto é inválido ou que a sequencia de números não faz sentido",
                    )
                }
            }

            describe("com boleto já pago") {
                it("deve retornar mensagem de erro") {
                    val barcode = "34191790010104351004791020150008787820026318"

                    coEvery { paymentAdapter.addBill(user.id, barcode, dryRun = true) } returns
                        PaymentAdapterError.BillAlreadyPaid.left()

                    conversationHistoryService.createUserMessage(user.id, barcode)
                    baseProcessor.process(user)

                    coVerify(exactly = 1) { paymentAdapter.addBill(user.id, barcode, dryRun = true) }
                    validateConversation("O assistente deve informar que o boleto já foi pago")
                }
            }

            describe("com boleto não pode ser pago") {
                it("deve retornar mensagem de erro") {
                    val barcode = "34191790010104351004791020150008787820026318"

                    coEvery { paymentAdapter.addBill(user.id, barcode, dryRun = true) } returns
                        PaymentAdapterError.BillNotPayable.left()

                    conversationHistoryService.createUserMessage(user.id, barcode)
                    baseProcessor.process(user)

                    coVerify(exactly = 1) { paymentAdapter.addBill(user.id, barcode, dryRun = true) }
                    validateConversation(
                        "O assistente deve informar que o boleto não pode ser pago",
                    )
                }
            }

            describe("com boleto vencido") {
                it("deve retornar mensagem de erro") {
                    val barcode = "34191790010104351004791020150008787820026318"

                    coEvery { paymentAdapter.addBill(user.id, barcode, dryRun = true) } returns
                        PaymentAdapterError.BillPaymentLimitExpired.left()

                    conversationHistoryService.createUserMessage(user.id, barcode)
                    baseProcessor.process(user)

                    coVerify(exactly = 1) { paymentAdapter.addBill(user.id, barcode, dryRun = true) }
                    validateConversation(
                        "O assistente deve informar que o boleto já está vencido",
                    )
                }
            }
        }
    }
}