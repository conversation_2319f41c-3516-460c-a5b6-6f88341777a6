package ai.integration.chatbot

import ai.chatbot.adapters.api.ConfirmTO
import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.SweepingParticipant
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.SweepingRequest
import ai.chatbot.app.balance
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.InterceptAction
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.notification.NotificationFormatter
import ai.chatbot.app.pix.PixQRCode
import ai.chatbot.app.subscription
import ai.chatbot.app.transaction.TransactionStatus
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.WalletWithBills
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.types.shouldBeSameInstanceAs
import io.micronaut.context.ApplicationContext
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.slot
import jakarta.inject.Named

class MakePaymentIntegrationTest(
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
    @Named(FRIDAY_ENV) tenantConfiguration: TenantConfiguration,
) : AbstractIntegrationTest(
    openAIAdapter = openAIAdapter,
    applicationContext = applicationContext,
    printConversation = true,
    tenantConfiguration = tenantConfiguration,
) {
    private val qrCode = "00020126580014br.gov.bcb.pix01367f7f55d9-356c-4439-9333-47e7d8842ae7520400005303986540510.005802BR5915ALEXANDRE SILVA6014RIO DE JANEIRO61082022029762100506CecApp6304DF14"

    init {

        beforeEach() {

            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
            coEvery { paymentAdapter.getContacts(any(), any()) } returns emptyList<Contact>().right()
            coEvery { paymentAdapter.getSubscription(user.id) } returns subscription.copy(
                type = SubscriptionType.IN_APP,
                fee = null,
            ).right()
            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.right()
            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(billViews, walletId = walletId, "walletName")).right()

            val billComingDueState =
                BillComingDueHistoryState(
                    walletWithBills =
                    WalletWithBills(
                        bills = billViews,
                        walletId = walletId,
                        walletName = "walletName",
                    ),
                    balance = null,
                    internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
                    user = user,
                    contacts = emptyList(),
                    subscription = null,
                )
            historyStateRepository.save(user.id, billComingDueState)
        }

        describe("Quando o usuário pedir para pagar as contas") {
            describe("Deve realizar o pagamento") {
                // bill comming due
                // pagar todas as contas clicando no botão
                // faz o pagamento sem autorização
                it("Se tiver SALDO e o valor estiver ABAIXO do limite do assistente") {
                    coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 100_000_00).right()
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns Unit.right()

                    val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                    val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                    conversationHistoryService.createAssistantMessage(user.id, text)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Sim, pagar todas",
                    )

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.SCHEDULE_BILLS, null, "2021-10-10T10:00:00Z"))

                    val selectedBillIds = slot<List<BillId>>()
                    val sweepingRequest = mutableListOf<SweepingRequest?>()
                    val authorizationToken = mutableListOf<String?>()

                    coVerify {
                        paymentAdapter.findPendingBills(any(), any(), any())
                        paymentAdapter.scheduleBills(any(), captureNullable(sweepingRequest), any(), capture(selectedBillIds), captureNullable(authorizationToken))
                    }

                    selectedBillIds.captured.map { it.value } shouldContainExactlyInAnyOrder billViews.map { it.billId.value }
                    sweepingRequest shouldContainExactly listOf(null)
                    authorizationToken shouldContainExactly listOf(null)

                    historyRepository.findLatest(user.id).messages.last().message shouldBe "Ok, estou processando e já volto com o resultado."
                }

                // pede as contas pendentes
                // pagar todas as contas falando
                // faz o pagamento com autorização
                it("Se tiver SALDO e o valor estiver ACIMA do limite do assistente") {
                    coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 100_000_00).right()
                    coEvery {
                        paymentAdapter.scheduleBills(any(), any(), any(), any(), any())
                    } returns PaymentAdapterError.AssistantLimitExceeded.left() andThen Unit.right()

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Quais as contas pendentes?",
                    )
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Sim, pagar todas",
                    )
                    baseProcessor.process(user, interceptAction = InterceptAction(type = InterceptMessagePayloadType.SCHEDULE_BILLS))

                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                    transactionController.confirmTransaction(transactionId.value, user.id.value, ConfirmTO("token", false, 1))

                    val selectedBillIds = mutableListOf<List<BillId>>()
                    val sweepingRequest = mutableListOf<SweepingRequest?>()
                    val authorizationToken = mutableListOf<String?>()

                    coVerify {
                        paymentAdapter.findPendingBills(any(), any(), any())
                        paymentAdapter.scheduleBills(any(), captureNullable(sweepingRequest), any(), capture(selectedBillIds), captureNullable(authorizationToken))
                    }

                    selectedBillIds.size shouldBe 2
                    selectedBillIds[0].map { it.value } shouldContainExactlyInAnyOrder billViews.map { it.billId.value }
                    selectedBillIds[1].map { it.value } shouldContainExactlyInAnyOrder billViews.map { it.billId.value }
                    sweepingRequest shouldContainExactly listOf(null, null)
                    authorizationToken shouldContainExactly listOf(null, "token")

                    historyRepository.findLatest(user.id).messages.last().message shouldBe "Transação confirmada com sucesso"
                }

                // envia uma notificação pra reconectar a conta ou pagar com OPP
                // paga usando OPP
                it("Se NÃO tiver SALDO, o valor estiver ACIMA do limite do assistente e tem conta CONECTADA, mas está acima do limite da conta CONECTADA") {
                    coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 1000).right()
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any(), any()) } returns Unit.right()
                    coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns listOf(SweepingConsent(SweepingParticipant(SweepingParticipantId("id"), "nome", "shortName"), 1000_00, null)).right()
                    coEvery { paymentAdapter.generateOnePixPay(user.id, any(), walletId) } returns PixQRCode(qrCode).right()

                    val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                    val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                    conversationHistoryService.createAssistantMessage(user.id, text)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Sim, pagar todas",
                    )
                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.SCHEDULE_BILLS, null, "2021-10-10T10:00:00Z"))

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Prefiro pagar com 1 Pix",
                    )
                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id
                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.SEND_PIX_CODE, transactionId, "2021-10-10T10:00:00Z"))

                    val selectedBillIds = slot<List<BillId>>()

                    coVerify {
                        paymentAdapter.findPendingBills(any(), any(), any())
                        paymentAdapter.generateOnePixPay(any(), capture(selectedBillIds), any())
                    }
                    coVerify(exactly = 0) {
                        paymentAdapter.scheduleBills(any(), any(), any(), any(), any())
                    }

                    selectedBillIds.captured.map { it.value } shouldContainExactlyInAnyOrder billViews.map { it.billId.value }

                    historyRepository.findLatest(user.id).messages.last().message shouldBe "código pix enviado ao usuário."
                }

                // faz o pagamento com one-pix-pay
                it("Se NÃO tiver SALDO e NÃO tem conta CONECTADA") {
                    coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 1000).right()
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any(), any()) } returns Unit.right()
                    coEvery { paymentAdapter.generateOnePixPay(user.id, any(), walletId) } returns PixQRCode(qrCode).right()

                    val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                    val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                    conversationHistoryService.createAssistantMessage(user.id, text)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Sim, pagar todas",
                    )
                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.SCHEDULE_BILLS, null, "2021-10-10T10:00:00Z"))

                    val selectedBillIds = slot<List<BillId>>()

                    coVerify {
                        paymentAdapter.findPendingBills(any(), any(), any())
                        paymentAdapter.generateOnePixPay(any(), capture(selectedBillIds), any())
                    }
                    coVerify(exactly = 0) {
                        paymentAdapter.scheduleBills(any(), any(), any(), any(), any())
                    }

                    selectedBillIds.captured.map { it.value } shouldContainExactlyInAnyOrder billViews.map { it.billId.value }

                    historyRepository.findLatest(user.id).messages.last().message shouldBe "código pix enviado ao usuário."
                }
            }

            describe("Não deve realizar o pagamento") {
                it("Se o usuário cancelar a autorização") {
                    coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 100_000_00).right()
                    coEvery {
                        paymentAdapter.scheduleBills(any(), any(), any(), any(), any())
                    } returns PaymentAdapterError.AssistantLimitExceeded.left()

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Quais as contas pendentes?",
                    )
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Sim, pagar todas",
                    )
                    baseProcessor.process(user, interceptAction = InterceptAction(type = InterceptMessagePayloadType.SCHEDULE_BILLS))

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Não autorizar",
                    )

                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id

                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CANCEL, transactionId, "2021-10-10T10:00:00Z"))

                    val selectedBillIds = mutableListOf<List<BillId>>()
                    val sweepingRequest = mutableListOf<SweepingRequest?>()
                    val authorizationToken = mutableListOf<String?>()

                    coVerify {
                        paymentAdapter.findPendingBills(any(), any(), any())
                        paymentAdapter.scheduleBills(any(), captureNullable(sweepingRequest), any(), capture(selectedBillIds), captureNullable(authorizationToken))
                    }

                    selectedBillIds.size shouldBe 1
                    selectedBillIds[0].map { it.value } shouldContainExactlyInAnyOrder billViews.map { it.billId.value }
                    sweepingRequest shouldContainExactly listOf(null)
                    authorizationToken shouldContainExactly listOf(null)

                    historyRepository.findLatest(user.id).messages.last().message shouldBe "Entendi. Se precisar de mais alguma coisa, estou à disposição."
                }

                it("Se o usuário desistir do pagamento") {
                    coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 1000).right()
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any(), any()) } returns Unit.right()
                    coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns listOf(SweepingConsent(SweepingParticipant(SweepingParticipantId("id"), "nome", "shortName"), 1000_00, null)).right()

                    val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                    val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                    conversationHistoryService.createAssistantMessage(user.id, text)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Pagar a 1 e a 3",
                    )
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Não",
                    )

                    val transactionId = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id
                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CANCEL, transactionId, "2021-10-10T10:00:00Z"))

                    coVerify {
                        paymentAdapter.findPendingBills(any(), any(), any())
                    }

                    coVerify(exactly = 0) {
                        paymentAdapter.scheduleBills(any(), any(), any(), any(), any())
                    }

                    val transaction = transactionRepository.find(user.id, TransactionStatus.CANCELED).first()
                    transaction.id.value shouldBe transactionId.value

                    historyRepository.findLatest(user.id).messages.last().message shouldBe "Entendi. Se precisar de mais alguma coisa, estou à disposição."
                }

                it("Se o usuário desistir de pagar usando a conta conectada") {
                    coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 1000).right()
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any(), any()) } returns Unit.right()
                    coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns listOf(SweepingConsent(SweepingParticipant(SweepingParticipantId("id"), "nome", "shortName"), 1000_00, null)).right()

                    val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                    val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                    conversationHistoryService.createAssistantMessage(user.id, text)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Pagar a 1 e a 3",
                    )
                    baseProcessor.process(user)

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Sim",
                    )

                    val transactionIdFirst = transactionRepository.find(user.id, TransactionStatus.ACTIVE).first().id
                    baseProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.SCHEDULE_BILLS, transactionIdFirst, "2021-10-10T10:00:00Z"))

                    conversationHistoryService.createUserMessage(
                        user.id,
                        "Não quero pagar",
                    )
                    baseProcessor.process(user)

                    coVerify {
                        paymentAdapter.findPendingBills(any(), any(), any())
                    }

                    coVerify(exactly = 0) {
                        paymentAdapter.scheduleBills(any(), any(), any(), any(), any())
                    }

                    val assistantMessage = historyRepository.findLatest(user.id).messages.last { it.type == MessageType.ASSISTANT }

                    if (assistantMessage.message != null) {
                        validateConversation("Deve finalizar a conversa sem realizar o pagamento.")
                    } else {
                        assistantMessage.completionMessage?.acoes?.first()?.name shouldBeSameInstanceAs ActionType.NOOP
                    }
                }
            }
        }

        describe("Quando o usuário pedir pra pagar as contas por texto") {
            it("Deve pagar só após a confirmação") {
                coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.copy(current = 100_000_00).right()
                coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns Unit.right()

                val formattedBills = NotificationFormatter.getFormattedBillInfo(billViews)
                val text = notificationContextTemplatesService.getBillsComingDue(user.name, formattedBills)

                conversationHistoryService.createAssistantMessage(user.id, text)

                conversationHistoryService.createUserMessage(
                    user.id,
                    "Pagar",
                )

                baseProcessor.process(user)

                coVerify(exactly = 0) {
                    paymentAdapter.scheduleBills(any(), any(), any(), any(), any())
                }

                historyRepository.findLatest(user.id).messages.last().message shouldContain "Posso prosseguir?"

                val transactionId = transactionRepository.find(user.id).first().id

                conversationHistoryService.createUserMessage(
                    user.id,
                    "Sim",
                )

                baseProcessor.process(user, interceptAction = InterceptAction(type = InterceptMessagePayloadType.SCHEDULE_BILLS, transactionId = transactionId))

                coVerify {
                    paymentAdapter.scheduleBills(
                        userId = any(),
                        sweepingRequest = null,
                        walletId = any(),
                        bills = match { it.map { b -> b.value }.toSet() == billViews.map { b -> b.billId.value }.toSet() },
                        authorizationToken = null,
                    )
                }

                historyRepository.findLatest(user.id).messages.last().message shouldBe "Ok, estou processando e já volto com o resultado."
            }
        }
    }
}