package ai.chatbot.archunit

import ai.chatbot.app.config.EachTenant
import ai.chatbot.app.job.TenantAbstractJob
import com.tngtech.archunit.core.domain.JavaClasses
import com.tngtech.archunit.core.domain.JavaModifier
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.junit.AnalyzeClasses
import com.tngtech.archunit.junit.ArchTest
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noMethods
import io.micronaut.context.annotation.Property
import io.micronaut.core.annotation.Generated
import io.micronaut.scheduling.annotation.Async
import kotlin.jvm.java
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode

@AnalyzeClasses(packages = ["ai.chatbot"], importOptions = [ImportOption.DoNotIncludeTests::class])
@Execution(ExecutionMode.CONCURRENT)
internal class ArchitectureTest {

    @ArchTest
    fun `nenhum método deve usar a anotação Async`(
        importedClasses: JavaClasses,
    ) {
        noMethods()
            .should().beAnnotatedWith(Async::class.java)
            .check(importedClasses)
    }

    @ArchTest
    fun `nenhuma classe deve ter como dependencia a classe PromptService exceto as classes no pacote ai-chatbot-app-prompt`(
        importedClasses: JavaClasses,
    ) {
        val rule = noClasses()
            .that().resideOutsideOfPackage("ai.chatbot.app.prompt")
            .should().dependOnClassesThat().haveFullyQualifiedName("ai.chatbot.app.prompt.PromptService")

        rule.check(importedClasses)
    }

    @ArchTest
    fun `nenhuma anotação Property deve usar o path tenants`(
        importedClasses: JavaClasses,
    ) {
        // Verifica campos com @Property
        val fieldsWithTenantsProperty = importedClasses
            .flatMap { it.fields }
            .filter { it.isAnnotatedWith(Property::class.java) }
            .filter { field ->
                val propertyAnnotation = field.getAnnotationOfType(Property::class.java)
                propertyAnnotation.name.contains("tenants")
            }

        if (fieldsWithTenantsProperty.isNotEmpty()) {
            throw AssertionError("Encontrados campos com @Property contendo 'tenants': ${fieldsWithTenantsProperty.map { "${it.owner.simpleName}.${it.name}" }}")
        }

        // Verifica parâmetros de métodos com @Property
        val methodsWithTenantsProperty = importedClasses
            .flatMap { it.methods }
            .filter { it.isAnnotatedWith(Property::class.java) }
            .filter { method ->
                val propertyAnnotation = method.getAnnotationOfType(Property::class.java)
                propertyAnnotation.name.contains("tenants")
            }

        if (methodsWithTenantsProperty.isNotEmpty()) {
            throw AssertionError("Encontrados métodos com @Property contendo 'tenants': ${methodsWithTenantsProperty.map { "${it.owner.simpleName}.${it.name}" }}")
        }

        // Verifica parâmetros de construtor com @Property
        val constructorsWithTenantsProperty = importedClasses
            .flatMap { it.constructors }
            .flatMap { it.parameters }
            .filter { it.isAnnotatedWith(Property::class.java) }
            .filter { parameter ->
                val propertyAnnotation = parameter.getAnnotationOfType(Property::class.java)
                propertyAnnotation.name.contains("tenants")
            }

        if (constructorsWithTenantsProperty.isNotEmpty()) {
            throw AssertionError("Encontrados parâmetros de construtor com @Property contendo 'tenants': ${constructorsWithTenantsProperty.map { "${it.owner.owner.simpleName}.${it.owner.name}(${it.getAnnotationOfType(Property::class.java).name})" }}")
        }
    }

    @ArchTest
    fun `todas as classes que extendem de MultiTenantAbstractJob devem estar anotadas com @EachTenant`(
        importedClasses: JavaClasses,
    ) {
        classes()
            .that().areAssignableTo(TenantAbstractJob::class.java)
            .and().doNotHaveModifier(JavaModifier.ABSTRACT)
            .and().areNotAnnotatedWith(Generated::class.java)
            .should().beAnnotatedWith(EachTenant::class.java)
            .check(importedClasses)
    }
}