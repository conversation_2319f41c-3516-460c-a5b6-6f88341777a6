package ai.chatbot.app.conversation

import ai.chatbot.app.utils.parseObjectFrom
import io.kotest.core.spec.style.DescribeSpec

class ActionParserTest : DescribeSpec() {
    init {
        describe("Action serialization and deserialization") {
            it("should deserialize message") {
                val string =
                    """
                    |{
                    |  "@type": "sendMessage",
                    |  "name": "MSG",
                    |  "content": "<PERSON><PERSON><PERSON>, <PERSON>! Para começarmos, você gostaria de pagar todas as contas vencendo hoje ou apenas algumas específicas?"
                    |}
                    """.trimMargin()

                parseObjectFrom<Action.SendMessage>(string)
            }
        }
    }
}