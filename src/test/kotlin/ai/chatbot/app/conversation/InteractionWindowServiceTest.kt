package ai.chatbot.app.conversation

import DynamoDBUtils.setupDynamoDB
import ai.chatbot.adapters.billPayment.AccountTO
import ai.chatbot.adapters.dynamodb.InteractionWindowDbRepository
import ai.chatbot.adapters.dynamodb.InteractionWindowDynamoDAO
import ai.chatbot.adapters.notification.MessagePublisher
import ai.chatbot.app.DYNAMOB_TEST_TABLE_NAME
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.notification.ChatBotNotificationGatewayTO
import ai.chatbot.app.notification.WelcomeDetailsTO
import ai.chatbot.app.notification.WelcomeMessageType
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.brazilTimeZone
import ai.chatbot.app.withGivenDateTime
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.context.ApplicationContext
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.ZonedDateTime
import java.util.*

class InteractionWindowServiceTest : DescribeSpec() {
    override fun isolationMode() = IsolationMode.InstancePerTest

    private val dynamoDbEnhancedClient = setupDynamoDB()
    private val mockedApplicationContext =
        mockk<ApplicationContext> {
            every { getProperty(any<String>(), String::class.java) } returns Optional.of(DYNAMOB_TEST_TABLE_NAME)
        }

    private val tenantService = mockk<TenantService>() {
        every { getTenantName() } returns "friday"
    }

    private val interactionWindowDynamoDAO = InteractionWindowDynamoDAO(dynamoDbEnhancedClient, mockedApplicationContext)
    private val interactionWindowRepository = InteractionWindowDbRepository(interactionWindowDynamoDAO, tenantService)

    private val messagePublisher = mockk<MessagePublisher>(relaxed = true)

    private val service = InteractionWindowService(
        repository = interactionWindowRepository,
        messagePublisher = messagePublisher,
        inboundNotificationQueue = "test-queue",
    )

    private val userId = UserId("*************")
    private val currentTime = ZonedDateTime.of(2024, 8, 10, 12, 50, 25, 0, brazilTimeZone)
    private val interactionWindow = InteractionWindow(
        userId = userId,
        type = InteractionWindowType.ONBOARDING_SINGLE_PIX,
        expiration = currentTime.plusHours(5),
    )

    private val user = User(
        accountId = AccountId(value = "ACCOUNT-ID"),
        id = userId,
        name = "Nome",
        accountGroups = emptyList(),
        status = AccountStatus.ACTIVE,
    )

    private val notification = ChatBotNotificationGatewayTO(
        walletId = "WALLET-ID",
        account = AccountTO(id = user.accountId.value, fullName = user.name, msisdn = user.id.value, accountGroups = emptyList(), status = AccountStatus.ACTIVE, paymentStatus = AccountPaymentStatus.UpToDate),
        details = WelcomeDetailsTO(WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX),
    )

    init {
        describe("ao criar ua janela de interação") {
            it("deve salvar no banco") {
                val result = withGivenDateTime(currentTime) {
                    service.create(userId, InteractionWindowType.ONBOARDING_SINGLE_PIX)
                }

                result.isRight() shouldBe true

                with(interactionWindowRepository.findByUserId(userId)) {
                    this.shouldNotBeNull()

                    this.userId shouldBe userId
                    this.state shouldBe InteractionWindowState.ACTIVE
                    this.notificationQueue shouldBe emptyList()
                    this.expiration shouldBe currentTime.plus(InteractionWindowType.ONBOARDING_SINGLE_PIX.duration)
                }
            }

            it("deve retornar erro se já houver uma janela ativa") {
                interactionWindowRepository.save(interactionWindow.copy(expiration = getZonedDateTime().plusHours(5)))

                val result = service.create(userId, InteractionWindowType.ONBOARDING_SINGLE_PIX)

                result.isLeft() shouldBe true
                result.mapLeft {
                    (it is InteractionWindowError.UserAlreadyHasOpenWindow) shouldBe true
                }
            }
        }

        describe("ao buscar uma janela de interação") {
            it("deve retornar uma janela ativa") {
                interactionWindowRepository.save(interactionWindow.copy(expiration = getZonedDateTime().plusHours(5)))

                val result = service.findActiveWindow(userId)

                result.shouldNotBeNull()
            }

            it("deve retornar nulo se não houver janela ativa") {
                val result = service.findActiveWindow(userId)

                result.shouldBeNull()
            }

            it("deve retornar nulo e fechar a janela se houver uma janela expirada") {
                interactionWindowRepository.save(interactionWindow.copy(expiration = getZonedDateTime().minusHours(5)))

                val result = service.findActiveWindow(userId)

                result.shouldBeNull()

                with(interactionWindowRepository.findByUserId(userId)) {
                    this.shouldBeNull()
                }
            }
        }

        describe("ao fechar uma janela") {
            it("deve remover a janela do banco e enfileirar as notificações represadas") {
                val notifications = listOf(notification)

                interactionWindowRepository.save(
                    interactionWindow.copy(
                        expiration = getZonedDateTime().plusHours(5),
                        notificationQueue = notifications,
                    ),
                )

                service.close(userId)

                with(interactionWindowRepository.findByUserId(userId)) {
                    this.shouldBeNull()
                }

                verify {
                    messagePublisher.sendMessage("test-queue", notifications[0], any())
                }
            }
        }

        describe("ao fechar todas as janelas expiradas") {
            it("deve remover as janelas expiradas do banco") {
                val notifications = listOf(notification)

                interactionWindowRepository.save(
                    interactionWindow.copy(
                        userId = UserId("EXPIRED 1"),
                        expiration = getZonedDateTime().minusHours(5),
                    ),
                )

                interactionWindowRepository.save(
                    interactionWindow.copy(
                        userId = UserId("EXPIRED 2"),
                        expiration = getZonedDateTime().minusHours(1),
                    ),
                )

                interactionWindowRepository.save(
                    interactionWindow.copy(
                        userId = UserId("NOT EXPIRED"),
                        expiration = getZonedDateTime().plusHours(5),
                    ),
                )

                service.expireAllDue()

                with(interactionWindowRepository.findByUserId(UserId("EXPIRED 1"))) {
                    this.shouldBeNull()
                }

                with(interactionWindowRepository.findByUserId(UserId("EXPIRED 2"))) {
                    this.shouldBeNull()
                }

                with(interactionWindowRepository.findByUserId(UserId("NOT EXPIRED"))) {
                    this.shouldNotBeNull()
                }
            }
        }

        describe("ao checar se uma notificação pode ser enviada") {

            describe("quando não houver janela de interação aberta para o usuário") {
                describe("quando a notificação necessitar de uma janela de interação") {
                    val notificationThatCreatesWindow = notification.copy(
                        details = WelcomeDetailsTO(type = WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX),
                    )

                    it("deve retornar ok") {
                        val result = service.checkAndCreate(notificationThatCreatesWindow)
                        result.isRight() shouldBe true
                    }

                    it("deve criar janela") {
                        service.checkAndCreate(notificationThatCreatesWindow)

                        with(interactionWindowRepository.findByUserId(userId)) {
                            this.shouldNotBeNull()
                            this.userId shouldBe user.id
                            this.type shouldBe InteractionWindowType.ONBOARDING_SINGLE_PIX
                        }
                    }
                }

                describe("quando a notificação não necessitar de uma janela de interação") {
                    val notificationWithoutWindow = notification.copy(
                        details = WelcomeDetailsTO(type = WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX),
                    )

                    it("deve retornar ok") {
                        val result = service.checkAndCreate(notificationWithoutWindow)
                        result.isRight() shouldBe true
                    }
                }
            }

            it("deve retornar erro quando já houver uma janela aberta") {
                interactionWindowRepository.save(interactionWindow.copy(expiration = getZonedDateTime().plusHours(5)))

                val result = service.checkAndCreate(notification)
                result.isLeft() shouldBe true

                result.mapLeft {
                    (it is InteractionWindowError.UserAlreadyHasOpenWindow) shouldBe true
                }
            }
        }
    }
}