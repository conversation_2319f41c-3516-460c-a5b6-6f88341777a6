package ai.chatbot.app.conversation

import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.HistoryStateRepository
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import java.time.ZonedDateTime

class ConversationHistoryServiceTest : DescribeSpec() {
    private val historyRepository = mockk<HistoryRepository>()
    private val historyStateRepository = mockk<HistoryStateRepository>(relaxed = true)

    private val conversationHistoryService = ConversationHistoryService(
        historyRepository = historyRepository,
        historyStateRepository = historyStateRepository,
        openAIAdapter = mockk(),
        paymentAdapter = mockk(),
        notificationService = mockk(),
        pendingBillsService = mockk(),
        promptService = mockk(),
    )
    private val userId = UserId("1")

    private val conversationHistoryNotActive = ConversationHistory(
        userId = userId,
        createdAt = LocalDate.now(),
        messages = listOf(
            ChatMessageWrapper(
                type = MessageType.SYSTEM,
                message = "Mansagem de sistema",
                completionMessage = null,
                timestamp = ZonedDateTime.now(),
            ),
            ChatMessageWrapper(
                type = MessageType.ASSISTANT,
                message = "O que preciso pagar",
                completionMessage = null,
                timestamp = ZonedDateTime.now(),
            ),
        ),
    )

    init {
        beforeEach {
            clearMocks(historyRepository, historyStateRepository)
        }

        describe("quando consultar se usuario está ativo") {

            describe("quando nao tiver histórico") {
                it("deve retornar false") {
                    every {
                        historyRepository.findLatest(userId)
                    } throws UserConversationHistoryNotFound.UserConversationNotFound

                    val result = conversationHistoryService.isUserActive(userId)
                    result.shouldBeFalse()
                }
            }

            describe("quando tiver histórico") {

                describe("quando o usuário enviou mensagem no dia") {
                    it("deve retornar true") {
                        every {
                            historyRepository.findLatest(userId)
                        } returns conversationHistoryNotActive.copy(
                            messages = conversationHistoryNotActive.messages + listOf(
                                ChatMessageWrapper(
                                    type = MessageType.USER,
                                    message = "Ola, Fred",
                                    completionMessage = null,
                                    timestamp = ZonedDateTime.now(),
                                ),
                            ),
                        )

                        val result = conversationHistoryService.isUserActive(userId)
                        result.shouldBeTrue()
                    }
                }

                describe("quando o usuário não enviou mensagem no dia") {
                    it("deve retornar false") {
                        every {
                            historyRepository.findLatest(userId)
                        } returns conversationHistoryNotActive

                        val result = conversationHistoryService.isUserActive(userId)
                        result.shouldBeFalse()
                    }
                }
            }
        }

        describe("quando salvar estado do histórico") {
            val validState = BillComingDueHistoryState(
                user = User(
                    id = userId,
                    accountId = AccountId("123"),
                    status = AccountStatus.ACTIVE,
                    paymentStatus = AccountPaymentStatus.UpToDate,
                    name = "Test User",
                    accountGroups = listOf(),
                ),
                walletWithBills = WalletWithBills(
                    bills = listOf(),
                    walletId = WalletId("456"),
                    walletName = "Test Wallet",
                    activeConsents = listOf(),
                ),
                balance = null,
                internalStateControl = InternalStateControl(
                    shouldSynchronizeBeforeCompletion = false,
                    billComingDueNotifiedAt = null,
                ),
                startDate = getLocalDate(),
                endDate = getLocalDate(),
                contacts = listOf(),
                lastBillsUpdatedAt = getZonedDateTime(),
                subscription = null,
                onboardingState = null,
                alreadyPromotedSweepingAccount = true,
            )

            it("deve salvar estado quando accountId é válido") {
                conversationHistoryService.saveHistoryState(userId, validState)

                verify(exactly = 1) {
                    historyStateRepository.save(userId, validState)
                }
            }

            it("não deve salvar estado quando accountId está vazio") {
                val invalidState = validState.copy(
                    user = validState.user.copy(accountId = AccountId("")),
                )

                conversationHistoryService.saveHistoryState(userId, invalidState)

                verify(exactly = 0) {
                    historyStateRepository.save(any(), any())
                }
            }
        }
    }
}