package ai.chatbot.app.conversation

import ai.chatbot.adapters.waCommCentre.MediaType
import ai.chatbot.adapters.waCommCentre.ProcessedMediaResult
import ai.chatbot.app.media.BoletoInfo
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.equals.shouldBeEqual
import io.kotest.matchers.shouldBe

class MessageContentTest : DescribeSpec() {
    init {
        val userText = "texto do usuário"
        val transcription = "Texto extraído da imagem"
        val singleBoleto = BoletoInfo("123456", "2024-12-31")
        val boletos = listOf(
            BoletoInfo("123456", "2024-12-31"),
            <PERSON><PERSON><PERSON><PERSON>n<PERSON>("789012", "2025-01-15"),
        )
        val textExtraction = "Texto extraído da imagem"
        val singleQrCode = "00020126580014BR.GOV.BCB.PIX013634377405-c606-4da0-b944-00503596d12c520400005303986540525.505802BR5924Marlon da Costa Moncores6009SAO PAULO61080540900062140510testepixqr63044A81"
        val qrCodes = listOf(
            "00020126580014BR.GOV.BCB.PIX013634377405-c606-4da0-b944-00503596d12c520400005303986540525.505802BR5924Marlon da Costa Moncores6009SAO PAULO61080540900062140510testepixqr63044A81",
            "00020126580014BR.GOV.BCB.PIX0136547b7870-2442-4bad-ba00-8f9c845421f952040000530398654040.015802BR5920RAFAEL HAERTEL PERES6007PELOTAS622605221HNOBKbErSuKI69IoEfNgc6304E921",
        )

        describe("quando tiver qr code") {
            it("o conteudo deve ser o qrcode") {
                val messageContent = MessageContent(
                    "",
                    ProcessedMediaResult(
                        mediaType = MediaType.IMAGE,
                        qrCodeValues = listOf(singleQrCode),
                        boletoInfo = boletos,
                        textExtraction = textExtraction,
                    ),
                )
                messageContent.content shouldBe singleQrCode
            }

            it("o conteudo deve ser o qrcode com texto do usuário quando houver texto") {
                val messageContent = MessageContent(
                    userText,
                    ProcessedMediaResult(
                        mediaType = MediaType.IMAGE,
                        qrCodeValues = listOf(singleQrCode),
                        boletoInfo = boletos,
                        textExtraction = textExtraction,
                    ),
                )
                messageContent.content shouldBe "$singleQrCode\n\n$userText"
            }
        }

        describe("quando tiver mais de 1 qr code com PIX") {
            it("o conteudo deve ser apenas 1") {
                val messageContent = MessageContent(
                    "",
                    ProcessedMediaResult(
                        mediaType = MediaType.IMAGE,
                        qrCodeValues = qrCodes,
                    ),
                )
                messageContent.content shouldBeEqual singleQrCode
            }

            it("o conteudo deve ser apenas 1 com texto do usuário quando houver texto") {
                val messageContent = MessageContent(
                    userText,
                    ProcessedMediaResult(
                        mediaType = MediaType.IMAGE,
                        qrCodeValues = qrCodes,
                    ),
                )
                messageContent.content shouldBeEqual "$singleQrCode\n\n$userText"
            }
        }

        describe("quando tiver transcrição") {
            it("o conteudo deve ser a transcrição") {
                val messageContent = MessageContent(
                    userText,
                    ProcessedMediaResult(
                        mediaType = MediaType.AUDIO,
                        transcription = transcription,
                        boletoInfo = boletos,
                        textExtraction = textExtraction,
                    ),
                )
                messageContent.content shouldBe transcription
            }
        }

        describe("quando tiver boleto") {
            it("o conteudo deve ser o código do boleto e data de vencimento") {
                val messageContent = MessageContent(
                    "",
                    ProcessedMediaResult(
                        mediaType = MediaType.DOCUMENT,
                        boletoInfo = listOf(singleBoleto),
                    ),
                )
                messageContent.content shouldBe "123456\n\nVencimento: 2024-12-31"
            }

            it("o conteudo deve ser o código do boleto e data de vencimento com texto do usuário quando houver texto") {
                val messageContent = MessageContent(
                    userText,
                    ProcessedMediaResult(
                        mediaType = MediaType.DOCUMENT,
                        boletoInfo = listOf(singleBoleto),
                    ),
                )
                messageContent.content shouldBe "123456\n\nVencimento: 2024-12-31\n\n$userText"
            }
        }

        describe("quando não for encontrado PIX nem boleto, mas a imagem contiver texto") {
            it("o conteudo deve ser o texto extraído da imagem") {
                val messageContent = MessageContent(
                    "",
                    ProcessedMediaResult(
                        mediaType = MediaType.IMAGE,
                        textExtraction = textExtraction,
                    ),
                )
                messageContent.content shouldBe textExtraction
            }

            it("o conteudo deve ser o texto extraído da imagem com texto do usuário quando houver texto") {
                val messageContent = MessageContent(
                    userText,
                    ProcessedMediaResult(
                        mediaType = MediaType.IMAGE,
                        textExtraction = textExtraction,
                    ),
                )
                messageContent.content shouldBe "$textExtraction\n\n$userText"
            }
        }

        describe("quando não tiver mídia processada") {
            it("o conteudo deve ser o texto do usuário") {
                val userText = "texto do usuário"
                val messageContent = MessageContent(
                    userText,
                    null,
                )
                messageContent.content shouldBe userText
            }
        }

        describe("quando tiver texto em branco do usuário") {
            it("não deve adicionar texto vazio para QR Code") {
                val messageContent = MessageContent(
                    "   ",
                    ProcessedMediaResult(
                        mediaType = MediaType.IMAGE,
                        qrCodeValues = listOf(singleQrCode),
                    ),
                )
                messageContent.content shouldBe singleQrCode
            }

            it("não deve adicionar texto vazio para Boleto") {
                val messageContent = MessageContent(
                    "",
                    ProcessedMediaResult(
                        mediaType = MediaType.DOCUMENT,
                        boletoInfo = listOf(singleBoleto),
                    ),
                )
                messageContent.content shouldBe "123456\n\nVencimento: 2024-12-31"
            }

            it("não deve adicionar texto vazio para extração de texto") {
                val messageContent = MessageContent(
                    "  ",
                    ProcessedMediaResult(
                        mediaType = MediaType.IMAGE,
                        textExtraction = textExtraction,
                    ),
                )
                messageContent.content shouldBe textExtraction
            }
        }
    }
}