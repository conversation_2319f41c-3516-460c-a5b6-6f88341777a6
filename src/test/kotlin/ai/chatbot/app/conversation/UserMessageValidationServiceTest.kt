package ai.chatbot.app.conversation

import ai.chatbot.adapters.api.MessagePayloadTO
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.parseObjectFrom
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe

class UserMessageValidationServiceTest : DescribeSpec() {
    val service = UserMessageValidationService()
    val today = getLocalDate()

    val payLoadWithTodayDate = """{"content":"Pagar tudo com 1 Pix","payload":"${today.format(dateFormat)}"}"""
    val payLoadWithPastDate = """{"content":"Pagar tudo com 1 Pix","payload":"${today.minusDays(1).format(dateFormat)}"}"""
    val payLoadWithOutDate = """{"content":"Adicionar saldo","payload":"Adicionar saldo"}"""

    init {
        describe("Quando validar uma mensagem com payload") {
            it("deve retornar INVALID se o payload não for uma data") {
                val messagePayloadTO = parseObjectFrom<MessagePayloadTO>(payLoadWithOutDate)
                val result = service.validatePayload(messagePayloadTO.payload, null)
                result shouldBe UserMessagePayloadValidationResult.INVALID
            }
            it("deve retornar OUTDATED se o payload for uma data diferente de hoje") {
                val messagePayloadTO = parseObjectFrom<MessagePayloadTO>(payLoadWithPastDate)
                val result = service.validatePayload(messagePayloadTO.payload, null)
                result shouldBe UserMessagePayloadValidationResult.OUTDATED
            }
            it("deve retornar VALID se o payload for uma data igual a hoje") {
                val messagePayloadTO = parseObjectFrom<MessagePayloadTO>(payLoadWithTodayDate)
                val result = service.validatePayload(messagePayloadTO.payload, null)
                result shouldBe UserMessagePayloadValidationResult.VALID
            }
        }

        describe("Quando validar uma mensagem para marcar lembrete como pago") {
            it("deve considerar válido um payload correto") {
                val payload = "REMINDER-ID"

                val result =
                    service.validatePayload(
                        payload = payload,
                        action = InterceptAction(type = InterceptMessagePayloadType.MARK_REMINDER_AS_DONE, payload = payload),
                    )

                result shouldBe UserMessagePayloadValidationResult.VALID
            }

            it("deve considerar inválido um payload vazio") {
                val payload = ""

                val result =
                    service.validatePayload(
                        payload = payload,
                        action = InterceptAction(type = InterceptMessagePayloadType.MARK_REMINDER_AS_DONE, payload = payload),
                    )

                result shouldBe UserMessagePayloadValidationResult.INVALID
            }
        }

        describe("Quando validar uma mensagem para adicionar mais conexões de consumo") {
            it("deve considerar válido um payload correto") {
                val payload = ""

                val result =
                    service.validatePayload(
                        payload = payload,
                        action = InterceptAction(type = InterceptMessagePayloadType.ADD_NEW_CONNECTION, payload = payload),
                    )

                result shouldBe UserMessagePayloadValidationResult.VALID
            }
        }

        describe("Quando validar uma mensagem de resposta do onboarding single pix") {
            describe("quando o payload estiver correto") {
                it("deve validar com sucesso CONFIRM") {
                    val payload = "CONFIRM"

                    val result =
                        service.validatePayload(
                            payload = payload,
                            action = InterceptAction(
                                type = InterceptMessagePayloadType.ONBOARDING_SINGLE_PIX,
                                payload = payload,
                            ),
                        )

                    result shouldBe UserMessagePayloadValidationResult.VALID
                }

                it("deve validar com sucesso SKIP") {
                    val payload = "SKIP"

                    val result =
                        service.validatePayload(
                            payload = payload,
                            action = InterceptAction(
                                type = InterceptMessagePayloadType.ONBOARDING_SINGLE_PIX,
                                payload = payload,
                            ),
                        )

                    result shouldBe UserMessagePayloadValidationResult.VALID
                }
            }

            describe("quando o payload não estiver formatado corretamente") {
                listOf(
                    listOf("PIX", "00466007019"),
                    listOf("PIX", "CPF"),
                    listOf("PIX", "00466007019", "AAAA"),
                    listOf("OLA", ""),
                    listOf("OLA"),
                ).forEach { parameters ->
                    it("deve falhar quando o parâmetro for ${parameters.joinToString("-")}") {
                        val payload = parameters.joinToString("-")

                        val result =
                            service.validatePayload(
                                payload = payload,
                                action = InterceptAction(
                                    type = InterceptMessagePayloadType.ONBOARDING_SINGLE_PIX,
                                    payload = payload,
                                ),
                            )

                        result shouldBe UserMessagePayloadValidationResult.INVALID
                    }
                }
            }
        }
    }
}