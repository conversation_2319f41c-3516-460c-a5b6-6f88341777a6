package ai.chatbot.app.conversation

import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.ActionFixture
import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.NotificationService
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.prompt.DEFAULT_ERROR_MESSAGE
import ai.chatbot.app.prompt.FridayPromptService
import ai.chatbot.app.prompt.TenantPromptService
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import arrow.core.left
import arrow.core.right
import io.kotest.core.annotation.Ignored
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifyOrder

@Ignored("Ignored until we have a better way to test this")
class ActionsManagerTest :
    FunSpec(
        {
            val accountId = AccountId("account_id")
            val userId = UserId.fromMsisdn("*************")
            val user =
                User(
                    accountId = accountId,
                    id = userId,
                    name = "Nome do usuário",
                    accountGroups = emptyList(),
                    status = AccountStatus.ACTIVE,
                )
            val notificationService = mockk<NotificationService>(relaxed = true)

            val openAIAdapter: OpenAIAdapter = mockk()
            val historyRepository = mockk<HistoryRepository>(relaxed = true)
            val conversationHistoryService =
                ConversationHistoryService(
                    historyRepository = historyRepository,
                    openAIAdapter = openAIAdapter,
                    paymentAdapter = mockk<PaymentAdapter>(relaxed = true),
                    notificationService = mockk(relaxed = true),
                    historyStateRepository = mockk(relaxed = true),
                    pendingBillsService = mockk(relaxed = true),
                    promptService = TenantPromptService(
                        prompts = mapOf("friday" to FridayPromptService()),
                        tenantService = mockk(),
                        multiTenantPromptService = mockk(),
                    ),
                )

            val actionExecutorLocator =
                ActionExecutorLocator(
                    messageActionExecutor = mockk(),
                    notificationActionExecutor = mockk(),
                    addFeatureRequestExecutor = mockk(),
                    refreshBalanceExecutor = mockk(),
                    markBillsAsPaidExecutor = mockk(),
                    markReminderAsDoneExecutor = mockk(),
                    noopExecutor = mockk(),
                    getContextExecutor = mockk(),
                    pixTransactionExecutor = mockk(),
                    searchContactsExecutor = mockk(),
                    onboardingSinglePixExecutor = mockk(),
                    makePaymentExecutor = mockk(),
                    promoteSweepingAccount = mockk(),
                    sendBillsPeriodExecutor = mockk(),
                    validateBoletoExecutor = mockk(),
                    scheduleBoletoExecutor = mockk(),
                    createManualEntryExecutor = mockk(),
                    removeManualEntryExecutor = mockk(),
                    selectSweepingAccountExecutor = mockk(),
                )

            val actionsManager =
                ActionsManager(
                    actionExecutorLocator = actionExecutorLocator,
                    conversationHistoryService = conversationHistoryService,
                    notificationService = notificationService,
                )

            val availableActions = ActionType.values()

            val actions = availableActions.map { ActionFixture.create(it) }

            beforeTest {
                clearAllMocks()
                availableActions.map {
                    val currentExecutor = actionExecutorLocator.locate(it)
                    every {
                        currentExecutor.execute(any())
                    } returns ActionResult.WithoutCompletion(actionType = it).right()
                    ActionFixture.create(it)
                }
            }

            context("quando executar uma lista de ações com sucesso") {

                test("todas as açoes devem ser executadas") {
                    actionsManager.execute(actions, user)
                    availableActions.map {
                        val currentExecutor = actionExecutorLocator.locate(it)
                        verify {
                            currentExecutor.execute(any())
                        }
                    }
                }

                test("deve ordenar pela prioridade") {
                    actionsManager.execute(actions, user)

                    val actionsByPriority = availableActions.groupBy { it.priority }
                    repeat(10) {
                        verifyOrder {
                            actionsByPriority.forEach { (_, actions) ->
                                actionExecutorLocator.locate(actions.random()).execute(any())
                            }
                        }
                    }
                }

                context("se nenhuma action precisar de completion") {
                    test("não deve pedir completion") {
                        val needCompletion = actionsManager.execute(actions, user)

                        needCompletion shouldBe Pair(false, null)
                    }
                }

                context("se alguma action precisar de completion") {
                    test("deve pedir completion") {
                        every {
                            actionExecutorLocator.locate(ActionType.MARK_BILLS_AS_PAID).execute(any())
                        } returns ActionResult.WithCompletion(systemMessageToCompletion = "", actionType = ActionType.MARK_BILLS_AS_PAID).right()

                        val needCompletion = actionsManager.execute(actions, user)

                        needCompletion shouldBe Pair(true, null)
                    }
                }

                context("se alguma action precisar de completion com mensagem") {
                    test("deve pedir completion com mensagem") {
                        every {
                            actionExecutorLocator.locate(ActionType.MARK_BILLS_AS_PAID).execute(any())
                        } returns ActionResult.WithCompletion(systemMessageToCompletion = "minha mensagem", actionType = ActionType.MARK_BILLS_AS_PAID).right()

                        val needCompletion = actionsManager.execute(actions, user)

                        needCompletion shouldBe Pair(true, "minha mensagem")
                    }
                }
            }

            context("quando uma action falhar") {

                val failedAction = ActionType.MARK_BILLS_AS_PAID
                beforeTest {
                    every {
                        actionExecutorLocator.locate(failedAction).execute(any())
                    } returns ActionError(false, "falha fake", null, ActionType.MARK_BILLS_AS_PAID).left()
                }

                test("deve interromper o processamento") {
                    actionsManager.execute(actions, user)

                    verify {
                        availableActions.filter { it.priority < failedAction.priority }.forEach {
                            val currentExecutor = actionExecutorLocator.locate(it)
                            currentExecutor.execute(any())
                        }
                    }

                    verify(exactly = 0) {
                        availableActions.filter { it.priority > failedAction.priority }.forEach {
                            val currentExecutor = actionExecutorLocator.locate(it)
                            currentExecutor.execute(any())
                        }
                    }
                }

                context("se a falha precisar de completion") {
                    beforeTest {
                        every {
                            actionExecutorLocator.locate(failedAction).execute(any())
                        } returns ActionError(true, "falha fake", null, failedAction).left()
                    }
                    test("deve pedir completion") {
                        val needCompletion = actionsManager.execute(actions, user)

                        needCompletion.filterIsInstance<ActionResult.WithCompletion>() shouldBe true
                    }
                }

                context("se a falha não precisar de completion") {
                    test("deve pedir completion") {
                        every {
                            actionExecutorLocator.locate(ActionType.MSG).execute(any())
                        } returns ActionResult.WithCompletion(systemMessageToCompletion = "", actionType = ActionType.MSG).right()

                        every {
                            actionExecutorLocator.locate(failedAction).execute(any())
                        } returns ActionError(false, "falha fake", null, failedAction).left()

                        val needCompletion = actionsManager.execute(actions, user)

                        needCompletion shouldBe Pair(false, null)
                    }
                }

                context("quando a falha for uma exception") {
                    beforeTest {
                        every {
                            actionExecutorLocator.locate(failedAction).execute(any())
                        } throws Exception("falha fake")
                    }
                    test("nao deve pedir completion e a mensagem tem que ser a padrão") {
                        val needCompletion = actionsManager.execute(actions, user)

                        needCompletion shouldBe Pair(false, null)
                        verify {
                            notificationService.notify(userId, any(), DEFAULT_ERROR_MESSAGE)
                        }
                    }
                }
            }
        },
    )