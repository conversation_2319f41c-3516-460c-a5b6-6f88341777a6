package ai.chatbot.app.conversation.actions

import DynamoD<PERSON><PERSON>tils.setupDynamoDB
import ai.chatbot.adapters.api.TransactionPaymentStatus
import ai.chatbot.adapters.dynamodb.TransactionDbRepository
import ai.chatbot.adapters.dynamodb.TransactionDynamoDAO
import ai.chatbot.app.DYNAMOB_TEST_TABLE_NAME
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.PixLimitStatus
import ai.chatbot.app.PixValidationResult
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.SweepingParticipant
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.balance
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.Action
import ai.chatbot.app.conversation.ActionExecutorCommand
import ai.chatbot.app.conversation.ActionResult
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.getCustomNotificationServiceSpy
import ai.chatbot.app.mockNotificationConfigs
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.NotificationMap
import ai.chatbot.app.notification.NotificationParam
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.transaction.DefaultTransactionService
import ai.chatbot.app.transaction.PixTransactionDetails
import ai.chatbot.app.transaction.SendPixCodeTransactionDetails
import ai.chatbot.app.transaction.SweepingTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionDetails
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.transaction.TransactionStatus
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import arrow.core.left
import arrow.core.right
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.context.ApplicationContext
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.util.*

class SelectSweepingAccountExecutorTest() : DescribeSpec() {

    override fun isolationMode() = IsolationMode.InstancePerTest

    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val mockedApplicationContext = mockk<ApplicationContext> {
        every { getProperty(any<String>(), String::class.java) } returns Optional.of(DYNAMOB_TEST_TABLE_NAME)
    }

    private val tenantService = mockk<TenantService>()

    private val transactionRepository = TransactionDbRepository(
        dynamoDbDAO = TransactionDynamoDAO(cli = dynamoDbEnhancedClient, mockedApplicationContext),
        tenantService,
    )

    private val defaultTransactionService = DefaultTransactionService(
        transactionRepository = transactionRepository,
        paymentAdapter = mockk(),
    )

    private val customNotificationService: CustomNotificationService = getCustomNotificationServiceSpy()

    private val walletId = WalletId(UUID.randomUUID().toString())
    private val user =
        User(
            accountId = AccountId(value = "ACCOUNT-ID"),
            id = UserId("*************"),
            name = "Nome",
            accountGroups = emptyList(),
            status = AccountStatus.ACTIVE,
        )

    private val pixValidationResult = PixValidationResult(
        keyType = PixKeyType.EVP,
        keyValue = "chave-pix",
        recipientName = "Recipient",
        recipientInstitution = "Banco Teste",
        recipientDocument = "***********",
        pixLimitStatus = PixLimitStatus.AVAILABLE,
    )

    private fun setupPixTransactionDetails(sweepingParticipantId: SweepingParticipantId?) = PixTransactionDetails(
        amount = 50_00,
        pixKey = PixKey(pixValidationResult.keyValue, pixValidationResult.keyType),
        recipientName = pixValidationResult.recipientName,
        recipientDocument = pixValidationResult.recipientDocument,
        recipientInstitution = pixValidationResult.recipientInstitution,
        sweepingAmount = 40_00,
        sweepingParticipantId = sweepingParticipantId,
        qrCode = null,
    )

    private fun setupSweepingTransactionDetails(sweepingParticipantId: SweepingParticipantId) = SweepingTransactionDetails(
        amount = 50_00,
        sweepingParticipantId = sweepingParticipantId,
        bills = emptyList(),
    )

    private fun setupTransaction(transactionId: TransactionId, transactionGroupId: TransactionGroupId, details: TransactionDetails): Transaction {
        return Transaction(
            id = transactionId,
            groupId = transactionGroupId,
            userId = user.id,
            walletId = WalletId(UUID.randomUUID().toString()),
            status = TransactionStatus.ACTIVE,
            details = details,
            paymentStatus = TransactionPaymentStatus.UNKNOWN,
        ).also {
            transactionRepository.save(it)
        }
    }

    private fun buildSweepingConsent(participant: String) = SweepingConsent(
        participant = SweepingParticipant(
            id = SweepingParticipantId(),
            name = "$participant-name",
            shortName = "$participant-shortName",
        ),
        transactionLimit = 1000L,
        lastSuccessfulCashIn = null,
    )

    val consent1 = buildSweepingConsent("participant1")
    val consent2 = buildSweepingConsent("participant2")
    val consent3 = buildSweepingConsent("participant3")

    val activeConsents = listOf(
        consent1,
        consent2,
        consent3,
    )

    private val conversationHistoryService: ConversationHistoryService = mockk()

    private val paymentAdapter: PaymentAdapter = mockk()

    private val selectSweepingAccountExecutor = SelectSweepingAccountExecutor(
        transactionService = defaultTransactionService,
        customNotificationService = customNotificationService,
        conversationHistoryService = conversationHistoryService,
        paymentAdapter = paymentAdapter,
    )

    private fun setupState(consents: List<SweepingConsent> = activeConsents, currentWalletId: WalletId? = walletId): BillComingDueHistoryState {
        val originalState = BillComingDueHistoryState(
            user = user,
            walletWithBills = WalletWithBills(
                bills = listOf(),
                walletId = currentWalletId,
                walletName = "Wallet Name",
                activeConsents = consents,
            ),
            internalStateControl = InternalStateControl(
                shouldSynchronizeBeforeCompletion = false,
                billComingDueNotifiedAt = LocalDateTime.now().minusHours(1),
            ),
            balance = balance,
            startDate = LocalDate.now(),
            endDate = LocalDate.now(),
            contacts = emptyList(),
            lastBillsUpdatedAt = ZonedDateTime.now(),
            subscription = null,
        )

        every {
            conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(any())
        } returns originalState

        return originalState
    }

    init {
        beforeEach {

            every { tenantService.getTenantName() } returns "friday"

            every { customNotificationService.config() } returns mockNotificationConfigs

            every {
                paymentAdapter.getOpenFinanceBalances(user.id, walletId, any())
            } returns mapOf(
                consent1.participant.id to 10L,
                consent2.participant.id to null,
                consent3.participant.id to 15L,
            ).right()
        }

        describe("ao selecionar as contas conectadas a partir de um transactionGroup") {
            describe("quando a transação é inválida") {
                describe("por não ser do tipo suportado") {
                    it("deve retornar erro generico") {
                        setupState()
                        val transaction = setupTransaction(TransactionId("transaction1"), TransactionGroupId(), SendPixCodeTransactionDetails(emptyList()))

                        val result = selectSweepingAccountExecutor.execute(
                            command = transaction.groupId.toCommand(),
                        )

                        result.isLeft() shouldBe true
                        result.mapLeft {
                            it.needCompletion shouldBe false
                            it.message shouldBe "O estado da transação não é válido"
                            it.actionType shouldBe ActionType.SELECT_SWEEPING_ACCOUNT
                        }
                    }
                }

                describe("por ter menos de três") {
                    it("deve retornar erro generico") {
                        setupState()
                        val transaction = setupTransaction(TransactionId("transaction1"), TransactionGroupId(), setupPixTransactionDetails(consent1.participant.id))

                        val result = selectSweepingAccountExecutor.execute(
                            command = transaction.groupId.toCommand(),
                        )

                        result.isLeft() shouldBe true
                        result.mapLeft {
                            it.needCompletion shouldBe false
                            it.message shouldBe "O estado da transação não é válido"
                            it.actionType shouldBe ActionType.SELECT_SWEEPING_ACCOUNT
                        }
                    }
                }

                describe("por não ter o id do participante") {
                    it("deve retornar erro generico") {
                        setupState()
                        val transaction = setupTransaction(TransactionId("transaction1"), TransactionGroupId(), setupPixTransactionDetails(sweepingParticipantId = null))

                        val result = selectSweepingAccountExecutor.execute(
                            command = transaction.groupId.toCommand(),
                        )

                        result.isLeft() shouldBe true
                        result.mapLeft {
                            it.needCompletion shouldBe false
                            it.message shouldBe "O estado da transação não é válido"
                            it.actionType shouldBe ActionType.SELECT_SWEEPING_ACCOUNT
                        }
                    }
                }
            }

            describe("quando o estado do usuario é inválido") {
                describe("por não ter o consentimento usado na transação") {
                    it("deve retornar erro generico") {
                        setupState(consents = emptyList())
                        val transactionGroupId = TransactionGroupId()

                        setupTransaction(TransactionId("transaction1"), transactionGroupId, setupPixTransactionDetails(consent1.participant.id))
                        setupTransaction(TransactionId("transaction2"), transactionGroupId, setupPixTransactionDetails(consent2.participant.id))
                        setupTransaction(TransactionId("transaction3"), transactionGroupId, setupPixTransactionDetails(consent3.participant.id))

                        val result = selectSweepingAccountExecutor.execute(
                            command = transactionGroupId.toCommand(),
                        )

                        result.isLeft() shouldBe true
                        result.mapLeft {
                            it.needCompletion shouldBe false
                            it.message shouldBe "O estado da transação não é válido"
                            it.actionType shouldBe ActionType.SELECT_SWEEPING_ACCOUNT
                        }
                    }
                }

                describe("por não ter o id da carteira") {
                    it("deve retornar erro generico") {
                        setupState(currentWalletId = null)
                        val transactionGroupId = TransactionGroupId()

                        setupTransaction(TransactionId("transaction1"), transactionGroupId, setupPixTransactionDetails(consent1.participant.id))
                        setupTransaction(TransactionId("transaction2"), transactionGroupId, setupPixTransactionDetails(consent2.participant.id))
                        setupTransaction(TransactionId("transaction3"), transactionGroupId, setupPixTransactionDetails(consent3.participant.id))

                        val result = selectSweepingAccountExecutor.execute(
                            command = transactionGroupId.toCommand(),
                        )

                        result.isLeft() shouldBe true
                        result.mapLeft {
                            it.needCompletion shouldBe false
                            it.message shouldBe "O estado da transação não é válido"
                            it.actionType shouldBe ActionType.SELECT_SWEEPING_ACCOUNT
                        }
                    }
                }
            }

            describe("quando nao consegue consultar o saldo") {
                it("deve enviar a notificação com o nome das contas e saldo indisponível") {
                    setupState()
                    val transactionGroupId = TransactionGroupId()

                    val transaction1 = setupTransaction(TransactionId("transaction1"), transactionGroupId, setupPixTransactionDetails(consent1.participant.id))
                    val transaction2 = setupTransaction(TransactionId("transaction2"), transactionGroupId, setupPixTransactionDetails(consent2.participant.id))
                    val transaction3 = setupTransaction(TransactionId("transaction3"), transactionGroupId, setupPixTransactionDetails(consent3.participant.id))

                    every {
                        paymentAdapter.getOpenFinanceBalances(user.id, walletId, any())
                    } returns PaymentAdapterError.NotFoundError.left()

                    val result = selectSweepingAccountExecutor.execute(transactionGroupId.toCommand())

                    result.isRight() shouldBe true
                    result.map {
                        it.shouldBeTypeOf<ActionResult.WithoutCompletion>()
                        it.actionType shouldBe ActionType.SELECT_SWEEPING_ACCOUNT
                    }

                    val participantIdsSlot = slot<List<SweepingParticipantId>>()
                    verify {
                        paymentAdapter.getOpenFinanceBalances(user.id, walletId, capture(participantIdsSlot))
                    }

                    val paramsSlot = slot<List<NotificationMap>>()
                    verify {
                        customNotificationService.send(
                            user = user,
                            notificationConfig = mockNotificationConfigs.selectSweepingAccount,
                            params = capture(paramsSlot),
                        )
                    }
                    with(paramsSlot.captured) {
                        shouldHave(NotificationParam.CONTA1_NOME, consent1.participant.name)
                        shouldHave(NotificationParam.CONTA1_NOME_CURTO, consent1.participant.shortName)
                        shouldHave(NotificationParam.CONTA1_SALDO, "Indisponível.")
                        shouldHave(NotificationParam.TRANSACTION_ID, transaction1.id.value)

                        shouldHave(NotificationParam.CONTA2_NOME, consent2.participant.name)
                        shouldHave(NotificationParam.CONTA2_NOME_CURTO, consent2.participant.shortName)
                        shouldHave(NotificationParam.CONTA2_SALDO, "Indisponível.")
                        shouldHave(NotificationParam.TRANSACTION_ID_2, transaction2.id.value)

                        shouldHave(NotificationParam.CONTA3_NOME, consent3.participant.name)
                        shouldHave(NotificationParam.CONTA3_NOME_CURTO, consent3.participant.shortName)
                        shouldHave(NotificationParam.CONTA3_SALDO, "Indisponível.")
                        shouldHave(NotificationParam.TRANSACTION_ID_3, transaction3.id.value)
                    }
                }
            }

            describe("quando é um PIX") {
                it("deve enviar a notificação com o nome e saldo das contas") {
                    setupState()
                    val transactionGroupId = TransactionGroupId()

                    val transaction1 = setupTransaction(TransactionId("transaction1"), transactionGroupId, setupPixTransactionDetails(consent1.participant.id))
                    val transaction2 = setupTransaction(TransactionId("transaction2"), transactionGroupId, setupPixTransactionDetails(consent2.participant.id))
                    val transaction3 = setupTransaction(TransactionId("transaction3"), transactionGroupId, setupPixTransactionDetails(consent3.participant.id))

                    val result = selectSweepingAccountExecutor.execute(transactionGroupId.toCommand())

                    result.isRight() shouldBe true
                    result.map {
                        it.shouldBeTypeOf<ActionResult.WithoutCompletion>()
                        it.actionType shouldBe ActionType.SELECT_SWEEPING_ACCOUNT
                    }

                    val participantIdsSlot = slot<List<SweepingParticipantId>>()
                    verify {
                        paymentAdapter.getOpenFinanceBalances(user.id, walletId, capture(participantIdsSlot))
                    }

                    val paramsSlot = slot<List<NotificationMap>>()
                    verify {
                        customNotificationService.send(
                            user = user,
                            notificationConfig = mockNotificationConfigs.selectSweepingAccount,
                            params = capture(paramsSlot),
                        )
                    }
                    with(paramsSlot.captured) {
                        shouldHave(NotificationParam.CONTA1_NOME, consent1.participant.name)
                        shouldHave(NotificationParam.CONTA1_NOME_CURTO, consent1.participant.shortName)
                        shouldHave(NotificationParam.CONTA1_SALDO, "R$ 0,10")
                        shouldHave(NotificationParam.TRANSACTION_ID, transaction1.id.value)

                        shouldHave(NotificationParam.CONTA2_NOME, consent2.participant.name)
                        shouldHave(NotificationParam.CONTA2_NOME_CURTO, consent2.participant.shortName)
                        shouldHave(NotificationParam.CONTA2_SALDO, "Indisponível.")
                        shouldHave(NotificationParam.TRANSACTION_ID_2, transaction2.id.value)

                        shouldHave(NotificationParam.CONTA3_NOME, consent3.participant.name)
                        shouldHave(NotificationParam.CONTA3_NOME_CURTO, consent3.participant.shortName)
                        shouldHave(NotificationParam.CONTA3_SALDO, "R$ 0,15")
                        shouldHave(NotificationParam.TRANSACTION_ID_3, transaction3.id.value)
                    }
                }
            }

            describe("quando é um SCHEDULE") {
                it("deve enviar a notificação com o nome e saldo das contas") {
                    setupState()
                    val transactionGroupId = TransactionGroupId()

                    val transaction1 = setupTransaction(TransactionId("transaction1"), transactionGroupId, setupSweepingTransactionDetails(consent1.participant.id))
                    val transaction2 = setupTransaction(TransactionId("transaction2"), transactionGroupId, setupSweepingTransactionDetails(consent2.participant.id))
                    val transaction3 = setupTransaction(TransactionId("transaction3"), transactionGroupId, setupSweepingTransactionDetails(consent3.participant.id))

                    val result = selectSweepingAccountExecutor.execute(transactionGroupId.toCommand())

                    result.isRight() shouldBe true
                    result.map {
                        it.shouldBeTypeOf<ActionResult.WithoutCompletion>()
                        it.actionType shouldBe ActionType.SELECT_SWEEPING_ACCOUNT
                    }

                    val participantIdsSlot = slot<List<SweepingParticipantId>>()
                    verify {
                        paymentAdapter.getOpenFinanceBalances(user.id, walletId, capture(participantIdsSlot))
                    }

                    val paramsSlot = slot<List<NotificationMap>>()
                    verify {
                        customNotificationService.send(
                            user = user,
                            notificationConfig = mockNotificationConfigs.selectSweepingAccount,
                            params = capture(paramsSlot),
                        )
                    }
                    with(paramsSlot.captured) {
                        shouldHave(NotificationParam.CONTA1_NOME, consent1.participant.name)
                        shouldHave(NotificationParam.CONTA1_NOME_CURTO, consent1.participant.shortName)
                        shouldHave(NotificationParam.CONTA1_SALDO, "R$ 0,10")
                        shouldHave(NotificationParam.TRANSACTION_ID, transaction1.id.value)

                        shouldHave(NotificationParam.CONTA2_NOME, consent2.participant.name)
                        shouldHave(NotificationParam.CONTA2_NOME_CURTO, consent2.participant.shortName)
                        shouldHave(NotificationParam.CONTA2_SALDO, "Indisponível.")
                        shouldHave(NotificationParam.TRANSACTION_ID_2, transaction2.id.value)

                        shouldHave(NotificationParam.CONTA3_NOME, consent3.participant.name)
                        shouldHave(NotificationParam.CONTA3_NOME_CURTO, consent3.participant.shortName)
                        shouldHave(NotificationParam.CONTA3_SALDO, "R$ 0,15")
                        shouldHave(NotificationParam.TRANSACTION_ID_3, transaction3.id.value)
                    }
                }
            }
        }
    }

    private fun List<NotificationMap>.shouldHave(param: NotificationParam, value: String) {
        this.single {
            it.param == param
        }.value shouldBe value
    }

    private fun TransactionGroupId.toCommand() = ActionExecutorCommand.SelectSweepingAccountCommand(
        action = Action.SelectSweepingAccount(
            transactionGroupId = this,
        ),
        user = user,
    )
}