package ai.chatbot.app.conversation.actions

import ai.chatbot.app.NotificationService
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.Action
import ai.chatbot.app.conversation.ActionExecutorCommand
import ai.chatbot.app.conversation.ActionResult
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.MarkReminderAsDoneAction
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.FridayNotificationContextTemplatesService
import ai.chatbot.app.notification.KnownTemplateConfigurationKeys
import ai.chatbot.app.tenantConfiguration
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import arrow.core.left
import arrow.core.right
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify

class MarkReminderAsDoneExecutorTest : DescribeSpec() {
    private val tenantService = mockk<TenantService>()

    private val paymentAdapter = mockk<PaymentAdapter>()
    private val notificationService = mockk<NotificationService>()
    private val executor = MarkReminderAsDoneExecutor(paymentAdapter, notificationService, buildNotificationService = BuildNotificationService(FridayNotificationContextTemplatesService(), tenantService = tenantService))
    private val userId = UserId("*************")

    private val user =
        User(
            accountId = AccountId(value = "ACCOUNT-ID"),
            id = userId,
            name = "Nome",
            accountGroups = emptyList(),
            status = AccountStatus.ACTIVE,
        )

    init {
        beforeEach {
            clearAllMocks()

            every { tenantService.getConfiguration() } returns tenantConfiguration().copy(appBaseUrl = "https://app.friday.ai")
        }

        describe("ao executar command") {
            it("deve chamar o adapter") {
                coEvery { paymentAdapter.markReminderAsDone(any(), any()) } returns Unit.right()
                every { notificationService.notify(any()) } returns Unit

                val command =
                    ActionExecutorCommand.MarkReminderAsDoneActionExecutorCommand(
                        action =
                        Action.MarkReminderAsDone(
                            payload =
                            MarkReminderAsDoneAction(
                                reminderId = "reminder1",
                            ),
                        ),
                        user = user,
                    )

                val result = executor.execute(command = command)

                result.isRight() shouldBe true

                result.getOrNull() shouldBe ActionResult.WithoutCompletion(actionType = ActionType.MARK_REMINDER_AS_DONE)

                coVerify { paymentAdapter.markReminderAsDone(userId = userId, reminderId = "reminder1") }

                val slot = slot<ChatbotRawTemplatedNotification>()
                verify { notificationService.notify(capture(slot)) }

                slot.captured.configurationKey shouldBe KnownTemplateConfigurationKeys.reminderNotificationResponseSuccess
                slot.captured.arguments shouldBe emptyMap()
                slot.captured.mobilePhone shouldBe user.id.value
                slot.captured.accountId shouldBe user.accountId
            }

            it("deve falhar command quando houver erro na chamada") {
                coEvery { paymentAdapter.markReminderAsDone(any(), any()) } returns PaymentAdapterError.ReminderError.left()
                every { notificationService.notify(any()) } returns Unit

                val command =
                    ActionExecutorCommand.MarkReminderAsDoneActionExecutorCommand(
                        action =
                        Action.MarkReminderAsDone(
                            payload =
                            MarkReminderAsDoneAction(
                                reminderId = "reminder1",
                            ),
                        ),
                        user = user,
                    )

                val result = executor.execute(command = command)

                result.isLeft() shouldBe true
                result.mapLeft {
                    it.needCompletion shouldBe false
                    it.message shouldBe "Não foi possível marcar como resolvido."
                }

                coVerify { paymentAdapter.markReminderAsDone(userId = userId, reminderId = "reminder1") }

                val slot = slot<ChatbotRawTemplatedNotification>()
                verify { notificationService.notify(capture(slot)) }

                slot.captured.configurationKey shouldBe KnownTemplateConfigurationKeys.reminderNotificationResponseError
                slot.captured.arguments shouldBe emptyMap()
                slot.captured.mobilePhone shouldBe user.id.value
                slot.captured.accountId shouldBe user.accountId
            }
        }
    }
}