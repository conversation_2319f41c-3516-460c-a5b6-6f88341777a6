package ai.chatbot.app.conversation.actions

import ai.chatbot.adapters.api.TransactionPaymentStatus
import ai.chatbot.adapters.waCommCentre.ClientId
import ai.chatbot.app.AvailableLimitResponse
import ai.chatbot.app.AvailableLimitType
import ai.chatbot.app.NotificationService
import ai.chatbot.app.OnePixPayInstrumentation
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.SweepingParticipant
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.TransactionService
import ai.chatbot.app.balance
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.billView
import ai.chatbot.app.billView2
import ai.chatbot.app.billView3
import ai.chatbot.app.conversation.Action
import ai.chatbot.app.conversation.ActionExecutorCommand
import ai.chatbot.app.conversation.ActionResult
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.conversation.PaymentType
import ai.chatbot.app.getCustomNotificationServiceMock
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.ChatbotWhatsappSimpleNotification
import ai.chatbot.app.notification.KnownTemplateConfigurationKeys
import ai.chatbot.app.notification.NotificationContextTemplatesService
import ai.chatbot.app.notification.NotificationMap
import ai.chatbot.app.notification.NotificationParam
import ai.chatbot.app.notification.QuickReplyButton
import ai.chatbot.app.notification.TextNotificationConfig
import ai.chatbot.app.payment.Balance
import ai.chatbot.app.pix.PixQRCode
import ai.chatbot.app.transaction.ScheduleBillsTransactionDetails
import ai.chatbot.app.transaction.SendPixCodeTransactionDetails
import ai.chatbot.app.transaction.SweepingTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionDetails
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.transaction.TransactionStatus
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.brazilDateFormat
import arrow.atomic.AtomicInt
import arrow.core.left
import arrow.core.right
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.util.UUID

class MakePaymentExecutorTest : DescribeSpec() {
    private val paymentAdapter: PaymentAdapter = mockk()
    private val conversationHistoryService: ConversationHistoryService = mockk()
    private val transactionService: TransactionService = mockk()
    private val notificationService: NotificationService = mockk(relaxed = true)
    private val buildNotificationService: BuildNotificationService = mockk()
    private val notificationContextTemplatesService: NotificationContextTemplatesService = mockk(relaxed = true)
    private val onePixPayInstrumentation: OnePixPayInstrumentation = mockk(relaxed = true)
    private val customNotificationService = getCustomNotificationServiceMock()
    private val actionExecutorsHelpers = ActionExecutorsHelpers(
        paymentAdapter = paymentAdapter,
        conversationHistoryService = conversationHistoryService,
        notificationService = notificationService,
        onePixPayInstrumentation = onePixPayInstrumentation,
        transactionService = transactionService,
        buildNotificationService = buildNotificationService,
        customNotificationService = customNotificationService,
        notificationContextTemplatesService = notificationContextTemplatesService,
    )

    private val executor = MakePaymentExecutor(
        notificationService = notificationService,
        conversationHistoryService = conversationHistoryService,
        onePixPayInstrumentation = onePixPayInstrumentation,
        buildNotificationService = buildNotificationService,
        actionExecutorsHelpers = actionExecutorsHelpers,
        transactionService = transactionService,
    )

    private val user =
        User(
            accountId = AccountId(value = "ACCOUNT-ID"),
            id = UserId("*************"),
            name = "Nome",
            accountGroups = emptyList(),
            status = AccountStatus.ACTIVE,
        )

    private val mockedAuthorizeTransactionNotification = ChatbotRawTemplatedNotification(
        accountId = AccountId("account-id"),
        mobilePhone = user.id.value,
        clientId = ClientId("client-id"),
        configurationKey = KnownTemplateConfigurationKeys.authorizeScheduleBills,
    )

    private val mockedScheduledAmountExceedsSweepingTransactionLimitNotification = ChatbotRawTemplatedNotification(
        accountId = AccountId("account-id"),
        mobilePhone = user.id.value,
        clientId = ClientId("client-id"),
        configurationKey = KnownTemplateConfigurationKeys.scheduledAmountExceedsSweepingLimit,
    )

    private val mockConfirmationNotification = ChatbotWhatsappSimpleNotification(
        mobilePhone = user.id.value,
        accountId = user.accountId,
        message = "payment-confirmation",
        quickReplyButtons = listOf(QuickReplyButton("Sim", "PAYLOAD_SIM"), QuickReplyButton("Não", "PAYLOAD_NAO")),
    )

    private val walletId = WalletId(UUID.randomUUID().toString())

    override fun isolationMode() = IsolationMode.InstancePerTest

    init {
        beforeEach {

            every {
                paymentAdapter.getOpenFinanceBalances(user.id, walletId, any())
            } answers {
                val index = AtomicInt(0)
                (thirdArg() as List<SweepingParticipantId>).associate {
                    it to 50L + index.getAndIncrement()
                }.right()
            }

            coEvery {
                paymentAdapter.getBalanceAndForecast(user.id, walletId)
            } returns Balance(current = 50L, open = mockk(), onlyScheduled = mockk(), walletId = walletId).right()
        }

        describe("quando o tipo de pagamento for PIX") {
            it("deve enviar código pix") {
                setup(
                    bills = listOf(billView, billView2, billView3),
                    currentBalance = 0L,
                    scheduleBillsError = null,
                )

                val result = executor.execute(
                    buildCommand(selectedBills = listOf(1, 2), type = PaymentType.PIX, confirmation = false),
                )

                coVerify { paymentAdapter.generateOnePixPay(user.id, listOf(billView.billId, billView2.billId), any()) }
                verify { notificationService.notify(any(), any(), "qrcode", delay = any()) }

                result.isRight() shouldBe true
                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MAKE_PAYMENT) }
            }
        }

        describe("quando o tipo de pagamento for SCHEDULE") {

            describe("se o usuário selecionou algumas contas para pagar") {
                describe("e tem saldo suficiente") {
                    it("deve criar a transação somente com as contas selecionadas") {
                        every { transactionService.create(any(), any(), any(), any()) } returns buildScheduleBillsTransaction(listOf(billView.billId, billView2.billId))
                        every { buildNotificationService.buildSweepingAccountBillsScheduleConfirmation(any(), any(), any(), any(), InterceptMessagePayloadType.TRANSACTION_CONFIRM) } returns mockConfirmationNotification

                        setup(
                            bills = listOf(billView, billView2, billView3),
                            currentBalance = billView.amountTotal + billView2.amountTotal + 100_00,
                            scheduleBillsError = PaymentAdapterError.AssistantLimitExceeded,
                            activeConsents = emptyList(),
                        )

                        val result = executor.execute(
                            buildCommand(selectedBills = listOf(1, 2), type = PaymentType.SCHEDULE, confirmation = true),
                        )

                        result.isRight() shouldBe true
                        result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MAKE_PAYMENT) }

                        val detailsSlot = slot<TransactionDetails>()
                        verify {
                            transactionService.create(user.id, walletId, capture(detailsSlot), any())
                        }
                        with(detailsSlot.captured) {
                            this.shouldBeTypeOf<ScheduleBillsTransactionDetails>()
                            this.bills shouldContainExactlyInAnyOrder listOf(billView.billId, billView2.billId)
                        }
                    }
                }
                describe("e não tem saldo suficiente mas tem UMA conta conectada") {
                    it("deve criar a transação somente com as contas selecionadas") {
                        setupTransaction()

                        every { buildNotificationService.buildScheduledAmountExceedsSweepingTransactionLimit(any(), any(), any()) } returns mockedScheduledAmountExceedsSweepingTransactionLimitNotification

                        val activeConsents = setupActiveConsents(1, transactionLimit = billView.amountTotal + billView2.amountTotal)
                        setup(
                            bills = listOf(billView, billView2, billView3),
                            currentBalance = 0,
                            scheduleBillsError = PaymentAdapterError.AssistantLimitExceeded,
                            activeConsents = activeConsents,
                        )

                        val command = buildCommand(selectedBills = listOf(1, 2), type = PaymentType.SCHEDULE)
                        val result = executor.execute(command)

                        result.isRight() shouldBe true
                        result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MAKE_PAYMENT) }

                        verifySweepingTransaction(
                            userId = command.user.id,
                            expectedBills = listOf(billView, billView2),
                            activeConsents = activeConsents,
                        )
                    }
                }
            }

            describe("se o saldo é suficiente") {

                describe("mas o limite do WA NÃO é suficiente") {
                    it("deve criar transação e pedir autorização no app") {
                        val transaction = buildScheduleBillsTransaction(listOf(billView.billId, billView2.billId))
                        every { transactionService.create(any(), any(), any(), any()) } returns transaction

                        setup(
                            bills = listOf(billView, billView2, billView3),
                            currentBalance = billView.amountTotal + billView2.amountTotal + 100_00,
                            scheduleBillsError = PaymentAdapterError.AssistantLimitExceeded,
                        )

                        val result = executor.execute(
                            buildCommand(selectedBills = listOf(1, 2), type = PaymentType.SCHEDULE),
                        )

                        result.isRight() shouldBe true
                        result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MAKE_PAYMENT) }

                        val details = slot<TransactionDetails>()
                        verify { transactionService.create(user.id, any(), capture(details)) }

                        (details.captured is ScheduleBillsTransactionDetails) shouldBe true
                        (details.captured as ScheduleBillsTransactionDetails).bills shouldBe listOf(
                            billView.billId,
                            billView2.billId,
                        )

                        val billsSlot = slot<List<BillView>>()
                        verify {
                            buildNotificationService.buildAuthorizeScheduleBillsNotification(
                                accountId = user.accountId,
                                mobilePhone = user.id.value,
                                bills = capture(billsSlot),
                                transactionId = transaction.id,
                            )
                            notificationService.notify(notification = mockedAuthorizeTransactionNotification)
                        }
                        billsSlot.captured.shouldContainExactlyInAnyOrder(listOf(billView, billView2))
                    }
                }
                describe("e o limite do WA é suficiente") {
                    it("deve agendar contas") {
                        setup(
                            bills = listOf(billView, billView2, billView3),
                            currentBalance = billView.amountTotal + billView2.amountTotal + 100_00,
                            scheduleBillsError = null,
                        )

                        val result = executor.execute(
                            buildCommand(selectedBills = listOf(1, 2), type = PaymentType.SCHEDULE),
                        )

                        result.isRight() shouldBe true
                        result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MAKE_PAYMENT) }

                        coVerify {
                            paymentAdapter.scheduleBills(
                                userId = user.id,
                                bills = listOf(billView.billId, billView2.billId),
                                walletId = walletId,
                                sweepingRequest = null,
                            )
                        }

                        verify(exactly = 0) { transactionService.create(any(), any(), any(), any()) }
                    }
                }
            }

            describe("se o saldo não for suficiente") {
                describe("quando não houver conta conectada") {
                    it("deve enviar código pix") {
                        setup(
                            bills = listOf(billView, billView2, billView3),
                            currentBalance = 0L,
                            scheduleBillsError = null,
                        )

                        val result = executor.execute(
                            buildCommand(selectedBills = listOf(1, 2), type = PaymentType.SCHEDULE),
                        )

                        coVerify { paymentAdapter.generateOnePixPay(user.id, listOf(billView.billId, billView2.billId), any()) }
                        verify { notificationService.notify(any(), any(), "qrcode", delay = any()) }

                        result.isRight() shouldBe true
                        result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MAKE_PAYMENT) }
                    }
                }

                describe("quando houver conta conectada") {

                    describe("e nenhuma conta conectada tem limite por transação suficiente") {
                        it("deve perguntar se quer refazer a conexão ou enviar código pix") {
                            setup(
                                bills = listOf(billView, billView2, billView3),
                                currentBalance = 0L,
                                scheduleBillsError = null,
                                activeConsents = setupActiveConsents(2, billView.amountTotal + billView2.amountTotal - 1),
                            )

                            val transaction = mockTransaction(transactionDetails = mockk())
                            every {
                                transactionService.create(any(), any(), any(), any())
                            } returns transaction

                            val transactionIdSlot = slot<TransactionId>()
                            every { buildNotificationService.buildScheduledAmountExceedsSweepingTransactionLimit(any(), any(), capture(transactionIdSlot)) } returns mockedScheduledAmountExceedsSweepingTransactionLimitNotification

                            val result = executor.execute(
                                buildCommand(selectedBills = listOf(1, 2), type = PaymentType.SCHEDULE),
                            )

                            result.isRight() shouldBe true
                            result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MAKE_PAYMENT) }

                            transactionIdSlot.captured shouldBe transaction.id

                            coVerify(exactly = 0) { paymentAdapter.generateOnePixPay(any(), any(), any()) }
                            verify { notificationService.notify(mockedScheduledAmountExceedsSweepingTransactionLimitNotification, any()) }
                        }
                    }

                    describe("e o limite por transação da conta conectada é suficiente para a transação") {

                        val dueDateMessage = "Vencimento em ${getLocalDate().format(brazilDateFormat)}"
                        val expectedBillsList = """
                                1️⃣ John Doe (conta da luz) no valor de R$ 96,97 - $dueDateMessage

                                2️⃣ Enzo (Pix para o enzo) no valor de R$ 70.000,00 - $dueDateMessage
                        """.trimIndent()

                        describe("mas o limite do WA NÃO é suficiente") {
                            describe("e tem UMA conta conectada") {
                                it("deve avisar que vai usar a conta conectada e pedir para autorizar na aplicação") {
                                    val activeConsents = setupActiveConsents(1, billView.amountTotal + billView2.amountTotal)
                                    setup(
                                        bills = listOf(billView, billView2, billView3),
                                        currentBalance = 0L,
                                        scheduleBillsError = null,
                                        activeConsents = activeConsents,
                                        availableLimitResponse = AvailableLimitResponse.ASSISTANT_LIMIT_EXCEEDED,
                                    )

                                    val transactionMap = setupTransaction()

                                    val command = buildCommand(selectedBills = listOf(1, 2), type = PaymentType.SCHEDULE)

                                    val result = executor.execute(command)

                                    result.isRight() shouldBe true
                                    result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MAKE_PAYMENT) }

                                    val transactionGroupId = verifySweepingTransaction(
                                        userId = command.user.id,
                                        expectedBills = listOf(billView, billView2),
                                        activeConsents = activeConsents,
                                    )

                                    val notificationConfigSlot = slot<TextNotificationConfig>()
                                    val paramsSlot = slot<List<NotificationMap>>()
                                    verify {
                                        customNotificationService.send(
                                            user = command.user,
                                            notificationConfig = capture(notificationConfigSlot),
                                            params = capture(paramsSlot),
                                        )
                                    }
                                    notificationConfigSlot.captured shouldBe customNotificationService.config().scheduleAuthorizationSingleSweepingConsent
                                    // paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_GROUP_ID, transactionGroupId.value)
                                    paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_ID, transactionMap[activeConsents[0].participant.id]!!.id.value)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_NOME, activeConsents.first().participant.name)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_SALDO, "R$ 0,50")
                                    paramsSlot.captured.shouldHave(NotificationParam.BILLS_LIST, expectedBillsList)
                                }
                            }

                            describe("e tem DUAS contas conectadas") {
                                it("deve avisar que vai usar a conta conectada e pedir para autorizar na aplicação") {
                                    val activeConsents = setupActiveConsents(2, billView.amountTotal + billView2.amountTotal)
                                    setup(
                                        bills = listOf(billView, billView2, billView3),
                                        currentBalance = 0L,
                                        scheduleBillsError = null,
                                        activeConsents = activeConsents,
                                        availableLimitResponse = AvailableLimitResponse.ASSISTANT_LIMIT_EXCEEDED,
                                    )

                                    val transactionMap = setupTransaction()

                                    val command = buildCommand(selectedBills = listOf(1, 2), type = PaymentType.SCHEDULE)

                                    val result = executor.execute(command)

                                    result.isRight() shouldBe true
                                    result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MAKE_PAYMENT) }

                                    val transactionGroupId = verifySweepingTransaction(
                                        userId = command.user.id,
                                        expectedBills = listOf(billView, billView2),
                                        activeConsents = activeConsents,
                                    )

                                    val notificationConfigSlot = slot<TextNotificationConfig>()
                                    val paramsSlot = slot<List<NotificationMap>>()
                                    verify {
                                        customNotificationService.send(
                                            user = command.user,
                                            notificationConfig = capture(notificationConfigSlot),
                                            params = capture(paramsSlot),
                                        )
                                    }
                                    notificationConfigSlot.captured shouldBe customNotificationService.config().scheduleAuthorizationMultipleSweepingConsent
                                    // paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_GROUP_ID, transactionGroupId.value)
                                    paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_ID, transactionMap[activeConsents[0].participant.id]!!.id.value)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_NOME, activeConsents[0].participant.name)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_SALDO, "R$ 0,50")
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA2_NOME, activeConsents[1].participant.name)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA2_SALDO, "R$ 0,51")
                                    paramsSlot.captured.shouldHave(NotificationParam.BILLS_LIST, expectedBillsList)
                                }
                            }

                            describe("e tem mais de DUAS contas conectadas") {
                                it("deve avisar que vai usar a conta conectada e pedir para autorizar na aplicação") {
                                    val activeConsents = setupActiveConsents(3, billView.amountTotal + billView2.amountTotal)
                                    setup(
                                        bills = listOf(billView, billView2, billView3),
                                        currentBalance = 0L,
                                        scheduleBillsError = null,
                                        activeConsents = activeConsents,
                                        availableLimitResponse = AvailableLimitResponse.ASSISTANT_LIMIT_EXCEEDED,
                                    )

                                    val transactionMap = setupTransaction()

                                    val command = buildCommand(selectedBills = listOf(1, 2), type = PaymentType.SCHEDULE)

                                    val result = executor.execute(command)

                                    result.isRight() shouldBe true
                                    result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MAKE_PAYMENT) }

                                    val transactionGroupId = verifySweepingTransaction(
                                        userId = command.user.id,
                                        expectedBills = listOf(billView, billView2),
                                        activeConsents = activeConsents,
                                    )

                                    val notificationConfigSlot = slot<TextNotificationConfig>()
                                    val paramsSlot = slot<List<NotificationMap>>()
                                    verify {
                                        customNotificationService.send(
                                            user = command.user,
                                            notificationConfig = capture(notificationConfigSlot),
                                            params = capture(paramsSlot),
                                        )
                                    }
                                    notificationConfigSlot.captured shouldBe customNotificationService.config().scheduleAuthorizationSelectSweepingConsent
                                    paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_GROUP_ID, transactionGroupId.value)
                                    paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_ID, transactionMap[activeConsents[0].participant.id]!!.id.value)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_NOME, activeConsents[0].participant.name)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_SALDO, "R$ 0,50")
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA2_NOME, activeConsents[1].participant.name)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA2_SALDO, "R$ 0,51")
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA3_NOME, activeConsents[2].participant.name)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA3_SALDO, "R$ 0,52")
                                    paramsSlot.captured.shouldHave(NotificationParam.BILLS_LIST, expectedBillsList)
                                }
                            }
                        }

                        describe("e o limite do WA é suficiente") {
                            describe("e tem UMA conta conectada") {
                                it("deve avisar que vai usar a conta conectada e pedir confirmação") {
                                    val activeConsents = setupActiveConsents(1, billView.amountTotal + billView2.amountTotal)
                                    setup(
                                        bills = listOf(billView, billView2, billView3),
                                        currentBalance = 0L,
                                        scheduleBillsError = null,
                                        activeConsents = activeConsents,
                                        availableLimitResponse = AvailableLimitResponse.OK,
                                    )

                                    val transactionMap = setupTransaction()

                                    val command = buildCommand(selectedBills = listOf(1, 2), type = PaymentType.SCHEDULE)

                                    val result = executor.execute(command)

                                    result.isRight() shouldBe true
                                    result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MAKE_PAYMENT) }

                                    verifySweepingTransaction(
                                        userId = command.user.id,
                                        expectedBills = listOf(billView, billView2),
                                        activeConsents = activeConsents,
                                    )

                                    val notificationConfigSlot = slot<TextNotificationConfig>()
                                    val paramsSlot = slot<List<NotificationMap>>()
                                    verify {
                                        customNotificationService.send(
                                            user = command.user,
                                            notificationConfig = capture(notificationConfigSlot),
                                            params = capture(paramsSlot),
                                        )
                                    }
                                    notificationConfigSlot.captured shouldBe customNotificationService.config().scheduleConfirmationSingleSweepingConsent
                                    paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_ID, transactionMap[activeConsents[0].participant.id]!!.id.value)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_NOME, activeConsents[0].participant.name)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_NOME_CURTO, activeConsents[0].participant.shortName)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_SALDO, "R$ 0,50")
                                    paramsSlot.captured.shouldHave(NotificationParam.BILLS_LIST, expectedBillsList)
                                }
                            }
                            describe("e tem DUAS contas conectadas") {
                                it("deve avisar que vai usar a conta conectada e pedir confirmação") {
                                    val activeConsents = setupActiveConsents(2, billView.amountTotal + billView2.amountTotal)
                                    setup(
                                        bills = listOf(billView, billView2, billView3),
                                        currentBalance = 0L,
                                        scheduleBillsError = null,
                                        activeConsents = activeConsents,
                                        availableLimitResponse = AvailableLimitResponse.OK,
                                    )

                                    val transactionMap = setupTransaction()

                                    val command = buildCommand(selectedBills = listOf(1, 2), type = PaymentType.SCHEDULE)

                                    val result = executor.execute(command)

                                    result.isRight() shouldBe true
                                    result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MAKE_PAYMENT) }

                                    verifySweepingTransaction(
                                        userId = command.user.id,
                                        expectedBills = listOf(billView, billView2),
                                        activeConsents = activeConsents,
                                    )

                                    val notificationConfigSlot = slot<TextNotificationConfig>()
                                    val paramsSlot = slot<List<NotificationMap>>()
                                    verify {
                                        customNotificationService.send(
                                            user = command.user,
                                            notificationConfig = capture(notificationConfigSlot),
                                            params = capture(paramsSlot),
                                        )
                                    }
                                    notificationConfigSlot.captured shouldBe customNotificationService.config().scheduleConfirmationMultipleSweepingConsent
                                    paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_ID, transactionMap[activeConsents[0].participant.id]!!.id.value)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_NOME, activeConsents[0].participant.name)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_NOME_CURTO, activeConsents[0].participant.shortName)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_SALDO, "R$ 0,50")
                                    paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_ID_2, transactionMap[activeConsents[1].participant.id]!!.id.value)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA2_NOME, activeConsents[1].participant.name)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA2_NOME_CURTO, activeConsents[1].participant.shortName)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA2_SALDO, "R$ 0,51")
                                    paramsSlot.captured.shouldHave(NotificationParam.BILLS_LIST, expectedBillsList)
                                }
                            }

                            describe("e tem mais de DUAS contas conectadas") {
                                it("deve avisar que vai usar a conta conectada e pedir confirmação") {
                                    val activeConsents = setupActiveConsents(3, billView.amountTotal + billView2.amountTotal)
                                    setup(
                                        bills = listOf(billView, billView2, billView3),
                                        currentBalance = 0L,
                                        scheduleBillsError = null,
                                        activeConsents = activeConsents,
                                        availableLimitResponse = AvailableLimitResponse.OK,
                                    )

                                    val transactionMap = setupTransaction()

                                    val command = buildCommand(selectedBills = listOf(1, 2), type = PaymentType.SCHEDULE)

                                    val result = executor.execute(command)

                                    result.isRight() shouldBe true
                                    result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MAKE_PAYMENT) }

                                    val transactionGroupId = verifySweepingTransaction(
                                        userId = command.user.id,
                                        expectedBills = listOf(billView, billView2),
                                        activeConsents = activeConsents,
                                    )

                                    val notificationConfigSlot = slot<TextNotificationConfig>()
                                    val paramsSlot = slot<List<NotificationMap>>()
                                    verify {
                                        customNotificationService.send(
                                            user = command.user,
                                            notificationConfig = capture(notificationConfigSlot),
                                            params = capture(paramsSlot),
                                        )
                                    }
                                    notificationConfigSlot.captured shouldBe customNotificationService.config().scheduleConfirmationSelectSweepingConsent
                                    paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_GROUP_ID, transactionGroupId.value)
                                    paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_ID, transactionMap[activeConsents[0].participant.id]!!.id.value)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_NOME, activeConsents[0].participant.name)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_NOME_CURTO, activeConsents[0].participant.shortName)
                                    paramsSlot.captured.shouldHave(NotificationParam.CONTA1_SALDO, "R$ 0,50")
                                    paramsSlot.captured.shouldHave(NotificationParam.BILLS_LIST, expectedBillsList)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun buildCommand(
        selectedBills: List<Int>,
        type: PaymentType,
        confirmation: Boolean = true,
    ) = ActionExecutorCommand.MakePaymentActionExecutorCommand(
        user = user,
        action = Action.MakePayment(
            bills = selectedBills,
            type = type.name,
            transactionId = null,
            confirmation = confirmation,
        ),
    )

    private fun buildScheduleBillsTransaction(billIds: List<BillId>) =
        Transaction(
            id = TransactionId(),
            groupId = TransactionGroupId(),
            userId = user.id,
            status = TransactionStatus.ACTIVE,
            walletId = walletId,
            details = ScheduleBillsTransactionDetails(billIds),
            paymentStatus = TransactionPaymentStatus.UNKNOWN,
        )

    private fun setup(
        bills: List<BillView>,
        currentBalance: Long,
        scheduleBillsError: PaymentAdapterError?,
        activeConsents: List<SweepingConsent> = emptyList(),
        availableLimitResponse: AvailableLimitResponse = AvailableLimitResponse.OK,
    ) {
        every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit
        every { conversationHistoryService.synchronizeBeforeCompletion(any()) } returns Unit
        every { conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(any()) } returns
            BillComingDueHistoryState(
                user = user,
                walletWithBills = WalletWithBills(
                    bills = bills,
                    walletId = walletId,
                    walletName = "Wallet Name",
                    activeConsents = activeConsents,
                ),
                internalStateControl = InternalStateControl(
                    shouldSynchronizeBeforeCompletion = false,
                    billComingDueNotifiedAt = LocalDateTime.now().minusHours(1),
                ),
                balance = balance.copy(current = currentBalance),
                startDate = LocalDate.now(),
                endDate = LocalDate.now(),
                contacts = emptyList(),
                lastBillsUpdatedAt = ZonedDateTime.now(),
                subscription = null,
            )

        val scheduleBillsResult = scheduleBillsError?.left() ?: Unit.right()

        coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any(), any()) } returns scheduleBillsResult
        coEvery { paymentAdapter.generateOnePixPay(any(), any(), any()) } returns PixQRCode("qrcode").right()

        every { buildNotificationService.buildAuthorizeScheduleBillsNotification(any(), any(), any(), any()) } returns mockedAuthorizeTransactionNotification
        every {
            paymentAdapter.validateAvailableLimit(
                userId = user.id,
                walletId = walletId,
                amount = any(), // deveria ser soma das bill - saldo da friday
                type = AvailableLimitType.SCHEDULE_BILLS,
            )
        } returns availableLimitResponse.right()
    }

    private fun setupActiveConsents(count: Int, transactionLimit: Long = 50_00) = mutableListOf<SweepingConsent>().also { activeConsents ->
        val now = getZonedDateTime()
        repeat(count) { index ->
            activeConsents.add(
                SweepingConsent(
                    participant = SweepingParticipant(
                        id = SweepingParticipantId("participantId-$index"),
                        name = "participantName-$index",
                        shortName = "participantShortName-$index",
                    ),
                    transactionLimit = transactionLimit,
                    lastSuccessfulCashIn = now.minusSeconds(index.toLong()),
                ),
            )
        }
    }

    private fun List<NotificationMap>.shouldHave(param: NotificationParam, value: String) {
        this.firstOrNull {
            it.param == param
        }?.value shouldBe value
    }

    private fun mockTransaction(transactionGroupId: TransactionGroupId = TransactionGroupId(), transactionDetails: TransactionDetails): Transaction {
        return mockk {
            every {
                id
            } returns TransactionId()
            every {
                groupId
            } returns transactionGroupId
            every {
                details
            } returns transactionDetails
        }
    }

    private fun setupTransaction(): MutableMap<SweepingParticipantId, Transaction> {
        val transactionMap = mutableMapOf<SweepingParticipantId, Transaction>()
        every {
            transactionService.create(any(), any(), any(), any())
        } answers {
            val details: TransactionDetails = arg(2)
            mockTransaction(transactionGroupId = arg(3), transactionDetails = details).also {
                if (details is SweepingTransactionDetails) {
                    transactionMap[details.sweepingParticipantId!!] = it
                }
            }
        }
        return transactionMap
    }

    private fun verifySweepingTransaction(userId: UserId, expectedBills: List<BillView>, activeConsents: List<SweepingConsent>): TransactionGroupId {
        val transactionSlot = mutableListOf<TransactionDetails>()
        val transactionGroupIdSlot = mutableListOf<TransactionGroupId>()
        verify {
            transactionService.create(
                userId = userId,
                walletId = walletId,
                details = capture(transactionSlot),
                transactionGroupId = capture(transactionGroupIdSlot),
            )
        }

        val sendPixCodeTransactionDetails = transactionSlot.filterIsInstance<SendPixCodeTransactionDetails>().singleOrNull()
        sendPixCodeTransactionDetails.shouldNotBeNull()
        sendPixCodeTransactionDetails.bills shouldContainExactlyInAnyOrder expectedBills.map { it.billId }

        val sweepingTransactions = transactionSlot.filterIsInstance<SweepingTransactionDetails>()
        sweepingTransactions.size shouldBe activeConsents.size

        sweepingTransactions.forEach { details ->
            details.amount shouldBe expectedBills.sumOf { it.amountTotal }
            details.bills shouldContainExactlyInAnyOrder expectedBills.map { it.billId }
        }

        sweepingTransactions.map {
            it.sweepingParticipantId
        }.shouldContainExactlyInAnyOrder(activeConsents.map { it.participant.id })

        // todas as transações tem que ter sido criadas com o MESMO transactionGroupId
        transactionGroupIdSlot.toSet().size shouldBe 1

        return transactionGroupIdSlot.first()
    }
}