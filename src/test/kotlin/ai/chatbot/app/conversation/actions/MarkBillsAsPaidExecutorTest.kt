package ai.chatbot.app.conversation.actions

import ai.chatbot.app.NotificationService
import ai.chatbot.app.OnePixPayInstrumentation
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.SweepingParticipant
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.TransactionService
import ai.chatbot.app.balance
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.billView
import ai.chatbot.app.billView2
import ai.chatbot.app.billView3
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.Action
import ai.chatbot.app.conversation.ActionExecutorCommand
import ai.chatbot.app.conversation.ActionResult
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.MarkBillsAsPaidOrIgnoreBillsAction
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.KnownTemplateConfigurationKeys
import ai.chatbot.app.notification.NotificationContextTemplatesService
import ai.chatbot.app.tenantConfiguration
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserActivity
import ai.chatbot.app.user.UserActivityType
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import arrow.core.right
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import io.mockk.verifyOrder
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime

class MarkBillsAsPaidExecutorTest : DescribeSpec() {
    private val tenantService = mockk<TenantService>()
    private val paymentAdapter: PaymentAdapter = mockk()
    private val conversationHistoryService: ConversationHistoryService = mockk(relaxed = true)
    private val notificationService: NotificationService = mockk(relaxed = true)
    private val onePixPayInstrumentation: OnePixPayInstrumentation = mockk(relaxed = true)
    private val notificationContextTemplatesService: NotificationContextTemplatesService = mockk(relaxed = true)
    private val buildNotificationService: BuildNotificationService = spyk(BuildNotificationService(notificationContextTemplatesService, tenantService))
    private val transactionService: TransactionService = mockk()

    private val executor = MarkBillsAsPaidExecutor(
        notificationService = notificationService,
        conversationHistoryService = conversationHistoryService,
        onePixPayInstrumentation = onePixPayInstrumentation,
        paymentAdapter = paymentAdapter,
        buildNotificationService = buildNotificationService,
        notificationContextTemplatesService = notificationContextTemplatesService,
        eventService = mockk(relaxed = true),
        transactionService = transactionService,
        tenantService = tenantService,
    )

    private val user =
        User(
            accountId = AccountId(value = "ACCOUNT-ID"),
            id = UserId("*************"),
            name = "Nome",
            accountGroups = mockk { every { contains(any()) } returns false },
            status = AccountStatus.ACTIVE,
        )

    private val mockFeatures = mockk<TenantConfiguration.FeaturesConfig>()

    private val tenantConfiguration = tenantConfiguration()

    init {
        beforeEach {
            clearAllMocks()

            every { mockFeatures.openFinanceIncentive } returns true
            every { tenantConfiguration.appBaseUrl } returns "https://app.friday.ai"
            every { tenantConfiguration.features } returns mockFeatures
            every { tenantService.getConfiguration() } returns tenantConfiguration
        }

        describe("quando tem bills para marcar como pagas") {
            it("deveria retornar sucesso") {
                setup(listOf(billView, billView2, billView3))

                val result = executor.execute(
                    buildCommand(
                        billsToMarkAsPaid = listOf(1, 2),
                        billsToIgnore = emptyList(),
                    ),
                )

                result.isRight() shouldBe true
                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MARK_BILLS_AS_PAID) }
            }

            describe("quando tentar enviar o incentivo da conta open finance") {

                it("não deveria enviar quando já foi promovida anteriormente") {
                    setup(listOf(billView, billView2, billView3), true)

                    val result = executor.execute(
                        buildCommand(
                            billsToMarkAsPaid = listOf(1, 2),
                            billsToIgnore = emptyList(),
                        ),
                    )

                    verify(exactly = 0) { notificationContextTemplatesService.getPromoteSweepingAccountMarkAsPaidMessage() }

                    result.isRight() shouldBe true
                    result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MARK_BILLS_AS_PAID) }
                }

                it("não deveria enviar quando não houver nenhuma bill para marcar como paga") {
                    setup(listOf(billView, billView2, billView3), false)

                    val result = executor.execute(
                        buildCommand(
                            billsToMarkAsPaid = emptyList(),
                            billsToIgnore = listOf(1, 2),
                        ),
                    )

                    verify(exactly = 0) { notificationContextTemplatesService.getPromoteSweepingAccountMarkAsPaidMessage() }

                    result.isRight() shouldBe true
                    result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MARK_BILLS_AS_PAID) }
                }

                it("deveria enviar quando ainda não foi promovida e existir pelo menos uma bill para marcar como paga e não for early access") {
                    setup(listOf(billView, billView2, billView3))

                    val result = executor.execute(
                        buildCommand(
                            billsToMarkAsPaid = listOf(1, 2),
                            billsToIgnore = listOf(1, 2),
                        ),
                    )
                    val markBillsAsPaidOrIgnoreNotificationMessage = """Marquei as seguintes contas como pagas:
                        |1️⃣ John Doe (conta da luz) no valor de R${'$'} 96,97
                        |2️⃣ Enzo (Pix para o enzo) no valor de R${'$'} 70.000,00

                        |E removi as seguintes contas da sua timeline:
                        |1️⃣ John Doe (conta da luz) no valor de R${'$'} 96,97
                        |2️⃣ Enzo (Pix para o enzo) no valor de R${'$'} 70.000,00
                    """.trimMargin()

                    val promoteSweepingAccountMessage = notificationContextTemplatesService.getPromoteSweepingAccountMarkAsPaidMessage()

                    verifyOrder {
                        conversationHistoryService.createAssistantMessage(userId = user.id, message = markBillsAsPaidOrIgnoreNotificationMessage)
                        conversationHistoryService.createAssistantMessage(userId = user.id, message = promoteSweepingAccountMessage)
                    }

                    result.isRight() shouldBe true
                    result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.MARK_BILLS_AS_PAID) }
                }
            }
        }

        describe("quando promover o sweeping account") {
            it("deve enviar a mensagem correta") {
                every { paymentAdapter.getUserActivities(any(), any()) } returns emptyList<UserActivity>().right()
                every { notificationContextTemplatesService.getPromoteSweepingAccountMarkAsPaidMessage() } returns "mensagem teste"

                executor.promoteSweepingAccount(
                    state = getBillComingDueHistoryState(listOf(billView, billView2, billView3), false),
                    user = getUser(true),
                    listOf(billView, billView2, billView3),
                )

                val notification = slot<ChatbotRawTemplatedNotification>()
                val delay = slot<Int>()
                val message = slot<String>()

                verify {
                    buildNotificationService.buildPromoteSweepingAccountMarkAsPaidNotification(user.accountId, user.id.value)
                    notificationService.notify(capture(notification), delaySeconds = capture(delay))
                    conversationHistoryService.createAssistantMessage(user.id, capture(message))
                }

                message.captured shouldBe "mensagem teste"
                notification.captured.configurationKey shouldBe KnownTemplateConfigurationKeys.promoteSweepingAccountMarkAsPaid
            }

            it("não deve enviar para quem já usa o sweeping account") {
                val user = getUser(true)

                executor.promoteSweepingAccount(
                    state = getBillComingDueHistoryState(
                        bills = listOf(billView, billView2, billView3),
                        alreadyPromotedSweepingAccount = false,
                        hasSweepingAccount = true,
                    ),
                    user = user,
                    selectedBillsToMarkAsPaid = listOf(billView, billView2, billView3),
                )

                verify(exactly = 0) {
                    paymentAdapter.getUserActivities(any(), any())
                    buildNotificationService.buildPromoteSweepingAccountMarkAsPaidNotification(any(), any())
                    notificationService.notify(any())
                    conversationHistoryService.createAssistantMessage(userId = any(), message = any())
                    notificationContextTemplatesService.getPromoteSweepingAccountMarkAsPaidMessage()
                }
            }

            it("não deve enviar novamente para quem já viu a mensagem hoje") {
                val user = getUser(true)
                val billComingDueHistoryState = getBillComingDueHistoryState(listOf(billView, billView2, billView3), true)

                executor.promoteSweepingAccount(
                    state = billComingDueHistoryState,
                    user = user,
                    listOf(billView, billView2, billView3),
                )

                verify(exactly = 0) {
                    paymentAdapter.getUserActivities(any(), any())
                    buildNotificationService.buildPromoteSweepingAccountMarkAsPaidNotification(any(), any())
                    notificationService.notify(any())
                    conversationHistoryService.createAssistantMessage(userId = any(), message = any())
                    notificationContextTemplatesService.getPromoteSweepingAccountMarkAsPaidMessage()
                }

                billComingDueHistoryState.alreadyPromotedSweepingAccount shouldBe true
            }

            it("não deve enviar para quem marcou pra não ver novamente") {
                val user = getUser(true)
                val billComingDueHistoryState = getBillComingDueHistoryState(listOf(billView, billView2, billView3), false)
                getUserActivities(true)

                executor.promoteSweepingAccount(
                    state = billComingDueHistoryState,
                    user = user,
                    listOf(billView, billView2, billView3),
                )

                verify(exactly = 1) { paymentAdapter.getUserActivities(any(), any()) }
                verify(exactly = 0) {
                    buildNotificationService.buildPromoteSweepingAccountMarkAsPaidNotification(any(), any())
                    notificationService.notify(any())
                    conversationHistoryService.createAssistantMessage(userId = any(), message = any())
                    notificationContextTemplatesService.getPromoteSweepingAccountMarkAsPaidMessage()
                }
            }

            it("não deve enviar para quem não tem bills para marcar como pagas") {
                val user = getUser(true)
                val billComingDueHistoryState = getBillComingDueHistoryState(listOf(billView, billView2, billView3), false)
                getUserActivities()

                executor.promoteSweepingAccount(
                    state = billComingDueHistoryState,
                    user = user,
                    emptyList(),
                )

                verify(exactly = 1) { paymentAdapter.getUserActivities(any(), any()) }
                verify(exactly = 0) {
                    buildNotificationService.buildPromoteSweepingAccountMarkAsPaidNotification(any(), any())
                    notificationService.notify(any())
                    conversationHistoryService.createAssistantMessage(userId = any(), message = any())
                    notificationContextTemplatesService.getPromoteSweepingAccountMarkAsPaidMessage()
                }
            }
        }
    }

    private fun buildCommand(
        billsToMarkAsPaid: List<Int>,
        billsToIgnore: List<Int>,
        userConfirmed: Boolean = true,
        userEarlyAccess: Boolean = true,
    ) = ActionExecutorCommand.MarkBillsAsPaidOrIgnoreBillsActionExecutorCommand(
        user = getUser(userEarlyAccess),
        action = Action.MarkAsPaidOrIgnore(
            payload = MarkBillsAsPaidOrIgnoreBillsAction(
                billsToMarkAsPaid = billsToMarkAsPaid,
                billsToIgnoreOrRemove = billsToIgnore,
                userConfirmed = userConfirmed,
            ),
        ),
    )

    private fun setup(
        bills: List<BillView>,
        alreadyPromotedSweepingAccount: Boolean = false,
    ) {
        every { conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(any()) } returns
            getBillComingDueHistoryState(bills, alreadyPromotedSweepingAccount)
        coEvery { paymentAdapter.markBillsAsPaid(any(), any(), any()) } returns Unit.right()
        every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit
        every { conversationHistoryService.synchronizeBeforeCompletion(any()) } returns Unit
        coEvery { paymentAdapter.ignoreBills(any(), any(), any()) } returns Unit.right()
        getUserActivities()
    }

    private fun getUser(userEarlyAccess: Boolean) = user.copy(
        accountGroups = if (userEarlyAccess) listOf("ALPHA") else emptyList(),
    )

    private fun getUserActivities(value: Boolean = false) {
        val userActivity = UserActivity(
            type = UserActivityType.PromotedSweepingAccountOptOut,
            value = value,
        )
        val userActivityList = listOf(userActivity)

        every { paymentAdapter.getUserActivities(any(), any()) } returns userActivityList.right()
    }

    private fun getBillComingDueHistoryState(
        bills: List<BillView>,
        alreadyPromotedSweepingAccount: Boolean,
        hasSweepingAccount: Boolean = false,
    ) = BillComingDueHistoryState(
        user = user,
        walletWithBills = WalletWithBills(
            bills = bills,
            walletId = WalletId("WALLET-ID"),
            walletName = "Wallet Name",
            activeConsents = if (hasSweepingAccount) {
                listOf(
                    SweepingConsent(
                        participant = SweepingParticipant(
                            id = SweepingParticipantId("participantId"),
                            name = "participantName",
                            shortName = "participantShortName",
                        ),
                        transactionLimit = 1000,
                        lastSuccessfulCashIn = null,
                    ),
                )
            } else {
                emptyList()
            },
        ),
        internalStateControl = InternalStateControl(
            shouldSynchronizeBeforeCompletion = false,
            billComingDueNotifiedAt = LocalDateTime.now().minusHours(1),
        ),
        balance = balance.copy(current = 200),
        startDate = LocalDate.now(),
        endDate = LocalDate.now(),
        contacts = emptyList(),
        lastBillsUpdatedAt = ZonedDateTime.now(),
        subscription = null,
        alreadyPromotedSweepingAccount = alreadyPromotedSweepingAccount,
    )
}