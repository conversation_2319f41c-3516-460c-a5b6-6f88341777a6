package ai.chatbot.app.conversation.actions

import ai.chatbot.adapters.waCommCentre.ClientId
import ai.chatbot.app.NotificationService
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.PixLimitStatus
import ai.chatbot.app.PixValidationResult
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.SweepingParticipant
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.TextSearchAdapter
import ai.chatbot.app.TransactionService
import ai.chatbot.app.balance
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.contact.ContactId
import ai.chatbot.app.contact.LastUsed
import ai.chatbot.app.conversation.Action
import ai.chatbot.app.conversation.ActionError
import ai.chatbot.app.conversation.ActionExecutorCommand
import ai.chatbot.app.conversation.ActionResult
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.conversation.Subscription
import ai.chatbot.app.conversation.SubscriptionPaymentStatus
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.getCustomNotificationServiceSpy
import ai.chatbot.app.mockNotificationConfigs
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.KnownNotificationTypes
import ai.chatbot.app.notification.KnownTemplateConfigurationKeys
import ai.chatbot.app.notification.NotificationConfig
import ai.chatbot.app.notification.NotificationMap
import ai.chatbot.app.notification.NotificationParam
import ai.chatbot.app.notification.NotificationType
import ai.chatbot.app.notification.RawTemplateNotificationConfig
import ai.chatbot.app.notification.TextNotificationConfig
import ai.chatbot.app.payment.Balance
import ai.chatbot.app.payment.Forecast
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.transaction.PixTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionDetails
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.atomic.AtomicInt
import arrow.core.left
import arrow.core.right
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.string.shouldEndWith
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.CapturingSlot
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime

class PixTransactionExecutorTest : DescribeSpec() {
    private val paymentAdapter: PaymentAdapter = mockk()
    private val conversationHistoryService: ConversationHistoryService = mockk()
    private val transactionService: TransactionService = mockk()
    private val notificationService: NotificationService = mockk(relaxed = true)
    private val buildNotificationService: BuildNotificationService = mockk()
    private val textSearchAdapter: TextSearchAdapter = mockk(relaxed = true)

    private val customNotificationService: CustomNotificationService = getCustomNotificationServiceSpy()

    private val executor = PixTransactionExecutor(
        notificationService = notificationService,
        conversationHistoryService = conversationHistoryService,
        paymentAdapter = paymentAdapter,
        transactionService = transactionService,
        buildNotificationService = buildNotificationService,
        textSearchAdapter = textSearchAdapter,
        customNotificationService = customNotificationService,
    )

    private val walletId = WalletId("WALLET-ID")

    private val user = User(
        accountId = AccountId(value = "ACCOUNT-ID"),
        id = UserId("*************"),
        name = "Nome",
        accountGroups = listOf("ALPHA"),
        status = AccountStatus.ACTIVE,
    )

    private val pixValidationResult = PixValidationResult(
        keyType = PixKeyType.EVP,
        keyValue = "chave-pix",
        recipientName = "Recipient",
        recipientInstitution = "Banco Teste",
        recipientDocument = "***********",
        pixLimitStatus = PixLimitStatus.AVAILABLE,
    )

    private val pixQRCodeValidationResult = PixValidationResult(
        keyType = PixKeyType.PHONE,
        keyValue = "***********",
        recipientName = "Rafael Haertel Peres",
        recipientInstitution = "Nubank",
        recipientDocument = "***********",
        pixLimitStatus = PixLimitStatus.AVAILABLE,
        amount = 2500,
    )

    private val mockedPixAmountExceedsSweepingTransactionLimitNotification = ChatbotRawTemplatedNotification(
        accountId = AccountId("account-id"),
        mobilePhone = user.id.value,
        clientId = ClientId("client-id"),
        configurationKey = KnownTemplateConfigurationKeys.pixAmountExceedsSweepingLimit,
    )

    private val contactSinglePixKey = Contact(
        id = ContactId("CONTACT-1"),
        alias = "Contact com uma chave",
        name = "Contato Contates",
        pixKeys = listOf(PixKey(type = PixKeyType.PHONE, value = "*************")),
    )

    private val contactMultiplePixKeys = Contact(
        id = ContactId("CONTACT-2"),
        alias = "Contact com mais de uma chave",
        name = "Contatinha Contata",
        pixKeys = listOf(PixKey(type = PixKeyType.PHONE, value = "*************"), PixKey(type = PixKeyType.EVP, value = "outra-chave")),
    )

    override fun isolationMode() = IsolationMode.InstancePerTest

    init {
        beforeEach {

            every {
                paymentAdapter.getOpenFinanceBalances(user.id, walletId, any())
            } answers {
                val index = AtomicInt(0)
                (thirdArg() as List<SweepingParticipantId>).associate {
                    it to 50L + index.getAndIncrement()
                }.right()
            }

            every { customNotificationService.config() } returns mockNotificationConfigs
        }

        describe("quando exceder o limite diário de pix") {
            it("deve informar o usuário") {
                setup(
                    limitStatus = PixLimitStatus.EXCEEDS_DAILY_LIMIT,
                    currentBalance = 100_00,
                    contacts = listOf(),
                    createPixError = null,
                )

                val result = executor.execute(
                    buildCommand(PixKeyType.PHONE.name, "21999999999", 50_00),
                )

                result.isRight() shouldBe true
                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                coVerify {
                    paymentAdapter.pixValidation(
                        amount = 50_00,
                        key = PixKey("21999999999", PixKeyType.PHONE),
                        userId = user.id,
                        walletId = walletId,
                    )
                }

                verify(exactly = 0) { transactionService.create(any(), any(), any(), any()) }

                val message = slot<String>()
                verify {
                    notificationService.notify(user.id, any(), capture(message), delay = any())
                }

                message.captured shouldContain "valor excede seu limite diário de Pix"
            }
        }

        describe("quando for um pix copia e cola") {
            val qrCode = "00020126360014BR.GOV.BCB.PIX0114+55***********520400005303986540525.005802BR5920Rafael Haertel Peres6009SAO PAULO61080540900062240520JSPvpLbB3No0M431d2fd63043E25"
            it("deve pedir o valor do pix se for um copia e cola sem valor") {
                setup(
                    limitStatus = PixLimitStatus.AVAILABLE,
                    currentBalance = 100_00,
                    contacts = listOf(),
                    createPixError = PaymentAdapterError.PixQrCodeAmountNotFound,
                )

                val result = executor.execute(
                    buildCommand("COPY_PASTE", qrCode, 0L),
                )

                result.isLeft() shouldBe true
                result.mapLeft {
                    it.needCompletion shouldBe true
                    it.actionType shouldBe ActionType.PIX_TRANSACTION
                }

                coVerify {
                    paymentAdapter.validatePixQrCode(
                        qrCode = qrCode,
                        amount = 0L,
                        userId = user.id,
                    )
                }
            }

            it("deve solicitar autorização pelo app quando exceder o limite do assistente") {
                setup(
                    limitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                    currentBalance = 100_00,
                    contacts = listOf(),
                    createPixError = null,
                )

                val result = executor.execute(
                    buildCommand("COPY_PASTE", qrCode, 25_00),
                )

                result.isRight() shouldBe true
                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                coVerify {
                    paymentAdapter.validatePixQrCode(
                        qrCode = qrCode,
                        amount = 25_00,
                        userId = user.id,
                    )
                }

                val pixKey = PixKey(pixQRCodeValidationResult.keyValue, pixQRCodeValidationResult.keyType)

                val details = slot<TransactionDetails>()
                verify { transactionService.create(user.id, walletId, capture(details)) }

                (details.captured is PixTransactionDetails) shouldBe true
                (details.captured as PixTransactionDetails).let {
                    it.amount shouldBe pixQRCodeValidationResult.amount
                    it.pixKey.type shouldBe pixKey.type
                    it.pixKey.value shouldBe pixKey.value
                    it.recipientName shouldBe pixQRCodeValidationResult.recipientName
                    it.recipientDocument shouldBe pixQRCodeValidationResult.recipientDocument
                    it.recipientInstitution shouldBe pixQRCodeValidationResult.recipientInstitution
                    it.qrCode!!.value shouldBe qrCode
                }

                verifyNotification(RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.authorizePix, KnownNotificationTypes.AUTHORIZE_PIX))
            }

            it("deve pedir confirmação do pix") {
                setup(
                    limitStatus = PixLimitStatus.AVAILABLE,
                    currentBalance = 100_00,
                    contacts = listOf(),
                    createPixError = null,
                )

                val result = executor.execute(
                    buildCommand("COPY_PASTE", qrCode, 25_00),
                )

                result.isRight() shouldBe true
                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                coVerify {
                    paymentAdapter.validatePixQrCode(
                        qrCode = qrCode,
                        amount = 25_00,
                        userId = user.id,
                    )
                }

                val details = slot<TransactionDetails>()
                verify { transactionService.create(user.id, walletId, capture(details)) }
                val pixKey = PixKey(pixQRCodeValidationResult.keyValue, pixQRCodeValidationResult.keyType)

                (details.captured is PixTransactionDetails) shouldBe true
                (details.captured as PixTransactionDetails).let {
                    it.amount shouldBe pixQRCodeValidationResult.amount
                    it.pixKey.type shouldBe pixKey.type
                    it.pixKey.value shouldBe pixKey.value
                    it.recipientName shouldBe pixQRCodeValidationResult.recipientName
                    it.recipientDocument shouldBe pixQRCodeValidationResult.recipientDocument
                    it.recipientInstitution shouldBe pixQRCodeValidationResult.recipientInstitution
                    it.qrCode!!.value shouldBe qrCode
                }

                verifyNotification(RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.pixConfirmation, NotificationType("notificationType")))
            }
        }

        describe("quando for pix para um contato") {
            it("deve informar o usuário quando contato náo for encontrado") {
                setup(
                    limitStatus = PixLimitStatus.AVAILABLE,
                    currentBalance = 100_00,
                    contacts = listOf(),
                    createPixError = null,
                )

                val result = executor.execute(
                    buildCommand("CONTACT", "Fulano", 50_00),
                )

                result.isRight() shouldBe true
                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                verify(exactly = 0) { transactionService.create(any(), any(), any(), any()) }

                val message = slot<String>()
                verify {
                    notificationService.notify(user.id, any(), capture(message), delay = any())
                }

                message.captured shouldContain "Não encontrei nenhum contato"
            }

            it("deve perguntar para o usuário quando encontrar mais de um contato") {
                setup(
                    limitStatus = PixLimitStatus.AVAILABLE,
                    currentBalance = 100_00,
                    contacts = listOf(contactSinglePixKey, contactMultiplePixKeys),
                    createPixError = null,
                )

                val result = executor.execute(
                    buildCommand("CONTACT", "Fulano", 50_00),
                )

                result.isRight() shouldBe true
                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                verify(exactly = 0) { transactionService.create(any(), any(), any(), any()) }

                val message = slot<String>()
                verify {
                    notificationService.notify(user.id, any(), capture(message), delay = any())
                }

                message.captured shouldContain contactSinglePixKey.name
                message.captured shouldContain contactMultiplePixKeys.name
                message.captured shouldContain "Qual deles você deseja enviar o pix?"
            }

            it("deve fazer o pix quando o contato tiver somente uma chave") {
                setup(
                    limitStatus = PixLimitStatus.AVAILABLE,
                    currentBalance = 100_00,
                    contacts = listOf(contactSinglePixKey),
                    createPixError = null,
                )

                val result = executor.execute(
                    buildCommand("CONTACT", "Fulano", 50_00),
                )

                result.isRight() shouldBe true
                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                verify { transactionService.create(any(), any(), any(), any()) }

                verifyNotification(RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.pixConfirmation, NotificationType("notificationType")))
            }

            it("deve perguntar ao usuário quando o contato tiver mais de uma chave") {
                setup(
                    limitStatus = PixLimitStatus.AVAILABLE,
                    currentBalance = 100_00,
                    contacts = listOf(contactMultiplePixKeys),
                    createPixError = null,
                )

                val result = executor.execute(
                    buildCommand("CONTACT", "Fulano", 50_00),
                )

                result.isRight() shouldBe true
                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                verify(exactly = 0) { transactionService.create(any(), any(), any(), any()) }

                val message = slot<String>()
                verify {
                    notificationService.notify(user.id, any(), capture(message), delay = any())
                }

                message.captured shouldContain "O contato possui mais de uma chave pix. Por favor, informe a chave desejada"
                contactMultiplePixKeys.pixKeys.forEach {
                    message.captured shouldContain it.value
                }
            }

            it("deve confirmar chave quando usuário quando o contato tiver mais de uma chave e tiver uma usada por último") {
                setup(
                    limitStatus = PixLimitStatus.AVAILABLE,
                    currentBalance = 100_00,
                    contacts = listOf(contactMultiplePixKeys.copy(lastUsed = LastUsed(contactMultiplePixKeys.pixKeys[0]))),
                    createPixError = null,
                )

                val result = executor.execute(
                    buildCommand("CONTACT", "Fulano", 50_00),
                )

                result.isRight() shouldBe true
                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                verify(exactly = 0) { transactionService.create(any(), any(), any(), any()) }

                val message = slot<String>()
                verify {
                    notificationService.notify(user.id, any(), capture(message), delay = any())
                }

                message.captured shouldContain "A ultima chave pix utilizada para esse contato é"
                message.captured shouldContain contactMultiplePixKeys.pixKeys[0].value
                message.captured shouldContain "Deseja usar esta chave?"
            }
        }

        describe("quando for para uma chave pix") {
            describe("quando precisar fazer fallback") {
                it("deve fazer fallback de CPF para PHONE se não encontrar") {
                    basicSetup(
                        limitStatus = PixLimitStatus.AVAILABLE,
                        currentBalance = 100_00,
                        contacts = listOf(),
                        createPixError = null,
                    )

                    coEvery { paymentAdapter.pixValidation(match { it.type == PixKeyType.CPF }, any(), any(), any()) } returns PaymentAdapterError.PixKeyNotFound.left()

                    coEvery { paymentAdapter.pixValidation(match { it.type == PixKeyType.PHONE }, any(), any(), any()) } returns pixValidationResult.copy(keyType = PixKeyType.PHONE, keyValue = "+5501234567890").right()

                    val result = executor.execute(
                        buildCommand(PixKeyType.CPF.name, "01234567890", 50_00),
                    )

                    result.isRight() shouldBe true
                    result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                    coVerify {
                        paymentAdapter.pixValidation(
                            amount = 50_00,
                            key = PixKey("01234567890", PixKeyType.CPF),
                            userId = user.id,
                            walletId = walletId,
                        )
                    }

                    coVerify {
                        paymentAdapter.pixValidation(
                            amount = 50_00,
                            key = PixKey("+5501234567890", PixKeyType.PHONE),
                            userId = user.id,
                            walletId = walletId,
                        )
                    }

                    val details = slot<TransactionDetails>()
                    verify { transactionService.create(user.id, walletId, capture(details)) }

                    (details.captured is PixTransactionDetails) shouldBe true
                    (details.captured as PixTransactionDetails).let {
                        it.amount shouldBe 50_00
                        it.pixKey.type shouldBe PixKeyType.PHONE
                        it.pixKey.value shouldBe "+5501234567890"
                    }

                    verifyNotification(RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.pixConfirmation, NotificationType("notificationType")))
                }

                it("deve fazer fallback de PHONE para CPF se não encontrar") {
                    basicSetup(
                        limitStatus = PixLimitStatus.AVAILABLE,
                        currentBalance = 100_00,
                        contacts = listOf(),
                        createPixError = null,
                    )

                    coEvery { paymentAdapter.pixValidation(match { it.type == PixKeyType.CPF }, any(), any(), any()) } returns pixValidationResult.copy(keyType = PixKeyType.CPF, keyValue = "01234567890").right()

                    coEvery { paymentAdapter.pixValidation(match { it.type == PixKeyType.PHONE }, any(), any(), any()) } returns PaymentAdapterError.PixKeyNotFound.left()

                    val result = executor.execute(
                        buildCommand(PixKeyType.PHONE.name, "01234567890", 50_00),
                    )

                    result.isRight() shouldBe true
                    result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                    coVerify {
                        paymentAdapter.pixValidation(
                            amount = 50_00,
                            key = PixKey("01234567890", PixKeyType.CPF),
                            userId = user.id,
                            walletId = walletId,
                        )
                    }

                    coVerify {
                        paymentAdapter.pixValidation(
                            amount = 50_00,
                            key = PixKey("+5501234567890", PixKeyType.PHONE),
                            userId = user.id,
                            walletId = walletId,
                        )
                    }

                    val details = slot<TransactionDetails>()
                    verify { transactionService.create(user.id, walletId, capture(details)) }

                    (details.captured is PixTransactionDetails) shouldBe true
                    (details.captured as PixTransactionDetails).let {
                        it.amount shouldBe 50_00
                        it.pixKey.type shouldBe PixKeyType.CPF
                        it.pixKey.value shouldBe "01234567890"
                    }

                    verifyNotification(RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.pixConfirmation, NotificationType("notificationType")))
                }
            }

            describe("e houver saldo") {
                it("deve solicitar autorização pelo app quando o valor do pix excede o limite do assistente") {
                    setup(
                        limitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                        currentBalance = 100_00,
                        contacts = listOf(),
                        createPixError = null,
                        pixKey = PixKey("21999999999", PixKeyType.PHONE),
                    )

                    val result = executor.execute(
                        buildCommand(PixKeyType.PHONE.name, "21999999999", 50_00),
                    )

                    result.isRight() shouldBe true
                    result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                    coVerify {
                        paymentAdapter.pixValidation(
                            amount = 50_00,
                            key = PixKey("21999999999", PixKeyType.PHONE),
                            userId = user.id,
                            walletId = walletId,
                        )
                    }

                    val details = slot<TransactionDetails>()
                    verify { transactionService.create(user.id, walletId, capture(details)) }

                    (details.captured is PixTransactionDetails) shouldBe true
                    (details.captured as PixTransactionDetails).let {
                        it.amount shouldBe 50_00
                        it.pixKey.type shouldBe PixKeyType.PHONE
                        it.pixKey.value shouldBe "+*************"
                        it.recipientName shouldBe pixValidationResult.recipientName
                        it.recipientDocument shouldBe pixValidationResult.recipientDocument
                        it.recipientInstitution shouldBe pixValidationResult.recipientInstitution
                    }

                    verifyNotification(RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.authorizePix, NotificationType("notificationType")))
                }

                it("deve pedir confirmação do pix") {
                    setup(
                        limitStatus = PixLimitStatus.AVAILABLE,
                        currentBalance = 100_00,
                        contacts = listOf(),
                        createPixError = null,
                        pixKey = PixKey("21999999999", PixKeyType.PHONE),
                    )

                    val result = executor.execute(
                        buildCommand(PixKeyType.PHONE.name, "21999999999", 50_00),
                    )

                    result.isRight() shouldBe true
                    result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                    coVerify {
                        paymentAdapter.pixValidation(
                            amount = 50_00,
                            key = PixKey("21999999999", PixKeyType.PHONE),
                            userId = user.id,
                            walletId = walletId,
                        )
                    }

                    val details = slot<TransactionDetails>()
                    verify { transactionService.create(user.id, walletId, capture(details)) }

                    (details.captured is PixTransactionDetails) shouldBe true
                    (details.captured as PixTransactionDetails).let {
                        it.amount shouldBe 50_00
                        it.pixKey.type shouldBe PixKeyType.PHONE
                        it.pixKey.value shouldBe "+*************"
                        it.recipientName shouldBe pixValidationResult.recipientName
                        it.recipientDocument shouldBe pixValidationResult.recipientDocument
                        it.recipientInstitution shouldBe pixValidationResult.recipientInstitution
                    }

                    verifyNotification(RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.pixConfirmation, NotificationType("notificationType")))
                }
            }

            describe(", não houver saldo") {
                describe("e o usuário não tem conta conectada") {
                    it("deve informar a falta de saldo") {
                        setup(
                            limitStatus = PixLimitStatus.AVAILABLE,
                            currentBalance = 10_00,
                            contacts = listOf(),
                            createPixError = null,
                        )

                        val result = executor.execute(
                            buildCommand(PixKeyType.PHONE.name, "21999999999", 50_00),
                        )

                        result.isLeft() shouldBe true
                        result.map { it shouldBe ActionError(needCompletion = false, message = "Você não tem saldo suficiente para realizar a transação.", actionType = ActionType.PIX_TRANSACTION) }

                        verify(exactly = 0) { transactionService.create(any(), any(), any(), any()) }
                    }
                }

                describe("mas o usuário tem conta conectada") {
                    describe("e nao tem assinautra vencida") {
                        it("deve sugerir aumentar o limite da conta conectada quando ele é inferior ao valor do pix") {
                            val balance = 10_00L
                            val activeConsents = setupActiveConsents(1)

                            setup(
                                limitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                                currentBalance = balance,
                                contacts = listOf(),
                                createPixError = null,
                                pixKey = PixKey("21999999999", PixKeyType.PHONE),
                                sweepingConsents = activeConsents,
                            )

                            val transactionId = TransactionId()
                            every {
                                transactionService.create(any(), any(), any(), any())
                            } returns mockk {
                                every {
                                    id
                                } returns transactionId
                            }
                            val command = buildCommand(
                                type = PixKeyType.PHONE.name,
                                key = "21999999999",
                                amount = activeConsents.first().transactionLimit + balance + 1,
                            )

                            val result = executor.execute(command)

                            result.isRight() shouldBe true
                            result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                            val transactionIdSlot = slot<TransactionId>()
                            val details = slot<TransactionDetails>()
                            verify {
                                transactionService.create(user.id, walletId, capture(details))
                                buildNotificationService.buildPixAmountExceedsSweepingTransactionLimit(any(), any(), capture(transactionIdSlot))
                                notificationService.notify(mockedPixAmountExceedsSweepingTransactionLimitNotification, any())
                            }
                            transactionIdSlot.captured shouldBe transactionId

                            with(details.captured) {
                                this.shouldBeTypeOf<PixTransactionDetails>()
                                this.amount shouldBe command.action.amount
                                this.pixKey.type.name shouldBe command.action.type
                                this.pixKey.value shouldEndWith command.action.key
                                this.sweepingAmount shouldBe null
                                this.sweepingParticipantId shouldBe null
                            }
                        }

                        it("deve solicitar autorização pelo app quando o valor do pix excede o limite do assistente") {
                            val activeConsents = setupActiveConsents(1)

                            val transactionMap = setup(
                                limitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                                currentBalance = 10_00,
                                contacts = listOf(),
                                createPixError = null,
                                pixKey = PixKey("21999999999", PixKeyType.PHONE),
                                sweepingConsents = activeConsents,
                            )

                            val command = buildCommand(
                                type = PixKeyType.PHONE.name,
                                key = "21999999999",
                                amount = activeConsents.first().transactionLimit,
                            )

                            val result = executor.execute(command)

                            result.isRight() shouldBe true
                            result.map {
                                it.shouldBeTypeOf<ActionResult.WithoutCompletion>()
                                it.actionType shouldBe ActionType.PIX_TRANSACTION
                            }

                            val transactionGroupId = verifySweepingTransaction(command, 40_00)

                            val paramsSlot = verifyNotification(mockNotificationConfigs.authorizePixSweeping)
                            paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_GROUP_ID, transactionGroupId.value)
                            paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_ID, transactionMap[activeConsents[0].participant.id]!!.id.value)
                            paramsSlot.captured.shouldHave(NotificationParam.SUBSCRIPTION_WARNING, "")
                        }

                        describe("mas tem apenas uma conta conectada") {
                            it("deve pedir confirmação do pix") {
                                val activeConsents = setupActiveConsents(1)

                                val command = buildCommand(
                                    type = PixKeyType.PHONE.name,
                                    key = "21999999999",
                                    amount = activeConsents.first().transactionLimit,
                                )

                                setup(
                                    limitStatus = PixLimitStatus.AVAILABLE,
                                    currentBalance = 10_00,
                                    contacts = listOf(),
                                    createPixError = null,
                                    pixKey = PixKey("21999999999", PixKeyType.PHONE),
                                    sweepingConsents = activeConsents,
                                )

                                val result = executor.execute(command)

                                result.isRight() shouldBe true
                                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                                coVerify {
                                    paymentAdapter.pixValidation(
                                        amount = 50_00,
                                        key = PixKey("21999999999", PixKeyType.PHONE),
                                        userId = user.id,
                                        walletId = walletId,
                                    )
                                }

                                val details = slot<TransactionDetails>()
                                verify { transactionService.create(user.id, walletId, capture(details), any()) }

                                with(details.captured) {
                                    this.shouldBeTypeOf<PixTransactionDetails>()
                                    this.amount shouldBe 50_00
                                    this.pixKey.type shouldBe PixKeyType.PHONE
                                    this.pixKey.value shouldBe "+*************"
                                    this.recipientName shouldBe pixValidationResult.recipientName
                                    this.recipientDocument shouldBe pixValidationResult.recipientDocument
                                    this.recipientInstitution shouldBe pixValidationResult.recipientInstitution
                                    this.sweepingAmount shouldBe 40_00
                                }

                                val params = verifyNotification(mockNotificationConfigs.pixConfirmationSingleSweepingConsent)
                                params.captured.shouldHave(NotificationParam.SUBSCRIPTION_WARNING, "")
                                params.captured.shouldHave(NotificationParam.RECIPIENT_NAME, pixValidationResult.recipientName)
                                params.captured.shouldHave(NotificationParam.RECIPIENT_DOCUMENT, "∗∗∗.456.789-∗∗")
                                params.captured.shouldHave(NotificationParam.RECIPIENT_INSTITUTION, pixValidationResult.recipientInstitution)
                                params.captured.shouldHave(NotificationParam.AMOUNT, "R$ 50,00")
                                params.captured.shouldHave(NotificationParam.PIX_KEY, "+*************")
                                params.captured.shouldHave(NotificationParam.SUBSCRIPTION_WARNING, "")
                                params.captured.shouldHave(NotificationParam.CONTA1_NOME, activeConsents[0].participant.name)
                                params.captured.shouldHave(NotificationParam.CONTA1_NOME_CURTO, activeConsents[0].participant.shortName.take(10))
                                params.captured.shouldHave(NotificationParam.CONTA1_SALDO, "R$ 0,50")
                            }
                        }

                        describe("mas tem duas contas conectadas") {
                            it("deve pedir confirmação do pix") {
                                val activeConsents = setupActiveConsents(2)

                                val command = buildCommand(
                                    type = PixKeyType.PHONE.name,
                                    key = "21999999999",
                                    amount = activeConsents.first().transactionLimit,
                                )

                                val transactionMap = setup(
                                    limitStatus = PixLimitStatus.AVAILABLE,
                                    currentBalance = 10_00,
                                    contacts = listOf(),
                                    createPixError = null,
                                    pixKey = PixKey("21999999999", PixKeyType.PHONE),
                                    sweepingConsents = activeConsents,
                                )

                                val result = executor.execute(command)

                                result.isRight() shouldBe true
                                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                                coVerify {
                                    paymentAdapter.pixValidation(
                                        amount = 50_00,
                                        key = PixKey("21999999999", PixKeyType.PHONE),
                                        userId = user.id,
                                        walletId = walletId,
                                    )
                                }

                                val details = mutableListOf<TransactionDetails>()
                                verify { transactionService.create(user.id, walletId, capture(details), any()) }

                                details.forEach {
                                    it.shouldBeTypeOf<PixTransactionDetails>()
                                    it.amount shouldBe 50_00
                                    it.pixKey.type shouldBe PixKeyType.PHONE
                                    it.pixKey.value shouldBe "+*************"
                                    it.recipientName shouldBe pixValidationResult.recipientName
                                    it.recipientDocument shouldBe pixValidationResult.recipientDocument
                                    it.recipientInstitution shouldBe pixValidationResult.recipientInstitution
                                    it.sweepingAmount shouldBe 40_00
                                }

                                details.mapNotNull {
                                    (it as PixTransactionDetails).sweepingParticipantId
                                } shouldContainExactlyInAnyOrder activeConsents.map {
                                    it.participant.id
                                }

                                val params = verifyNotification(mockNotificationConfigs.pixConfirmationMultipleSweepingConsent)
                                params.captured.shouldHave(NotificationParam.SUBSCRIPTION_WARNING, "")
                                params.captured.shouldHave(NotificationParam.RECIPIENT_NAME, pixValidationResult.recipientName)
                                params.captured.shouldHave(NotificationParam.RECIPIENT_DOCUMENT, "∗∗∗.456.789-∗∗")
                                params.captured.shouldHave(NotificationParam.RECIPIENT_INSTITUTION, pixValidationResult.recipientInstitution)
                                params.captured.shouldHave(NotificationParam.AMOUNT, "R$ 50,00")
                                params.captured.shouldHave(NotificationParam.CONTA1_NOME, activeConsents[0].participant.name)
                                params.captured.shouldHave(NotificationParam.CONTA1_NOME_CURTO, activeConsents[0].participant.shortName.take(10))
                                params.captured.shouldHave(NotificationParam.CONTA1_SALDO, "R$ 0,50")
                                params.captured.shouldHave(NotificationParam.TRANSACTION_ID, transactionMap[activeConsents[0].participant.id]!!.id.value)
                                params.captured.shouldHave(NotificationParam.CONTA2_NOME, activeConsents[1].participant.name)
                                params.captured.shouldHave(NotificationParam.CONTA2_NOME_CURTO, activeConsents[1].participant.shortName.take(10))
                                params.captured.shouldHave(NotificationParam.CONTA2_SALDO, "R$ 0,51")
                                params.captured.shouldHave(NotificationParam.TRANSACTION_ID_2, transactionMap[activeConsents[1].participant.id]!!.id.value)
                            }
                        }

                        describe("mas tem mais de duas contas conectadas") {
                            it("deve pedir confirmação do pix") {
                                val activeConsents = setupActiveConsents(3)

                                val command = buildCommand(
                                    type = PixKeyType.PHONE.name,
                                    key = "21999999999",
                                    amount = activeConsents.first().transactionLimit,
                                )

                                val transactionMap = setup(
                                    limitStatus = PixLimitStatus.AVAILABLE,
                                    currentBalance = 10_00,
                                    contacts = listOf(),
                                    createPixError = null,
                                    pixKey = PixKey("21999999999", PixKeyType.PHONE),
                                    sweepingConsents = activeConsents,
                                )

                                val result = executor.execute(command)

                                result.isRight() shouldBe true
                                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                                coVerify {
                                    paymentAdapter.pixValidation(
                                        amount = 50_00,
                                        key = PixKey("21999999999", PixKeyType.PHONE),
                                        userId = user.id,
                                        walletId = walletId,
                                    )
                                }

                                val detailsSlot = mutableListOf<TransactionDetails>()
                                val transactionGroupIdSlot = mutableListOf<TransactionGroupId>()
                                verify { transactionService.create(user.id, walletId, capture(detailsSlot), capture(transactionGroupIdSlot)) }

                                detailsSlot.forEach {
                                    it.shouldBeTypeOf<PixTransactionDetails>()
                                    it.amount shouldBe 50_00
                                    it.pixKey.type shouldBe PixKeyType.PHONE
                                    it.pixKey.value shouldBe "+*************"
                                    it.recipientName shouldBe pixValidationResult.recipientName
                                    it.recipientDocument shouldBe pixValidationResult.recipientDocument
                                    it.recipientInstitution shouldBe pixValidationResult.recipientInstitution
                                    it.sweepingAmount shouldBe 40_00
                                }

                                detailsSlot.mapNotNull {
                                    (it as PixTransactionDetails).sweepingParticipantId
                                } shouldContainExactlyInAnyOrder activeConsents.map {
                                    it.participant.id
                                }

                                // todas as transações tem que ter sido criadas com o MESMO transactionGroupId
                                transactionGroupIdSlot.toSet().size shouldBe 1
                                val transactionGroupId = transactionGroupIdSlot.first()

                                val params = verifyNotification(mockNotificationConfigs.pixConfirmationSelectSweepingConsent)
                                params.captured.shouldHave(NotificationParam.SUBSCRIPTION_WARNING, "")
                                params.captured.shouldHave(NotificationParam.RECIPIENT_NAME, pixValidationResult.recipientName)
                                params.captured.shouldHave(NotificationParam.RECIPIENT_DOCUMENT, "∗∗∗.456.789-∗∗")
                                params.captured.shouldHave(NotificationParam.RECIPIENT_INSTITUTION, pixValidationResult.recipientInstitution)
                                params.captured.shouldHave(NotificationParam.AMOUNT, "R$ 50,00")
                                params.captured.shouldHave(NotificationParam.CONTA1_NOME, activeConsents[0].participant.name)
                                params.captured.shouldHave(NotificationParam.CONTA1_NOME_CURTO, activeConsents[0].participant.shortName.take(10))
                                params.captured.shouldHave(NotificationParam.CONTA1_SALDO, "R$ 0,50")
                                params.captured.shouldHave(NotificationParam.TRANSACTION_ID, transactionMap[activeConsents[0].participant.id]!!.id.value)
                                params.captured.shouldHave(NotificationParam.TRANSACTION_GROUP_ID, transactionGroupId.value)
                                params.captured.shouldHave(NotificationParam.SUBSCRIPTION_WARNING, "")
                            }
                        }
                    }

                    describe("e tem assinatura vencida") {
                        it("deve sugerir aumentar o limite da conta conectada quando ele é inferior ao valor do pix com assinatura") {
                            val balance = 10_00L
                            val activeConsents = setupActiveConsents(1)
                            setup(
                                limitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                                currentBalance = balance,
                                contacts = listOf(),
                                createPixError = null,
                                pixKey = PixKey("21999999999", PixKeyType.PHONE),
                                sweepingConsents = activeConsents,
                                hasSubscriptionFee = true,
                            )

                            val transactionId = TransactionId()
                            every {
                                transactionService.create(any(), any(), any(), any())
                            } returns mockk {
                                every {
                                    id
                                } returns transactionId
                            }
                            val command = buildCommand(
                                type = PixKeyType.PHONE.name,
                                key = "21999999999",
                                amount = activeConsents.first().transactionLimit + balance + 1,
                            )

                            val result = executor.execute(command)

                            result.isRight() shouldBe true
                            result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                            val transactionIdSlot = slot<TransactionId>()
                            val details = slot<TransactionDetails>()
                            verify {
                                transactionService.create(user.id, walletId, capture(details), any())
                                buildNotificationService.buildPixAmountExceedsSweepingTransactionLimit(any(), any(), capture(transactionIdSlot))
                                notificationService.notify(mockedPixAmountExceedsSweepingTransactionLimitNotification, any())
                            }
                            transactionIdSlot.captured shouldBe transactionId

                            with(details.captured) {
                                this.shouldBeTypeOf<PixTransactionDetails>()
                                this.amount shouldBe command.action.amount
                                this.pixKey.type.name shouldBe command.action.type
                                this.pixKey.value shouldEndWith command.action.key
                                this.sweepingAmount shouldBe null
                                this.sweepingParticipantId shouldBe null
                            }
                        }

                        it("deve solicitar autorização pelo app quando o valor do pix com assinatura excede o limite do assistente") {
                            val activeConsents = setupActiveConsents(1)
                            val transactionMap = setup(
                                limitStatus = PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT,
                                currentBalance = 10_00,
                                contacts = listOf(),
                                createPixError = null,
                                pixKey = PixKey("21999999999", PixKeyType.PHONE),
                                sweepingConsents = activeConsents,
                                hasSubscriptionFee = true,
                            )

                            val command = buildCommand(
                                type = PixKeyType.PHONE.name,
                                key = "21999999999",
                                amount = activeConsents.first().transactionLimit,
                            )

                            val result = executor.execute(command)

                            result.isRight() shouldBe true
                            result.map {
                                it.shouldBeTypeOf<ActionResult.WithoutCompletion>()
                                it.actionType shouldBe ActionType.PIX_TRANSACTION
                            }

                            val transactionGroupId = verifySweepingTransaction(command, 49_90)

                            val paramsSlot = verifyNotification(mockNotificationConfigs.authorizePixSweeping)
                            paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_GROUP_ID, transactionGroupId.value)
                            paramsSlot.captured.shouldHave(NotificationParam.TRANSACTION_ID, transactionMap[activeConsents[0].participant.id]!!.id.value)
                            paramsSlot.captured.shouldHave(NotificationParam.SUBSCRIPTION_WARNING, subscriptionWarning)
                        }

                        describe("mas tem apenas uma conta conectada") {
                            it("deve pedir confirmação do pix") {
                                val activeConsents = setupActiveConsents(1)
                                val command = buildCommand(
                                    type = PixKeyType.PHONE.name,
                                    key = "21999999999",
                                    amount = activeConsents.first().transactionLimit,
                                )

                                setup(
                                    limitStatus = PixLimitStatus.AVAILABLE,
                                    currentBalance = 10_00,
                                    contacts = listOf(),
                                    createPixError = null,
                                    pixKey = PixKey("21999999999", PixKeyType.PHONE),
                                    sweepingConsents = activeConsents,
                                    hasSubscriptionFee = true,
                                )

                                val result = executor.execute(command)

                                result.isRight() shouldBe true
                                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                                coVerify {
                                    paymentAdapter.pixValidation(
                                        amount = 50_00,
                                        key = PixKey("21999999999", PixKeyType.PHONE),
                                        userId = user.id,
                                        walletId = walletId,
                                    )
                                }

                                val details = slot<TransactionDetails>()
                                verify { transactionService.create(user.id, walletId, capture(details), any()) }

                                with(details.captured) {
                                    this.shouldBeTypeOf<PixTransactionDetails>()
                                    this.amount shouldBe 50_00
                                    this.pixKey.type shouldBe PixKeyType.PHONE
                                    this.pixKey.value shouldBe "+*************"
                                    this.recipientName shouldBe pixValidationResult.recipientName
                                    this.recipientDocument shouldBe pixValidationResult.recipientDocument
                                    this.recipientInstitution shouldBe pixValidationResult.recipientInstitution
                                    this.sweepingAmount shouldBe 49_90
                                }

                                val params = verifyNotification(mockNotificationConfigs.pixConfirmationSingleSweepingConsent)
                                params.captured.shouldHave(NotificationParam.SUBSCRIPTION_WARNING, subscriptionWarning)
                            }
                        }

                        describe("mas tem duas contas conectadas") {
                            it("deve pedir confirmação do pix") {
                                val activeConsents = setupActiveConsents(2)

                                val command = buildCommand(
                                    type = PixKeyType.PHONE.name,
                                    key = "21999999999",
                                    amount = activeConsents.first().transactionLimit,
                                )

                                setup(
                                    limitStatus = PixLimitStatus.AVAILABLE,
                                    currentBalance = 10_00,
                                    contacts = listOf(),
                                    createPixError = null,
                                    pixKey = PixKey("21999999999", PixKeyType.PHONE),
                                    sweepingConsents = activeConsents,
                                    hasSubscriptionFee = true,
                                )

                                val result = executor.execute(command)

                                result.isRight() shouldBe true
                                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                                coVerify {
                                    paymentAdapter.pixValidation(
                                        amount = 50_00,
                                        key = PixKey("21999999999", PixKeyType.PHONE),
                                        userId = user.id,
                                        walletId = walletId,
                                    )
                                }

                                val details = mutableListOf<TransactionDetails>()
                                verify { transactionService.create(user.id, walletId, capture(details), any()) }

                                details.forEach {
                                    it.shouldBeTypeOf<PixTransactionDetails>()
                                    it.amount shouldBe 50_00
                                    it.pixKey.type shouldBe PixKeyType.PHONE
                                    it.pixKey.value shouldBe "+*************"
                                    it.recipientName shouldBe pixValidationResult.recipientName
                                    it.recipientDocument shouldBe pixValidationResult.recipientDocument
                                    it.recipientInstitution shouldBe pixValidationResult.recipientInstitution
                                    it.sweepingAmount shouldBe 49_90
                                }

                                details.mapNotNull {
                                    (it as PixTransactionDetails).sweepingParticipantId
                                } shouldContainExactlyInAnyOrder activeConsents.map {
                                    it.participant.id
                                }

                                val params = verifyNotification(mockNotificationConfigs.pixConfirmationMultipleSweepingConsent)
                                params.captured.shouldHave(NotificationParam.SUBSCRIPTION_WARNING, subscriptionWarning)
                            }
                        }

                        describe("mas tem mais de duas contas conectadas") {
                            it("deve pedir confirmação do pix") {
                                val activeConsents = setupActiveConsents(3)

                                val command = buildCommand(
                                    type = PixKeyType.PHONE.name,
                                    key = "21999999999",
                                    amount = activeConsents.first().transactionLimit,
                                )

                                setup(
                                    limitStatus = PixLimitStatus.AVAILABLE,
                                    currentBalance = 10_00,
                                    contacts = listOf(),
                                    createPixError = null,
                                    pixKey = PixKey("21999999999", PixKeyType.PHONE),
                                    sweepingConsents = activeConsents,
                                    hasSubscriptionFee = true,
                                )

                                val result = executor.execute(command)

                                result.isRight() shouldBe true
                                result.map { it shouldBe ActionResult.WithoutCompletion(ActionType.PIX_TRANSACTION) }

                                coVerify {
                                    paymentAdapter.pixValidation(
                                        amount = 50_00,
                                        key = PixKey("21999999999", PixKeyType.PHONE),
                                        userId = user.id,
                                        walletId = walletId,
                                    )
                                }

                                val details = mutableListOf<TransactionDetails>()
                                verify { transactionService.create(user.id, walletId, capture(details), any()) }

                                details.forEach {
                                    it.shouldBeTypeOf<PixTransactionDetails>()
                                    it.amount shouldBe 50_00
                                    it.pixKey.type shouldBe PixKeyType.PHONE
                                    it.pixKey.value shouldBe "+*************"
                                    it.recipientName shouldBe pixValidationResult.recipientName
                                    it.recipientDocument shouldBe pixValidationResult.recipientDocument
                                    it.recipientInstitution shouldBe pixValidationResult.recipientInstitution
                                    it.sweepingAmount shouldBe 49_90
                                }

                                details.mapNotNull {
                                    (it as PixTransactionDetails).sweepingParticipantId
                                } shouldContainExactlyInAnyOrder activeConsents.map {
                                    it.participant.id
                                }

                                val params = verifyNotification(mockNotificationConfigs.pixConfirmationSelectSweepingConsent)
                                params.captured.shouldHave(NotificationParam.SUBSCRIPTION_WARNING, subscriptionWarning)
                            }
                        }
                    }
                }
            }
        }
    }

    private fun buildCommand(
        type: String,
        key: String,
        amount: Long,
    ) = ActionExecutorCommand.PixTransactionActionExecutorCommand(
        user = user,
        action = Action.PixTransaction(
            type = type,
            amount = amount,
            key = key,
        ),
    )

    private fun basicSetup(
        limitStatus: PixLimitStatus,
        currentBalance: Long,
        contacts: List<Contact>,
        createPixError: PaymentAdapterError?,
        sweepingConsents: List<SweepingConsent> = emptyList(),
        hasSubscriptionFee: Boolean = false,
    ): MutableMap<SweepingParticipantId, Transaction> {
        every { textSearchAdapter.search(any(), any()) } returns contacts.map { it.id.value }

        every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit
        every { conversationHistoryService.synchronizeBeforeCompletion(any()) } returns Unit
        val subscription = if (hasSubscriptionFee) {
            Subscription(
                fee = 9_90,
                dueDate = LocalDate.now(),
                paymentStatus = SubscriptionPaymentStatus.PAID,
                type = SubscriptionType.PIX,
            )
        } else {
            null
        }

        val originalState = BillComingDueHistoryState(
            user = user,
            walletWithBills = WalletWithBills(
                bills = listOf(),
                walletId = walletId,
                walletName = "Wallet Name",
                activeConsents = emptyList(),
            ),
            internalStateControl = InternalStateControl(
                shouldSynchronizeBeforeCompletion = false,
                billComingDueNotifiedAt = LocalDateTime.now().minusHours(1),
            ),
            balance = balance.copy(current = currentBalance),
            startDate = LocalDate.now(),
            endDate = LocalDate.now(),
            contacts = contacts,
            lastBillsUpdatedAt = ZonedDateTime.now(),
            subscription = subscription,
        )
        every { conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(any()) } returns originalState
        every { conversationHistoryService.updateBillComingDueHistoryState(any(), any()) } answers {
            secondArg<(BillComingDueHistoryState) -> BillComingDueHistoryState>().invoke(originalState).right()
        }

        coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns Balance(
            walletId = walletId,
            current = currentBalance,
            open = mockk<Forecast>(),
            onlyScheduled = mockk<Forecast>(),
        ).right()

        coEvery { paymentAdapter.validatePixQrCode(any(), any(), any()) } returns (createPixError?.left() ?: pixQRCodeValidationResult.copy(pixLimitStatus = limitStatus).right())

        every {
            paymentAdapter.getActiveSweepingConsents(any(), any())
        } returns sweepingConsents.right()

        every { buildNotificationService.buildPixAmountExceedsSweepingTransactionLimit(any(), any(), any()) } returns mockedPixAmountExceedsSweepingTransactionLimitNotification

        return setupTransaction()
    }

    private fun setup(
        limitStatus: PixLimitStatus,
        currentBalance: Long,
        contacts: List<Contact>,
        createPixError: PaymentAdapterError?,
        pixKey: PixKey? = null,
        sweepingConsents: List<SweepingConsent> = emptyList(),
        hasSubscriptionFee: Boolean = false,
    ): MutableMap<SweepingParticipantId, Transaction> {
        coEvery { paymentAdapter.pixValidation(any(), any(), any(), any()) } returns (createPixError?.left() ?: pixKey?.let { pixValidationResult.copy(keyType = pixKey.type, keyValue = pixKey.value, pixLimitStatus = limitStatus).right() } ?: pixValidationResult.copy(pixLimitStatus = limitStatus).right())
        return basicSetup(limitStatus, currentBalance, contacts, createPixError, sweepingConsents, hasSubscriptionFee)
    }

    private fun verifyNotification(expected: NotificationConfig): CapturingSlot<List<NotificationMap>> {
        val slot = slot<NotificationConfig>()
        val paramsSlot = slot<List<NotificationMap>>()
        verify {
            customNotificationService.send(user, capture(paramsSlot), capture(slot))
        }
        when (slot.captured) {
            is RawTemplateNotificationConfig -> {
                expected.shouldBeTypeOf<RawTemplateNotificationConfig>()
                (slot.captured as RawTemplateNotificationConfig).configurationKey shouldBe expected.configurationKey
            }

            is TextNotificationConfig -> slot.captured shouldBe expected
        }

        return paramsSlot
    }

    private fun setupActiveConsents(count: Int, transactionLimit: Long = 50_00) = mutableListOf<SweepingConsent>().also { activeConsents ->
        val now = getZonedDateTime()
        repeat(count) { index ->
            activeConsents.add(
                SweepingConsent(
                    participant = SweepingParticipant(
                        id = SweepingParticipantId("participantId-$index"),
                        name = "participantName-$index",
                        shortName = "participantShortName-$index",
                    ),
                    transactionLimit = transactionLimit,
                    lastSuccessfulCashIn = now.minusSeconds(index.toLong()),
                ),
            )
        }
    }

    private fun List<NotificationMap>.shouldHave(param: NotificationParam, value: String) {
        this.firstOrNull {
            it.param == param
        }?.value shouldBe value
    }

    private fun setupTransaction(): MutableMap<SweepingParticipantId, Transaction> {
        val transactionMap = mutableMapOf<SweepingParticipantId, Transaction>()
        every {
            transactionService.create(any(), any(), any(), any())
        } answers {
            val details: TransactionDetails = arg(2)
            mockTransaction(transactionGroupId = arg(3), transactionDetails = details).also {
                if (details is PixTransactionDetails) {
                    with(details.sweepingParticipantId) {
                        if (this != null) {
                            transactionMap[this] = it
                        }
                    }
                }
            }
        }
        return transactionMap
    }

    private fun mockTransaction(transactionGroupId: TransactionGroupId?, transactionDetails: TransactionDetails): Transaction {
        return mockk {
            every {
                id
            } returns TransactionId()
            every {
                groupId
            } returns (transactionGroupId ?: TransactionGroupId())
            every {
                details
            } returns transactionDetails
        }
    }

    private fun verifySweepingTransaction(command: ActionExecutorCommand.PixTransactionActionExecutorCommand, expectedAmount: Long): TransactionGroupId {
        val details = slot<TransactionDetails>()
        val transactionGroupIdSlot = mutableListOf<TransactionGroupId>()
        verify { transactionService.create(user.id, walletId, capture(details), capture(transactionGroupIdSlot)) }
        with(details.captured) {
            this.shouldBeTypeOf<PixTransactionDetails>()
            this.amount shouldBe command.action.amount
            this.pixKey.type.name shouldBe command.action.type
            this.pixKey.value shouldEndWith command.action.key
            this.sweepingAmount shouldBe expectedAmount
        }
        // todas as transações tem que ter sido criadas com o MESMO transactionGroupId
        transactionGroupIdSlot.toSet().size shouldBe 1

        return transactionGroupIdSlot.first()
    }
}