package ai.chatbot.app.conversation.actions

import ai.chatbot.adapters.waCommCentre.ClientId
import ai.chatbot.app.NotificationService
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.balance
import ai.chatbot.app.billView
import ai.chatbot.app.billView2
import ai.chatbot.app.billView3
import ai.chatbot.app.config.FeaturesConfig
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.Action
import ai.chatbot.app.conversation.ActionExecutorCommand
import ai.chatbot.app.conversation.ActionResult
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InteractionWindowService
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.OnboardingSinglePixAction
import ai.chatbot.app.conversation.OnboardingState
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.getCustomNotificationServiceMock
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.CTALink
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.ConfigurationKey
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.OnboardingSinglePixNotificationService
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.pix.isValid
import ai.chatbot.app.tenantConfiguration
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import arrow.core.left
import arrow.core.right
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.mockk.Runs
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.mockk.verifyOrder
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime

class OnboardingExecutorTest : DescribeSpec() {
    private val notificationService: NotificationService = mockk(relaxed = true)
    private val conversationHistoryService: ConversationHistoryService = mockk(relaxed = true)
    private val buildNotificationService: BuildNotificationService = mockk(relaxed = true)
    private val onboardingSinglePixNotificationService: OnboardingSinglePixNotificationService = mockk(relaxed = true)
    private val paymentAdapter: PaymentAdapter = mockk(relaxed = true)
    private val interactionWindowService: InteractionWindowService = mockk(relaxed = true)
    private val customNotificationService: CustomNotificationService = getCustomNotificationServiceMock()
    private val tenantConfiguration = tenantConfiguration()
    private val tenantService = mockk<TenantService>(relaxed = true) {
        every {
            getConfiguration()
        } returns tenantConfiguration.copy(appBaseUrl = "https://app.friday.ai")
    }

    private val onboardingExecutor = OnboardingSinglePixExecutor(
        notificationService = notificationService,
        conversationHistoryService = conversationHistoryService,
        buildNotificationService = buildNotificationService,
        onboardingSinglePixNotificationService = onboardingSinglePixNotificationService,
        paymentAdapter = paymentAdapter,
        interactionWindowService = interactionWindowService,
        customNotificationService = customNotificationService,
        tenantService = tenantService,
    )

    private val user =
        User(
            accountId = AccountId(value = "ACCOUNT-ID"),
            id = UserId("*************"),
            name = "Nome",
            accountGroups = emptyList(),
            status = AccountStatus.ACTIVE,
        )

    private val billViews = listOf(billView, billView2, billView3)
    private val walletId = "walletId"
    private val walletName = "walletName"

    private val pixKey: PixKey = mockk(relaxed = true)

    private val mockedNotification = ChatbotRawTemplatedNotification(
        accountId = AccountId("account-id"),
        mobilePhone = user.id.value,
        configurationKey = ConfigurationKey("configuration-key"),
        clientId = ClientId("client-id"),

    )

    private val mockFeatures = mockk<FeaturesConfig>()

    init {
        beforeEach {
            clearAllMocks()
            setup()
        }

        describe("quando receber uma action ACCEPT") {
            it("quando consegue criar o PIX") {
                every { tenantService.getConfiguration() } returns tenantConfiguration.copy(features = mockFeatures)
                coEvery { paymentAdapter.createOnboardingSinglePix(userId = user.id) } returns billView.right()
                coEvery { buildNotificationService.buildOnboardingPixConfirmationNotification(any(), any(), any(), any()) } returns mockedNotification

                val result = onboardingExecutor.execute(
                    buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.ACCEPT, "pixKey", "CPF"),
                )

                verify(exactly = 2) {
                    conversationHistoryService.createAssistantMessage(userId = user.id, message = any())
                }

                val messageSlot = slot<String>()
                val chatbotTemplateNotificationSlot = slot<ChatbotRawTemplatedNotification>()

                verifyOrder {
                    notificationService.notify(user.id, any(), capture(messageSlot))
                    notificationService.notify(capture(chatbotTemplateNotificationSlot))
                }

                result.isRight() shouldBe true
                result shouldBe ActionResult.WithoutCompletion(actionType = ActionType.ONBOARDING_SINGLE_PIX).right()
                messageSlot.captured shouldContain "getTestPaymentExplanationMessage"
                chatbotTemplateNotificationSlot.captured.configurationKey shouldBe mockedNotification.configurationKey
            }

            describe("quando não consegue criar o PIX") {
                it("ocorre erro genérico ao criar o PIX") {
                    coEvery { paymentAdapter.createOnboardingSinglePix(userId = user.id) } returns PaymentAdapterError.PixKeyNotFound.left()

                    val result = onboardingExecutor.execute(
                        buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.ACCEPT, "pixKey", "CPF"),
                    )

                    verify(exactly = 2) {
                        conversationHistoryService.createAssistantMessage(userId = user.id, message = any())
                    }

                    val messageNotFoundPixSlot = slot<String>()
                    val messageAskForPixSlot = slot<String>()

                    verifyOrder {
                        notificationService.notify(user.id, any(), capture(messageNotFoundPixSlot))
                        notificationService.notify(user.id, any(), capture(messageAskForPixSlot))
                    }

                    result.isLeft() shouldBe false
                    result shouldBe ActionResult.WithoutCompletion(actionType = ActionType.ONBOARDING_SINGLE_PIX).right()
                    messageNotFoundPixSlot.captured shouldContain "getTestNotFoundPixKey"
                    messageAskForPixSlot.captured shouldContain "getTestAskForPixKey"
                }

                it("porque o PIX já foi pago") {
                    coEvery { paymentAdapter.createOnboardingSinglePix(userId = user.id) } returns PaymentAdapterError.OnboardingSinglePixAlreadyPaid.left()

                    val result = onboardingExecutor.execute(
                        buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.ACCEPT, "pixKey", "CPF"),
                    )

                    verify(exactly = 3) {
                        conversationHistoryService.createAssistantMessage(userId = user.id, message = any())
                    }

                    val messageAlreadyPaidSlot = slot<String>()
                    val messageSkippedFinishedSlot = slot<String>()
                    val messageFinishedContextSlot = slot<String>()

                    verifyOrder {
                        notificationService.notify(
                            user.id,
                            any(),
                            capture(messageAlreadyPaidSlot),
                            any(),
                            CTALink("Abrir no App", "app/contas"),
                            null,
                        )
                        notificationService.notify(
                            user.id,
                            any(),
                            capture(messageSkippedFinishedSlot),
                            any(),
                            CTALink("Adicionar no App", "app/contas"),
                            null,
                        )
                        notificationService.notify(
                            user.id,
                            any(),
                            capture(messageFinishedContextSlot),
                            any(),
                            CTALink("Abrir no App", "app/contas"),
                            null,
                        )
                    }

                    result.isLeft() shouldBe false
                    result shouldBe ActionResult.WithoutCompletion(actionType = ActionType.ONBOARDING_SINGLE_PIX).right()
                    messageAlreadyPaidSlot.captured shouldContain "getTestPixAlreadyPaid"
                    messageSkippedFinishedSlot.captured shouldContain "getSkippedFinishedMessage"
                    messageFinishedContextSlot.captured shouldContain "getFinishedContextMessage"
                }
            }
        }

        describe("quando receber uma action CONFIRM") {
            describe("e existe um PIX para ser realizado") {
                it("e consegue agendar o PIX normalmente") {
                    every { tenantConfiguration.features } returns mockFeatures
                    every { tenantService.getConfiguration() } returns tenantConfiguration

                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns Unit.right()

                    val command = buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.CONFIRM, null, null)

                    val result = onboardingExecutor.execute(
                        // Early access temporário para enviar a mensagem do incentivo do open-finance
                        command.copy(user = command.user.copy(accountGroups = listOf("BETA"))),
                    )

                    verify {
                        conversationHistoryService.createAssistantMessage(userId = user.id, message = any())
                    }

                    val messageFinishedContextSlot = slot<String>()

                    val config = customNotificationService.config().onboardingPromoteSweepingAccount!!
                    verify {
                        customNotificationService.send(match { it.id.value == user.id.value }, emptyList(), config)
                        notificationService.notify(
                            user.id,
                            any(),
                            capture(messageFinishedContextSlot),
                            any(),
                            CTALink("Abrir no App", "app/contas"),
                            null,
                        )
                    }
                    messageFinishedContextSlot.captured shouldContain "getFinishedContextMessage"
                    result shouldBe ActionResult.WithoutCompletion(actionType = ActionType.ONBOARDING_SINGLE_PIX).right()
                }

                it("e não consegue agendar o PIX") {
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns PaymentAdapterError.BillNotActiveError.left()

                    val result = onboardingExecutor.execute(
                        buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.CONFIRM, null, null),
                    )

                    result.isLeft() shouldBe true
                    result.mapLeft {
                        it.needCompletion shouldBe false
                        it.message shouldBe "Não é possível completar a transação porque uma ou mais contas solicitadas não está mais disponível para pagamento. Tente novamente."
                        it.actionType shouldBe ActionType.ONBOARDING_SINGLE_PIX
                    }
                }
            }

            describe("e não existe um PIX para ser realizado") {
                coEvery { conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(any()) } returns
                    BillComingDueHistoryState(
                        user = user,
                        walletWithBills = WalletWithBills(bills = billViews, walletId = WalletId(walletId), walletName = walletName),
                        internalStateControl = InternalStateControl(
                            shouldSynchronizeBeforeCompletion = false,
                            billComingDueNotifiedAt = LocalDateTime.now().minusHours(1),
                        ),
                        balance = balance,
                        startDate = LocalDate.now(),
                        endDate = LocalDate.now(),
                        contacts = emptyList(),
                        lastBillsUpdatedAt = ZonedDateTime.now(),
                        subscription = null,
                        onboardingState = null,
                    )

                it("e não consegue buscar o PIX no histórico do onbarding") {
                    every { conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(any()) } returns
                        BillComingDueHistoryState(
                            user = user,
                            walletWithBills = WalletWithBills(bills = billViews, walletId = WalletId(walletId), walletName = walletName),
                            internalStateControl = InternalStateControl(
                                shouldSynchronizeBeforeCompletion = false,
                                billComingDueNotifiedAt = LocalDateTime.now().minusHours(1),
                            ),
                            balance = balance,
                            startDate = LocalDate.now(),
                            endDate = LocalDate.now(),
                            contacts = emptyList(),
                            lastBillsUpdatedAt = ZonedDateTime.now(),
                            subscription = null,
                            onboardingState = null,
                        )

                    coEvery { paymentAdapter.createOnboardingSinglePix(userId = user.id, keyValue = any(), keyType = any()) } returns PaymentAdapterError.BillNotActiveError.left()

                    val result = onboardingExecutor.execute(
                        buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.CONFIRM, null, null),
                    )

                    result.isRight() shouldBe true
                }
            }
        }

        describe("quando receber uma action SKIP") {
            it("deve finalizar o fluxo") {
                val result = onboardingExecutor.execute(
                    buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.SKIP, null, null),
                )

                verify(exactly = 3) {
                    conversationHistoryService.createAssistantMessage(userId = user.id, message = any())
                }

                val messageRemindMeLaterSlot = slot<String>()
                val messageFinishedSlot = slot<String>()
                val messageFinishedContextSlot = slot<String>()

                verifyOrder {
                    notificationService.notify(user.id, any(), capture(messageRemindMeLaterSlot))
                    notificationService.notify(
                        user.id,
                        any(),
                        capture(messageFinishedSlot),
                        any(),
                        CTALink("Adicionar no App", "app/contas"),
                        null,
                    )
                    notificationService.notify(
                        user.id,
                        any(),
                        capture(messageFinishedContextSlot),
                        any(),
                        CTALink("Abrir no App", "app/contas"),
                        null,
                    )
                }

                result shouldBe ActionResult.WithoutCompletion(actionType = ActionType.ONBOARDING_SINGLE_PIX).right()
                messageRemindMeLaterSlot.captured shouldContain "getRemindMeLaterMessage"
                messageFinishedSlot.captured shouldContain "getSkippedFinishedMessage"
                messageFinishedContextSlot.captured shouldContain "getFinishedContextMessage"
            }
        }

        describe("quando receber uma action PIX") {
            it("que a chave PIX é inválida") {
                every { pixKey.isValid() } returns false

                val result = onboardingExecutor.execute(
                    buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.PIX, "pixKey", "CPF"),
                )

                result.isLeft() shouldBe true
                result.mapLeft {
                    it.needCompletion shouldBe false
                    it.actionType shouldBe ActionType.ONBOARDING_SINGLE_PIX
                }
            }

            it("e não possuir histórico para seguir a operação") {
                every { conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(any()) } returns
                    BillComingDueHistoryState(
                        user = user,
                        walletWithBills = WalletWithBills(bills = billViews, walletId = WalletId(walletId), walletName = walletName),
                        internalStateControl = InternalStateControl(
                            shouldSynchronizeBeforeCompletion = false,
                            billComingDueNotifiedAt = LocalDateTime.now().minusHours(1),
                        ),
                        balance = balance,
                        startDate = LocalDate.now(),
                        endDate = LocalDate.now(),
                        contacts = emptyList(),
                        lastBillsUpdatedAt = ZonedDateTime.now(),
                        subscription = null,
                        onboardingState = OnboardingState(
                            hasAcceptedExamplePayment = true,
                            pixKeyType = PixKeyType.CPF,
                            pixKeyValue = "94544906059",
                            billView = billView,
                        ),
                    )

                coEvery { paymentAdapter.createOnboardingSinglePix(userId = user.id, keyValue = any(), keyType = any()) } returns PaymentAdapterError.BillNotActiveError.left()

                val result = onboardingExecutor.execute(
                    buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.PIX, "94544906059", "CPF"),
                )

                result.isRight() shouldBe true
            }

            describe("e ocorre") {
                it("erro de chave PIX de usuário diferente") {
                    coEvery { paymentAdapter.createOnboardingSinglePix(userId = user.id, keyValue = any(), keyType = any()) } returns PaymentAdapterError.PixFromDifferentOwner.left()

                    val result = onboardingExecutor.execute(
                        buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.PIX, "94544906059", "CPF"),
                    )

                    verify(exactly = 1) {
                        conversationHistoryService.createAssistantMessage(userId = user.id, message = any())
                    }

                    val message = slot<String>()

                    verify {
                        notificationService.notify(user.id, any(), capture(message))
                    }

                    result.isRight() shouldBe true
                    result shouldBe ActionResult.WithoutCompletion(actionType = ActionType.ONBOARDING_SINGLE_PIX).right()
                    message.captured shouldContain "getTestOtherOwnerPixKey"
                }

                it("erro de chave PIX não encontrada") {
                    coEvery { paymentAdapter.createOnboardingSinglePix(userId = user.id, keyValue = any(), keyType = any()) } returns PaymentAdapterError.PixKeyNotFound.left()

                    val result = onboardingExecutor.execute(
                        buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.PIX, "94544906059", "CPF"),
                    )

                    verify(exactly = 1) {
                        conversationHistoryService.createAssistantMessage(userId = user.id, message = any())
                    }

                    val message = slot<String>()

                    verify {
                        notificationService.notify(user.id, any(), capture(message))
                    }

                    result.isRight() shouldBe true
                    result shouldBe ActionResult.WithoutCompletion(actionType = ActionType.ONBOARDING_SINGLE_PIX).right()
                    message.captured shouldContain "getTestIncorrectPixKey"
                }

                it("erro de PIX já pago") {
                    coEvery { paymentAdapter.createOnboardingSinglePix(userId = user.id, keyValue = any(), keyType = any()) } returns PaymentAdapterError.OnboardingSinglePixAlreadyPaid.left()

                    val result = onboardingExecutor.execute(
                        buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.PIX, "94544906059", "CPF"),
                    )

                    verify(exactly = 3) {
                        conversationHistoryService.createAssistantMessage(userId = user.id, message = any())
                    }

                    val messageAlreadyPaidSlot = slot<String>()
                    val messageSkippedFinishedSlot = slot<String>()
                    val messageFinishedContextSlot = slot<String>()

                    verifyOrder {
                        notificationService.notify(
                            user.id,
                            any(),
                            capture(messageAlreadyPaidSlot),
                            any(),
                            CTALink("Abrir no App", "app/contas"),
                            null,
                        )
                        notificationService.notify(
                            user.id,
                            any(),
                            capture(messageSkippedFinishedSlot),
                            any(),
                            CTALink("Adicionar no App", "app/contas"),
                            null,
                        )
                        notificationService.notify(
                            user.id,
                            any(),
                            capture(messageFinishedContextSlot),
                            any(),
                            CTALink("Abrir no App", "app/contas"),
                            null,
                        )
                    }

                    result.isRight() shouldBe true
                    result shouldBe ActionResult.WithoutCompletion(actionType = ActionType.ONBOARDING_SINGLE_PIX).right()
                    messageAlreadyPaidSlot.captured shouldContain "getTestPixAlreadyPaid"
                    messageSkippedFinishedSlot.captured shouldContain "getSkippedFinishedMessage"
                    messageFinishedContextSlot.captured shouldContain "getFinishedContextMessage"
                }

                it("erro de criação") {
                    coEvery { paymentAdapter.createOnboardingSinglePix(userId = user.id, keyValue = any(), keyType = any()) } returns PaymentAdapterError.OnboardingSinglePixCreateError.left()

                    val result = onboardingExecutor.execute(
                        buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.PIX, "94544906059", "CPF"),
                    )

                    verify(exactly = 1) {
                        conversationHistoryService.createAssistantMessage(userId = user.id, message = any())
                    }

                    val message = slot<String>()

                    verify {
                        notificationService.notify(user.id, any(), capture(message))
                    }

                    result.isRight() shouldBe true
                    result shouldBe ActionResult.WithoutCompletion(actionType = ActionType.ONBOARDING_SINGLE_PIX).right()
                    message.captured shouldContain "getTestNotFoundPixKeyInputted"
                }

                it("erro de genérico") {
                    coEvery { paymentAdapter.createOnboardingSinglePix(userId = user.id, keyValue = any(), keyType = any()) } returns PaymentAdapterError.NotFoundError.left()

                    val result = onboardingExecutor.execute(
                        buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.PIX, "94544906059", "CPF"),
                    )

                    verify(exactly = 3) {
                        conversationHistoryService.createAssistantMessage(userId = user.id, message = any())
                    }

                    val messageCallSupportSlot = slot<String>()
                    val messageSkippedFinishedSlot = slot<String>()
                    val messageFinishedContextSlot = slot<String>()

                    verifyOrder {
                        notificationService.notify(
                            user.id,
                            any(),
                            capture(messageCallSupportSlot),
                            any(),
                            CTALink("Falar com Atendimento", "https://wa.me/5521997151483"),
                            null,
                        )
                        notificationService.notify(
                            user.id,
                            any(),
                            capture(messageSkippedFinishedSlot),
                            any(),
                            CTALink("Adicionar no App", "app/contas"),
                            null,
                        )
                        notificationService.notify(
                            user.id,
                            any(),
                            capture(messageFinishedContextSlot),
                            any(),
                            CTALink("Abrir no App", "app/contas"),
                            null,
                        )
                    }

                    result.isRight() shouldBe true
                    result shouldBe ActionResult.WithoutCompletion(actionType = ActionType.ONBOARDING_SINGLE_PIX).right()
                    messageCallSupportSlot.captured shouldContain "getTestCallSupportError"
                    messageSkippedFinishedSlot.captured shouldContain "getSkippedFinishedMessage"
                    messageFinishedContextSlot.captured shouldContain "getFinishedContextMessage"
                }
            }

            describe("realiza o agendamento do PIX") {
                coEvery { paymentAdapter.createOnboardingSinglePix(userId = user.id, keyValue = any(), keyType = any()) } returns billView.right()

                val result = onboardingExecutor.execute(
                    buildCommand(OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.PIX, "94544906059", "CPF"),
                )

                result.isRight() shouldBe true
                result shouldBe ActionResult.WithoutCompletion(actionType = ActionType.ONBOARDING_SINGLE_PIX).right()
            }
        }
    }

    private fun buildCommand(
        action: OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum,
        key: String?,
        type: String?,
    ) = ActionExecutorCommand.OnboardingSinglePixActionExecutorCommand(
        user = user,
        action = Action.OnboardingSinglePix(
            payload = OnboardingSinglePixAction(
                action = action.name,
                key = key,
                type = type,
            ),
        ),
    )

    private fun setup() {
        every { mockFeatures.openFinanceIncentive } returns true
        every { tenantService.getConfiguration() } returns tenantConfiguration.copy(features = mockFeatures)
        every { customNotificationService.send(any<User>(), any(), any()) } just Runs
        every { onboardingSinglePixNotificationService.getSinglePixPaymentExplanationMessage() } returns "getTestPaymentExplanationMessage"
        every { onboardingSinglePixNotificationService.getSinglePixNotFoundPixKey() } returns "getTestNotFoundPixKey"
        every { onboardingSinglePixNotificationService.getSinglePixAskForPixKey() } returns "getTestAskForPixKey"
        every { onboardingSinglePixNotificationService.getSinglePixRemindMeLaterMessage() } returns "getRemindMeLaterMessage"
        every { onboardingSinglePixNotificationService.getSinglePixPromoteSweepingAccountMessage() } returns "getPromoteSweepingAccountMessage"
        every { onboardingSinglePixNotificationService.getSinglePixFinishedContextMessage() } returns "getFinishedContextMessage"
        every { onboardingSinglePixNotificationService.getSinglePixOtherOwnerPixKey() } returns "getTestOtherOwnerPixKey"
        every { onboardingSinglePixNotificationService.getSinglePixIncorrectPixKey() } returns "getTestIncorrectPixKey"
        every { onboardingSinglePixNotificationService.getSinglePixSkippedFinishedMessage() } returns "getSkippedFinishedMessage"
        every { onboardingSinglePixNotificationService.getSinglePixAlreadyPaid() } returns "getTestPixAlreadyPaid"
        every { onboardingSinglePixNotificationService.getSinglePixNotFoundPixKeyInputted() } returns "getTestNotFoundPixKeyInputted"
        every { onboardingSinglePixNotificationService.getSinglePixCallSupportError() } returns "getTestCallSupportError"
    }
}