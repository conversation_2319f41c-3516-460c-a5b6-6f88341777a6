package ai.chatbot.app.conversation.actions

import ai.chatbot.app.ManualEntryId
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.conversation.Action
import ai.chatbot.app.conversation.ActionExecutorCommand
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.getCustomNotificationServiceMock
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.prompt.DEFAULT_ERROR_MESSAGE
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import arrow.core.left
import arrow.core.right
import io.kotest.assertions.fail
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class RemoveManualEntryExecutorTest : DescribeSpec() {
    lateinit var customNotificationService: CustomNotificationService
    lateinit var paymentAdapter: PaymentAdapter
    lateinit var executor: RemoveManualEntryExecutor

    private val user = User(
        id = UserId("*************"),
        accountId = AccountId("ACCOUNT-ID"),
        name = "Test User",
        accountGroups = emptyList(),
        status = AccountStatus.ACTIVE,
    )

    private fun buildCommand(
        manualEntryId: String = "MANUAL-ENTRY-ID",
    ) = ActionExecutorCommand.RemoveManualEntryActionExecutorCommand(
        user = user,
        action = Action.RemoveManualEntry(manualEntryId = manualEntryId),
    )

    init {
        beforeEach {
            clearAllMocks()
            customNotificationService = getCustomNotificationServiceMock()
            paymentAdapter = mockk()

            executor = RemoveManualEntryExecutor(
                paymentAdapter = paymentAdapter,
                customNotificationService = customNotificationService,
            )
        }

        describe("validate") {
            it("sempre retorna true") {
                val command = buildCommand()
                val result = executor.validate(command)
                result shouldBe true
                verify(exactly = 0) { customNotificationService.send(any<User>(), any(), any()) }
            }
        }

        describe("execute") {
            it("deve remover com sucesso e enviar mensagem") {
                val command = buildCommand()
                val manualEntryId = ManualEntryId(command.action.manualEntryId)

                every {
                    paymentAdapter.removeManualEntry(
                        userId = user.id,
                        manualEntryId = manualEntryId,
                    )
                } returns Unit.right()

                val result = executor.execute(command)
                result.isRight() shouldBe true

                val expectedConfig = customNotificationService.config().removeManualEntrySuccess
                verify {
                    customNotificationService.send(
                        user = any(),
                        notificationConfig = expectedConfig,
                        params = listOf(),
                    )
                }
            }

            it("deve retornar erro quando o payment adapter retornar erro") {
                val command = buildCommand()
                val error = PaymentAdapterError.ServerError(Exception("Error removing manual entry"))

                every {
                    paymentAdapter.removeManualEntry(
                        userId = user.id,
                        manualEntryId = ManualEntryId(command.action.manualEntryId),
                    )
                } returns error.left()

                val result = executor.execute(command)
                result.isLeft() shouldBe true
                result.fold(
                    { error ->
                        error.message shouldBe DEFAULT_ERROR_MESSAGE
                        error.actionType shouldBe ActionType.REMOVE_MANUAL_ENTRY
                    },
                    { fail("Should have returned error") },
                )

                verify(exactly = 0) { customNotificationService.send(any()) }
            }
        }
    }
}