package ai.chatbot.app.conversation.actions

import ai.chatbot.app.ManualEntryId
import ai.chatbot.app.ManualEntryType
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.conversation.Action
import ai.chatbot.app.conversation.ActionExecutorCommand
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.getCustomNotificationServiceMock
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.NotificationMap
import ai.chatbot.app.notification.NotificationParam
import ai.chatbot.app.prompt.DEFAULT_ERROR_MESSAGE
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.brazilDateFormat
import ai.chatbot.app.utils.dateFormat
import arrow.core.left
import arrow.core.right
import io.kotest.assertions.fail
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate

class CreateManualEntryExecutorTest : DescribeSpec() {

    lateinit var customNotificationService: CustomNotificationService
    lateinit var paymentAdapter: PaymentAdapter
    lateinit var executor: CreateManualEntryExecutor

    private val user = User(
        id = UserId("*************"),
        accountId = AccountId("ACCOUNT-ID"),
        name = "Test User",
        accountGroups = emptyList(),
        status = AccountStatus.ACTIVE,
    )

    private fun buildCommand(
        amount: Long = 1000L,
        title: String = "Mercado",
        type: ManualEntryType = ManualEntryType.EXTERNAL_PAYMENT,
        dueDate: String = LocalDate.now().toString(),
    ) = ActionExecutorCommand.CreateManualEntryActionExecutorCommand(
        user = user,
        action = Action.CreateManualEntry(
            amount = amount,
            title = title,
            type = type,
            dueDate = dueDate,
        ),
    )

    private fun buildReminderCommand(
        amount: Long = 200000L,
        title: String = "Impostos",
        type: ManualEntryType = ManualEntryType.REMINDER,
        dueDate: String = getLocalDate().plusDays(1).format(dateFormat),
    ) = ActionExecutorCommand.CreateManualEntryActionExecutorCommand(
        user = user,
        action = Action.CreateManualEntry(
            amount = amount,
            title = title,
            type = type,
            dueDate = dueDate,
        ),
    )

    init {
        beforeEach {
            clearAllMocks()
            customNotificationService = getCustomNotificationServiceMock()
            paymentAdapter = mockk()

            executor = CreateManualEntryExecutor(
                paymentAdapter = paymentAdapter,
                customNotificationService = customNotificationService,
            )
        }

        describe("validate") {
            it("EXTERNAL_PAYMENT com valor e título corretos") {
                val command = buildCommand()
                val result = executor.validate(command)
                result shouldBe true
                verify(exactly = 0) { customNotificationService.send(any<User>(), any(), any()) }
            }

            it("EXTERNAL_PAYMENT sem valor e título") {
                val command = buildCommand(amount = 0, title = "")
                val result = executor.validate(command)
                result shouldBe false

                val expectedConfig = customNotificationService.config().createManualEntryErrorNoAmountAndTitle
                verify {
                    customNotificationService.send(any(), any(), expectedConfig)
                }
            }

            it("EXTERNAL_PAYMENT sem valor") {
                val command = buildCommand(amount = 0)
                val result = executor.validate(command)
                result shouldBe false

                val expectedConfig = customNotificationService.config().createManualEntryErrorNoAmount
                verify {
                    customNotificationService.send(
                        user = any(),
                        notificationConfig = expectedConfig,
                        params = match { NotificationMap(NotificationParam.TITLE, "Mercado") in it },
                    )
                }
            }

            it("EXTERNAL_PAYMENT sem título") {
                val command = buildCommand(title = "")
                val result = executor.validate(command)
                result shouldBe false

                val expectedConfig = customNotificationService.config().createManualEntryErrorNoTitle
                verify {
                    customNotificationService.send(
                        user = any(),
                        notificationConfig = expectedConfig,
                        params = match { NotificationMap(NotificationParam.AMOUNT, "R\$ 10,00") in it },
                    )
                }
            }

            it("REMINDER sem título e data") {
                val command = buildReminderCommand(amount = 0, title = "", dueDate = "")
                val result = executor.validate(command)
                result shouldBe false

                val expectedConfig = customNotificationService.config().createReminderErrorNoTitleAndDate
                verify {
                    customNotificationService.send(any(), any(), expectedConfig)
                }
            }

            it("REMINDER sem título") {
                val command = buildReminderCommand(amount = 0, title = "")
                val result = executor.validate(command)
                result shouldBe false

                val expectedConfig = customNotificationService.config().createReminderErrorNoTitle
                verify {
                    customNotificationService.send(any(), any(), expectedConfig)
                }
            }

            it("REMINDER sem data") {
                val command = buildReminderCommand(amount = 0, dueDate = "")
                val result = executor.validate(command)
                result shouldBe false

                val expectedConfig = customNotificationService.config().createReminderErrorNoDate
                verify {
                    customNotificationService.send(any(), any(), expectedConfig)
                }
            }

            it("REMINDER com data no passado") {
                val command = buildReminderCommand(amount = 0, dueDate = getLocalDate().minusDays(1).format(dateFormat))
                val result = executor.validate(command)
                result shouldBe false

                val expectedConfig = customNotificationService.config().createReminderErrorDateInPast
                verify {
                    customNotificationService.send(any(), any(), expectedConfig)
                }
            }

            it("REMINDER com data de hoje") {
                val command = buildReminderCommand(amount = 0, dueDate = getLocalDate().format(dateFormat))
                val result = executor.validate(command)
                result shouldBe false

                val expectedConfig = customNotificationService.config().createReminderErrorDateInPast
                verify {
                    customNotificationService.send(any(), any(), expectedConfig)
                }
            }
        }

        describe("execute") {
            it("EXTERNAL_PAYMENT criado com sucesso") {
                val command = buildCommand()
                val manualEntryId = ManualEntryId("MANUAL-ENTRY-ID")

                every {
                    paymentAdapter.createManualEntry(
                        userId = user.id,
                        title = command.action.title,
                        description = "",
                        amount = command.action.amount,
                        type = command.action.type,
                        dueDate = LocalDate.parse(command.action.dueDate),
                    )
                } returns manualEntryId.right()

                val result = executor.execute(command)
                result.isRight() shouldBe true

                val expectedConfig = customNotificationService.config().createManualEntrySuccess
                verify {
                    customNotificationService.send(
                        user = any(),
                        notificationConfig = expectedConfig,
                        params = match {
                            NotificationMap(NotificationParam.TITLE, "Mercado") in it &&
                                NotificationMap(NotificationParam.AMOUNT, "R\$ 10,00") in it &&
                                NotificationMap(NotificationParam.MANUAL_ENTRY_ID, manualEntryId.value) in it
                        },
                    )
                }
            }

            it("REMINDER criado com sucesso") {
                val command = buildReminderCommand()
                val manualEntryId = ManualEntryId("MANUAL-ENTRY-ID")

                every {
                    paymentAdapter.createManualEntry(
                        userId = user.id,
                        title = command.action.title,
                        description = "",
                        amount = command.action.amount,
                        type = command.action.type,
                        dueDate = LocalDate.parse(command.action.dueDate),
                    )
                } returns manualEntryId.right()

                val result = executor.execute(command)
                result.isRight() shouldBe true

                val expectedConfig = customNotificationService.config().createReminderSuccess
                val expectedDate = LocalDate.parse(command.action.dueDate, dateFormat).format(brazilDateFormat)
                verify {
                    customNotificationService.send(
                        user = any(),
                        notificationConfig = expectedConfig,
                        params = match {
                            NotificationMap(NotificationParam.TITLE, "Impostos") in it &&
                                NotificationMap(NotificationParam.AMOUNT, "R\$ 2.000,00") in it &&
                                NotificationMap(NotificationParam.DATE, expectedDate) in it &&
                                NotificationMap(NotificationParam.MANUAL_ENTRY_ID, manualEntryId.value) in it
                        },
                    )
                }
            }

            it("REMINDER sem valor criado com sucesso") {
                val command = buildReminderCommand(amount = 0L)
                val manualEntryId = ManualEntryId("MANUAL-ENTRY-ID")

                every {
                    paymentAdapter.createManualEntry(
                        userId = user.id,
                        title = command.action.title,
                        description = "",
                        amount = command.action.amount,
                        type = command.action.type,
                        dueDate = LocalDate.parse(command.action.dueDate),
                    )
                } returns manualEntryId.right()

                val result = executor.execute(command)
                result.isRight() shouldBe true

                val expectedConfig = customNotificationService.config().createReminderSuccessNoAmount
                val expectedDate = LocalDate.parse(command.action.dueDate, dateFormat).format(brazilDateFormat)
                verify {
                    customNotificationService.send(
                        user = any(),
                        notificationConfig = expectedConfig,
                        params = match {
                            NotificationMap(NotificationParam.TITLE, "Impostos") in it &&
                                NotificationMap(NotificationParam.DATE, expectedDate) in it &&
                                NotificationMap(NotificationParam.MANUAL_ENTRY_ID, manualEntryId.value) in it
                        },
                    )
                }
            }

            it("erro ao criar EXTERNAL_PAYMENT") {
                val command = buildCommand()
                val error = PaymentAdapterError.ServerError(Exception("Error creating manual entry"))

                every {
                    paymentAdapter.createManualEntry(
                        userId = user.id,
                        title = command.action.title,
                        description = "",
                        amount = command.action.amount,
                        type = command.action.type,
                        dueDate = LocalDate.parse(command.action.dueDate),
                    )
                } returns error.left()

                val result = executor.execute(command)
                result.isLeft() shouldBe true
                result.fold(
                    { error ->
                        error.message shouldBe DEFAULT_ERROR_MESSAGE
                        error.actionType shouldBe ActionType.CREATE_MANUAL_ENTRY
                    },
                    { fail("Should have returned error") },
                )

                verify(exactly = 0) { customNotificationService.send(any()) }
            }
        }
    }
}