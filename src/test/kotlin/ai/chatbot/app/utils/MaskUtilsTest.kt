package ai.chatbot.app.utils

import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe

class MaskUtilsTest : DescribeSpec() {
    init {
        describe("ao receber uma mensagem com nomes de beneficiários e valores") {
            listOf(
                Pair(
                    "O<PERSON> Fu<PERSON>o, aqui é o <PERSON>, assistente pessoal da Friday. Vi que você tem as seguintes contas vencendo hoje: 1\uFE0F⃣ Aluguel Apartamento no valor de R\$ 10.030,65 Você quer que eu te ajude a fazer esses pagamentos por aqui?",
                    "<PERSON><PERSON>lan<PERSON>, aqui é o <PERSON>, assistente pessoal da Friday. Vi que você tem as seguintes contas vencendo hoje: 1\uFE0F⃣ ******************* no valor de R\$ **.***,** Você quer que eu te ajude a fazer esses pagamentos por aqui?",
                ),
                Pair(
                    "<PERSON><PERSON>, aqui é o <PERSON>, assistente pessoal da Friday. Vi que você tem as seguintes contas vencendo hoje: 1\uFE0F⃣ Terapeuta (Terapia - Novembro) no valor de R\$ 600,00 2\uFE0F⃣ Empresa Fake S.A. no valor de R\$ 15.392,07 3\uFE0F⃣ Empresa Fake S.A. no valor de R\$ 3.663,71 Você quer que eu te ajude a fazer esses pagamentos por aqui?",
                    "Oi Fulano, aqui é o Fred, assistente pessoal da Friday. Vi que você tem as seguintes contas vencendo hoje: 1\uFE0F⃣ ****************************** no valor de R\$ ***,** 2\uFE0F⃣ ***************** no valor de R\$ **.***,** 3\uFE0F⃣ ***************** no valor de R\$ *.***,** Você quer que eu te ajude a fazer esses pagamentos por aqui?",
                ),
                Pair(
                    "Olá Fulano, você tem a seguinte conta pendente para hoje: 1\uFE0F⃣ Jane Foo Bar Doe (Diarista) no valor de R\$ 150,00, agendada para hoje.",
                    "Olá Fulano, você tem a seguinte conta pendente para hoje: 1\uFE0F⃣ *************************** no valor de R\$ ***,**, agendada para hoje.",
                ),
                Pair(
                    "Oi Fulano, aqui é o Fred, assistente pessoal da Friday. Vi que você tem as seguintes contas vencendo hoje:\\n1\uFE0F⃣ BANCO BRADESCO S.A no valor de R\$ 1.943,29\\nVocê quer que eu te ajude a fazer esses pagamentos por aqui?",
                    "Oi Fulano, aqui é o Fred, assistente pessoal da Friday. Vi que você tem as seguintes contas vencendo hoje:\\n1\uFE0F⃣ ****************** no valor de R\$ *.***,**\\nVocê quer que eu te ajude a fazer esses pagamentos por aqui?",
                ),
            ).forEach { (message, expected) ->
                it("deve retornar a mensagem com os nomes e valores escondidos") {
                    maskConversationMessage(message) shouldBe expected
                }
            }
        }
    }
}