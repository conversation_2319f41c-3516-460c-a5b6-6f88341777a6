package ai.chatbot.app.utils

import ai.chatbot.app.classification.ConversationTag.CONTA_CONECTADA
import ai.chatbot.app.classification.ConversationTag.NON_USER
import ai.chatbot.app.classification.ConversationTag.PAGAMENTO_CONTAS
import ai.chatbot.app.classification.ConversationTag.PIX
import ai.chatbot.app.classification.ConversationTag.PROBLEMAS_TECNICOS
import ai.chatbot.app.classification.ConversationTag.USUARIO_ATENDIDO
import ai.chatbot.app.classification.ConversationTag.USUARIO_NAO_ATENDIDO
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe

class DailyLogUtilsKtTest : DescribeSpec() {

    init {

        describe("ao contar tags") {
            it("deve contar tags concomitantes corretamente") {
                val result = calculateTagTable(
                    PIX,
                    listOf(
                        listOf(PIX, USUARIO_ATENDIDO, CONTA_CONECTADA),
                        listOf(PIX, USUARIO_ATENDIDO),
                        listOf(PIX, USUARIO_ATENDIDO),
                        listOf(PIX, USUARIO_NAO_ATENDIDO),
                        listOf(PIX, USUARIO_NAO_ATENDIDO, CONTA_CONECTADA, PROBLEMAS_TECNICOS),
                        listOf(PIX, USUARIO_NAO_ATENDIDO, PROBLEMAS_TECNICOS),
                        listOf(PIX, NON_USER),
                        listOf(PAGAMENTO_CONTAS, USUARIO_ATENDIDO),
                    ),
                )

                result[PIX] shouldBe 7
                result[USUARIO_ATENDIDO] shouldBe 3
                result[USUARIO_NAO_ATENDIDO] shouldBe 3
                result[CONTA_CONECTADA] shouldBe 2
                result[PROBLEMAS_TECNICOS] shouldBe 2
                result[NON_USER] shouldBe 1
                result[PAGAMENTO_CONTAS] shouldBe 0
            }

            it("não deve quebrar se a tag não estiver presente") {
                val result = calculateTagTable(
                    PAGAMENTO_CONTAS,
                    listOf(
                        listOf(PIX, USUARIO_ATENDIDO, CONTA_CONECTADA),
                        listOf(PIX, USUARIO_ATENDIDO),
                        listOf(PIX, USUARIO_ATENDIDO),
                        listOf(PIX, USUARIO_NAO_ATENDIDO),
                        listOf(PIX, USUARIO_NAO_ATENDIDO, CONTA_CONECTADA, PROBLEMAS_TECNICOS),
                        listOf(PIX, USUARIO_NAO_ATENDIDO, PROBLEMAS_TECNICOS),
                        listOf(PIX, NON_USER),
                    ),
                )

                result[PAGAMENTO_CONTAS] shouldBe 0
            }
        }
    }
}