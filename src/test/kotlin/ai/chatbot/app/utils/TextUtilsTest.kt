package ai.chatbot.app.utils

import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe

class TextUtilsTest : DescribeSpec() {
    init {
        describe("ao verificar se uma string contém apenas emojis") {
            it("deve retornar true para mensagens só com emojis") {
                listOf(
                    "\uD83D\uDC4D",
                    "\uD83D\uDC4D\uD83C\uDFFB",
                    "\uD83D\uDC4D",
                    "\uD83D\uDE00",
                    "\uD83D\uDE00\uD83D\uDC4D",
                ).filter { it.containsOnlyEmojis() }.size shouldBe 5
            }

            it("deve retornar false para mensagens com caracteres que não sejam emojis") {
                listOf(
                    "Olá",
                    "Sim",
                    "1",
                    ".",
                    "Obrigada \uD83D\uDE00",
                ).filter { it.containsOnlyEmojis() }.size shouldBe 0
            }

            it("deve desconsiderar espaços") {
                listOf(
                    "\uD83D\uDC4D ",
                    "\uD83D\uDC4D \uD83D\uDC4D ",
                ).filter { it.containsOnlyEmojis() }.size shouldBe 2
            }

            it("deve retornar false para mensagens vazias") {
                listOf(
                    "",
                    " ",
                    "\n",
                ).filter { it.containsOnlyEmojis() }.size shouldBe 0
            }
        }
    }
}