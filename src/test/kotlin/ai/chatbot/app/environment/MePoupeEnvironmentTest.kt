package ai.chatbot.app.environment

import ai.chatbot.app.ME_POUPE_ENV
import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.notification.CustomNotificationService
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldNotBe
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest

@MicronautTest(environments = [ME_POUPE_ENV])
class MePoupeEnvironmentTest(
    private val tenantService: TenantService,
    private val tenantPropagator: TenantPropagator,
    private val templateNotificationConfig: CustomNotificationService,
) : DescribeSpec() {
    init {
        describe("Ao tentar carregar templates da Me Poupe") {
            it("não deve falhar") {
                tenantPropagator.executeWithTenant("ME-POUPE") {
                    templateNotificationConfig shouldNotBe null
                }
            }
        }
    }
}