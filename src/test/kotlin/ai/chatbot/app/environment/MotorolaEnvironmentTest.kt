package ai.chatbot.app.environment

import ai.chatbot.app.MOTOROLA_ENV
import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.notification.CustomNotificationService
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldNotBe
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest

@MicronautTest(environments = [MOTOROLA_ENV])
class MotorolaEnvironmentTest(
    private val tenantService: TenantService,
    private val tenantPropagator: TenantPropagator,
    private val templateNotificationConfig: CustomNotificationService,
) : DescribeSpec() {
    init {
        describe("Ao tentar carregar templates da Motorola") {
            it("não deve falhar") {
                tenantPropagator.executeWithTenant("MOTOROLA") {
                    templateNotificationConfig.config() shouldNotBe null
                }
            }
        }
    }
}