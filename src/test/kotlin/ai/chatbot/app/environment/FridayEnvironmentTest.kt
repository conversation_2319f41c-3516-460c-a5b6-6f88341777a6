package ai.chatbot.app.environment

import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.notification.CustomNotificationService
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldNotBe
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest

@MicronautTest(environments = [FRIDAY_ENV])
class FridayTemplatesTest(
    private val templateNotificationConfig: CustomNotificationService,
    private val tenantService: TenantService,
    private val tenantPropagator: TenantPropagator,
) : DescribeSpec() {
    init {
        describe("Ao tentar carregar templates da Friday") {
            it("não deve falhar") {
                tenantPropagator.executeWithTenant("FRIDAY") {
                    templateNotificationConfig.config() shouldNotBe null
                }
            }
        }
    }
}