package ai.chatbot.app.job
import ai.chatbot.app.classification.ClassificationResult
import ai.chatbot.app.classification.ConversationTag
import ai.chatbot.app.conversation.ChatMessageWrapper
import ai.chatbot.app.conversation.ConversationHistory
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.user.UserId
import ai.chatbot.integration.utils.generateConversation
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import java.time.LocalDate

class DailyLogQueryTest : DescribeSpec() {
    init {
        describe("rules") {
            describe("tag") {
                it("deve avaliar true se a tag estiver presente") {
                    val query = DailyLogQuery(
                        date = LocalDate.now(),
                        queries = listOf(
                            listOf(DailyLogQuery.Rule.parse("""{"tag": "OUTROS"}""")),
                        ),
                    )

                    val execution = buildExecution(
                        messages = generateConversation(
                            MessageType.USER to "Olá",
                            MessageType.ASSISTANT to "Ol<PERSON>, eu sou o Fred.",
                        ),
                        tags = listOf(ConversationTag.OUTROS, ConversationTag.APLICATIVO),
                    )

                    query.check(execution) shouldBe true
                }

                it("deve avaliar false se a tag não estiver presente") {
                    val query = DailyLogQuery(
                        date = LocalDate.now(),
                        queries = listOf(
                            listOf(DailyLogQuery.Rule.parse("""{"tag": "PAGAMENTO_CONTAS"}""")),
                        ),
                    )

                    val execution = buildExecution(
                        messages = generateConversation(
                            MessageType.USER to "Olá",
                            MessageType.ASSISTANT to "Olá, eu sou o Fred.",
                        ),
                        tags = listOf(ConversationTag.OUTROS, ConversationTag.APLICATIVO),
                    )

                    query.check(execution) shouldBe false
                }
            }

            describe("text") {
                it("deve avaliar true se alguma mensagem contiver o texto") {
                    val query = DailyLogQuery(
                        date = LocalDate.now(),
                        queries = listOf(
                            listOf(DailyLogQuery.Rule.parse("""{"text": "todas"}""")),
                        ),
                    )

                    val execution = buildExecution(
                        messages = generateConversation(
                            MessageType.ASSISTANT to "Aqui estão suas contas em aberto: ...",
                            MessageType.USER to "Sim, pagar todas",
                            MessageType.ASSISTANT to "O pagamento foi efetuado.",
                        ),
                        tags = listOf(ConversationTag.PAGAMENTO_CONTAS, ConversationTag.USUARIO_ATENDIDO),
                    )

                    query.check(execution) shouldBe true
                }
                it("deve avaliar true se alguma mensagem contiver o texto independente de case") {
                    val query = DailyLogQuery(
                        date = LocalDate.now(),
                        queries = listOf(
                            listOf(DailyLogQuery.Rule.parse("""{"text": "aqui"}""")),
                        ),
                    )

                    val execution = buildExecution(
                        messages = generateConversation(
                            MessageType.ASSISTANT to "Aqui estão suas contas em aberto: ...",
                            MessageType.USER to "Sim, pagar todas",
                            MessageType.ASSISTANT to "O pagamento foi efetuado.",
                        ),
                        tags = listOf(ConversationTag.PAGAMENTO_CONTAS, ConversationTag.USUARIO_ATENDIDO),
                    )

                    query.check(execution) shouldBe true
                }

                it("deve avaliar false se alguma mensagem não contiver o texto") {
                    val query = DailyLogQuery(
                        date = LocalDate.now(),
                        queries = listOf(
                            listOf(DailyLogQuery.Rule.parse("""{"text": "pix"}""")),
                        ),
                    )

                    val execution = buildExecution(
                        messages = generateConversation(
                            MessageType.ASSISTANT to "Aqui estão suas contas em aberto: ...",
                            MessageType.USER to "Sim, pagar todas",
                            MessageType.ASSISTANT to "O pagamento foi efetuado.",
                        ),
                        tags = listOf(ConversationTag.PAGAMENTO_CONTAS, ConversationTag.USUARIO_ATENDIDO),
                    )

                    query.check(execution) shouldBe false
                }
            }

            describe("not") {
                it("deve avaliar true se a regra interior avaliar false") {
                    val query = DailyLogQuery(
                        date = LocalDate.now(),
                        queries = listOf(
                            listOf(DailyLogQuery.Rule.parse("""{"not": {"tag": "PAGAMENTO_CONTAS"}}""")),
                        ),
                    )

                    val execution = buildExecution(
                        messages = generateConversation(
                            MessageType.USER to "Olá",
                            MessageType.ASSISTANT to "Olá, eu sou o Fred.",
                        ),
                        tags = listOf(ConversationTag.OUTROS, ConversationTag.APLICATIVO),
                    )

                    query.check(execution) shouldBe true
                }

                it("deve avaliar false se a regra interior avaliar true") {
                    val query = DailyLogQuery(
                        date = LocalDate.now(),
                        queries = listOf(
                            listOf(DailyLogQuery.Rule.parse("""{"not": {"tag": "OUTROS"}}""")),
                        ),
                    )

                    val execution = buildExecution(
                        messages = generateConversation(
                            MessageType.USER to "Olá",
                            MessageType.ASSISTANT to "Olá, eu sou o Fred.",
                        ),
                        tags = listOf(ConversationTag.OUTROS, ConversationTag.APLICATIVO),
                    )

                    query.check(execution) shouldBe false
                }
            }
        }

        describe("multiple rules") {
            it("deve avaliar true se todas as regras passarem") {
                val query = DailyLogQuery(
                    date = LocalDate.now(),
                    queries = listOf(
                        listOf(
                            DailyLogQuery.Rule.parse("""{"tag": "OUTROS"}"""),
                            DailyLogQuery.Rule.parse("""{"text": "fred"}"""),
                        ),
                    ),
                )

                val execution = buildExecution(
                    messages = generateConversation(
                        MessageType.USER to "Olá",
                        MessageType.ASSISTANT to "Olá, eu sou o Fred.",
                    ),
                    tags = listOf(ConversationTag.OUTROS, ConversationTag.APLICATIVO),
                )

                query.check(execution) shouldBe true
            }

            it("deve avaliar false se uma das regras falhar") {
                val query = DailyLogQuery(
                    date = LocalDate.now(),
                    queries = listOf(
                        listOf(
                            DailyLogQuery.Rule.parse("""{"tag": "OUTROS"}"""),
                            DailyLogQuery.Rule.parse("""{"text": "não existe na conversa"}"""),
                        ),
                    ),
                )

                val execution = buildExecution(
                    messages = generateConversation(
                        MessageType.USER to "Olá",
                        MessageType.ASSISTANT to "Olá, eu sou o Fred.",
                    ),
                    tags = listOf(ConversationTag.OUTROS, ConversationTag.APLICATIVO),
                )

                query.check(execution) shouldBe false
            }
        }

        describe("multiple queries") {
            it("deve avaliar true se uma das queries passar") {
                val query = DailyLogQuery(
                    date = LocalDate.now(),
                    queries = listOf(
                        listOf(
                            DailyLogQuery.Rule.parse("""{"tag": "OUTROS"}"""),
                        ),
                        listOf(
                            DailyLogQuery.Rule.parse("""{"text": "não existe no texto"}"""),
                        ),
                    ),
                )

                val execution = buildExecution(
                    messages = generateConversation(
                        MessageType.USER to "Olá",
                        MessageType.ASSISTANT to "Olá, eu sou o Fred.",
                    ),
                    tags = listOf(ConversationTag.OUTROS, ConversationTag.APLICATIVO),
                )

                query.check(execution) shouldBe true
            }

            it("deve avaliar false se nenhuma das queries passar") {
                val query = DailyLogQuery(
                    date = LocalDate.now(),
                    queries = listOf(
                        listOf(
                            DailyLogQuery.Rule.parse("""{"tag": "PAGAMENTO_CONTAS"}"""),
                        ),
                        listOf(
                            DailyLogQuery.Rule.parse("""{"text": "não existe no texto"}"""),
                        ),
                    ),
                )

                val execution = buildExecution(
                    messages = generateConversation(
                        MessageType.USER to "Olá",
                        MessageType.ASSISTANT to "Olá, eu sou o Fred.",
                    ),
                    tags = listOf(ConversationTag.OUTROS, ConversationTag.APLICATIVO),
                )

                query.check(execution) shouldBe false
            }
        }
    }

    fun buildExecution(
        tags: List<ConversationTag>,
        messages: List<ChatMessageWrapper>,
    ) = ChatBotDailyLogService.DailyLogExecution(
        history = ConversationHistory(
            userId = UserId("5521999999999"),
            createdAt = LocalDate.now(),
            messages = messages,
        ),
        result = ClassificationResult(
            tags = tags,
            entendimento = mapOf(),
            acoes = listOf(),
        ),
    )
}