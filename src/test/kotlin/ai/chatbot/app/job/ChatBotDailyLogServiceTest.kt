package ai.chatbot.app.job

import DynamoDB<PERSON>tils.setupDynamoDB
import ai.chatbot.adapters.dynamodb.ChatHistoryDbRepository
import ai.chatbot.adapters.dynamodb.ChatHistoryDynamoDAO
import ai.chatbot.adapters.dynamodb.ChatHistoryStateDbRepository
import ai.chatbot.adapters.dynamodb.ChatHistoryStateDynamoDAO
import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.HistoryStateRepository
import ai.chatbot.app.classification.ClassificationResult
import ai.chatbot.app.classification.ConversationAction
import ai.chatbot.app.classification.ConversationActionSource
import ai.chatbot.app.classification.ConversationActionStatus
import ai.chatbot.app.classification.ConversationTag
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.Action
import ai.chatbot.app.conversation.ChatMessageWrapper
import ai.chatbot.app.conversation.CompletionMessage
import ai.chatbot.app.conversation.ConversationHistory
import ai.chatbot.app.conversation.HistoryStateType
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.defaultFeatureFlags
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import io.kotest.core.annotation.Ignored
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.collections.shouldContainInOrder
import io.kotest.matchers.shouldBe
import io.micronaut.context.ApplicationContext
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.via1.communicationcentre.app.integrations.EmailSenderService
import java.time.LocalDate
import java.time.ZonedDateTime

@Ignored
@MicronautTest
class ChatBotDailyLogServiceTest(
    senderService: EmailSenderService,
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
) : DescribeSpec() {
    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val sender: String = "<EMAIL>"

    private val chatBotConversationHistoryEmail: String = "<EMAIL>"
    private val chatBotConversationAiHistoryEmail: String = "<EMAIL>"

    private val mockTenantConfiguration = mockk<TenantConfiguration> {
        every { flags } returns defaultFeatureFlags()
        every { communicationCentre } returns mockk(relaxed = true) {
            every { email } returns mockk(relaxed = true) {
                every { notification } returns mockk(relaxed = true) {
                    every { email } returns sender
                }
                every { dailyLogEmail } returns chatBotConversationHistoryEmail
                every { dailyLogAiEmail } returns chatBotConversationAiHistoryEmail
            }
        }
        every { dailyLog } returns mockk {
            every { tenant } returns "test"
            every { bucket } returns "testBucket"
        }
    }

    private val tenantService: TenantService = mockk {
        every { getConfiguration() } returns mockTenantConfiguration
        every { getTenantName() } returns "friday"
    }

    private val historyRepository: HistoryRepository = ChatHistoryDbRepository(ChatHistoryDynamoDAO(dynamoDbEnhancedClient, applicationContext), tenantService)
    private val historyStateRepository: HistoryStateRepository = ChatHistoryStateDbRepository(ChatHistoryStateDynamoDAO(dynamoDbEnhancedClient, applicationContext), tenantService)

    private val chatBotDailyLogService = ChatBotDailyLogService(
        historyDbRepository = historyRepository,
        historyStateRepository = historyStateRepository,
        emailSenderService = senderService,
        openAIAdapter = openAIAdapter,
        dailyLogSummaryRepository = mockk(relaxed = true),
        objectRepository = mockk(relaxed = true),
        awsRegion = "us-east-1",
        tenantService = tenantService,
    )

    private val user =
        User(
            accountId = AccountId(value = "ACCOUNT-ID"),
            id = UserId("*************"),
            name = "João da Silva Sauro",
            accountGroups = emptyList(),
            status = AccountStatus.ACTIVE,
        )

    init {
        describe("quando enviar o relatório") {
            it("deve enviar um email com o log do dia anterior") {
                historyRepository.create(
                    userId = user.id,
                    historyStateType = HistoryStateType.BILLS_COMING_DUE,
                    initialUserMessage = null,
                )
                historyRepository.saveUserMessage(user.id, "Olá")
                historyRepository.saveAssistantMessage(user.id, "Olá tudo bem?")

                chatBotDailyLogService.conversationsReportByDate(LocalDate.now())
            }
        }
    }
}

class ChatBotConversationTimeTakenTest : DescribeSpec() {
    private val mockTenantConfiguration = mockk<TenantConfiguration> {
        every { communicationCentre } returns mockk(relaxed = true) {
            every { email } returns mockk(relaxed = true) {
                every { dailyLogEmail } returns "test"
                every { dailyLogAiEmail } returns "teste"
                every { notification } returns mockk(relaxed = true) {
                    every { email } returns "teste"
                }
            }
        }
        every {
            dailyLog
        } returns mockk {
            every { bucket } returns "testBucket"
            every { tenant } returns "test"
        }
    }

    private val tenantService: TenantService = mockk {
        every { getConfiguration() } returns mockTenantConfiguration
        every { getTenantName() } returns "friday"
    }

    private val chatBotDailyLogService = ChatBotDailyLogService(
        historyDbRepository = mockk(),
        historyStateRepository = mockk(),
        emailSenderService = mockk(),
        openAIAdapter = mockk(),
        dailyLogSummaryRepository = mockk(relaxed = true),
        objectRepository = mockk(relaxed = true),
        awsRegion = "us-east-1",
        tenantService = tenantService,
    )

    private val conversationHistory = ConversationHistory(
        userId = UserId("*************"),
        createdAt = LocalDate.now(),
        messages = listOf(
            ChatMessageWrapper(
                type = MessageType.SYSTEM,
                message = "Mansagem de sistema",
                completionMessage = null,
                timestamp = ZonedDateTime.now(),
            ),
            ChatMessageWrapper(
                type = MessageType.USER,
                message = "O que preciso pagar",
                completionMessage = null,
                timestamp = ZonedDateTime.now(),
            ),
            ChatMessageWrapper(
                type = MessageType.ASSISTANT,
                message = null,
                completionMessage = CompletionMessage(
                    verificacaoDeIntegridade = "passou",
                    entendimento = "O usuário está solicitando a lista de contas pendentes para hoje.",
                    acoes = listOf(Action.SendPendingBillsPeriod("2024-08-27", "2024-08-27", false)),
                ),
                timestamp = ZonedDateTime.now().plusSeconds(4),
            ),
            ChatMessageWrapper(
                type = MessageType.ASSISTANT,
                message = "Olá! Como posso ajudá-lo hoje? Se precisar de informações sobre suas contas ou qualquer outra funcionalidade do App Friday, estou à disposição!",
                completionMessage = CompletionMessage(
                    verificacaoDeIntegridade = "",
                    entendimento = "",
                    acoes = listOf(Action.SendMessage("Olá! Como posso ajudá-lo hoje? Se precisar de informações sobre suas contas ou qualquer outra funcionalidade do App Friday, estou à disposição!")),
                ),
                timestamp = ZonedDateTime.now().plusSeconds(8),
            ),
            ChatMessageWrapper(
                type = MessageType.SYSTEM,
                message = "Mansagem de sistema",
                completionMessage = null,
                timestamp = ZonedDateTime.now().plusSeconds(10),
            ),
            ChatMessageWrapper(
                type = MessageType.USER,
                message = "O que preciso pagar",
                completionMessage = null,
                timestamp = ZonedDateTime.now().plusSeconds(66),
            ),
            ChatMessageWrapper(
                type = MessageType.ASSISTANT,
                message = null,
                completionMessage = CompletionMessage(
                    verificacaoDeIntegridade = "ok",
                    entendimento = "Usuário pedindo por contas pendentes para hoje.",
                    acoes = listOf(Action.SendPendingBillsPeriod("2024-08-27", "2024-08-27", false)),
                ),
                timestamp = ZonedDateTime.now().plusSeconds(68),
            ),
            ChatMessageWrapper(
                type = MessageType.ASSISTANT,
                message = "Olá! Como posso ajudá-lo hoje? Se precisar verificar contas pendentes, pagar alguma conta ou se tiver qualquer outra dúvida sobre o App Friday, estou aqui para ajudar.",
                completionMessage = CompletionMessage(
                    verificacaoDeIntegridade = "",
                    entendimento = "",
                    acoes = listOf(Action.SendMessage("Olá! Como posso ajudá-lo hoje? Se precisar verificar contas pendentes, pagar alguma conta ou se tiver qualquer outra dúvida sobre o App Friday, estou aqui para ajudar.")),
                ),
                timestamp = ZonedDateTime.now().plusSeconds(73),
            ),
        ),
    )

    init {
        describe("quando for filtrar a lista de conversa") {
            it("deve retornar apenas as mensagens do usuário e assistente") {

                val list = chatBotDailyLogService.getConversationTimeTaken(
                    conversationHistory,
                    ClassificationResult(
                        tags = listOf(ConversationTag.USUARIO_ATENDIDO),
                        entendimento = mapOf(ConversationTag.USUARIO_ATENDIDO to ""),
                        acoes = listOf(
                            ConversationAction(
                                acao = "",
                                fonte = ConversationActionSource.USER,
                                status = ConversationActionStatus.DONE,
                                entendimento = "",
                            ),
                        ),
                    ),
                )

                list.size shouldBe 2

                list.map { it.timeBetweenMessages } shouldContainInOrder listOf(
                    8,
                    7,
                )
            }

            it("não deve retornar se for SPAM") {

                val list = chatBotDailyLogService.getConversationTimeTaken(
                    conversationHistory,
                    ClassificationResult(
                        tags = listOf(ConversationTag.SPAM),
                        entendimento = mapOf(ConversationTag.SPAM to ""),
                        acoes = listOf(),
                    ),
                )

                list.size shouldBe 0
            }
        }
    }
}