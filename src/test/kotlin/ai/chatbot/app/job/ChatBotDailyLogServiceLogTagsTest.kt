package ai.chatbot.app.job

import ai.chatbot.app.classification.ClassificationResult
import ai.chatbot.app.classification.ConversationTag
import ai.chatbot.app.classification.ConversationTag.CONTA_CONECTADA
import ai.chatbot.app.classification.ConversationTag.ONE_PIX_PAY
import ai.chatbot.app.classification.ConversationTag.PAGAMENTO_CONTAS
import ai.chatbot.app.classification.ConversationTag.PIX
import ai.chatbot.app.classification.ConversationTag.USUARIO_ATENDIDO
import ai.chatbot.app.classification.ConversationTag.USUARIO_NAO_ATENDIDO
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.defaultFeatureFlags
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDate

class ChatBotDailyLogServiceLogTagsTest : DescribeSpec() {
    private val mockTenantConfiguration = mockk<TenantConfiguration> {
        every { flags } returns defaultFeatureFlags()
        every { communicationCentre } returns mockk(relaxed = true)
        every { dailyLog } returns mockk {
            every { tenant } returns "test"
            every { bucket } returns "testBucket"
        }
    }

    private val tenantService: TenantService = mockk {
        every {
            getConfiguration()
        } returns mockTenantConfiguration
    }

    private val service = ChatBotDailyLogService(
        historyDbRepository = mockk(relaxed = true),
        historyStateRepository = mockk(relaxed = true),
        emailSenderService = mockk(relaxed = true),
        openAIAdapter = mockk(relaxed = true),
        dailyLogSummaryRepository = mockk(relaxed = true),
        objectRepository = mockk(relaxed = true),
        awsRegion = "us-east-1",
        tenantService = tenantService,
    )

    init {
        describe("ao logar tags") {
            it("deve contar as tags corretamente") {
                service.logTags(
                    date = LocalDate.now(),
                    classifications = listOf(
                        buildResult(USUARIO_ATENDIDO, PIX, CONTA_CONECTADA),
                        buildResult(USUARIO_NAO_ATENDIDO, PIX),
                        buildResult(USUARIO_ATENDIDO, PAGAMENTO_CONTAS),
                        buildResult(USUARIO_ATENDIDO, PAGAMENTO_CONTAS, CONTA_CONECTADA),
                    ),
                ).let {
                    it[USUARIO_ATENDIDO] shouldBe 3
                    it[USUARIO_NAO_ATENDIDO] shouldBe 1
                    it[PIX] shouldBe 2
                    it[PAGAMENTO_CONTAS] shouldBe 2
                    it[ONE_PIX_PAY] shouldBe 0
                }
            }
        }
    }
}

fun buildResult(vararg tags: ConversationTag) =
    ClassificationResult(tags.toList(), tags.associateWith { "entendimento" })