package ai.chatbot.app.media

import ai.chatbot.adapters.media.DocumentBarCodeProcessor
import ai.chatbot.adapters.media.ImageQRCodeProcessor
import ai.chatbot.adapters.media.RemoteBarCodeMediaProcessor
import ai.chatbot.app.file.ObjectRepository
import ai.chatbot.app.file.StoredObject
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainInOrder
import io.kotest.matchers.collections.shouldContainOnly
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import io.via1.communicationcentre.app.integrations.MachineLearningParser
import io.via1.communicationcentre.app.parser.ParsedBill
import org.apache.commons.lang3.tuple.Pair as ApachePair

class MediaProcessorServiceTest : DescribeSpec() {

    private val objectRepository = mockk<ObjectRepository>()

    private val machineLearningParser = mockk<MachineLearningParser>()

    private val remoteBarCodeMediaProcessor = spyk(RemoteBarCodeMediaProcessor(machineLearningParser))

    private val documentBarCodeProcessor = spyk(DocumentBarCodeProcessor())

    private val imageQRCodeProcessor = ImageQRCodeProcessor()

    private val mediaProcessorService = MediaProcessorService(
        imageQRCodeProcessor,
        documentBarCodeProcessor,
        remoteBarCodeMediaProcessor,
        objectRepository,
    )

    init {
        describe("ao processar uma imagem") {
            beforeEach {
                clearAllMocks()

                every {
                    objectRepository.loadObject(any(), any())
                } answers {
                    javaClass.getResourceAsStream("/media/${secondArg<String>()}") ?: throw IllegalStateException("Media not found")
                }
            }

            describe("quando contém QR codes PIX") {
                it("deve encontrar um QR code PIX") {
                    val response = mediaProcessorService.process(StoredObject("test", "bucket", "qr_code_single.jpeg"))
                    response.shouldNotBeNull()
                    response.qrCodeValues.shouldNotBeEmpty()
                    response.qrCodeValues.size shouldBe 1
                    response.qrCodeValues.single() shouldBe "00020126580014BR.GOV.BCB.PIX013634377405-c606-4da0-b944-00503596d12c520400005303986540525.505802BR5924Marlon da Costa Moncores6009SAO PAULO61080540900062140510testepixqr63044A81"
                    response.boletos.shouldBeEmpty()
                    verify(exactly = 0) { remoteBarCodeMediaProcessor.process(any()) }
                }

                it("deve encontrar múltiplos QR codes PIX") {
                    val response = mediaProcessorService.process(StoredObject("test", "bucket", "qr_code_multi_type.png"))
                    response.shouldNotBeNull()
                    response.qrCodeValues.shouldNotBeEmpty()
                    response.qrCodeValues.size shouldBe 2
                    response.qrCodeValues shouldContainOnly listOf(
                        "00020126580014BR.GOV.BCB.PIX013634377405-c606-4da0-b944-00503596d12c520400005303986540525.505802BR5924Marlon da Costa Moncores6009SAO PAULO61080540900062140510testepixqr63044A81",
                        "00020126580014BR.GOV.BCB.PIX0136547b7870-2442-4bad-ba00-8f9c845421f952040000530398654040.015802BR5920RAFAEL HAERTEL PERES6007PELOTAS622605221HNOBKbErSuKI69IoEfNgc6304E921",
                    )
                    response.boletos.shouldBeEmpty()
                    verify(exactly = 0) { remoteBarCodeMediaProcessor.process(any()) }
                }
            }

            describe("quando não contém QR codes PIX") {
                it("deve chamar o remoteBarCodeMediaProcessor") {
                    val bill = ParsedBill().also {
                        it.digitableLine = "8123456789012"
                        it.dueDate = "31/12/2024"
                    }

                    every { remoteBarCodeMediaProcessor.process(any()) } returns Pair(bill, "")

                    val response = mediaProcessorService.process(StoredObject("test", "bucket", "no_qr_code.png"))
                    response.shouldNotBeNull()
                    response.qrCodeValues.shouldBeEmpty()
                    response.boletos.shouldNotBeEmpty()
                    response.boletos shouldContainOnly listOf(
                        BoletoInfo(codigo = "8123456789012", vencimento = "2024-12-31"),
                    )
                    verify(exactly = 1) { remoteBarCodeMediaProcessor.process(any()) }
                }
            }

            describe("ao chamar o remoteBarCodeMediaProcessor") {
                it("deve retornar uma lista vazia quando o resultado remoto for nulo") {
                    every { machineLearningParser.parseContentWithText(any()) } returns null

                    val response = mediaProcessorService.process(StoredObject("test", "bucket", "no_qr_code.png"))
                    response.shouldNotBeNull()
                    response.qrCodeValues.shouldBeEmpty()
                    response.boletos.shouldBeEmpty()
                    verify(exactly = 1) { remoteBarCodeMediaProcessor.process(any()) }
                }

                it("deve retornar uma lista vazia quando o resultado for vazio") {
                    every { machineLearningParser.parseContentWithText(any()) } returns ApachePair.of(
                        ParsedBill().also {
                            it.digitableLine = ""
                            it.dueDate = ""
                        },
                        null,
                    )

                    val response = mediaProcessorService.process(StoredObject("test", "bucket", "no_qr_code.png"))
                    response.shouldNotBeNull()
                    response.qrCodeValues.shouldBeEmpty()
                    response.boletos.shouldBeEmpty()
                    verify(exactly = 1) { remoteBarCodeMediaProcessor.process(any()) }
                }
            }
        }

        describe("ao processar um documento PDF") {
            beforeEach {
                clearAllMocks()

                every {
                    objectRepository.loadObject(any(), any())
                } answers {
                    javaClass.getResourceAsStream("/media/${secondArg<String>()}") ?: throw IllegalStateException("Media not found")
                }
            }

            it("deve encontrar múltiplos QR codes PIX em páginas diferentes") {
                val response = mediaProcessorService.process(StoredObject("test", "bucket", "pdf_qr_code_multi.pdf"))
                response.shouldNotBeNull()
                response.qrCodeValues.shouldNotBeEmpty()
                response.qrCodeValues.size shouldBe 2
                response.qrCodeValues shouldContainInOrder listOf(
                    "00020101021126360014BR.GOV.BCB.PIX011433938119000169520400005303986540570.975802BR5924CIA DISTRIBUIDORA DE GAS6014RIO DE JANEIRO6229052500000009007147390420251506304FACA",
                    "00020101021126580014br.gov.bcb.pix013661b64162-1555-40e9-8f2c-d76f97fd30415204000053039865406236.265802BR5909Claro S/A6014RIO DE JANEIRO622705230631180380000037865441363042A98",
                )
                verify(exactly = 0) { remoteBarCodeMediaProcessor.process(any()) }
            }

            it("deve encontrar códigos de barras do tipo ficha na mesma página e em páginas diferentes") {
                val response = mediaProcessorService.process(StoredObject("test", "bucket", "boleto_ficha.pdf"))
                response.shouldNotBeNull()
                response.qrCodeValues.shouldBeEmpty()
                response.boletos.shouldNotBeEmpty()
                response.boletos.size shouldBe 4
                response.boletos shouldContainOnly listOf(
                    BoletoInfo(codigo = "23790448099134353296518014850004399360000048159", vencimento = null),
                    BoletoInfo(codigo = "23790448099134353296519014850002599880000048159", vencimento = null),
                    BoletoInfo(codigo = "23790448099134353296520014850000110160000048159", vencimento = null),
                    BoletoInfo(codigo = "23790448099134353296521014850008910470000048159", vencimento = null),
                )
                verify(exactly = 0) { remoteBarCodeMediaProcessor.process(any()) }
            }

            it("deve chamar o remoteBarCodeMediaProcessor a cada página em que nada for encontrado") {
                val bill = ParsedBill().also {
                    it.digitableLine = "8123456789012"
                    it.dueDate = "31/12/2024"
                }

                every { remoteBarCodeMediaProcessor.process(any()) } returns Pair(bill, "")

                val response = mediaProcessorService.process(StoredObject("test", "bucket", "pdf_empty.pdf"))
                response.shouldNotBeNull()
                response.boletos.shouldNotBeEmpty()
                response.boletos shouldContainOnly listOf(
                    BoletoInfo(codigo = "8123456789012", vencimento = "2024-12-31"),
                )
                verify(exactly = 2) { remoteBarCodeMediaProcessor.process(any()) }
            }

            it("deve chamar o remoteBarCodeMediaProcessor a cada página em que um barcode de concessionaria for encontrado") {
                val bill = ParsedBill().also {
                    it.digitableLine = "836000000007709700560002000090071473390420251501"
                    it.dueDate = "31/12/2024"
                }

                every { remoteBarCodeMediaProcessor.process(any()) } returns Pair(bill, "")

                val response = mediaProcessorService.process(StoredObject("test", "bucket", "pdf_bar_code.pdf"))
                response.shouldNotBeNull()
                response.boletos.shouldNotBeEmpty()
                response.boletos shouldContainOnly listOf(
                    BoletoInfo(codigo = "836000000007709700560002000090071473390420251501", vencimento = "2024-12-31"),
                )
                verify(exactly = 1) { remoteBarCodeMediaProcessor.process(any()) }
            }

            it("deve retornar uma lista vazia quando o resultado remoto inteiro for nulo") {
                every { machineLearningParser.parseContentWithText(any()) } returns null

                val response = mediaProcessorService.process(StoredObject("test", "bucket", "pdf_empty.pdf"))
                response.shouldNotBeNull()
                response.qrCodeValues.shouldBeEmpty()
                response.boletos.shouldBeEmpty()
                verify(exactly = 2) { remoteBarCodeMediaProcessor.process(any()) }
            }

            it("deve retornar uma lista vazia quando o resultado remoto de parsedBill for nulo") {
                every { machineLearningParser.parseContentWithText(any()) } returns ApachePair.of(
                    null,
                    "",
                )

                val response = mediaProcessorService.process(StoredObject("test", "bucket", "pdf_empty.pdf"))
                response.shouldNotBeNull()
                response.qrCodeValues.shouldBeEmpty()
                response.boletos.shouldBeEmpty()
                verify(exactly = 2) { remoteBarCodeMediaProcessor.process(any()) }
            }

            it("deve retornar uma lista vazia quando o resultado remoto dos campos de parsedBill for nulo") {
                coEvery { machineLearningParser.parseContentWithText(any()) } returns ApachePair.of(
                    ParsedBill().also {
                        it.digitableLine = null
                        it.dueDate = null
                    },
                    "",
                )

                val response = mediaProcessorService.process(StoredObject("test", "bucket", "pdf_empty.pdf"))
                response.shouldNotBeNull()
                response.qrCodeValues.shouldBeEmpty()
                response.boletos.shouldBeEmpty()
                coVerify(exactly = 2) { remoteBarCodeMediaProcessor.process(any()) }
            }

            it("deve unir barcode pego antes via regex e vencimento remoto quando só encontrar o vencimento") {
                every { machineLearningParser.parseContentWithText(any()) } returns ApachePair.of(
                    ParsedBill().also {
                        it.digitableLine = ""
                        it.dueDate = "31/12/2024"
                    },
                    "",
                )

                val response = mediaProcessorService.process(StoredObject("test", "bucket", "pdf_bar_code.pdf"))
                response.shouldNotBeNull()
                response.qrCodeValues.shouldBeEmpty()
                response.boletos shouldContainOnly listOf(
                    BoletoInfo(codigo = "836000000007709700560002000090071473390420251501", vencimento = "2024-12-31"),
                )
                verify(exactly = 1) { remoteBarCodeMediaProcessor.process(any()) }
            }

            it("deve unir barcode pego antes via regex e vencimento remoto quando só encontrar o vencimento em arquivos de várias páginas") {
                every { machineLearningParser.parseContentWithText(any()) } returns ApachePair.of(
                    ParsedBill().also {
                        it.digitableLine = null
                        it.dueDate = "31/12/2024"
                    },
                    "",
                )

                val response = mediaProcessorService.process(StoredObject("test", "bucket", "boleto_many_pages.pdf"))
                response.shouldNotBeNull()
                response.qrCodeValues.shouldBeEmpty()
                response.boletos shouldContainOnly listOf(
                    BoletoInfo(codigo = "846900000023362601622024506150380000003826566469", vencimento = "2024-12-31"),
                )
                verify(exactly = 5) { remoteBarCodeMediaProcessor.process(any()) }
            }

            it("deve usar o que vier do parser remoto se encontrar o mesmo barcode e o vencimento") {
                every { machineLearningParser.parseContentWithText(any()) } returns ApachePair.of(
                    ParsedBill().also {
                        it.digitableLine = "836000000007709700560002000090071473390420251501"
                        it.dueDate = "31/12/2024"
                    },
                    "",
                )

                val response = mediaProcessorService.process(StoredObject("test", "bucket", "pdf_bar_code.pdf"))
                response.shouldNotBeNull()
                response.qrCodeValues.shouldBeEmpty()
                response.boletos shouldContainOnly listOf(
                    BoletoInfo(codigo = "836000000007709700560002000090071473390420251501", vencimento = "2024-12-31"),
                )
                verify(exactly = 1) { remoteBarCodeMediaProcessor.process(any()) }
            }

            it("deve retornar todo o texto extraído pelo parser remoto se nenhum QrCode ou Boleto for identificado") {
                every { machineLearningParser.parseContentWithText(any()) } returns ApachePair.of(
                    ParsedBill(),
                    "texto extraído pelo parser remoto",
                )

                val response = mediaProcessorService.process(StoredObject("test", "bucket", "pdf_empty.pdf"))
                response.shouldNotBeNull()
                response.boletos.shouldBeEmpty()
                response.qrCodeValues.shouldBeEmpty()
                response.text shouldBe "texto extraído pelo parser remoto"
                verify(exactly = 2) { remoteBarCodeMediaProcessor.process(any()) }
            }
        }
    }
}