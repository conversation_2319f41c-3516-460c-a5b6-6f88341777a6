package ai.chatbot.app.prompt

import ai.chatbot.adapters.dynamodb.PromptTenant
import ai.chatbot.adapters.dynamodb.StagingPromptTenantRepository
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.ME_POUPE_ENV
import ai.chatbot.app.MOTOROLA_ENV
import ai.chatbot.app.billView
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.string.shouldNotContain
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk

class StagingPromptServiceTest : DescribeSpec() {
    val promptTenantRepository = mockk<StagingPromptTenantRepository>()
    val service = StagingPromptService(repository = promptTenantRepository)

    init {
        beforeEach {
            clearAllMocks()
        }

        describe("testa o servico") {
            val msisdn = "***********"
            val user = User(
                id = UserId.fromMsisdn(msisdn),
                accountId = AccountId("accountId"),
                name = "Nome do usuário",
                accountGroups = emptyList(),
                status = AccountStatus.ACTIVE,
            )

            val state = BillComingDueHistoryState(
                walletWithBills =
                WalletWithBills(
                    bills = listOf(billView),
                    walletId = WalletId("walletId"),
                    walletName = "nome da carteira",
                ),
                internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
                balance = null,
                user = user,
                contacts = emptyList(),
                subscription = null,
            )
            it("deve retornar Na_th, guest e campanhas") {
                every {
                    promptTenantRepository.find(any())
                } returns PromptTenant(msisdn, ME_POUPE_ENV, PromptType.GUEST, true)

                val prompt = service.getPrompt(
                    state,
                )
                prompt shouldNotBe null
                prompt.prompt shouldContain "Na_th"
                prompt.type shouldBe PromptType.GUEST
                prompt.prompt shouldContain mePoupeInvestmentPrompt
            }

            it("deve retornar Na_th, default e sem campanhas") {
                every {
                    promptTenantRepository.find(any())
                } returns PromptTenant(msisdn, ME_POUPE_ENV, PromptType.DEFAULT, false)

                val prompt = service.getPrompt(
                    state,
                )
                prompt shouldNotBe null
                prompt.prompt shouldContain "Na_th"
                prompt.type shouldBe PromptType.DEFAULT
                prompt.prompt shouldNotContain mePoupeInvestmentPrompt
            }

            it("deve retornar Fred") {
                every {
                    promptTenantRepository.find(any())
                } returns PromptTenant(msisdn, FRIDAY_ENV)

                val prompt = service.getPrompt(state = state)
                prompt shouldNotBe null
                prompt.prompt shouldContain "Fred"
                prompt.type shouldBe PromptType.DEFAULT
            }

            it("deve retornar Dime") {
                every {
                    promptTenantRepository.find(any())
                } returns PromptTenant(msisdn, MOTOROLA_ENV)

                val prompt = service.getPrompt(state = state)
                prompt shouldNotBe null
                prompt.prompt shouldContain "Dime"
                prompt.type shouldBe PromptType.DEFAULT
            }
        }
    }
}