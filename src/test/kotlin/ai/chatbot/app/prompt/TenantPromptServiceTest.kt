package ai.chatbot.app.prompt

import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.ME_POUPE_ENV
import ai.chatbot.app.MOTOROLA_ENV
import ai.chatbot.app.billView
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class TenantPromptServiceTest : DescribeSpec() {
    private val mockTenantService = mockk<TenantService>()
    private val mockMultiTenantPromptService = mockk<MultiTenantPromptService>()
    private val mockFridayPromptService = mockk<PromptService>()
    private val mockMePoupePromptService = mockk<PromptService>()

    init {
        beforeEach {
            clearAllMocks()
        }

        describe("TenantPromptService") {
            val msisdn = "***********"
            val user = User(
                id = UserId.fromMsisdn(msisdn),
                accountId = AccountId("accountId"),
                name = "Nome do usuário",
                accountGroups = emptyList(),
                status = AccountStatus.ACTIVE,
            )

            val state = BillComingDueHistoryState(
                walletWithBills = WalletWithBills(
                    bills = listOf(billView),
                    walletId = WalletId("walletId"),
                    walletName = "nome da carteira",
                ),
                internalStateControl = InternalStateControl(
                    shouldSynchronizeBeforeCompletion = false,
                    billComingDueNotifiedAt = null,
                ),
                balance = null,
                user = user,
                contacts = emptyList(),
                subscription = null,
            )

            val expectedPrompt = Prompt(
                prompt = "Test prompt",
                type = PromptType.DEFAULT,
            )

            val fallbackPrompt = Prompt(
                prompt = "Fallback prompt from MultiTenantPromptService",
                type = PromptType.DEFAULT,
            )

            context("when tenant-specific prompt service is found") {
                it("should return prompt from Friday prompt service") {
                    val prompts = mapOf(FRIDAY_ENV to mockFridayPromptService)
                    val service = TenantPromptService(
                        prompts = prompts,
                        tenantService = mockTenantService,
                        multiTenantPromptService = mockMultiTenantPromptService,
                    )

                    every { mockTenantService.getTenantName() } returns FRIDAY_ENV
                    every { mockFridayPromptService.getPrompt(state) } returns expectedPrompt

                    val result = service.getPrompt(state)

                    result shouldBe expectedPrompt
                    verify { mockFridayPromptService.getPrompt(state) }
                    verify(exactly = 0) { mockMultiTenantPromptService.getPrompt(state) }
                }

                it("should return prompt from Me Poupe prompt service") {
                    val prompts = mapOf(ME_POUPE_ENV to mockMePoupePromptService)
                    val service = TenantPromptService(
                        prompts = prompts,
                        tenantService = mockTenantService,
                        multiTenantPromptService = mockMultiTenantPromptService,
                    )

                    every { mockTenantService.getTenantName() } returns ME_POUPE_ENV
                    every { mockMePoupePromptService.getPrompt(state) } returns expectedPrompt

                    val result = service.getPrompt(state)

                    result shouldBe expectedPrompt
                    verify { mockMePoupePromptService.getPrompt(state) }
                    verify(exactly = 0) { mockMultiTenantPromptService.getPrompt(state) }
                }

                it("should handle case-insensitive tenant names") {
                    val prompts = mapOf(FRIDAY_ENV to mockFridayPromptService)
                    val service = TenantPromptService(
                        prompts = prompts,
                        tenantService = mockTenantService,
                        multiTenantPromptService = mockMultiTenantPromptService,
                    )

                    every { mockTenantService.getTenantName() } returns "FRIDAY"
                    every { mockFridayPromptService.getPrompt(state) } returns expectedPrompt

                    val result = service.getPrompt(state)

                    result shouldBe expectedPrompt
                    verify { mockFridayPromptService.getPrompt(state) }
                    verify(exactly = 0) { mockMultiTenantPromptService.getPrompt(state) }
                }
            }

            context("when tenant-specific prompt service is NOT found") {
                it("should fallback to MultiTenantPromptService when tenant is unknown") {
                    val prompts = mapOf(FRIDAY_ENV to mockFridayPromptService)
                    val service = TenantPromptService(
                        prompts = prompts,
                        tenantService = mockTenantService,
                        multiTenantPromptService = mockMultiTenantPromptService,
                    )

                    every { mockTenantService.getTenantName() } returns "unknown-tenant"
                    every { mockMultiTenantPromptService.getPrompt(state) } returns fallbackPrompt

                    val result = service.getPrompt(state)

                    result shouldBe fallbackPrompt
                    verify { mockMultiTenantPromptService.getPrompt(state) }
                    verify(exactly = 0) { mockFridayPromptService.getPrompt(state) }
                }

                it("should fallback when prompts map is empty") {
                    val prompts = emptyMap<String, PromptService>()
                    val service = TenantPromptService(
                        prompts = prompts,
                        tenantService = mockTenantService,
                        multiTenantPromptService = mockMultiTenantPromptService,
                    )

                    every { mockTenantService.getTenantName() } returns FRIDAY_ENV
                    every { mockMultiTenantPromptService.getPrompt(state) } returns fallbackPrompt

                    val result = service.getPrompt(state)

                    result shouldBe fallbackPrompt
                    verify { mockMultiTenantPromptService.getPrompt(state) }
                }

                it("should fallback when tenant name is empty") {
                    val prompts = mapOf(FRIDAY_ENV to mockFridayPromptService)
                    val service = TenantPromptService(
                        prompts = prompts,
                        tenantService = mockTenantService,
                        multiTenantPromptService = mockMultiTenantPromptService,
                    )

                    every { mockTenantService.getTenantName() } returns ""
                    every { mockMultiTenantPromptService.getPrompt(state) } returns fallbackPrompt

                    val result = service.getPrompt(state)

                    result shouldBe fallbackPrompt
                    verify { mockMultiTenantPromptService.getPrompt(state) }
                }
            }

            context("integration scenarios") {
                it("should work with all supported tenant environments") {
                    val motorolaService = mockk<PromptService>()
                    val prompts = mapOf(
                        FRIDAY_ENV to mockFridayPromptService,
                        ME_POUPE_ENV to mockMePoupePromptService,
                        MOTOROLA_ENV to motorolaService,
                    )
                    val service = TenantPromptService(
                        prompts = prompts,
                        tenantService = mockTenantService,
                        multiTenantPromptService = mockMultiTenantPromptService,
                    )

                    every { mockTenantService.getTenantName() } returns FRIDAY_ENV
                    every { mockFridayPromptService.getPrompt(state) } returns expectedPrompt

                    val result = service.getPrompt(state)

                    result shouldNotBe null
                    result shouldBe expectedPrompt
                }

                it("should prefer tenant-specific prompt over fallback when both are available") {
                    val prompts = mapOf(FRIDAY_ENV to mockFridayPromptService)
                    val service = TenantPromptService(
                        prompts = prompts,
                        tenantService = mockTenantService,
                        multiTenantPromptService = mockMultiTenantPromptService,
                    )

                    every { mockTenantService.getTenantName() } returns FRIDAY_ENV
                    every { mockFridayPromptService.getPrompt(state) } returns expectedPrompt

                    val result = service.getPrompt(state)

                    result shouldBe expectedPrompt
                    verify { mockFridayPromptService.getPrompt(state) }
                    verify(exactly = 0) { mockMultiTenantPromptService.getPrompt(state) }
                }
            }
        }
    }
}