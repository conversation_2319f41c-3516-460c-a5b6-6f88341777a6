package ai.chatbot.app.transaction

import DynamoDBUtils.setupDynamoDB
import ai.chatbot.adapters.api.TransactionPaymentStatus
import ai.chatbot.adapters.dynamodb.TransactionDbRepository
import ai.chatbot.adapters.dynamodb.TransactionDynamoDAO
import ai.chatbot.app.DYNAMOB_TEST_TABLE_NAME
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.SweepingRequest
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.utils.timestampFormatWithBrazilTimeZone
import arrow.core.left
import arrow.core.right
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.date.shouldBeAfter
import io.kotest.matchers.equality.shouldBeEqualToIgnoringFields
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.context.ApplicationContext
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import java.util.*

class TransactionServiceTest : DescribeSpec() {
    private val userId = UserId(value = "userId")
    private val walletId = WalletId(value = "walletId")
    private val pixKey = PixKey(value = "<EMAIL>", type = PixKeyType.EMAIL)

    private val dynamoDbEnhancedClient = setupDynamoDB()
    private val mockedApplicationContext =
        mockk<ApplicationContext> {
            every { getProperty(any<String>(), String::class.java) } returns Optional.of(DYNAMOB_TEST_TABLE_NAME)
        }
    private val transactionDAO = TransactionDynamoDAO(cli = dynamoDbEnhancedClient, mockedApplicationContext)

    private val tenantService = mockk<TenantService>() {
        every { getTenantName() } returns "friday"
    }

    private val transactionRepository = TransactionDbRepository(dynamoDbDAO = transactionDAO, tenantService = tenantService)
    private val paymentAdapter = mockk<PaymentAdapter>()

    private val transactionService = DefaultTransactionService(
        transactionRepository = transactionRepository,
        paymentAdapter = paymentAdapter,
    )

    override fun isolationMode() = IsolationMode.InstancePerTest

    private fun setupTransaction(status: TransactionStatus, details: TransactionDetails, paymentStatus: TransactionPaymentStatus = TransactionPaymentStatus.UNKNOWN, transactionGroupId: TransactionGroupId = TransactionGroupId()) =
        Transaction(
            id = TransactionId(),
            groupId = transactionGroupId,
            userId = userId,
            walletId = walletId,
            status = status,
            details = details,
            paymentStatus = paymentStatus,
        ).also {
            transactionRepository.save(it)
        }

    private fun setupPixTransaction(status: TransactionStatus, paymentStatus: TransactionPaymentStatus = TransactionPaymentStatus.UNKNOWN, transactionGroupId: TransactionGroupId = TransactionGroupId(), participantId: SweepingParticipantId = SweepingParticipantId()) = setupTransaction(
        status,
        PixTransactionDetails(
            amount = 0,
            pixKey = pixKey,
            recipientName = "Recipient",
            recipientDocument = "***.456.789-**",
            recipientInstitution = "Banco teste",
            sweepingAmount = 10,
            sweepingParticipantId = participantId,
            qrCode = null,
        ),
        paymentStatus,
        transactionGroupId = transactionGroupId,
    )

    private fun setupScheduleBillsTransaction(status: TransactionStatus, bills: List<BillId> = listOf(BillId("BILL-1"), BillId("BILL-2")), paymentStatus: TransactionPaymentStatus = TransactionPaymentStatus.UNKNOWN) =
        setupTransaction(status, ScheduleBillsTransactionDetails(bills), paymentStatus)

    private fun setupSweepingTransaction(status: TransactionStatus, bills: List<BillId> = listOf(BillId("BILL-1"), BillId("BILL-2")), paymentStatus: TransactionPaymentStatus = TransactionPaymentStatus.UNKNOWN, participantId: SweepingParticipantId) =
        setupTransaction(status, SweepingTransactionDetails(bills = bills, sweepingParticipantId = participantId, amount = 50), paymentStatus)

    init {
        describe("ao cancelar uma transação") {
            it("deve retornar IllegalUser se a transação não for do usuário") {
                val transaction = setupPixTransaction(status = TransactionStatus.ACTIVE)

                val result = transactionService.cancel(transaction.id, UserId("anotherUserId"))

                result shouldBe TransactionResult.IllegalUser(transaction.userId)

                with(transactionRepository.find(transaction.id)!!) {
                    this.id shouldBe transaction.id
                    this.userId shouldBe transaction.userId
                    this.walletId shouldBe transaction.walletId
                    this.status shouldBe transaction.status
                    this.details shouldBe transaction.details
                }
            }

            describe("se ela estiver ativa") {
                val transaction = setupPixTransaction(status = TransactionStatus.ACTIVE)

                it("deve salvar a transação com estado CANCELED") {
                    val result = transactionService.cancel(transaction.id, userId)

                    result shouldBe TransactionResult.Canceled

                    val canceledTransaction = transactionRepository.find(transaction.id)

                    canceledTransaction!!.status shouldBe TransactionStatus.CANCELED
                }

                it("deve cancelar todas as transações do mesmo grupo") {
                    val anotherTransaction = setupPixTransaction(status = TransactionStatus.ACTIVE, transactionGroupId = transaction.groupId)

                    val result = transactionService.cancel(transaction.id, userId)

                    result shouldBe TransactionResult.Canceled

                    val canceledTransaction = transactionRepository.find(anotherTransaction.id)

                    canceledTransaction!!.status shouldBe TransactionStatus.CANCELED
                }
            }

            TransactionStatus.values().filter { it != TransactionStatus.ACTIVE }.forEach { status ->
                it("não deve alterar a transação se ela estiver $status") {
                    val transaction = setupPixTransaction(status = status)

                    val result = transactionService.cancel(transaction.id, userId)

                    result shouldBe TransactionResult.IllegalState(status)

                    with(transactionRepository.find(transaction.id)!!) {
                        this.id shouldBe transaction.id
                        this.userId shouldBe transaction.userId
                        this.walletId shouldBe transaction.walletId
                        this.status shouldBe transaction.status
                        this.details shouldBe transaction.details
                    }
                }
            }
        }

        describe("ao aprovar uma transação") {
            it("deve retornar IllegalUser se a transação não for do usuário") {
                val transaction = setupPixTransaction(status = TransactionStatus.ACTIVE)

                val result = transactionService.confirm(transaction.id, UserId("anotherUserId"))

                result shouldBe TransactionResult.IllegalUser(transaction.userId)

                with(transactionRepository.find(transaction.id)!!) {
                    this.id shouldBe transaction.id
                    this.userId shouldBe transaction.userId
                    this.walletId shouldBe transaction.walletId
                    this.status shouldBe transaction.status
                    this.details shouldBe transaction.details
                }
            }

            describe("se ela estiver ativa") {
                coEvery { paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any()) } returns Unit.right()

                it("deve salvar a transação com estado COMPLETED") {
                    val participantId = SweepingParticipantId()
                    val transaction = setupPixTransaction(status = TransactionStatus.ACTIVE, participantId = participantId)

                    val result = transactionService.confirm(transaction.id, userId)

                    (result is TransactionResult.Success) shouldBe true

                    val completedTransaction = transactionRepository.find(transaction.id)

                    completedTransaction!!.status shouldBe TransactionStatus.COMPLETED

                    val sweepingRequestSlot = slot<SweepingRequest>()
                    coVerify { paymentAdapter.pixTransaction(any(), any(), any(), capture(sweepingRequestSlot), any(), any(), retryTransaction = false) }
                    sweepingRequestSlot.captured.retry shouldBe false
                    sweepingRequestSlot.captured.participantId shouldBe participantId
                }

                it("deve expirar outras transações do mesmo grupo") {
                    val transaction = setupPixTransaction(status = TransactionStatus.ACTIVE)
                    val anotherTransaction = setupPixTransaction(status = TransactionStatus.ACTIVE, transactionGroupId = transaction.groupId)

                    val result = transactionService.confirm(transaction.id, userId)

                    (result is TransactionResult.Success) shouldBe true

                    val expiredTransaction = transactionRepository.find(anotherTransaction.id)

                    expiredTransaction!!.status shouldBe TransactionStatus.EXPIRED
                }
            }

            it("deve retornar TransactionError se não conseguir executar a transação") {
                coEvery { paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), any(), any()) } returns PaymentAdapterError.InvalidPixKey.left()

                val transaction = setupPixTransaction(status = TransactionStatus.ACTIVE)

                val result = transactionService.confirm(transaction.id, userId, "authToken")

                result.shouldBeTypeOf<TransactionResult.TransactionError>()

                with(transactionRepository.find(transaction.id)!!) {
                    this.id shouldBe transaction.id
                    this.userId shouldBe transaction.userId
                    this.walletId shouldBe transaction.walletId
                    this.status shouldBe transaction.status
                    this.details shouldBe transaction.details
                    this.authorizationToken shouldBe "authToken"
                }
            }

            TransactionStatus.values().filter { it != TransactionStatus.ACTIVE }.forEach { status ->
                it("não deve alterar a transação se ela estiver $status") {
                    val transaction = setupPixTransaction(status = status)

                    val result = transactionService.confirm(transaction.id, userId)

                    result shouldBe TransactionResult.IllegalState(status)

                    with(transactionRepository.find(transaction.id)!!) {
                        this.id shouldBe transaction.id
                        this.userId shouldBe transaction.userId
                        this.walletId shouldBe transaction.walletId
                        this.status shouldBe transaction.status
                        this.details shouldBe transaction.details
                    }
                }
            }

            describe("quando for uma transação do tipo SCHEDULE_BILLS") {
                it("deve agendar contas") {
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns Unit.right()

                    val transaction = setupScheduleBillsTransaction(TransactionStatus.ACTIVE, listOf(BillId("BILL-1"), BillId("BILL-2")))

                    val result = transactionService.confirm(transaction.id, userId)

                    (result is TransactionResult.Success) shouldBe true

                    coVerify {
                        paymentAdapter.scheduleBills(userId, null, walletId, listOf(BillId("BILL-1"), BillId("BILL-2")))
                    }

                    with(transactionRepository.find(transaction.id)!!) {
                        this.status shouldBe TransactionStatus.COMPLETED
                    }
                }

                it("deve dar erro se não conseguir agendar as contas") {
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns PaymentAdapterError.ErrorSchedulingBill.left()

                    val transaction = setupScheduleBillsTransaction(TransactionStatus.ACTIVE, listOf(BillId("BILL-1"), BillId("BILL-2")))

                    val result = transactionService.confirm(transaction.id, userId)

                    result shouldBe TransactionResult.TransactionError("Error scheduling bills")

                    coVerify {
                        paymentAdapter.scheduleBills(userId, null, walletId, listOf(BillId("BILL-1"), BillId("BILL-2")))
                    }

                    with(transactionRepository.find(transaction.id)!!) {
                        this.status shouldBe TransactionStatus.ACTIVE
                    }
                }

                it("deve dar erro se uma das contas não estiver ativa") {
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns PaymentAdapterError.BillNotActiveError.left()

                    val transaction = setupScheduleBillsTransaction(TransactionStatus.ACTIVE, listOf(BillId("BILL-1"), BillId("BILL-2")))

                    val result = transactionService.confirm(transaction.id, userId)

                    result shouldBe TransactionResult.TransactionError("Bill not active")

                    coVerify {
                        paymentAdapter.scheduleBills(userId, null, walletId, listOf(BillId("BILL-1"), BillId("BILL-2")))
                    }

                    with(transactionRepository.find(transaction.id)!!) {
                        this.status shouldBe TransactionStatus.ACTIVE
                    }
                }
            }

            describe("quando for uma transação do tipo SCHEDULE_BILLS com sweeping") {
                it("deve agendar contas") {
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns Unit.right()

                    val participantId = SweepingParticipantId()
                    val transaction = setupSweepingTransaction(
                        status = TransactionStatus.ACTIVE,
                        bills = listOf(BillId("BILL-1"), BillId("BILL-2")),
                        participantId = participantId,
                    )

                    val result = transactionService.confirm(transaction.id, userId)

                    (result is TransactionResult.Success) shouldBe true

                    val sweepingRequestSlot = slot<SweepingRequest>()
                    coVerify {
                        paymentAdapter.scheduleBills(userId, capture(sweepingRequestSlot), walletId, listOf(BillId("BILL-1"), BillId("BILL-2")))
                    }
                    sweepingRequestSlot.captured.retry shouldBe false
                    sweepingRequestSlot.captured.participantId shouldBe participantId
                }
            }
        }

        describe("ao retentar uma transação") {
            it("deve retornar IllegalUser se a transação não for do usuário") {
                val transaction = setupPixTransaction(status = TransactionStatus.COMPLETED, paymentStatus = TransactionPaymentStatus.FAILED)

                val result = transactionService.retry(transaction.id, UserId("anotherUserId"))

                result shouldBe TransactionResult.IllegalUser(transaction.userId)

                with(transactionRepository.find(transaction.id)!!) {
                    this.id shouldBe transaction.id
                    this.userId shouldBe transaction.userId
                    this.walletId shouldBe transaction.walletId
                    this.status shouldBe transaction.status
                    this.details shouldBe transaction.details
                }
            }

            TransactionStatus.values().filter { it !in listOf(TransactionStatus.EXPIRED, TransactionStatus.CANCELED) }.forEach { status ->
                it("deve retentar a transação se o pagamento falhou e ela estiver $status") {
                    val sweepingRequestSlot = slot<SweepingRequest>()
                    coEvery { paymentAdapter.pixTransaction(any(), any(), any(), capture(sweepingRequestSlot), any(), any(), retryTransaction = true) } returns Unit.right()

                    val participantId = SweepingParticipantId()
                    val transaction = setupPixTransaction(status = status, paymentStatus = TransactionPaymentStatus.FAILED, participantId = participantId)

                    val result = transactionService.retry(transaction.id, userId)

                    result.shouldBeTypeOf<TransactionResult.Success>()

                    val completedTransaction = transactionRepository.find(transaction.id)

                    completedTransaction!!.paymentStatus shouldBe TransactionPaymentStatus.UNKNOWN

                    sweepingRequestSlot.captured.retry shouldBe true
                    sweepingRequestSlot.captured.participantId shouldBe participantId
                }
            }

            it("deve retornar TransactionError se não conseguir executar a transação") {
                coEvery { paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any(), retryTransaction = true) } returns PaymentAdapterError.InvalidPixKey.left()

                val transaction = setupPixTransaction(status = TransactionStatus.COMPLETED, paymentStatus = TransactionPaymentStatus.FAILED)

                val result = transactionService.retry(transaction.id, userId)

                result.shouldBeTypeOf<TransactionResult.TransactionError>()

                with(transactionRepository.find(transaction.id)!!) {
                    this.id shouldBe transaction.id
                    this.userId shouldBe transaction.userId
                    this.walletId shouldBe transaction.walletId
                    this.status shouldBe transaction.status
                    this.details shouldBe transaction.details
                }
            }

            TransactionStatus.values().filter { it !in listOf(TransactionStatus.ACTIVE, TransactionStatus.COMPLETED) }.forEach { status ->
                it("não deve retentar a transação se ela estiver $status") {
                    val transaction = setupPixTransaction(status = status)

                    val result = transactionService.retry(transaction.id, userId)

                    result shouldBe TransactionResult.IllegalState(status)

                    with(transactionRepository.find(transaction.id)!!) {
                        this.id shouldBe transaction.id
                        this.userId shouldBe transaction.userId
                        this.walletId shouldBe transaction.walletId
                        this.status shouldBe transaction.status
                        this.details shouldBe transaction.details
                    }
                }
            }

            TransactionPaymentStatus.values().filter { it != TransactionPaymentStatus.FAILED }.forEach { status ->
                it("não deve retentar a transação se o estado do pagamento for $status") {
                    val transaction = setupPixTransaction(status = TransactionStatus.COMPLETED, paymentStatus = status)

                    val result = transactionService.retry(transaction.id, userId)

                    result shouldBe TransactionResult.IllegalState(TransactionStatus.COMPLETED, status)

                    with(transactionRepository.find(transaction.id)!!) {
                        this.id shouldBe transaction.id
                        this.userId shouldBe transaction.userId
                        this.walletId shouldBe transaction.walletId
                        this.status shouldBe transaction.status
                        this.details shouldBe transaction.details
                    }
                }
            }

            describe("quando for uma transação do tipo SCHEDULE_BILLS") {
                it("deve agendar contas") {
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns Unit.right()

                    val transaction = setupScheduleBillsTransaction(TransactionStatus.COMPLETED, listOf(BillId("BILL-1"), BillId("BILL-2")), TransactionPaymentStatus.FAILED)

                    val result = transactionService.retry(transaction.id, userId)

                    (result is TransactionResult.Success) shouldBe true

                    coVerify {
                        paymentAdapter.scheduleBills(userId, null, walletId, listOf(BillId("BILL-1"), BillId("BILL-2")))
                    }

                    with(transactionRepository.find(transaction.id)!!) {
                        this.status shouldBe TransactionStatus.COMPLETED
                        this.paymentStatus shouldBe TransactionPaymentStatus.UNKNOWN
                    }
                }

                it("deve dar erro se não conseguir agendar as contas") {
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns PaymentAdapterError.ErrorSchedulingBill.left()

                    val transaction = setupScheduleBillsTransaction(TransactionStatus.COMPLETED, listOf(BillId("BILL-1"), BillId("BILL-2")), TransactionPaymentStatus.FAILED)

                    val result = transactionService.retry(transaction.id, userId)

                    result shouldBe TransactionResult.TransactionError("Error scheduling bills")

                    coVerify {
                        paymentAdapter.scheduleBills(userId, null, walletId, listOf(BillId("BILL-1"), BillId("BILL-2")))
                    }

                    with(transactionRepository.find(transaction.id)!!) {
                        this.status shouldBe TransactionStatus.COMPLETED
                        this.paymentStatus shouldBe TransactionPaymentStatus.FAILED
                    }
                }

                it("deve dar erro se uma das contas não estiver ativa") {
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns PaymentAdapterError.BillNotActiveError.left()

                    val transaction = setupScheduleBillsTransaction(TransactionStatus.COMPLETED, listOf(BillId("BILL-1"), BillId("BILL-2")), TransactionPaymentStatus.FAILED)

                    val result = transactionService.retry(transaction.id, userId)

                    result shouldBe TransactionResult.TransactionError("Bill not active")

                    coVerify {
                        paymentAdapter.scheduleBills(userId, null, walletId, listOf(BillId("BILL-1"), BillId("BILL-2")))
                    }

                    with(transactionRepository.find(transaction.id)!!) {
                        this.status shouldBe TransactionStatus.COMPLETED
                        this.paymentStatus shouldBe TransactionPaymentStatus.FAILED
                    }
                }
            }

            describe("quando for uma transação do tipo SCHEDULE_BILLS com sweeping") {
                it("deve agendar contas") {
                    coEvery { paymentAdapter.scheduleBills(any(), any(), any(), any()) } returns Unit.right()

                    val participantId = SweepingParticipantId()
                    val transaction = setupSweepingTransaction(
                        status = TransactionStatus.ACTIVE,
                        bills = listOf(BillId("BILL-1"), BillId("BILL-2")),
                        participantId = participantId,
                        paymentStatus = TransactionPaymentStatus.FAILED,
                    )

                    val result = transactionService.retry(transaction.id, userId)

                    (result is TransactionResult.Success) shouldBe true

                    val sweepingRequestSlot = slot<SweepingRequest>()
                    coVerify {
                        paymentAdapter.scheduleBills(userId, capture(sweepingRequestSlot), walletId, listOf(BillId("BILL-1"), BillId("BILL-2")))
                    }
                    sweepingRequestSlot.captured.retry shouldBe true
                    sweepingRequestSlot.captured.participantId shouldBe participantId
                }
            }
        }

        describe("ao atualizar o estado do pagamento de uma transação") {
            it("deve retornar IllegalUser se a transação não for do usuário") {
                val transaction = setupPixTransaction(status = TransactionStatus.COMPLETED)

                val result = transactionService.updatePaymentStatus(transaction.id, UserId("anotherUserId"), TransactionPaymentStatus.SUCCESS)

                result shouldBe TransactionResult.IllegalUser(transaction.userId)

                with(transactionRepository.find(transaction.id)!!) {
                    this.id shouldBe transaction.id
                    this.userId shouldBe transaction.userId
                    this.walletId shouldBe transaction.walletId
                    this.status shouldBe transaction.status
                    this.details shouldBe transaction.details
                    this.paymentStatus shouldBe transaction.paymentStatus
                }
            }

            TransactionStatus.values().filter { it !in listOf(TransactionStatus.EXPIRED, TransactionStatus.CANCELED) }.forEach { status ->
                it("deve atualizar o estado do pagamento de uma transação $status se ele estiver UNKNOWN") {
                    coEvery { paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any()) } returns Unit.right()

                    val transaction = setupPixTransaction(status = status, paymentStatus = TransactionPaymentStatus.UNKNOWN)

                    val result = transactionService.updatePaymentStatus(transaction.id, userId, TransactionPaymentStatus.SUCCESS)

                    result.shouldBeTypeOf<TransactionResult.Success>()
                    result.transaction.paymentStatus shouldBe TransactionPaymentStatus.SUCCESS

                    val completedTransaction = transactionRepository.find(transaction.id)

                    completedTransaction?.shouldBeEqualToIgnoringFields(result.transaction, result.transaction::updatedAt)
                }
            }

            TransactionStatus.values().filter { it !in listOf(TransactionStatus.ACTIVE, TransactionStatus.COMPLETED) }.forEach { status ->
                it("não deve alterar a transação se ela estiver $status") {
                    val transaction = setupPixTransaction(status = status)

                    val result = transactionService.updatePaymentStatus(transaction.id, userId, TransactionPaymentStatus.SUCCESS)

                    result shouldBe TransactionResult.IllegalState(status)

                    with(transactionRepository.find(transaction.id)!!) {
                        this.id shouldBe transaction.id
                        this.userId shouldBe transaction.userId
                        this.walletId shouldBe transaction.walletId
                        this.status shouldBe transaction.status
                        this.details shouldBe transaction.details
                        this.paymentStatus shouldBe transaction.paymentStatus
                    }
                }
            }

            TransactionPaymentStatus.values().filter { it != TransactionPaymentStatus.UNKNOWN }.forEach { status ->
                it("não deve alterar a transação se o estado do pagamento for $status") {
                    val transaction = setupPixTransaction(status = TransactionStatus.COMPLETED, paymentStatus = status)

                    val result = transactionService.updatePaymentStatus(transaction.id, userId, TransactionPaymentStatus.SUCCESS)

                    result shouldBe TransactionResult.IllegalState(TransactionStatus.COMPLETED, status)

                    with(transactionRepository.find(transaction.id)!!) {
                        this.id shouldBe transaction.id
                        this.userId shouldBe transaction.userId
                        this.walletId shouldBe transaction.walletId
                        this.status shouldBe transaction.status
                        this.details shouldBe transaction.details
                        this.paymentStatus shouldBe transaction.paymentStatus
                    }
                }
            }
        }

        describe("ao tentar criar uma transacao nova") {
            it("deve salvar a transação com estado ACTIVE") {
                val newTransaction =
                    transactionService.create(
                        userId = userId,
                        walletId = walletId,
                        details =
                        PixTransactionDetails(
                            amount = 1,
                            pixKey = pixKey,
                            recipientName = "Recipient",
                            recipientDocument = "***.456.789-**",
                            recipientInstitution = "Banco teste",
                            sweepingAmount = null,
                            sweepingParticipantId = null,
                            qrCode = null,
                        ),
                    )

                val transactions = transactionRepository.find(userId)

                transactions.size shouldBe 1
                with(transactions.single()) {
                    this.id shouldBe newTransaction.id
                    this.groupId shouldBe newTransaction.groupId
                    this.userId shouldBe newTransaction.userId
                    this.walletId shouldBe newTransaction.walletId
                    this.status shouldBe newTransaction.status
                    this.paymentStatus shouldBe TransactionPaymentStatus.UNKNOWN
                    this.details shouldBe newTransaction.details
                    this.createdAt.format(timestampFormatWithBrazilTimeZone) shouldBe newTransaction.createdAt.format(timestampFormatWithBrazilTimeZone)
                    this.updatedAt shouldBeAfter newTransaction.updatedAt
                }
            }

            it("deve expirar a transação ativa de outro grupo se ja existir") {
                val oldTransaction = setupPixTransaction(status = TransactionStatus.ACTIVE)

                val newTransaction =
                    transactionService.create(
                        userId = userId,
                        walletId = walletId,
                        details =
                        PixTransactionDetails(
                            amount = 1,
                            pixKey = pixKey,
                            recipientName = "Recipient",
                            recipientDocument = "***.456.789-**",
                            recipientInstitution = "Banco teste",
                            sweepingAmount = null,
                            sweepingParticipantId = null,
                            qrCode = null,
                        ),
                    )

                val transactions = transactionRepository.find(userId)

                transactions.size shouldBe 2
                with(transactions.single { it.status == TransactionStatus.ACTIVE }) {
                    this.id shouldBe newTransaction.id
                    this.userId shouldBe newTransaction.userId
                    this.walletId shouldBe newTransaction.walletId
                    this.status shouldBe newTransaction.status
                    this.paymentStatus shouldBe TransactionPaymentStatus.UNKNOWN
                    this.details shouldBe newTransaction.details
                    this.createdAt.format(timestampFormatWithBrazilTimeZone) shouldBe newTransaction.createdAt.format(timestampFormatWithBrazilTimeZone)
                    this.updatedAt shouldBeAfter newTransaction.updatedAt
                }
                with(transactions.single { it.status == TransactionStatus.EXPIRED }) {
                    this.id shouldBe oldTransaction.id
                    this.userId shouldBe oldTransaction.userId
                    this.walletId shouldBe oldTransaction.walletId
                    this.status shouldBe TransactionStatus.EXPIRED
                    this.paymentStatus shouldBe TransactionPaymentStatus.UNKNOWN
                    this.details shouldBe oldTransaction.details
                    this.createdAt.format(timestampFormatWithBrazilTimeZone) shouldBe oldTransaction.createdAt.format(timestampFormatWithBrazilTimeZone)
                    this.updatedAt shouldBeAfter oldTransaction.updatedAt
                }
            }

            it("não deve expirar a transação ativa do mesmo grupo se ja existir") {
                val oldTransaction = setupPixTransaction(status = TransactionStatus.ACTIVE)

                val newTransaction =
                    transactionService.create(
                        userId = userId,
                        walletId = walletId,
                        details =
                        PixTransactionDetails(
                            amount = 1,
                            pixKey = pixKey,
                            recipientName = "Recipient",
                            recipientDocument = "***.456.789-**",
                            recipientInstitution = "Banco teste",
                            sweepingAmount = null,
                            sweepingParticipantId = null,
                            qrCode = null,
                        ),
                        transactionGroupId = oldTransaction.groupId,
                    )

                val transactions = transactionRepository.find(userId)

                transactions.size shouldBe 2
                transactions.all { it.status == TransactionStatus.ACTIVE } shouldBe true
            }
        }
    }
}