package ai.chatbot.app

import ai.chatbot.adapters.billPayment.BillStatus
import ai.chatbot.adapters.lucene.LuceneTextSearch
import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.adapters.waCommCentre.ClientId
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.BillType
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.bill.PendingBillsService
import ai.chatbot.app.bill.Recipient
import ai.chatbot.app.bill.ScheduledInfo
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.Action
import ai.chatbot.app.conversation.ActionExecutorLocator
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.AdditionalContext
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InteractionWindowService
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.MarkBillsAsPaidOrIgnoreBillsAction
import ai.chatbot.app.conversation.MarkReminderAsDoneAction
import ai.chatbot.app.conversation.OnboardingSinglePixAction
import ai.chatbot.app.conversation.PromoteSweepingAccountAction
import ai.chatbot.app.conversation.Subscription
import ai.chatbot.app.conversation.SubscriptionPaymentStatus
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.conversation.actions.ActionExecutorsHelpers
import ai.chatbot.app.conversation.actions.AddFeatureRequestExecutor
import ai.chatbot.app.conversation.actions.CreateManualEntryExecutor
import ai.chatbot.app.conversation.actions.GetContextExecutor
import ai.chatbot.app.conversation.actions.MakePaymentExecutor
import ai.chatbot.app.conversation.actions.MarkBillsAsPaidExecutor
import ai.chatbot.app.conversation.actions.MarkReminderAsDoneExecutor
import ai.chatbot.app.conversation.actions.MessageActionExecutor
import ai.chatbot.app.conversation.actions.NoopExecutor
import ai.chatbot.app.conversation.actions.NotificationActionExecutor
import ai.chatbot.app.conversation.actions.OnboardingSinglePixExecutor
import ai.chatbot.app.conversation.actions.PixTransactionExecutor
import ai.chatbot.app.conversation.actions.RefreshBalanceExecutor
import ai.chatbot.app.conversation.actions.RemoveManualEntryExecutor
import ai.chatbot.app.conversation.actions.ScheduleBoletoExecutor
import ai.chatbot.app.conversation.actions.SearchContactsExecutor
import ai.chatbot.app.conversation.actions.SelectSweepingAccountExecutor
import ai.chatbot.app.conversation.actions.SendBillsPeriodExecutor
import ai.chatbot.app.conversation.actions.ValidateBoletoExecutor
import ai.chatbot.app.featureflag.FeatureFlag
import ai.chatbot.app.featureflag.FeatureFlagStatus
import ai.chatbot.app.featureflag.FeatureFlags
import ai.chatbot.app.media.BoletoInfo
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.CTALink
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.ConfigurationKey
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.FridayNotificationContextTemplatesService
import ai.chatbot.app.notification.FridayOnboardingSinglePixNotificationService
import ai.chatbot.app.notification.NotificationContextTemplatesService
import ai.chatbot.app.notification.NotificationMessages
import ai.chatbot.app.notification.TemplateInfoService
import ai.chatbot.app.notification.TextNotificationConfig
import ai.chatbot.app.payment.Balance
import ai.chatbot.app.payment.Forecast
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.dateFormat
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.unmockkObject
import java.awt.SystemColor.text
import java.time.ZonedDateTime

val DOCUMENT = "***********"
val DOCUMENT_2 = "***********"
val CNPJ = "**************"
const val BILL_ID = "BILL-********-0a1f-3d4f-ac30-40659849c9e8"
const val BILL_ID_2 = "BILL-********-15b8-42c9-927a-fc86a5c049c6"
const val BILL_ID_3 = "BILL-********-4eb0-4f2c-980d-78106052a4bd"
const val BILL_ID_4 = "BILL-********-08ee-427c-9521-e33f68021839"
const val BILL_ID_5 = "BILL-55555555-3400-4938-9926-3f8c9e866934"
const val BILL_ID_6 = "BILL-66666666-3400-4938-9926-3f8c9e866934"
const val BILL_ID_7 = "BILL-77777777-9a28-45f1-8409-ef8432dfe24f"
const val BILL_ID_8 = "BILL-88888888-9a28-45f1-8409-ef8432dfe24f"

val subscription =
    Subscription(
        fee = 990,
        dueDate = getLocalDate(),
        paymentStatus = SubscriptionPaymentStatus.PAID,
        type = SubscriptionType.PIX,
    )

val balance =
    Balance(
        current = 1000_00,
        open =
        Forecast(
            amountToday = 100_00,
            amountInSevenDays = 300_00,
            amountInFifteenDays = 1100_00,
            amountMonth = 1200_00,
            amountForNextMonth = 1300_00,
        ),
        onlyScheduled =
        Forecast(
            amountToday = 50_00,
            amountInSevenDays = 250_00,
            amountInFifteenDays = 1150_00,
            amountMonth = 1150_00,
            amountForNextMonth = 1250_00,
        ),
        walletId = WalletId("walletId"),
    )

val subscriptionBillView =
    BillView(
        billId = BillId(BILL_ID_4),
        billDescription = "Assinatura Friday",
        recipient = Recipient(name = "Assinatura Friday"),
        amount = 990,
        amountTotal = 990,
        billType = BillType.PIX,
        scheduledInfo = ScheduledInfo.SCHEDULED,
        paymentLimitTime = "23:59",
        dueDate = getLocalDate(),
        externalBillId = 1,
        assignor = null,
        discount = 0,
        fine = 0,
        interest = 0,
        subscriptionFee = true,
        status = BillStatus.ACTIVE,
    )

val billView =
    BillView(
        billId = BillId(BILL_ID),
        billDescription = "conta da luz",
        recipient = Recipient(name = "John Doe"),
        amount = 4846,
        discount = 4751,
        interest = 2381,
        fine = 3696,
        amountTotal = 9697,
        billType = BillType.FICHA_COMPENSACAO,
        assignor = "John Doe",
        scheduledInfo = ScheduledInfo.NOT_SCHEDULED,
        paymentLimitTime = "20:00",
        dueDate = getLocalDate(),
        externalBillId = 1,
        subscriptionFee = false,
        status = BillStatus.ACTIVE,
    )

val billView2 =
    BillView(
        billId = BillId(BILL_ID_2),
        billDescription = "Pix para o enzo",
        recipient = Recipient(name = "Enzo"),
        amount = 70_000_00,
        discount = 0,
        interest = 0,
        fine = 0,
        amountTotal = 70_000_00,
        billType = BillType.PIX,
        assignor = "Enzo",
        scheduledInfo = ScheduledInfo.NOT_SCHEDULED,
        paymentLimitTime = "20:00",
        dueDate = getLocalDate(),
        externalBillId = 2,
        subscriptionFee = false,
        status = BillStatus.ACTIVE,
    )

val billView3 =
    BillView(
        billId = BillId(BILL_ID_3),
        billDescription = "Pix para João",
        recipient = Recipient(name = "João Pedro Bretanha"),
        amount = 7_00,
        discount = 0,
        interest = 0,
        fine = 0,
        amountTotal = 7_00,
        billType = BillType.PIX,
        assignor = "João Pedro Bretanha",
        scheduledInfo = ScheduledInfo.NOT_SCHEDULED,
        paymentLimitTime = "20:00",
        dueDate = getLocalDate(),
        externalBillId = 3,
        subscriptionFee = false,
        status = BillStatus.ACTIVE,
    )

fun billComingDueHistoryState(
    userId: UserId = UserId("*************"),
    accountId: AccountId = AccountId("ACCOUNT-ID"),
    walletId: WalletId = WalletId("WALLET-ID"),
): BillComingDueHistoryState {
    return BillComingDueHistoryState(
        walletWithBills =
        WalletWithBills(
            bills = listOf(billView, billView2, billView3),
            walletId = walletId,
            walletName = "nome da carteira",
        ),
        internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
        balance = null,
        user =
        User(
            accountId = accountId,
            id = userId,
            name = "Nome do usuário",
            accountGroups = listOf(),
            status = AccountStatus.ACTIVE,
        ),
        contacts = emptyList(),
        subscription = null,
    )
}

fun <T> withGivenDateTime(
    time: ZonedDateTime,
    toBeExecuted: () -> T,
): T {
    mockkObject(BrazilZonedDateTimeSupplier)
    every { getZonedDateTime() } returns time
    every { getLocalDate() } returns time.toLocalDate()
    try {
        return toBeExecuted()
    } finally {
        unmockkObject(BrazilZonedDateTimeSupplier)
    }
}

fun newActionExecutorLocator(
    notificationService: NotificationService,
    paymentAdapter: PaymentAdapter,
    conversationHistoryService: ConversationHistoryService,
    transactionService: TransactionService,
    openAIAdapter: OpenAIAdapter,
    pendingBillsService: PendingBillsService,
    buildNotificationService: BuildNotificationService = mockk(),
    notificationContextTemplatesService: NotificationContextTemplatesService = mockk(),
    interactionWindowService: InteractionWindowService = mockk(),
    customNotificationService: CustomNotificationService = mockk(relaxed = true) { every { config() } returns mockNotificationConfigs },
    actionExecutorsHelpers: ActionExecutorsHelpers = mockk(relaxed = true),
    tenantService: TenantService = mockk(relaxed = true),
): ActionExecutorLocator {
    return ActionExecutorLocator(
        messageActionExecutor = MessageActionExecutor(notificationService = notificationService),
        notificationActionExecutor = NotificationActionExecutor(notificationService = notificationService),
        addFeatureRequestExecutor = AddFeatureRequestExecutor(),
        refreshBalanceExecutor =
        RefreshBalanceExecutor(
            paymentAdapter = paymentAdapter,
            conversationHistoryService = conversationHistoryService,
        ),
        markBillsAsPaidExecutor =
        MarkBillsAsPaidExecutor(
            paymentAdapter = paymentAdapter,
            conversationHistoryService = conversationHistoryService,
            notificationService = notificationService,
            onePixPayInstrumentation = mockk(relaxed = true),
            buildNotificationService = BuildNotificationService(FridayNotificationContextTemplatesService(), tenantService = defaultTenantService()),
            notificationContextTemplatesService = FridayNotificationContextTemplatesService(),
            eventService = mockk(relaxed = true),
            transactionService = transactionService,
            tenantService = defaultTenantService(),
        ),
        markReminderAsDoneExecutor =
        MarkReminderAsDoneExecutor(
            paymentAdapter = paymentAdapter,
            notificationService = notificationService,
            buildNotificationService = BuildNotificationService(FridayNotificationContextTemplatesService(), tenantService = defaultTenantService()),
        ),
        noopExecutor = NoopExecutor(),
        getContextExecutor = GetContextExecutor(),
        pixTransactionExecutor = PixTransactionExecutor(
            paymentAdapter = paymentAdapter,
            conversationHistoryService = conversationHistoryService,
            notificationService = notificationService,
            transactionService = transactionService,
            textSearchAdapter = LuceneTextSearch(),
            buildNotificationService = buildNotificationService,
            customNotificationService = customNotificationService,
        ),
        searchContactsExecutor = SearchContactsExecutor(),

        onboardingSinglePixExecutor = spyk(
            OnboardingSinglePixExecutor(
                notificationService = notificationService,
                conversationHistoryService = conversationHistoryService,
                buildNotificationService = buildNotificationService,
                onboardingSinglePixNotificationService = FridayOnboardingSinglePixNotificationService(),
                paymentAdapter = paymentAdapter,
                interactionWindowService = interactionWindowService,
                customNotificationService = customNotificationService,
                tenantService = tenantService,
            ),
        ),
        makePaymentExecutor = MakePaymentExecutor(
            notificationService = notificationService,
            conversationHistoryService = conversationHistoryService,
            onePixPayInstrumentation = mockk(relaxed = true),
            buildNotificationService = BuildNotificationService(FridayNotificationContextTemplatesService(), tenantService = defaultTenantService()),
            actionExecutorsHelpers = actionExecutorsHelpers,
            transactionService = transactionService,
        ),
        promoteSweepingAccount = mockk(),
        sendBillsPeriodExecutor =
        SendBillsPeriodExecutor(
            pendingBillsService = pendingBillsService,
            buildNotificationService = BuildNotificationService(FridayNotificationContextTemplatesService(), tenantService = defaultTenantService()),
            notificationService = notificationService,
            conversationHistoryService = conversationHistoryService,
            customNotificationService = customNotificationService,
            tenantService = defaultTenantService(),
        ),
        validateBoletoExecutor = ValidateBoletoExecutor(
            paymentAdapter = paymentAdapter,
            customNotificationService = customNotificationService,
            transactionService = transactionService,
            notificationContextTemplatesService = notificationContextTemplatesService,
        ),
        scheduleBoletoExecutor = ScheduleBoletoExecutor(
            actionExecutorsHelpers = actionExecutorsHelpers,
            paymentAdapter = paymentAdapter,
            transactionService = transactionService,
            conversationHistoryService = conversationHistoryService,
            customNotificationService = customNotificationService,
        ),
        createManualEntryExecutor = CreateManualEntryExecutor(
            paymentAdapter = paymentAdapter,
            customNotificationService = customNotificationService,
        ),
        removeManualEntryExecutor = RemoveManualEntryExecutor(
            paymentAdapter = paymentAdapter,
            customNotificationService = customNotificationService,
        ),
        selectSweepingAccountExecutor = SelectSweepingAccountExecutor(
            transactionService = transactionService,
            customNotificationService = customNotificationService,
            conversationHistoryService = conversationHistoryService,
            paymentAdapter = paymentAdapter,
        ),
    )
}

object ActionFixture {
    fun create(actionType: ActionType): Action {
        return when (actionType) {
            ActionType.MSG -> Action.SendMessage(content = "mensagem")
            ActionType.NOTIFICATION -> Action.SendNotification(ChatbotRawTemplatedNotification(mobilePhone = "+*************", accountId = AccountId("ACCOUNT-ID"), clientId = ClientId("FAKE"), configurationKey = ConfigurationKey("configuration-key")))
            ActionType.REFRESH_BALANCE_AND_FORECASTS -> Action.RefreshBalance
            ActionType.ADD_FEATURE_REQUEST -> Action.NotSupportedFeature(content = emptyList())
            ActionType.MARK_BILLS_AS_PAID -> Action.MarkAsPaidOrIgnore(payload = MarkBillsAsPaidOrIgnoreBillsAction(billsToIgnoreOrRemove = emptyList(), billsToMarkAsPaid = emptyList(), userConfirmed = false))
            ActionType.MARK_REMINDER_AS_DONE -> Action.MarkReminderAsDone(payload = MarkReminderAsDoneAction(reminderId = ""))
            ActionType.NOOP -> Action.Noop
            ActionType.GET_CONTEXT -> Action.GetContext(context = AdditionalContext.ORIGINAL, subject = "")
            ActionType.PIX_TRANSACTION -> Action.PixTransaction(amount = 0, key = "", type = "", ignoreLastUsed = true)
            ActionType.SEARCH_CONTACTS -> Action.SearchContacts("", "")
            ActionType.ONBOARDING_SINGLE_PIX -> Action.OnboardingSinglePix(OnboardingSinglePixAction(action = "", key = "", type = ""))
            ActionType.MAKE_PAYMENT -> Action.MakePayment(bills = listOf(1, 2, 3), type = "SCHEDULE")
            ActionType.PROMOTE_SWEEPING_ACCOUNT -> Action.PromoteSweepingAccount(PromoteSweepingAccountAction(action = ""))
            ActionType.SEND_PENDING_BILLS_PERIOD -> Action.SendPendingBillsPeriod(
                startDate = getLocalDate().format(dateFormat),
                endDate = getLocalDate().format(dateFormat),
                amount = false,
            )

            ActionType.VALIDATE_BOLETO -> Action.ValidateBoleto(listOf(BoletoInfo("34191004791020150008787820026318901790010104351")))
            ActionType.SCHEDULE_BOLETO -> Action.ScheduleBoleto(
                transactionId = TransactionId("1111111"),
            )

            ActionType.CREATE_MANUAL_ENTRY -> Action.CreateManualEntry(title = "Lançamento manual", amount = 1234L, type = ManualEntryType.EXTERNAL_PAYMENT, dueDate = getLocalDate().format(dateFormat))
            ActionType.REMOVE_MANUAL_ENTRY -> Action.RemoveManualEntry(manualEntryId = "MANUAL-ENTRY-ID")
            ActionType.SELECT_SWEEPING_ACCOUNT -> Action.SelectSweepingAccount(transactionGroupId = TransactionGroupId())
        }
    }
}

fun defaultFeatureFlags() = FeatureFlags(
    waCommCentre = FeatureFlag(enabled = FeatureFlagStatus.EVERYONE),
    dailyLogS3 = FeatureFlag(enabled = FeatureFlagStatus.EVERYONE),
    sendViaWaCommCentre = FeatureFlag(enabled = FeatureFlagStatus.EVERYONE),
    userEventsHttp = FeatureFlag(enabled = FeatureFlagStatus.EVERYONE),
)

val mockNotificationConfigs = NotificationMessages(
    onboardingPromoteSweepingAccount = TextNotificationConfig(text = "onboardingPromoteSweepingAccount"),
    pixConfirmationSingleSweepingConsent = TextNotificationConfig(text = "pixConfirmationSingleSweepingConsent"),
    pixConfirmationMultipleSweepingConsent = TextNotificationConfig(text = "pixConfirmationMultipleSweepingConsent"),
    pixConfirmationSelectSweepingConsent = TextNotificationConfig(text = "pixConfirmationSelectSweepingConsent"),
    noBillsFoundForPeriod = TextNotificationConfig(text = "Nenhuma conta encontrada para o período informado."),
    createManualEntryErrorNoTitle = TextNotificationConfig(text = "createManualEntryErrorNoTitle"),
    createManualEntryErrorNoAmount = TextNotificationConfig(text = "createManualEntryErrorNoAmount"),
    createManualEntryErrorNoAmountAndTitle = TextNotificationConfig(text = "createManualEntryErrorNoAmountAndTitle"),
    createManualEntrySuccess = TextNotificationConfig(text = "createManualEntrySuccess"),
    removeManualEntrySuccess = TextNotificationConfig(text = "removeManualEntrySuccess"),
    createReminderErrorNoDate = TextNotificationConfig(text = "createReminderErrorNoDate"),
    createReminderErrorNoTitle = TextNotificationConfig(text = "createReminderErrorNoTitle"),
    createReminderErrorNoTitleAndDate = TextNotificationConfig(text = "createReminderErrorNoTitleAndDate"),
    createReminderErrorDateInPast = TextNotificationConfig(text = "createReminderErrorDateInPast"),
    createReminderSuccess = TextNotificationConfig(text = "createReminderSuccess"),
    createReminderSuccessNoAmount = TextNotificationConfig(text = "createReminderSuccessNoAmount"),
    selectSweepingAccount = TextNotificationConfig(text = "selectSweepingAccount"),
    scheduleAuthorizationSingleSweepingConsent = TextNotificationConfig(text = "scheduleAuthorizationSingleSweepingConsent"),
    scheduleConfirmationSingleSweepingConsent = TextNotificationConfig(text = "scheduleConfirmationSingleSweepingConsent"),
    scheduleAuthorizationMultipleSweepingConsent = TextNotificationConfig(text = "scheduleAuthorizationMultipleSweepingConsent"),
    scheduleAuthorizationSelectSweepingConsent = TextNotificationConfig(text = "scheduleAuthorizationSelectSweepingConsent"),
    scheduleConfirmationMultipleSweepingConsent = TextNotificationConfig(text = "scheduleConfirmationMultipleSweepingConsent"),
    scheduleConfirmationSelectSweepingConsent = TextNotificationConfig(text = "scheduleConfirmationSelectSweepingConsent"),
    authorizePixSweeping = TextNotificationConfig(text = "authorizePixSweeping"),
)

fun getCustomNotificationServiceSpy(): CustomNotificationService {
    val spy = spyk(
        CustomNotificationService(
            notificationService = mockk(relaxed = true),
            conversationHistoryService = mockk(relaxed = true),
            templateInfoService = mockk(relaxed = true),
            tenantService = defaultTenantService(),
            notificationContextTemplatesService = mockk(relaxed = true),
        ),
    )

    every { spy.send(any()) } just Runs
    every { spy.send(any(), any(), any(), any<CTALink>()) } just Runs
    every { spy.send(any(), any(), any(), any(), any()) } just Runs

    return spy
}

fun tenantConfiguration(): TenantConfiguration = mockk(relaxed = true) {
    every { appBaseUrl } returns "https://app.base.url"
    every { messages } returns mockNotificationConfigs
}

fun defaultTenantService() = mockk<TenantService>(relaxed = true) {
    every {
        getConfiguration()
    } returns tenantConfiguration()
}

fun getCustomNotificationServiceMock(
    notificationContextTemplatesService: NotificationContextTemplatesService = mockk(relaxed = true),
    templateInfoService: TemplateInfoService = mockk(relaxed = true),
): CustomNotificationService {
    val mock = spyk(
        CustomNotificationService(
            notificationService = mockk(relaxed = true),
            conversationHistoryService = mockk(relaxed = true),
            templateInfoService = templateInfoService,
            tenantService = defaultTenantService(),
            notificationContextTemplatesService = notificationContextTemplatesService,
        ),
    )

    every { mock.send(any()) } returns Unit
    every { mock.send(any(), any(), any(), any<CTALink>()) } returns Unit
    every { mock.send(any(), any(), any(), any(), any()) } returns Unit

    return mock
}

object PixKeyFixture {
    fun create(
        value: String = "01866430041",
        type: PixKeyType = PixKeyType.CPF,
    ): PixKey {
        return PixKey(value = value, type = type)
    }
}

const val DYNAMOB_TEST_TABLE_NAME = "TABLE_NAME"