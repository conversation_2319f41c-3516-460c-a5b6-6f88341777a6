package ai.chatbot.app.pix

import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe

class PixKeyTest : DescribeSpec() {
    init {

        describe("quando for uma chave pix CNPJ, CPF ou email") {
            it("deve sanitizar o valor") {
                val pixKeyCNPJ = PixKey(value = "24.933.901/0001-02", type = PixKeyType.CNPJ)
                val pixKeyCPF = PixKey(value = "841.672.060-64", type = PixKeyType.CPF)
                val pixKeyEmail = PixKey(value = "<EMAIL>", type = PixKeyType.EMAIL)

                pixKeyCNPJ.value shouldBe "24933901000102"
                pixKeyCPF.value shouldBe "84167206064"
                pixKeyEmail.value shouldBe "<EMAIL>"
            }
        }

        describe("quando for uma chave aleatória ou telefone") {
            it("não deve sanitizar o valor") {
                val pixKeyEVP = PixKey(value = "b2a5c5b4-1b0d-11ec-9d64-0242ac120002", type = PixKeyType.EVP)
                val pixKeyPhone = PixKey(value = "+5511999999999", type = PixKeyType.PHONE)

                pixKeyEVP.value shouldBe "b2a5c5b4-1b0d-11ec-9d64-0242ac120002"
                pixKeyPhone.value shouldBe "+5511999999999"
            }
        }
    }
}