package ai.chatbot.app.transcription

import ai.chatbot.adapters.awstranscribe.AWSTranscriptionAdapter
import ai.chatbot.adapters.openai.OpenAITranscriptionAdapter
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.file.ObjectRepository
import ai.chatbot.app.file.StoredObject
import ai.integration.chatbot.utils.OpenAITokenCondition
import io.kotest.core.annotation.EnabledIf
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk

@EnabledIf(OpenAITokenCondition::class)
@MicronautTest(environments = [FRIDAY_ENV, "test"])
class TranscriptionServiceTest(
    private val aWSTranscriptionAdapter: AWSTranscriptionAdapter,
    private val openAITranscriptionAdapter: OpenAITranscriptionAdapter,
) : DescribeSpec() {

    @MockBean(ObjectRepository::class)
    fun objectRepository(): ObjectRepository = objectRepository
    private val objectRepository = mockk<ObjectRepository>()

    init {
        describe("ao receber uma mensagem com audio") {
            beforeEach {
                every {
                    objectRepository.loadObject(any(), any())
                } answers {
                    javaClass.getResourceAsStream("/media/${secondArg<String>()}") ?: throw IllegalStateException("Image not found")
                }
            }

            it("deve fazer a transcrição do audio com aws") {
                val response = aWSTranscriptionAdapter.transcribe(StoredObject("test", "bucket", "pix_p_caio.ogg"))
                response shouldBe "Faz um pix de um centavo pro Caio."
            }
            it("deve fazer a transcrição do audio com openai") {
                val response = openAITranscriptionAdapter.transcribe(StoredObject("test", "bucket", "pix_p_caio.ogg"))
                response shouldBe "Faz um pix de um centavo pro Caio."
            }
        }
    }
}