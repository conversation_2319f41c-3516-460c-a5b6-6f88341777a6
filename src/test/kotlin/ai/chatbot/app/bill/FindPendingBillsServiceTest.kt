package ai.chatbot.app.bill

import ai.chatbot.adapters.billPayment.BillPaymentAdapter
import ai.chatbot.app.HistoryStateRepository
import ai.chatbot.app.billView
import ai.chatbot.app.billView2
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.HistoryState
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import arrow.core.Either
import arrow.core.getOrElse
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.fail

class FindPendingBillsServiceTest : DescribeSpec() {
    private val historyStateRepository = mockk<HistoryStateRepository>(relaxed = true)
    private val paymentAdapter = mockk<BillPaymentAdapter>()

    private val userId = UserId("123")

    val user =
        User(
            id = userId,
            accountId = AccountId("accountId"),
            name = "Nome do usuário",
            accountGroups = emptyList(),
            status = AccountStatus.ACTIVE,
        )

    private lateinit var pendingBillsService: PendingBillsService

    init {

        beforeEach {
            clearAllMocks()

            pendingBillsService =
                PendingBillsService(
                    historyStateRepository = historyStateRepository,
                    paymentAdapter = paymentAdapter,
                )

            every {
                historyStateRepository.findLatest(any())
            } returns
                BillComingDueHistoryState(
                    walletWithBills =
                    WalletWithBills(
                        bills = listOf(billView),
                        walletId = WalletId("walletId"),
                        walletName = "nome da carteira",
                    ),
                    internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
                    balance = null,
                    user = user,
                    contacts = emptyList(),
                    subscription = null,
                )
        }

        describe("quando buscar as contas pendentes") {
            describe("e a lista de contas no estado estiver vazia") {
                it("deve retornar billsUpdated como true") {
                    setupPendingBills(listOf(billView))

                    every {
                        historyStateRepository.findLatest(any())
                    } returns
                        BillComingDueHistoryState(
                            walletWithBills =
                            WalletWithBills(
                                bills = emptyList(),
                                walletId = WalletId("walletId"),
                                walletName = "nome da carteira",
                            ),
                            internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
                            balance = null,
                            user = user,
                            contacts = emptyList(),
                            subscription = null,
                        )

                    val result =
                        pendingBillsService.updatePendingBills(userId, LocalDate.now(), LocalDate.now()).getOrElse {
                            fail { "não deveria falhar" }
                        }

                    verify { historyStateRepository.save(any(), any()) }

                    result.billsUpdated.shouldBeTrue()
                }
            }
            describe("e as contas no estado do usuário estiver diferentes do resultado da busca") {
                it("deve retonar o billsUpdated como true") {
                    setupPendingBills(listOf(billView.copy(amountTotal = 500)))

                    val result =
                        pendingBillsService.updatePendingBills(userId, LocalDate.now(), LocalDate.now()).getOrElse {
                            fail { "não deveria falhar" }
                        }

                    verify { historyStateRepository.save(any(), any()) }

                    result.billsUpdated.shouldBeTrue()
                }
            }

            describe("e as contas no estado do usuário estiverem iguais o resultado da busca") {
                it("deve retonar o billsUpdated como false") {
                    setupPendingBills(listOf(billView))

                    val result =
                        pendingBillsService.updatePendingBills(userId, LocalDate.now(), LocalDate.now()).getOrElse {
                            fail { "não deveria falhar" }
                        }

                    result.billsUpdated.shouldBeFalse()
                    verify(exactly = 0) {
                        historyStateRepository.save(any(), any())
                    }
                }
            }

            it("deve preservar walletId existente quando resposta da API tem walletId vazio") {
                val currentState = BillComingDueHistoryState(
                    walletWithBills = WalletWithBills(
                        bills = listOf(billView),
                        walletId = WalletId("existing-wallet-id"),
                        walletName = "existing-wallet-name",
                    ),
                    internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
                    balance = null,
                    user = user,
                    contacts = emptyList(),
                    subscription = null,
                )

                every { historyStateRepository.findLatest(any()) } returns currentState

                val apiResponse = UserAndWallet(
                    user = user,
                    wallet = WalletWithBills(
                        bills = listOf(billView2),
                        walletId = WalletId(""),
                        walletName = "",
                    ),
                )

                coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns Either.Right(apiResponse)

                pendingBillsService.updatePendingBills(userId, LocalDate.now(), LocalDate.now()).getOrElse {
                    fail { "não deveria falhar" }
                }

                val savedStateSlot = slot<HistoryState>()
                verify { historyStateRepository.save(any(), capture(savedStateSlot)) }

                val savedState = savedStateSlot.captured
                savedState.shouldBeTypeOf<BillComingDueHistoryState>()
                savedState.walletWithBills.walletId.shouldBe(WalletId("existing-wallet-id"))
                savedState.walletWithBills.walletName.shouldBe("existing-wallet-name")
            }

            it("deve preservar walletName existente quando resposta da API tem walletName vazio") {
                val currentState = BillComingDueHistoryState(
                    walletWithBills = WalletWithBills(
                        bills = listOf(billView),
                        walletId = WalletId("existing-wallet-id"),
                        walletName = "existing-wallet-name",
                    ),
                    internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
                    balance = null,
                    user = user,
                    contacts = emptyList(),
                    subscription = null,
                )

                every { historyStateRepository.findLatest(any()) } returns currentState

                val apiResponse = UserAndWallet(
                    user = user,
                    wallet = WalletWithBills(
                        bills = listOf(billView2),
                        walletId = WalletId("new-wallet-id"),
                        walletName = "",
                    ),
                )

                coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns Either.Right(apiResponse)

                pendingBillsService.updatePendingBills(userId, LocalDate.now(), LocalDate.now()).getOrElse {
                    fail { "não deveria falhar" }
                }

                val savedStateSlot = slot<HistoryState>()
                verify { historyStateRepository.save(any(), capture(savedStateSlot)) }

                val savedState = savedStateSlot.captured
                savedState.shouldBeTypeOf<BillComingDueHistoryState>()
                savedState.walletWithBills.walletId.shouldBe(WalletId("new-wallet-id"))
                savedState.walletWithBills.walletName.shouldBe("existing-wallet-name")
            }

            it("deve usar valores da API quando são válidos") {
                val currentState = BillComingDueHistoryState(
                    walletWithBills = WalletWithBills(
                        bills = listOf(billView),
                        walletId = WalletId("existing-wallet-id"),
                        walletName = "existing-wallet-name",
                    ),
                    internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
                    balance = null,
                    user = user,
                    contacts = emptyList(),
                    subscription = null,
                )

                every { historyStateRepository.findLatest(any()) } returns currentState

                val apiResponse = UserAndWallet(
                    user = user,
                    wallet = WalletWithBills(
                        bills = listOf(billView2),
                        walletId = WalletId("new-wallet-id"),
                        walletName = "new-wallet-name",
                    ),
                )

                coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns Either.Right(apiResponse)

                pendingBillsService.updatePendingBills(userId, LocalDate.now(), LocalDate.now()).getOrElse {
                    fail { "não deveria falhar" }
                }

                val savedStateSlot = slot<HistoryState>()
                verify { historyStateRepository.save(any(), capture(savedStateSlot)) }

                val savedState = savedStateSlot.captured
                savedState.shouldBeTypeOf<BillComingDueHistoryState>()
                savedState.walletWithBills.walletId.shouldBe(WalletId("new-wallet-id"))
                savedState.walletWithBills.walletName.shouldBe("new-wallet-name")
            }
        }
    }

    private fun setupPendingBills(bills: List<BillView>) {
        coEvery {
            paymentAdapter.findPendingBills(any(), any(), any())
        } returns Either.Right(UserAndWallet(user = user, wallet = WalletWithBills(bills = bills, walletId = WalletId("walletId"), walletName = "nome da carteira")))
    }
}