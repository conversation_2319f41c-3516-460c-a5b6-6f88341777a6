package ai.chatbot.app.notification

import ai.chatbot.app.NotificationService
import ai.chatbot.app.config.FeaturesConfig
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.defaultTenantService
import ai.chatbot.app.tenantConfiguration
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.parseObjectFrom
import ai.chatbot.app.utils.timestampFormatWithBrazilTimeZone
import ai.chatbot.app.withGivenDateTime
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.ZonedDateTime
import org.junit.jupiter.api.assertDoesNotThrow

class CustomNotificationServiceTest : DescribeSpec() {
    private val tenantService = defaultTenantService()

    private val templateInfoService: TemplateInfoService = mockk()
    private val conversationHistoryService: ConversationHistoryService = mockk(relaxed = true)
    private val notificationService: NotificationService = mockk(relaxed = true)
    private val service = CustomNotificationService(
        templateInfoService = templateInfoService,
        conversationHistoryService = conversationHistoryService,
        notificationService = notificationService,
        tenantService = tenantService,
        notificationContextTemplatesService = mockk(relaxed = true),
    )

    private val templateInfo = TemplateInfo(
        text = "Texto do template",
        params = 0,
        buttons = listOf(),
        category = "MARKETING",
    )

    private val user = User(
        id = UserId("user-id"),
        accountId = AccountId("account-id"),
        name = "Fulano Beltrano",
        accountGroups = listOf(),
        status = AccountStatus.ACTIVE,
        paymentStatus = AccountPaymentStatus.UpToDate,
    )

    private val templateConfig = RawTemplateNotificationConfig(
        configurationKey = ConfigurationKey("configuration-key"),
        notificationType = NotificationType("notificationType"),
    )

    private val simpleConfig = TextNotificationConfig(
        text = "Notificação de teste",
    )

    private val mockFeatures = mockk<FeaturesConfig> {
        every { openFinanceIncentive } returns true
    }

    private val tenantConfiguration = tenantConfiguration()

    init {
        beforeEach {
            clearMocks(
                templateInfoService,
                conversationHistoryService,
                notificationService,
                mockFeatures,
            )

            every { templateInfoService.getHistoryMessage(any()) } returns "Texto do template"

            every { tenantConfiguration.features } returns mockFeatures
            every { tenantService.getConfiguration() } returns tenantConfiguration
        }

        describe("Ao tentar enviar uma notificação de template") {
            it("deve enviar template simples com sucesso") {
                every { notificationService.notify(any()) } returns Unit
                every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit

                service.send(user, listOf(), templateConfig)

                val slot = slot<ChatbotRawTemplatedNotification>()

                verify {
                    notificationService.notify(capture(slot))
                }

                with(slot.captured) {
                    accountId shouldBe user.accountId
                    mobilePhone shouldBe user.id.value
                    arguments shouldBe emptyMap()
                }

                verify {
                    conversationHistoryService.createAssistantMessage(user.id, templateInfo.text)
                }
            }

            it("deve enviar o mapa de parâmetros") {
                every { notificationService.notify(any()) } returns Unit
                every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit

                every {
                    templateInfoService.getHistoryMessage(any())
                } returns "Texto com parâmetro Fulano"

                service.send(user, listOf(NotificationMap(NotificationParam.USER_NAME, "Fulano")), templateConfig)

                val slot = slot<ChatbotRawTemplatedNotification>()

                verify {
                    notificationService.notify(capture(slot))
                }

                with(slot.captured) {
                    arguments shouldBe mapOf(
                        "USER_NAME" to "Fulano",
                    )
                }

                verify {
                    conversationHistoryService.createAssistantMessage(user.id, "Texto com parâmetro Fulano")
                }
            }
        }

        describe("ao tentar construir uma notificacao simples") {
            val params = listOf(
                NotificationMap(NotificationParam.TRANSACTION_ID, "TRANSACTION-123"),
                NotificationMap(NotificationParam.CONTA1_NOME, "Itaú"),
                NotificationMap(NotificationParam.TRANSACTION_ID_2, "TRANSACTION-456"),
            )

            it("quando o origem do transactionId NÃO foi configurado, o payload do botão deve usar o parametro TRANSACTION_ID") {
                val notificationConfig = TextNotificationConfig(
                    text = "messageText",
                    params = params.map { it.param.name },
                    quickReplies = listOf(
                        QuickReplyConfig(
                            text = "Sim, usar {{CONTA1_NOME}}",
                            action = InterceptMessagePayloadType.TRANSACTION_CONFIRM.name,
                            transactionId = null,
                        ),
                    ),
                )

                val result = service.buildFromTextNotificationConfig(user, notificationConfig, params)

                with(result.notification) {
                    shouldBeTypeOf<ChatbotWhatsappSimpleNotification>()
                    message shouldBe "messageText"
                    with(quickReplyButtons) {
                        shouldNotBeNull()
                        size shouldBe 1
                        with(first()) {
                            text shouldBe "Sim, usar Itaú"
                            val buttonPayload = assertDoesNotThrow {
                                parseObjectFrom<BlipPayload>(payload)
                            }
                            buttonPayload.payload shouldBe getLocalDate().format(dateFormat)
                            buttonPayload.action shouldBe InterceptMessagePayloadType.TRANSACTION_CONFIRM.name
                            buttonPayload.transactionId shouldBe "TRANSACTION-123"
                        }
                    }
                }
            }

            it("quando a origem do transactionId foi configurada, o payload do botão deve respeitar o parametro informado") {
                val notificationConfig = TextNotificationConfig(
                    text = "messageText",
                    params = params.map { it.param.name },
                    quickReplies = listOf(
                        QuickReplyConfig(
                            text = "Sim, usar {{CONTA1_NOME}}",
                            action = InterceptMessagePayloadType.TRANSACTION_CONFIRM.name,
                            transactionId = NotificationParam.TRANSACTION_ID_2,
                        ),
                    ),
                )

                val result = service.buildFromTextNotificationConfig(user, notificationConfig, params)

                with(result.notification) {
                    shouldBeTypeOf<ChatbotWhatsappSimpleNotification>()
                    message shouldBe "messageText"
                    with(quickReplyButtons) {
                        shouldNotBeNull()
                        size shouldBe 1
                        with(first()) {
                            text shouldBe "Sim, usar Itaú"
                            val buttonPayload = assertDoesNotThrow {
                                parseObjectFrom<BlipPayload>(payload)
                            }
                            buttonPayload.payload shouldBe getLocalDate().format(dateFormat)
                            buttonPayload.action shouldBe InterceptMessagePayloadType.TRANSACTION_CONFIRM.name
                            buttonPayload.transactionId shouldBe "TRANSACTION-456"
                        }
                    }
                }
            }
        }

        describe("Ao tentar enviar uma notificação simples") {
            it("deve enviar com sucesso") {
                every { notificationService.notify(any()) } returns Unit
                every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit

                service.send(user, listOf(), simpleConfig)

                val slot = slot<ChatbotWhatsappSimpleNotification>()

                verify {
                    notificationService.notify(capture(slot))
                }

                with(slot.captured) {
                    accountId shouldBe user.accountId
                    mobilePhone shouldBe user.id.value
                    message shouldBe simpleConfig.text
                    ctaLink shouldBe null
                    quickReplyButtons shouldBe null
                }

                verify {
                    conversationHistoryService.createAssistantMessage(user.id, simpleConfig.text!!)
                }
            }

            it("deve enviar imagem com sucesso") {
                every { notificationService.notify(any()) } returns Unit
                every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit

                val imgUrl = "http://google.com"
                service.send(user, listOf(), simpleConfig.copy(mediaUrl = imgUrl, mediaType = NotificationMediaType.IMAGE))

                val slot = slot<ChatbotWhatsappSimpleNotification>()

                verify {
                    notificationService.notify(capture(slot))
                }

                with(slot.captured) {
                    accountId shouldBe user.accountId
                    mobilePhone shouldBe user.id.value
                    message shouldBe simpleConfig.text
                    ctaLink shouldBe null
                    quickReplyButtons shouldBe null
                    media shouldBe NotificationMedia.Image(url = imgUrl)
                    media!!.type shouldBe NotificationMediaType.IMAGE
                }

                verify {
                    conversationHistoryService.createAssistantMessage(user.id, simpleConfig.text!!)
                }
            }

            it("deve enviar documento com sucesso") {
                every { notificationService.notify(any()) } returns Unit
                every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit

                val imgUrl = "http://google.com"
                service.send(user, listOf(), simpleConfig.copy(mediaUrl = imgUrl, mediaType = NotificationMediaType.DOCUMENT))

                val slot = slot<ChatbotWhatsappSimpleNotification>()

                verify {
                    notificationService.notify(capture(slot))
                }

                with(slot.captured) {
                    accountId shouldBe user.accountId
                    mobilePhone shouldBe user.id.value
                    message shouldBe simpleConfig.text
                    ctaLink shouldBe null
                    quickReplyButtons shouldBe null
                    media shouldBe NotificationMedia.Document(url = imgUrl, filename = "no_name", null)
                    media!!.type shouldBe NotificationMediaType.DOCUMENT
                }

                verify {
                    conversationHistoryService.createAssistantMessage(user.id, simpleConfig.text!!)
                }
            }

            it("deve substituir parâmetros") {
                every { notificationService.notify(any()) } returns Unit
                every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit

                service.send(user, listOf(NotificationMap(NotificationParam.USER_NAME, "Fulano")), simpleConfig.copy(text = "Olá {{USER_NAME}}"))

                val slot = slot<ChatbotWhatsappSimpleNotification>()

                verify {
                    notificationService.notify(capture(slot))
                }

                with(slot.captured) {
                    message shouldBe "Olá Fulano"
                }

                verify {
                    conversationHistoryService.createAssistantMessage(user.id, "Olá Fulano")
                }
            }

            it("deve enviar botões de quick reply") {
                every { notificationService.notify(any()) } returns Unit
                every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit

                withGivenDateTime(ZonedDateTime.parse("2025-01-13 15:00:00.000 Z", timestampFormatWithBrazilTimeZone)) {
                    service.send(
                        user,
                        listOf(NotificationMap(NotificationParam.USER_NAME, "Fulano")),
                        simpleConfig.copy(
                            quickReplies = listOf(
                                QuickReplyConfig(text = "Sim", action = "TRANSACTION_CONFIRM"),
                                QuickReplyConfig(text = "Não", action = "TRANSACTION_CANCEL", payload = "TEST_PAYLOAD"),
                            ),
                        ),
                    )
                }

                val slot = slot<ChatbotWhatsappSimpleNotification>()

                verify {
                    notificationService.notify(capture(slot))
                }

                with(slot.captured) {
                    quickReplyButtons shouldBe listOf(
                        QuickReplyButton(
                            text = "Sim",
                            payload = """{"payload":"2025-01-13","action":"TRANSACTION_CONFIRM","transactionId":null}""",
                        ),
                        QuickReplyButton(
                            text = "Não",
                            payload = """{"payload":"TEST_PAYLOAD","action":"TRANSACTION_CANCEL","transactionId":null}""",
                        ),
                    )
                }

                verify {
                    conversationHistoryService.createAssistantMessage(user.id, any<String>())
                }
            }

            it("deve substituir parâmetros nos payloads de quick reply") {
                every { notificationService.notify(any()) } returns Unit
                every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit

                service.send(
                    user,
                    listOf(NotificationMap(NotificationParam.USER_NAME, "Fulano")),
                    simpleConfig.copy(
                        quickReplies = listOf(
                            QuickReplyConfig(text = "Sim", action = "TRANSACTION_CONFIRM", payload = "{{USER_NAME}}"),
                        ),
                    ),
                )

                val slot = slot<ChatbotWhatsappSimpleNotification>()

                verify {
                    notificationService.notify(capture(slot))
                }

                with(slot.captured) {
                    quickReplyButtons shouldBe listOf(
                        QuickReplyButton(
                            text = "Sim",
                            payload = """{"payload":"Fulano","action":"TRANSACTION_CONFIRM","transactionId":null}""",
                        ),
                    )
                }

                verify {
                    conversationHistoryService.createAssistantMessage(user.id, any<String>())
                }
            }

            it("deve incluir transaction id nos payloads de quick reply se o parâmetro estiver presente") {
                every { notificationService.notify(any()) } returns Unit
                every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit

                service.send(
                    user,
                    listOf(NotificationMap(NotificationParam.TRANSACTION_ID, "TRANSACTION-1")),
                    simpleConfig.copy(
                        quickReplies = listOf(
                            QuickReplyConfig(text = "Sim", action = "TRANSACTION_CONFIRM", payload = "TEST_PAYLOAD"),
                        ),
                    ),
                )

                val slot = slot<ChatbotWhatsappSimpleNotification>()

                verify {
                    notificationService.notify(capture(slot))
                }

                with(slot.captured) {
                    quickReplyButtons shouldBe listOf(
                        QuickReplyButton(
                            text = "Sim",
                            payload = """{"payload":"TEST_PAYLOAD","action":"TRANSACTION_CONFIRM","transactionId":"TRANSACTION-1"}""",
                        ),
                    )
                }

                verify {
                    conversationHistoryService.createAssistantMessage(user.id, any<String>())
                }
            }

            it("deve enviar botão com link") {
                every { notificationService.notify(any()) } returns Unit
                every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit

                service.send(user, listOf(), simpleConfig.copy(link = LinkConfig(text = "Contas", url = "contas")))

                val slot = slot<ChatbotWhatsappSimpleNotification>()

                verify {
                    notificationService.notify(capture(slot))
                }

                with(slot.captured) {
                    ctaLink?.displayText shouldBe "Contas"
                    ctaLink?.url shouldBe "https://app.base.url/app/contas"
                }

                verify {
                    conversationHistoryService.createAssistantMessage(user.id, any<String>())
                }
            }

            it("deve substituir parametros nos links") {
                every { notificationService.notify(any()) } returns Unit
                every { conversationHistoryService.createAssistantMessage(any(), any<String>()) } returns Unit

                service.send(user, listOf(NotificationMap(NotificationParam.TRANSACTION_ID, "TRANSACTION-1")), simpleConfig.copy(link = LinkConfig(text = "Contas", url = "transacao/{{TRANSACTION_ID}}")))

                val slot = slot<ChatbotWhatsappSimpleNotification>()

                verify {
                    notificationService.notify(capture(slot))
                }

                with(slot.captured) {
                    ctaLink?.displayText shouldBe "Contas"
                    ctaLink?.url shouldBe "https://app.base.url/app/transacao/TRANSACTION-1"
                }

                verify {
                    conversationHistoryService.createAssistantMessage(user.id, any<String>())
                }
            }
        }

        describe("Ao tentar obter a mensagem de histórico") {
            it("deve retornar a mensagem do template com os parâmetros substituídos") {
                every { templateInfoService.getInfo(any()) } returns templateInfo.copy(
                    text = "Mensagem com parâmetros {{1}} e {{2}}",
                    params = 2,
                )

                val notification = ChatbotWhatsappTemplatedNotification(
                    accountId = AccountId("account-id"),
                    mobilePhone = "user-id",
                    template = NotificationTemplate("template_name"),
                    configurationKey = "configurationKey",
                    parameters = listOf("primeiro", "segundo"),
                )

                val result = service.getHistoryMessage(notification)

                result shouldBe "Mensagem com parâmetros primeiro e segundo"

                verify {
                    templateInfoService.getInfo("template_name")
                }
            }

            it("deve retornar mensagem padrão quando o template não for encontrado") {
                every { templateInfoService.getInfo(any()) } throws RuntimeException("Template not found")

                val notification = ChatbotWhatsappTemplatedNotification(
                    accountId = AccountId("account-id"),
                    mobilePhone = "user-id",
                    template = NotificationTemplate("template_name"),
                    parameters = listOf(),
                    configurationKey = "configurationKey",
                )

                val result = service.getHistoryMessage(notification)

                result shouldBe "(Não foi possível encontrar o conteúdo da mensagem)"

                verify {
                    templateInfoService.getInfo("template_name")
                }
            }

            it("deve manter o placeholder quando houver mais parâmetros no template que na notificação") {
                every { templateInfoService.getInfo(any()) } returns templateInfo.copy(
                    text = "Mensagem com parâmetros {{1}} e {{2}}",
                    params = 2,
                )

                val notification = ChatbotWhatsappTemplatedNotification(
                    accountId = AccountId("account-id"),
                    mobilePhone = "user-id",
                    template = NotificationTemplate("template_name"),
                    parameters = listOf("primeiro"),
                    configurationKey = "configurationKey",
                )

                val result = service.getHistoryMessage(notification)

                result shouldBe "Mensagem com parâmetros primeiro e {{2}}"

                verify {
                    templateInfoService.getInfo("template_name")
                }
            }
        }

        describe("Ao tentar resolver parâmetros com nomes repetidos") {
            it("deve criar mapa com sufixos numéricos para parâmetros repetidos") {
                every { templateInfoService.getHistoryMessage(any()) } returns "Texto do template"
                every { tenantService.getConfiguration() } returns tenantConfiguration

                val params = listOf(
                    NotificationMap(NotificationParam.TRANSACTION_ID, "TRANSACTION-123"),
                    NotificationMap(NotificationParam.TRANSACTION_ID, "TRANSACTION-456"),
                    NotificationMap(NotificationParam.TRANSACTION_ID, "TRANSACTION-789"),
                    NotificationMap(NotificationParam.USER_NAME, "Fulano"),
                    NotificationMap(NotificationParam.AMOUNT, "100.00"),
                )

                val result = service.buildRawTemplateNotification(user, params, templateConfig)

                with(result.notification) {
                    shouldBeTypeOf<ChatbotRawTemplatedNotification>()

                    arguments shouldBe mapOf(
                        "TRANSACTION_ID_1" to "TRANSACTION-123",
                        "TRANSACTION_ID_2" to "TRANSACTION-456",
                        "TRANSACTION_ID_3" to "TRANSACTION-789",
                        "USER_NAME" to "Fulano",
                        "AMOUNT" to "100.00",
                    )
                }
            }

            it("deve manter parâmetros únicos sem sufixo") {
                every { templateInfoService.getHistoryMessage(any()) } returns "Texto do template"
                every { tenantService.getConfiguration() } returns tenantConfiguration

                val params = listOf(
                    NotificationMap(NotificationParam.USER_NAME, "Fulano"),
                    NotificationMap(NotificationParam.AMOUNT, "100.00"),
                    NotificationMap(NotificationParam.PIX_KEY, "<EMAIL>"),
                )

                val result = service.buildRawTemplateNotification(user, params, templateConfig)

                with(result.notification) {
                    shouldBeTypeOf<ChatbotRawTemplatedNotification>()
                    with(this as ChatbotRawTemplatedNotification) {
                        arguments shouldBe mapOf(
                            "USER_NAME" to "Fulano",
                            "AMOUNT" to "100.00",
                            "PIX_KEY" to "<EMAIL>",
                        )
                    }
                }
            }

            it("deve lidar com múltiplos grupos de parâmetros repetidos") {
                every { templateInfoService.getHistoryMessage(any()) } returns "Texto do template"
                every { tenantService.getConfiguration() } returns tenantConfiguration

                val params = listOf(
                    NotificationMap(NotificationParam.TRANSACTION_ID, "TRANSACTION-123"),
                    NotificationMap(NotificationParam.TRANSACTION_ID, "TRANSACTION-456"),
                    NotificationMap(NotificationParam.USER_NAME, "Fulano"),
                    NotificationMap(NotificationParam.USER_NAME, "Beltrano"),
                    NotificationMap(NotificationParam.AMOUNT, "100.00"),
                    NotificationMap(NotificationParam.AMOUNT, "200.00"),
                    NotificationMap(NotificationParam.AMOUNT, "300.00"),
                )

                val result = service.buildRawTemplateNotification(user, params, templateConfig)

                with(result.notification) {
                    shouldBeTypeOf<ChatbotRawTemplatedNotification>()
                    with(this as ChatbotRawTemplatedNotification) {
                        arguments shouldBe mapOf(
                            "TRANSACTION_ID_1" to "TRANSACTION-123",
                            "TRANSACTION_ID_2" to "TRANSACTION-456",
                            "USER_NAME_1" to "Fulano",
                            "USER_NAME_2" to "Beltrano",
                            "AMOUNT_1" to "100.00",
                            "AMOUNT_2" to "200.00",
                            "AMOUNT_3" to "300.00",
                        )
                    }
                }
            }

            it("deve manter a ordem dos parâmetros repetidos") {
                every { templateInfoService.getHistoryMessage(any()) } returns "Texto do template"
                every { tenantService.getConfiguration() } returns tenantConfiguration

                val params = listOf(
                    NotificationMap(NotificationParam.TRANSACTION_ID, "PRIMEIRO"),
                    NotificationMap(NotificationParam.AMOUNT, "100.00"),
                    NotificationMap(NotificationParam.TRANSACTION_ID, "SEGUNDO"),
                    NotificationMap(NotificationParam.AMOUNT, "200.00"),
                    NotificationMap(NotificationParam.TRANSACTION_ID, "TERCEIRO"),
                )

                val result = service.buildRawTemplateNotification(user, params, templateConfig)

                with(result.notification) {
                    shouldBeTypeOf<ChatbotRawTemplatedNotification>()

                    arguments shouldBe mapOf(
                        "TRANSACTION_ID_1" to "PRIMEIRO",
                        "TRANSACTION_ID_2" to "SEGUNDO",
                        "TRANSACTION_ID_3" to "TERCEIRO",
                        "AMOUNT_1" to "100.00",
                        "AMOUNT_2" to "200.00",
                    )
                }
            }
        }
    }
}