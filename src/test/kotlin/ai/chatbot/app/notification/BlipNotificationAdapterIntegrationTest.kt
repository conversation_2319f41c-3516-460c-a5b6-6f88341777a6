package ai.chatbot.app.notification

import ai.chatbot.app.NotificationService
import ai.chatbot.app.user.UserId
import io.kotest.core.spec.style.FunSpec
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest

// Para rodar esse teste é necessário configurar a api key nas variáveis de ambiente da sua máquina
@MicronautTest
class BlipNotificationAdapterIntegrationTest(private val blipNotificationAdapter: NotificationService) : FunSpec(
    {
        xcontext("quando enviar uma notificação para o blip") {
            test("deve retornar uma mensagem de sucesso") {
                val result =
                    blipNotificationAdapter.notify(
                        UserId.fromMsisdn("5553981442413"),
                        null,
                        """Teste de \n quebra de linha""",
                    )
                println(result)
            }
        }
    },
)