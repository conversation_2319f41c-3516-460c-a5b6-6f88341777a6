package ai.chatbot.app.notification

import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.config.TenantConfiguration
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.string.shouldContain
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import jakarta.inject.Named

@MicronautTest(environments = [FRIDAY_ENV, "test"])
class NotificationConfigInjectionIntegrationTest(
    @Named("friday") private val tenantConfiguration: TenantConfiguration,
) : DescribeSpec() {

    init {
        describe("Ao carregar configuracoes de notificacoo") {
            it("deve injetar corretamente a propriedade name em NotificationConfig") {

                // Verifica se a configuração do tenant foi carregada
                tenantConfiguration shouldNotBe null

                // Obtém as mensagens de notificação da configuração
                val notificationMessages = tenantConfiguration.messages
                notificationMessages shouldNotBe null
            }

            it("deve carregar configurações de notificação simples com texto") {
                val notificationMessages = tenantConfiguration.messages

                // Verifica configurações que usam texto simples ao invés de template
                notificationMessages.scheduleAuthorizationSingleSweepingConsent shouldNotBe null
                notificationMessages.scheduleAuthorizationSingleSweepingConsent.text shouldNotBe null
                notificationMessages.scheduleAuthorizationSingleSweepingConsent.text!!.isNotEmpty() shouldBe true

                // Verifica se o texto contém placeholders esperados
                notificationMessages.scheduleAuthorizationSingleSweepingConsent.text!! shouldContain "{{BILLS_LIST}}"
                notificationMessages.scheduleAuthorizationSingleSweepingConsent.text!! shouldContain "{{CONTA1_NOME}}"
                notificationMessages.scheduleAuthorizationSingleSweepingConsent.text!! shouldContain "{{CONTA1_SALDO}}"
            }

            it("deve carregar configurações de notificação com links e quick replies") {
                val notificationMessages = tenantConfiguration.messages

                // Verifica configuração com link
                val configWithLink = notificationMessages.scheduleAuthorizationSingleSweepingConsent
                configWithLink.link shouldNotBe null
                configWithLink.link!!.text shouldBe "Autorizar pagamentos"
                configWithLink.link!!.url shouldContain "chatbot/autorizar-transacoes/{{TRANSACTION_GROUP_ID}}"

                // Verifica configuração com quick replies
                configWithLink.quickReplies shouldNotBe null
                configWithLink.quickReplies!!.size shouldBe 1
                configWithLink.quickReplies!!.first().action shouldBe "TRANSACTION_CANCEL"
                configWithLink.quickReplies!!.first().text shouldBe "Não"
            }

            it("deve carregar configurações de notificação de erro e sucesso") {
                val notificationMessages = tenantConfiguration.messages

                // Verifica configurações de erro
                notificationMessages.createManualEntryErrorNoTitle shouldNotBe null
                notificationMessages.createManualEntryErrorNoAmount shouldNotBe null
                notificationMessages.createManualEntryErrorNoAmountAndTitle shouldNotBe null

                // Verifica configurações de sucesso
                notificationMessages.createManualEntrySuccess shouldNotBe null
                notificationMessages.removeManualEntrySuccess shouldNotBe null

                // Verifica configurações de lembretes
                notificationMessages.createReminderErrorNoDate shouldNotBe null
                notificationMessages.createReminderErrorNoTitle shouldNotBe null
                notificationMessages.createReminderErrorNoTitleAndDate shouldNotBe null
                notificationMessages.createReminderErrorDateInPast shouldNotBe null
                notificationMessages.createReminderSuccess shouldNotBe null
                notificationMessages.createReminderSuccessNoAmount shouldNotBe null
            }
        }
    }
}