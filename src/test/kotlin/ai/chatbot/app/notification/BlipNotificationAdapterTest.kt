package ai.chatbot.app.notification

import ai.chatbot.adapters.notification.BlipNotificationAdapter
import ai.chatbot.adapters.notification.SQSMessagePublisher
import ai.chatbot.adapters.waCommCentre.ClientId
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.user.AccountId
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import io.via1.communicationcentre.app.integrations.CheckableNotificationService
import io.via1.communicationcentre.app.integrations.NotificationService as BlipService

class BlipNotificationAdapterTest : FunSpec(
    {
        val sqsMessagePublisher = mockk<SQSMessagePublisher>()
        val blipNotificationService = mockk<BlipService>()
        val checkableNotificationService = mockk<CheckableNotificationService>()
        val tenantService = mockk<TenantService>()

        val adapter = BlipNotificationAdapter(
            sqsMessagePublisher = sqsMessagePublisher,
            blipNotificationService = blipNotificationService,
            checkableNotificationService = checkableNotificationService,
            tenantService = tenantService,
            verifyNotificationQueue = "verify-queue",
            templateDelayQueue = "template-delay-queue",
            simpleDelayQueue = "simple-delay-queue",
        )

        test("should throw NotImplementedError when trying to notify with ChatbotRawTemplatedNotification") {
            val notification = ChatbotRawTemplatedNotification(
                mobilePhone = "*************",
                accountId = AccountId("test-account"),
                clientId = ClientId("test-client"),
                configurationKey = ConfigurationKey("test-config"),
            )

            shouldThrow<NotImplementedError> {
                adapter.notify(notification)
            }.message shouldBe "Raw templated notifications are not supported in this adapter. ChatbotAI não sabe resolver o template associado a config."
        }
    },
)