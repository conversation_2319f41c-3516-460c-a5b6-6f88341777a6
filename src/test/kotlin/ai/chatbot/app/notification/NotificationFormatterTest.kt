package ai.chatbot.app.notification

import ai.chatbot.adapters.billPayment.BillStatus
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.BillType
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.bill.Recipient
import ai.chatbot.app.bill.ScheduledInfo
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import java.time.LocalDate

class NotificationFormatterTest : DescribeSpec() {
    init {
        describe("ao formatar bill") {
            it("deve remover espaços no final do recipient e descrição") {
                val bills = listOf(
                    BillView(
                        billId = BillId("BILL-ID"),
                        externalBillId = 1,
                        assignor = null,
                        recipient = Recipient("BANCO BMG SA                            "),
                        billDescription = "     Descrição com espaço           ",
                        amount = 12345L,
                        discount = 0L,
                        interest = 0L,
                        fine = 0L,
                        amountTotal = 12345L,
                        billType = BillType.INVOICE,
                        scheduledInfo = ScheduledInfo.NOT_SCHEDULED,
                        paymentLimitTime = "2025-06-19T22:00:00.000Z",
                        dueDate = LocalDate.of(2025, 6, 19),
                        subscriptionFee = false,
                        status = BillStatus.ACTIVE,
                    ),
                )

                val result = NotificationFormatter.getFormattedBillInfo(bills)

                result shouldBe listOf(FormattedBillInfo("BANCO BMG SA (Descrição com espaço)", "R$ 123,45"))
            }
        }
    }
}