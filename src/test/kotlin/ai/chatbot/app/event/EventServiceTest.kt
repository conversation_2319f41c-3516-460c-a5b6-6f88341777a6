package ai.chatbot.app.event

import ai.chatbot.adapters.billPayment.BillPaymentAdapter
import ai.chatbot.adapters.notification.MessagePublisher
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.defaultFeatureFlags
import ai.chatbot.app.featureflag.FeatureFlag
import ai.chatbot.app.featureflag.FeatureFlagStatus
import ai.chatbot.app.featureflag.FeatureFlags
import ai.chatbot.app.user.AccountId
import io.kotest.core.spec.style.DescribeSpec
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class EventServiceTest : DescribeSpec() {
    init {
        describe("DefaultEventService") {
            val messagePublisher = mockk<MessagePublisher>(relaxed = true)
            val billPaymentAdapter = mockk<BillPaymentAdapter>(relaxed = true)
            val tenantService = mockk<TenantService>()
            val tenantConfiguration = mockk<TenantConfiguration>()
            val eventQueueName = "test-event-queue"
            val accountId = AccountId("test-account-id")
            val event = UserEvent.PROMOTE_SWEEPING_SENT
            val metadata = mapOf("key" to "value")

            beforeEach {
                clearAllMocks()
            }

            describe("when userEventsHttp feature flag is enabled") {
                val featureFlags = defaultFeatureFlags()

                beforeTest {
                    every { tenantService.getConfiguration() } returns tenantConfiguration
                    every { tenantConfiguration.flags } returns featureFlags
                }

                it("should send event via HTTP using billPaymentAdapter") {
                    // Arrange
                    val eventService = DefaultEventService(
                        messagePublisher = messagePublisher,
                        billPaymentAdapter = billPaymentAdapter,
                        tenantService = tenantService,
                        eventQueueName = eventQueueName,
                    )

                    // Act
                    eventService.send(accountId, event, metadata)

                    // Assert
                    verify(exactly = 1) { billPaymentAdapter.sendUserEvent(accountId, event, metadata) }
                    verify(exactly = 0) { messagePublisher.sendMessage(any(), any()) }
                }
            }

            describe("when userEventsHttp feature flag is disabled") {
                val featureFlags = FeatureFlags(
                    waCommCentre = FeatureFlag(enabled = FeatureFlagStatus.EVERYONE),
                    dailyLogS3 = FeatureFlag(enabled = FeatureFlagStatus.EVERYONE),
                    sendViaWaCommCentre = FeatureFlag(enabled = FeatureFlagStatus.EVERYONE),
                    userEventsHttp = FeatureFlag(enabled = FeatureFlagStatus.NO_ONE),
                )

                beforeTest {
                    every { tenantConfiguration.flags } returns featureFlags
                    every { tenantService.getConfiguration() } returns tenantConfiguration
                }

                it("should send event via message queue using messagePublisher") {
                    // Arrange
                    val eventService = DefaultEventService(
                        messagePublisher = messagePublisher,
                        billPaymentAdapter = billPaymentAdapter,
                        tenantService = tenantService,
                        eventQueueName = eventQueueName,
                    )

                    // Act
                    eventService.send(accountId, event, metadata)

                    // Assert
                    verify(exactly = 0) { billPaymentAdapter.sendUserEvent(any(), any(), any()) }
                    verify(exactly = 1) { messagePublisher.sendMessage(eventQueueName, any()) }
                }
            }
        }
    }
}