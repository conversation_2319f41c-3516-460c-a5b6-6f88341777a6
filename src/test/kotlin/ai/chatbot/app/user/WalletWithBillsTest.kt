package ai.chatbot.app.user

import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.SweepingConsentPeriodicLimitUsage
import ai.chatbot.app.SweepingConsentPeriodicUsage
import ai.chatbot.app.SweepingParticipant
import ai.chatbot.app.SweepingParticipantId
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.mockk.mockk

class WalletWithBillsTest : DescribeSpec() {

    fun buildWalletWithBills(
        transactionLimit: Long = 1,
        dailyAmountUsed: Long = 0,
        dailyQuantityUsed: Long = 0,
        weeklyAmountUsed: Long = 0,
        weeklyQuantityUsed: Long = 0,
        monthlyAmountUsed: Long = 0,
        monthlyQuantityUsed: Long = 0,
        yearlyAmountUsed: Long = 0,
        yearlyQuantityUsed: Long = 0,
        totalUsed: Long = 0,
    ) = WalletWithBills(
        bills = emptyList(),
        walletId = WalletId("walletId"),
        walletName = "walletName",
        activeConsents = listOf(
            SweepingConsent(
                participant = mockk(relaxed = true),
                transactionLimit = transactionLimit,
                lastSuccessfulCashIn = mockk(relaxed = true),
                periodicUsage = SweepingConsentPeriodicUsage(
                    daily = SweepingConsentPeriodicLimitUsage(
                        amountLimit = 1,
                        amountUsed = dailyAmountUsed,
                        quantityLimit = 1,
                        quantityUsed = dailyQuantityUsed,
                    ),
                    weekly = SweepingConsentPeriodicLimitUsage(
                        amountLimit = 1,
                        amountUsed = weeklyAmountUsed,
                        quantityLimit = 1,
                        quantityUsed = weeklyQuantityUsed,
                    ),
                    monthly = SweepingConsentPeriodicLimitUsage(
                        amountLimit = 1,
                        amountUsed = monthlyAmountUsed,
                        quantityLimit = 1,
                        quantityUsed = monthlyQuantityUsed,
                    ),
                    yearly = SweepingConsentPeriodicLimitUsage(
                        amountLimit = 1,
                        amountUsed = yearlyAmountUsed,
                        quantityLimit = 1,
                        quantityUsed = yearlyQuantityUsed,
                    ),
                    totalLimit = 1,
                    totalUsed = totalUsed,
                ),
            ),
        ),
    )

    init {
        describe("deve retornar o consentimento") {
            it("se o valor for inferior ao limite transacional e não tem limite periódico") {
                val walletWithBills = WalletWithBills(
                    bills = emptyList(),
                    walletId = WalletId("walletId"),
                    walletName = "walletName",
                    activeConsents = listOf(
                        SweepingConsent(
                            participant = mockk(relaxed = true),
                            transactionLimit = 1,
                            lastSuccessfulCashIn = mockk(relaxed = true),
                        ),
                    ),
                )

                val activeConsents = walletWithBills.sweepingConsentsWithTransactionLimitFor(1)
                activeConsents.size shouldBe 1
            }

            it("se o valor for inferior a todos os limites") {
                val walletWithBills = buildWalletWithBills()

                val activeConsents = walletWithBills.sweepingConsentsWithTransactionLimitFor(1)
                activeConsents.size shouldBe 1
            }
        }

        describe("não deve retornar o consentimento") {
            describe("se o valor for superior") {
                it("ao limite transacional") {
                    val walletWithBills = buildWalletWithBills(transactionLimit = 0)

                    val activeConsents = walletWithBills.sweepingConsentsWithTransactionLimitFor(1)
                    activeConsents.size shouldBe 0
                }
                it("ao limite diário") {
                    val walletWithBills = buildWalletWithBills(dailyAmountUsed = 1)

                    val activeConsents = walletWithBills.sweepingConsentsWithTransactionLimitFor(1)
                    activeConsents.size shouldBe 0
                }
                it("ao limite semanal") {
                    val walletWithBills = buildWalletWithBills(weeklyAmountUsed = 1)

                    val activeConsents = walletWithBills.sweepingConsentsWithTransactionLimitFor(1)
                    activeConsents.size shouldBe 0
                }
                it("ao limite mensal") {
                    val walletWithBills = buildWalletWithBills(monthlyAmountUsed = 1)

                    val activeConsents = walletWithBills.sweepingConsentsWithTransactionLimitFor(1)
                    activeConsents.size shouldBe 0
                }
                it("ao limite anual") {
                    val walletWithBills = buildWalletWithBills(yearlyAmountUsed = 1)

                    val activeConsents = walletWithBills.sweepingConsentsWithTransactionLimitFor(1)
                    activeConsents.size shouldBe 0
                }
                it("ao limite total") {
                    val walletWithBills = buildWalletWithBills(totalUsed = 1)

                    val activeConsents = walletWithBills.sweepingConsentsWithTransactionLimitFor(1)
                    activeConsents.size shouldBe 0
                }
            }
            describe("se a quantidade for superior") {
                it("ao limite diário") {
                    val walletWithBills = buildWalletWithBills(dailyQuantityUsed = 1)

                    val activeConsents = walletWithBills.sweepingConsentsWithTransactionLimitFor(1)
                    activeConsents.size shouldBe 0
                }
                it("ao limite semanal") {
                    val walletWithBills = buildWalletWithBills(weeklyQuantityUsed = 1)

                    val activeConsents = walletWithBills.sweepingConsentsWithTransactionLimitFor(1)
                    activeConsents.size shouldBe 0
                }
                it("ao limite mensal") {
                    val walletWithBills = buildWalletWithBills(monthlyQuantityUsed = 1)

                    val activeConsents = walletWithBills.sweepingConsentsWithTransactionLimitFor(1)
                    activeConsents.size shouldBe 0
                }
                it("ao limite anual") {
                    val walletWithBills = buildWalletWithBills(yearlyQuantityUsed = 1)

                    val activeConsents = walletWithBills.sweepingConsentsWithTransactionLimitFor(1)
                    activeConsents.size shouldBe 0
                }
            }
        }

        describe("deve retornar apenas o consentimento com limite") {
            val walletWithBills = WalletWithBills(
                bills = emptyList(),
                walletId = WalletId("walletId"),
                walletName = "walletName",
                activeConsents = listOf(
                    SweepingConsent(
                        participant = SweepingParticipant(
                            id = SweepingParticipantId(value = "pri"),
                            name = "ParticipantWithLimit",
                            shortName = "Merrill Brown",
                        ),
                        transactionLimit = 1,
                        lastSuccessfulCashIn = mockk(relaxed = true),
                    ),
                    SweepingConsent(
                        participant = SweepingParticipant(
                            id = SweepingParticipantId(value = "pri"),
                            name = "ParticipantWithoutLimit",
                            shortName = "Merrill Brown",
                        ),
                        transactionLimit = 0,
                        lastSuccessfulCashIn = mockk(relaxed = true),
                    ),
                ),
            )

            val activeConsents = walletWithBills.sweepingConsentsWithTransactionLimitFor(1)
            activeConsents.size shouldBe 1
            activeConsents.first().participant.name shouldBe "ParticipantWithLimit"
        }
    }
}