package ai.chatbot.integration.utils

import ai.chatbot.app.conversation.ChatMessageWrapper
import ai.chatbot.app.conversation.MessageType
import java.time.ZonedDateTime

fun generateConversation(vararg messages: Pair<MessageType, String>): List<ChatMessageWrapper> {
    return messages.mapIndexed { i, pair ->
        ChatMessageWrapper(
            type = pair.first,
            message = pair.second,
            completionMessage = null,
            timestamp = ZonedDateTime.now().plusSeconds((5 * i).toLong()),
        )
    }
}