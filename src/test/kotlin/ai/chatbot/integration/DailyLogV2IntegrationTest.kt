package ai.chatbot.integration

import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.classification.ConversationActionSource
import ai.chatbot.app.classification.ConversationActionStatus
import ai.chatbot.app.classification.ConversationTag
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ChatMessageWrapper
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.conversation.actions.ActionExecutorsHelpers
import ai.chatbot.app.notification.FormattedBillInfo
import ai.chatbot.app.notification.FridayNotificationContextTemplatesService
import ai.chatbot.app.notification.NotificationContextTemplatesService
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.integration.utils.generateConversation
import ai.integration.chatbot.utils.OpenAITokenCondition
import io.kotest.core.annotation.EnabledIf
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import io.mockk.mockk
import org.junit.Assert

@EnabledIf(OpenAITokenCondition::class)
@MicronautTest
class DailyLogIntegrationTest(private val openAIAdapter: OpenAIAdapter) : DescribeSpec() {
    val contextService: NotificationContextTemplatesService = FridayNotificationContextTemplatesService()

    val billInfoList = listOf("1. Conta de luz no valor R$ 123,45", "2. Conta de gás no valor de R$ 67,89")

    val billInfos = listOf(
        FormattedBillInfo("Conta de luz", "R$ 123,45"),
        FormattedBillInfo("Conta de gás", "R$ 67,89"),
    )

    val errorMessage = "Desculpe, mas estou enfrentando problemas técnicos. Nossa equipe já foi avisada. Você quer tentar novamente?"

    val pixConfirmationMessage = contextService.getUserConfirmationNotificationMessage(
        userName = "Fulano",
        amount = 10_00L,
        key = "15473748708",
        recipientName = "Ciclando Beltrano",
        recipientDocument = "123.456.789-10",
        recipientInstitution = "Banco Teste",
    )

    val actionExecutorsHelpers = ActionExecutorsHelpers(
        paymentAdapter = mockk(relaxed = true),
        conversationHistoryService = mockk(relaxed = true),
        notificationService = mockk(relaxed = true),
        onePixPayInstrumentation = mockk(relaxed = true),
        transactionService = mockk(relaxed = true),
        buildNotificationService = mockk(relaxed = true),
        customNotificationService = mockk(relaxed = true),
        notificationContextTemplatesService = mockk(relaxed = true),
    )

    init {
        describe("quando usuário tentar marcar conta como paga") {
            it("com sucesso") {
                testTags(
                    shouldContain = listOf(
                        ConversationTag.MARCAR_COMO_PAGO,
                        ConversationTag.USUARIO_ATENDIDO,
                    ),
                    shouldNotContain = listOf(
                        ConversationTag.USUARIO_NAO_ATENDIDO,
                    ),
                    messages = generateConversation(
                        MessageType.ASSISTANT to contextService.getBillsComingUserRequested(billInfoList).first(),
                        MessageType.USER to "Já paguei",
                        MessageType.SYSTEM to "Usuário clicou no botão 'Já paguei'",
                        MessageType.ASSISTANT to "Ok, marquei a conta como paga.",
                    ),
                )
            }

            it("com erro") {
                testTags(
                    shouldContain = listOf(
                        ConversationTag.MARCAR_COMO_PAGO,
                        ConversationTag.USUARIO_NAO_ATENDIDO,
                        ConversationTag.PROBLEMAS_TECNICOS,
                    ),
                    shouldNotContain = listOf(
                        ConversationTag.USUARIO_ATENDIDO,
                    ),
                    messages = generateConversation(
                        MessageType.ASSISTANT to contextService.getBillsComingUserRequested(billInfoList).first(),
                        MessageType.USER to "Já paguei",
                        MessageType.SYSTEM to "Usuário clicou no botão 'Já paguei'",
                        MessageType.ASSISTANT to errorMessage,
                    ),
                )
            }
        }

        describe("quando usuário tentar pagar as contas") {
            it("com sucesso") {
                testTags(
                    shouldContain = listOf(
                        ConversationTag.PAGAMENTO_CONTAS,
                        ConversationTag.USUARIO_ATENDIDO,
                    ),
                    shouldNotContain = listOf(
                        ConversationTag.USUARIO_NAO_ATENDIDO,
                    ),
                    messages = generateConversation(
                        MessageType.ASSISTANT to contextService.getBillsComingUserRequested(billInfoList).first(),
                        MessageType.USER to "Sim, pagar todas",
                        MessageType.SYSTEM to "Usuário confirmou o agendamento das contas.",
                        MessageType.ASSISTANT to "Já estou processando seu pedido. Informarei o resultado em breve",
                    ),
                )
            }

            it("com conta conectada") {
                testTags(
                    shouldContain = listOf(
                        ConversationTag.PAGAMENTO_CONTAS,
                        ConversationTag.USUARIO_ATENDIDO,
                        ConversationTag.CONTA_CONECTADA,
                    ),
                    shouldNotContain = listOf(
                        ConversationTag.USUARIO_NAO_ATENDIDO,
                    ),
                    messages = generateConversation(
                        MessageType.ASSISTANT to contextService.getBillsComingUserRequested(billInfoList).first(),
                        MessageType.USER to "Sim, pagar todas",
                        MessageType.SYSTEM to "Usuário confirmou o agendamento das contas.",
                        MessageType.ASSISTANT to contextService.getSweepingAccountWarnConfirmation(),
                        MessageType.USER to "Sim, usar conta conectada",
                        MessageType.ASSISTANT to "Já estou processando seu pedido. Informarei o resultado em breve",
                    ),
                )
            }

            it("com autorização") {
                testTags(
                    shouldContain = listOf(
                        ConversationTag.PAGAMENTO_CONTAS,
                        ConversationTag.USUARIO_ATENDIDO,
                    ),
                    shouldNotContain = listOf(
                        ConversationTag.USUARIO_NAO_ATENDIDO,
                    ),
                    messages = generateConversation(
                        MessageType.ASSISTANT to contextService.getBillsComingUserRequested(billInfoList).first(),
                        MessageType.USER to "Sim, pagar todas",
                        MessageType.SYSTEM to "Usuário confirmou o agendamento das contas.",
                        MessageType.ASSISTANT to contextService.getAuthorizeScheduleBillsMessage(billInfos),
                        MessageType.ASSISTANT to "Já estou processando seu pedido. Informarei o resultado em breve",
                    ),
                )
            }

            it("com erro") {
                testTags(
                    shouldContain = listOf(
                        ConversationTag.PAGAMENTO_CONTAS,
                        ConversationTag.USUARIO_NAO_ATENDIDO,
                        ConversationTag.PROBLEMAS_TECNICOS,
                    ),
                    shouldNotContain = listOf(),
                    messages = generateConversation(
                        MessageType.ASSISTANT to contextService.getBillsComingUserRequested(billInfoList).first(),
                        MessageType.USER to "Sim, pagar todas",
                        MessageType.SYSTEM to "Usuário confirmou o agendamento das contas.",
                        MessageType.ASSISTANT to errorMessage,
                    ),
                )
            }
        }

        describe("quando usuário tentar fazer um pix") {
            it("pix com chave com sucesso") {
                testTags(
                    shouldContain = listOf(
                        ConversationTag.PIX,
                        ConversationTag.USUARIO_ATENDIDO,
                    ),
                    shouldNotContain = listOf(
                        ConversationTag.ONE_PIX_PAY,
                    ),
                    messages = generateConversation(
                        MessageType.USER to "Pix de 10 reais para 123.456.789-10",
                        MessageType.ASSISTANT to pixConfirmationMessage,
                        MessageType.USER to "Sim",
                        MessageType.SYSTEM to "Usuário confirmou a transação",
                        MessageType.ASSISTANT to "Já estou processando seu pedido. Informarei o resultado em breve.",
                    ),
                )
            }

            it("pix para contato com sucesso") {
                testTags(
                    shouldContain = listOf(
                        ConversationTag.PIX,
                        ConversationTag.USUARIO_ATENDIDO,
                    ),
                    shouldNotContain = listOf(
                        ConversationTag.ONE_PIX_PAY,
                    ),
                    messages = generateConversation(
                        MessageType.USER to "20 reais para Ciclano",
                        MessageType.ASSISTANT to pixConfirmationMessage,
                        MessageType.USER to "Sim",
                        MessageType.SYSTEM to "Usuário confirmou a transação",
                        MessageType.ASSISTANT to "Já estou processando seu pedido. Informarei o resultado em breve.",
                    ),
                )
            }

            it("usuário desiste da transação") {
                testTags(
                    shouldContain = listOf(ConversationTag.PIX),
                    shouldNotContain = listOf(
                        ConversationTag.USUARIO_NAO_ATENDIDO,
                        ConversationTag.ONE_PIX_PAY,
                    ),
                    messages = generateConversation(
                        MessageType.USER to "Pix 20 reais para Ciclano",
                        MessageType.ASSISTANT to pixConfirmationMessage,
                        MessageType.USER to "Não",
                        MessageType.SYSTEM to "Usuário quer cancelar a transação",
                        MessageType.SYSTEM to "Transação cancelada",
                        MessageType.ASSISTANT to "Entendi. Se precisar de mais alguma coisa, estou à disposição.",
                    ),
                )
            }

            it("erro na conta conectada") {
                testTags(
                    shouldContain = listOf(
                        ConversationTag.PIX,
                        ConversationTag.CONTA_CONECTADA,
                        ConversationTag.PROBLEMAS_TECNICOS,
                    ),
                    shouldNotContain = listOf(),
                    messages = generateConversation(
                        MessageType.USER to "Manda 12.000 para juliana",
                        MessageType.ASSISTANT to "null",
                        MessageType.ASSISTANT to "O valor do Pix excede o seu limite transacional por WhatsApp, mas você pode autorizar o pagamento se os dados estiverem corretos:\n👤Nome: JULIANA CHUERY PERLINGIERE\n🪪Documento: ∗∗∗.220.938-∗∗\n🏦Instituição: ITAÚ UNIBANCO S.A.\n💵Valor: R$ 12000,00\n🔑Chave: 36922093801",
                        MessageType.ASSISTANT to "Já estou processando seu pedido. Informarei o resultado em breve.",
                        MessageType.ASSISTANT to "Erro ao tentar transferir saldo da sua conta conectada. Verifique no seu banco.",
                    ),
                )
            }
        }

        describe("quando usuário fizer mais de uma ação na mesma conversa") {
            it("deve classificar todas") {
                testTags(
                    shouldContain = listOf(
                        ConversationTag.PAGAMENTO_CONTAS,
                        ConversationTag.USUARIO_ATENDIDO,
                        ConversationTag.PIX,
                        ConversationTag.CONTA_CONECTADA,
                    ),
                    shouldNotContain = listOf(ConversationTag.ONE_PIX_PAY),
                    messages = generateConversation(
                        MessageType.ASSISTANT to contextService.getBillsComingUserRequested(billInfoList).first(),
                        MessageType.USER to "Sim, pagar todas",
                        MessageType.SYSTEM to "Usuário confirmou o agendamento das contas.",
                        MessageType.ASSISTANT to contextService.getSweepingAccountWarnConfirmation(),
                        MessageType.USER to "Sim, usar conta conectada",
                        MessageType.ASSISTANT to "Já estou processando seu pedido. Informarei o resultado em breve",
                        MessageType.USER to "20 reais para Ciclano",
                        MessageType.ASSISTANT to pixConfirmationMessage,
                        MessageType.USER to "Sim",
                        MessageType.SYSTEM to "Usuário confirmou a transação",
                        MessageType.ASSISTANT to "Já estou processando seu pedido. Informarei o resultado em breve.",
                    ),
                )
            }
        }

        describe("conta conectada") {
            it("não deve classificar como conta conectada quando for apenas o incentivo") {
                testTags(
                    shouldContain = listOf(
                        ConversationTag.PAGAMENTO_CONTAS,
                        ConversationTag.USUARIO_ATENDIDO,
                        ConversationTag.ONE_PIX_PAY,
                    ),
                    shouldNotContain = listOf(ConversationTag.CONTA_CONECTADA),
                    messages = generateConversation(
                        MessageType.ASSISTANT to contextService.getBillsComingUserRequested(billInfoList).first(),
                        MessageType.USER to "Sim, pagar todas",
                        MessageType.SYSTEM to "Usuário clicou no botão 'Sim, pagar todas'",
                        MessageType.ASSISTANT to "Percebi que você não tem saldo na Friday. Posso tentar retirar da sua conta conectada. Se essa conta também estiver sem saldo, a operação pode usar seu limite de crédito.",
                        MessageType.USER to "Prefiro pagar com 1 Pix",
                        MessageType.SYSTEM to "Usuário clicou no botão 'Pagar tudo com 1 pix'",
                        MessageType.ASSISTANT to "código pix enviado ao usuário.",
                    ),
                )
            }

            it("não deve classificar como conta quando for incentivo após o 1PP") {
                testTags(
                    shouldContain = listOf(
                        ConversationTag.PAGAMENTO_CONTAS,
                        ConversationTag.USUARIO_ATENDIDO,
                        ConversationTag.ONE_PIX_PAY,
                    ),
                    shouldNotContain = listOf(ConversationTag.CONTA_CONECTADA),
                    messages = generateConversation(
                        MessageType.ASSISTANT to contextService.getBillsComingUserRequested(billInfoList).first(),
                        MessageType.USER to "Sim, pagar todas",
                        MessageType.SYSTEM to "Usuário clicou no botão 'Sim, pagar todas'",
                        MessageType.ASSISTANT to actionExecutorsHelpers.generateOnePixPayMessage(billInfoList, 191_34, 0L),
                        MessageType.ASSISTANT to "código pix enviado ao usuário.",
                        MessageType.ASSISTANT to "Ainda acessando seu banco para colocar saldo na Friday? Conecte a Friday com seu banco para trazer saldo em segundos.",
                    ),
                )
            }
        }

        describe("usuário não atendido") {
            describe("deve classificar") {
                it("erro no processamento") {
                    testTags(
                        shouldContain = listOf(ConversationTag.USUARIO_NAO_ATENDIDO),
                        messages = generateConversation(
                            MessageType.USER to "Pix 20 reais para Ciclano",
                            MessageType.ASSISTANT to pixConfirmationMessage,
                            MessageType.USER to "Sim",
                            MessageType.ASSISTANT to errorMessage,
                        ),
                    )
                }

                it("chatbot ignora o usuário") {
                    testTags(
                        shouldContain = listOf(ConversationTag.USUARIO_NAO_ATENDIDO),
                        messages = generateConversation(
                            MessageType.USER to "Pix 20 reais para Ciclano",
                        ),
                    )
                }
            }

            describe("não deve classificar") {
                it("usuário cancela transação") {
                    testTags(
                        shouldNotContain = listOf(ConversationTag.USUARIO_NAO_ATENDIDO),
                        messages = generateConversation(
                            MessageType.USER to "Pix 20 reais para Ciclano",
                            MessageType.ASSISTANT to pixConfirmationMessage,
                            MessageType.USER to "Não",
                            MessageType.SYSTEM to "Usuário quer cancelar a transação",
                            MessageType.SYSTEM to "Transação cancelada",
                            MessageType.ASSISTANT to "Entendi. Se precisar de mais alguma coisa, estou à disposição.",
                        ),
                    )
                }

                it("usuário recusa incentivo do open-finance") {
                    testTags(
                        shouldNotContain = listOf(ConversationTag.USUARIO_NAO_ATENDIDO),
                        messages = generateConversation(
                            MessageType.ASSISTANT to "Ainda acessando seu banco para colocar saldo na Friday? Conecte a Friday com seu banco para trazer saldo em segundos.",
                            MessageType.USER to "Não tenho interesse",
                            MessageType.ASSISTANT to "Ok, você pode conectar seu banco a qualquer momento em carteira > open finance",
                        ),
                    )
                }

                it("usuário recusa incentivo do open-finance após contas vencendo") {
                    testTags(
                        shouldNotContain = listOf(ConversationTag.USUARIO_NAO_ATENDIDO),
                        messages = generateConversation(
                            MessageType.ASSISTANT to "Oi Andre, vi que você tem as seguintes contas vencendo hoje:\n\n1. COND EDIFICIO MARQUEZA DE ITU no valor de R$ 2.966,74\n\nVocê quer fazer estes pagamentos?",
                            MessageType.ASSISTANT to "Contas pagas manualmente?\n\nConecte a Friday com seu banco para fazer seus próximos pagamentos digitando “sim” no WhatsApp!",
                            MessageType.USER to "Não tenho interesse",
                            MessageType.ASSISTANT to "Ok, você pode conectar seu banco a qualquer momento em carteira > open finance",
                        ),
                    )
                }

                it("usuário não pede para pagar as contas") {
                    testTags(
                        shouldNotContain = listOf(ConversationTag.USUARIO_NAO_ATENDIDO),
                        messages = generateConversation(
                            MessageType.ASSISTANT to contextService.getBillsComingDue("Fulano", billInfos),
                            MessageType.ASSISTANT to contextService.getBillsComingDue("Fulano", billInfos),
                            MessageType.ASSISTANT to contextService.getBillComingDueLastWarn("Fulano", "23:59"),
                            MessageType.USER to "Ver contas",
                            MessageType.ASSISTANT to contextService.getBillsComingUserRequested(billInfoList).first(),
                        ),
                    )
                }

                it("usuário ignora sugestão de conectar conta") {
                    testTags(
                        shouldNotContain = listOf(ConversationTag.USUARIO_NAO_ATENDIDO),
                        messages = generateConversation(
                            MessageType.ASSISTANT to "Oi Regina, vi que você tem as seguintes contas vencendo hoje:\n\n1. ITURAN SERVICOS LTDA no valor de R$ 261,66\n\nVocê quer fazer estes pagamentos?",
                            MessageType.ASSISTANT to "Oi Regina, vi que você tem as seguintes contas vencendo hoje:\n\n1. ITURAN SERVICOS LTDA no valor de R$ 261,66\n\nVocê quer fazer estes pagamentos?",
                            MessageType.USER to "Já paguei",
                            MessageType.SYSTEM to "Usuário clicou no botão 'Já paguei'",
                            MessageType.ASSISTANT to "Ok, marquei a conta como paga.",
                            MessageType.ASSISTANT to "Contas pagas manualmente?\n\nConecte a Friday com seu banco para fazer seus próximos pagamentos digitando “sim” no WhatsApp!",
                        ),
                    )
                }
            }
        }

        describe("problemas técnicos") {
            describe("não deve considerar") {
                it("quando tem mensagem de autorização de pix") {
                    testTags(
                        shouldContain = listOf(
                            ConversationTag.PIX,
                            ConversationTag.USUARIO_ATENDIDO,
                        ),
                        shouldNotContain = listOf(
                            ConversationTag.PROBLEMAS_TECNICOS,
                        ),
                        messages = generateConversation(
                            MessageType.USER to "21966000143",
                            MessageType.USER to "5,00",
                            MessageType.ASSISTANT to "Desculpe, mas esse valor excede seu limite diário de Pix.\n\nVocê pode configurar seu limite em Carteira > Limites Transacionais.",
                            MessageType.ASSISTANT to "Você confirma os dados abaixo?\n👤Nome: Wilmar Baptista\n🪪Documento: ∗∗∗.330.057-∗∗\n🏦Instituição: PAGSEGURO INTERNET IP S.A.\n💵Valor: R$ 5,00\n🔑Chave: +5521966000143",
                            MessageType.USER to "Editar o valor para 7,00",
                            MessageType.ASSISTANT to "Você confirma os dados abaixo?\n👤Nome: Wilmar Baptista\n🪪Documento: ∗∗∗.330.057-∗∗\n🏦Instituição: PAGSEGURO INTERNET IP S.A.\n💵Valor: R$ 7,00\n🔑Chave: +5521966000143",
                            MessageType.USER to "Sim",
                            MessageType.SYSTEM to "Usuário confirmou a transação",
                            MessageType.ASSISTANT to "Já estou processando seu pedido. Informarei o resultado em breve.",
                            MessageType.SYSTEM to "Transação confirmada com sucesso",
                        ),
                    )
                }
            }
        }

        describe("ação não suportada") {
            it("usuário pede uma funcionalidade que só está disponível no app") {
                val result = openAIAdapter.createLogCompletion(
                    messages = generateConversation(
                        MessageType.USER to "Gerar relatório de gastos por categoria",
                        MessageType.ASSISTANT to "Para gerar um relatório de gastos por categoria, você pode acessar a aba " +
                            "'Relatórios' no app Friday. Lá, você encontrará um relatório de gastos por " +
                            "categoria no formato de pizza para o mês específico que desejar. Se precisar " +
                            "de mais alguma coisa, é só chamar",
                    ),
                    retry = false,
                    state = generateState(),
                )

                result.classificationResult.acoes.filter {
                    it.status == ConversationActionStatus.NOT_SUPPORTED &&
                        it.fonte == ConversationActionSource.USER
                }.size shouldBe 1
            }

            it("usuário pede uma funcionalidade que só está disponível no app após realizar outra ação") {
                val result = openAIAdapter.createLogCompletion(
                    messages = generateConversation(
                        MessageType.ASSISTANT to contextService.getBillsComingDue("Fulano", billInfos.take(1)),
                        MessageType.USER to "Sim, pagar este",
                        MessageType.ASSISTANT to "Ok, estou processando e já volto com o resultado.",
                        MessageType.ASSISTANT to "Este é o comprovante de pagamento no valor de R$ 123,45 para LIGHT.",
                        MessageType.USER to "Categorizar essa conta como Moradia",
                        MessageType.ASSISTANT to "Para categorizar a conta de luz como 'Moradia', por favor, acesse o app Friday e siga as instruções na aba de categorização de pagamentos. Se precisar de mais ajuda, estou por aqui!",
                    ),
                    retry = false,
                    state = generateState(),
                )

                result.classificationResult.acoes.filter {
                    it.status == ConversationActionStatus.NOT_SUPPORTED &&
                        it.fonte == ConversationActionSource.USER
                }.size shouldBe 1
            }
        }

        describe("usuário não cadastrado") {
            it("deve adicionar a tag no pós processamento") {
                testTags(
                    messages = generateConversation(
                        MessageType.USER to "Como faço para criar uma conta",
                        MessageType.ASSISTANT to "Olá, eu sou o Fred, o assistente pessoal da Friday. Para instruções de como criar sua conta na Friday, acesse https://friday.ai.",
                    ),
                    state = generateState().let { it.copy(user = it.user.copy(status = AccountStatus.NOT_REGISTERED)) },
                    shouldContain = listOf(ConversationTag.NON_USER),
                )
            }
        }
    }

    private fun testTags(
        shouldContain: List<ConversationTag> = listOf(),
        shouldNotContain: List<ConversationTag> = listOf(),
        messages: List<ChatMessageWrapper>,
        state: BillComingDueHistoryState = generateState(),
    ) {
        val result = openAIAdapter.createLogCompletion(messages, retry = false, state = state)
        val tags = result.classificationResult.tags

        val entendimento = result.classificationResult.entendimento.entries.joinToString("\n") { (key, value) -> "$key: $value" }
        val acoes = result.classificationResult.acoes.joinToString { "${it.fonte} ${it.status} ${it.acao}: ${it.entendimento}" }

        val tagNotFound = shouldContain.firstOrNull { it !in tags }
        if (tagNotFound != null) {
            Assert.fail("Should contain $tagNotFound: $tags\n\nEntendimento:\n$entendimento\n\nAções:\n$acoes\n")
        }

        val tagFound = shouldNotContain.firstOrNull { it in tags }
        if (tagFound != null) {
            Assert.fail("Should not contain $tagFound: $tags\n\nEntendimento:\n$entendimento\n\nAções:\n$acoes\n")
        }
    }
}

private fun generateState(): BillComingDueHistoryState {
    return BillComingDueHistoryState(
        walletWithBills = WalletWithBills(
            bills = emptyList(),
            walletId = WalletId("WALLET-ID"),
            walletName = "Wallet Name",
            activeConsents = emptyList(),
            totalWaitingApproval = 0,
        ),
        user = User(
            accountId = AccountId("ACCOUNT-ID"),
            id = UserId("*************"),
            name = "User Name",
            accountGroups = emptyList(),
            status = AccountStatus.ACTIVE,
        ),
        balance = null,
        internalStateControl = InternalStateControl(false, null),
        contacts = emptyList(),
        subscription = null,
    )
}