package ai.chatbot.integration

import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.classification.ConversationAction
import ai.chatbot.app.job.DailyLogErrorSummary
import ai.chatbot.app.utils.getObjectMapper
import ai.integration.chatbot.utils.OpenAITokenCondition
import com.fasterxml.jackson.module.kotlin.readValue
import io.kotest.core.annotation.EnabledIf
import io.kotest.core.spec.style.DescribeSpec
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest

@EnabledIf(OpenAITokenCondition::class)
@MicronautTest
class DailyLogSummaryIntegrationTest(private val openAIAdapter: OpenAIAdapter) : DescribeSpec() {

    init {
        it("deve printar o resumo das ações") {
//            val summaries = listOf(
//                DailyLogErrorSummary(
//                    listOf(
//                        ConversationAction(
//                            acao = "Fazer pix com conta conectada",
//                            entendimento = "O usuário fez um pix e optou por usar a conta conectada, mas houve erro ao transferir o saldo",
//                            fonte = ConversationActionSource.USER,
//                            status = ConversationActionStatus.FAILED,
//                        ),
//                        ConversationAction(
//                            acao = "Marcar contas como pagas",
//                            entendimento = "O usuário pediu pra marcar contas como pagas, mas o assistente disse que ocorreu um erro",
//                            fonte = ConversationActionSource.USER,
//                            status = ConversationActionStatus.FAILED,
//                        ),
//                        ConversationAction(
//                            acao = "Pagar contas",
//                            entendimento = "Usuário pediu para pagar contas com a conta conectada, mas houve erro ao transferir o saldo",
//                            fonte = ConversationActionSource.USER,
//                            status = ConversationActionStatus.FAILED,
//                        ),
//                        ConversationAction(
//                            acao = "Gerar relatório",
//                            entendimento = "Usuário pediu um relatório de gastos e o assistente informou como fazer pelo app",
//                            fonte = ConversationActionSource.USER,
//                            status = ConversationActionStatus.NOT_SUPPORTED,
//                        ),
//                        ConversationAction(
//                            acao = "Categorizar pix",
//                            entendimento = "O usuário pediu para adicionar uma categoria no pix, mas o assistente disse que só é possível fazer pelo app",
//                            fonte = ConversationActionSource.USER,
//                            status = ConversationActionStatus.NOT_SUPPORTED,
//                        ),
//                    ),
//                ),
//
//                DailyLogErrorSummary(
//                    listOf(
//                        ConversationAction(
//                            acao = "Fazer pix",
//                            entendimento = "O usuário fez um pix, mas houve erro ao transferir o saldo da conta conectada",
//                            fonte = ConversationActionSource.USER,
//                            status = ConversationActionStatus.FAILED,
//                        ),
//                        ConversationAction(
//                            acao = "Gerar relatório",
//                            entendimento = "Usuário pediu um relatório de gastos e o assistente informou como fazer pelo app",
//                            fonte = ConversationActionSource.USER,
//                            status = ConversationActionStatus.NOT_SUPPORTED,
//                        ),
//                        ConversationAction(
//                            acao = "Gerar resumo de gastos",
//                            entendimento = "Usuário pediu um resumo dos gastos do mês, mas o assistente informou que só está disponível no app",
//                            fonte = ConversationActionSource.USER,
//                            status = ConversationActionStatus.NOT_SUPPORTED,
//                        ),
//                    ),
//                ),
//            )

            val listActions: List<String> = getObjectMapper().readValue(actionsString)
            val actions = listActions.map {
                val action: ConversationAction = getObjectMapper().readValue(it)
                action
            }

            val summaries = listOf(DailyLogErrorSummary(actions, totalActions = 212, totalConversations = 121))

            val result = openAIAdapter.createSummaryCompletion(summaries, true)

            println("Feature requests:")
            println()
            result.summaryResult.naoImplementadas.forEach { println("- $it") }
            println()
            println(result.summaryResult.entendimentoNaoImplementadas)
            println()
            println("----------------")
            println()
            println("Falhas:")
            println()
            result.summaryResult.naoAtendidas.forEach { println("- $it") }
            println()
            println(result.summaryResult.entendimentoNaoAtendidas)
        }
    }
}

private val actionsString = """
    [
  "{\"acao\":\"Anotar dívida\",\"fonte\":\"USER\",\"status\":\"NOT_SUPPORTED\",\"entendimento\":\"O usuário pediu para anotar uma dívida, mas o assistente informou que essa funcionalidade não está disponível pelo WhatsApp.\"}",
  "{\"acao\":\"Pagar contas\",\"fonte\":\"USER\",\"status\":\"FAILED\",\"entendimento\":\"Usuário pediu para pagar todas as contas, mas houve um erro técnico.\"}",
  "{\"acao\":\"Fazer pix\",\"fonte\":\"USER\",\"status\":\"FAILED\",\"entendimento\":\"Usuário tentou fazer um pix, mas a chave pix foi considerada inválida pelo assistente.\"}",
  "{\"acao\":\"Pagar contas\",\"fonte\":\"USER\",\"status\":\"FAILED\",\"entendimento\":\"Usuário tentou pagar as contas usando a conta conectada, mas houve falhas técnicas.\"}",
  "{\"acao\":\"Fazer pix\",\"fonte\":\"USER\",\"status\":\"FAILED\",\"entendimento\":\"Usuário pediu para fazer um pix, mas houve um erro na primeira tentativa.\"}",
  "{\"acao\":\"Tentar trazer saldo do C6 Bank novamente\",\"fonte\":\"USER\",\"status\":\"FAILED\",\"entendimento\":\"O usuário pediu para tentar trazer o saldo do C6 Bank novamente, mas houve uma falha.\"}",
  "{\"acao\":\"Pagar assinatura\",\"fonte\":\"USER\",\"status\":\"FAILED\",\"entendimento\":\"Usuário pediu para pagar a assinatura, o assistente enviou o código pix, mas houve um problema com a confirmação do pagamento.\"}"
 ]
""".trimIndent()