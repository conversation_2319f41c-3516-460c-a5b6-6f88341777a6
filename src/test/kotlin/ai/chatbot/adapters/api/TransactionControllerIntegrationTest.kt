package ai.chatbot.adapters.api

import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.HistoryStateRepository
import ai.chatbot.app.NotificationService
import ai.chatbot.app.TransactionService
import ai.chatbot.app.billComingDueHistoryState
import ai.chatbot.app.config.TENANT_KEY
import ai.chatbot.app.notification.CTALink
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.transaction.PixTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.transaction.TransactionResult
import ai.chatbot.app.transaction.TransactionStatus
import ai.chatbot.app.transaction.TransactionType
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV, "test"])
class TransactionControllerIntegrationTest(
    embeddedServer: EmbeddedServer,
) : DescribeSpec() {
    private val client = RxHttpClient.create(embeddedServer.url)

    @MockBean(TransactionService::class)
    fun transactionService(): TransactionService = transactionService
    private val transactionService: TransactionService = mockk(relaxed = true)

    @MockBean(HistoryStateRepository::class)
    fun historyStateRepository(): HistoryStateRepository = historyStateRepository
    private val historyStateRepository: HistoryStateRepository = mockk()

    @MockBean(HistoryRepository::class)
    fun historyRepository(): HistoryRepository = historyRepository
    private val historyRepository: HistoryRepository = mockk(relaxed = true)

    @MockBean(NotificationService::class)
    fun notificationService(): NotificationService = notificationService
    private val notificationService: NotificationService = mockk(relaxed = true)

    private val controllerPath = "/transaction"

    private val clientId = "test-client-id"
    private val secret = "test-passwd"
    private val userId = UserId(value = "5521999999999")

    private val walletId = WalletId(value = "walletId")
    private val pixKey = PixKey(value = "<EMAIL>", type = PixKeyType.EMAIL)

    private val pixTransactionDetails = PixTransactionDetails(
        amount = 0,
        pixKey = pixKey,
        recipientName = "Recipient",
        recipientDocument = "***.456.789-**",
        recipientInstitution = "Banco teste",
        sweepingAmount = null,
        sweepingParticipantId = null,
        qrCode = null,
    )

    private val transaction = Transaction(
        id = TransactionId("123"),
        groupId = TransactionGroupId(),
        userId = userId,
        walletId = walletId,
        status = TransactionStatus.ACTIVE,
        details = pixTransactionDetails,
        paymentStatus = TransactionPaymentStatus.UNKNOWN,
    )

    init {
        beforeEach {
            clearAllMocks()
        }

        describe("ao tentar acessar o controller") {
            it("deve retornar UNAUTHORIZED quando não estiver autenticado") {
                val request = HttpRequest.GET<Unit>("$controllerPath/123")
                    .header(TENANT_KEY, "FRIDAY")

                val thrown =
                    assertThrows<HttpClientResponseException> {
                        client.toBlocking()
                            .exchange(request, Argument.of(TransactionTO::class.java))
                    }

                thrown.status shouldBe HttpStatus.UNAUTHORIZED
            }

            it("deve retornar UNAUTHORIZED se a senha tiver errada") {
                val request =
                    HttpRequest.GET<Unit>("$controllerPath/123")
                        .basicAuth(clientId, "INVALID_SECRET")
                        .header(TENANT_KEY, "FRIDAY")

                val thrown =
                    assertThrows<HttpClientResponseException> {
                        client.toBlocking()
                            .exchange(request, Argument.of(TransactionTO::class.java))
                    }

                thrown.status shouldBe HttpStatus.UNAUTHORIZED
            }
        }

        describe("ao consultar uma transação") {
            it("deve retornar a transação") {
                every {
                    transactionService.find(any())
                } returns transaction

                val request =
                    HttpRequest.GET<Unit>("$controllerPath/123")
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val response =
                    client.toBlocking()
                        .exchange(request, Argument.of(TransactionTO::class.java))

                response.status shouldBe HttpStatus.OK
                response.body().status shouldBe TransactionStatus.ACTIVE
                response.body().details.type shouldBe TransactionType.PIX
                response.body().id.value shouldBe "123"
            }

            it("não deve retornar a transação se ela não existir") {
                every {
                    transactionService.find(any())
                } returns null

                val request =
                    HttpRequest.GET<Unit>("$controllerPath/123")
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val thrown =
                    assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

                thrown.status shouldBe HttpStatus.NOT_FOUND
            }
        }

        describe("ao confirmar uma transação") {
            val confirmBody = ConfirmTO("authorization-token", true, 1)

            it("deve confirmar se estiver ativa e for do mesmo usuário") {
                every {
                    transactionService.confirm(any(), any(), any())
                } returns TransactionResult.Success(transaction)

                every { historyStateRepository.findLatest(any()) } returns billComingDueHistoryState()

                val request =
                    HttpRequest.POST("$controllerPath/123/5521999999999", Unit)
                        .body(confirmBody)
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val response = client.toBlocking().exchange(request, String::class.java)

                response.status shouldBe HttpStatus.OK
            }

            it("não deve confirmar se ela não existir") {
                every {
                    transactionService.confirm(any(), any(), any())
                } returns TransactionResult.TransactionNotFound

                val request =
                    HttpRequest.POST("$controllerPath/123/5521999999999", Unit)
                        .body(confirmBody)
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val thrown =
                    assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

                thrown.status shouldBe HttpStatus.NOT_FOUND
            }

            it("não deve confirmar se ela não estiver ativa") {
                every {
                    transactionService.confirm(any(), any(), any())
                } returns TransactionResult.IllegalState(TransactionStatus.COMPLETED)

                val request =
                    HttpRequest.POST("$controllerPath/123/5521999999999", Unit)
                        .body(confirmBody)
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val thrown =
                    assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

                thrown.status shouldBe HttpStatus.CONFLICT
            }

            it("não deve confirmar se ela não for do mesmo usuário") {
                every {
                    transactionService.confirm(any(), any(), any())
                } returns TransactionResult.IllegalUser(UserId("5521999999999"))

                val request =
                    HttpRequest.POST("$controllerPath/123/552188888888", Unit)
                        .body(confirmBody)
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val thrown =
                    assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

                thrown.status shouldBe HttpStatus.NOT_FOUND
            }

            it("deve notificar usuário da configuração de limite se passar nos critérios") {
                every {
                    transactionService.confirm(any(), any(), any())
                } returns TransactionResult.Success(transaction.copy(details = pixTransactionDetails.copy(amount = 999_99L)))

                every { historyStateRepository.findLatest(any()) } returns billComingDueHistoryState()

                val request =
                    HttpRequest.POST("$controllerPath/123/5521999999999", Unit)
                        .body(ConfirmTO("authorization-token", false, 1))
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val response = client.toBlocking().exchange(request, String::class.java)

                response.status shouldBe HttpStatus.OK

                verify {
                    notificationService.notify(
                        UserId("5521999999999"),
                        any(),
                        any(),
                        null,
                        CTALink(displayText = "Ajustar valor", url = "app/carteira/limites"),
                        any(),
                    )
                }
            }

            it("não deve notificar usuário da configuração de limite se já tiver configurado") {
                every {
                    transactionService.confirm(any(), any(), any())
                } returns TransactionResult.Success(transaction.copy(details = pixTransactionDetails.copy(amount = 999_99L)))

                every { historyStateRepository.findLatest(any()) } returns billComingDueHistoryState()

                val request =
                    HttpRequest.POST("$controllerPath/123/5521999999999", Unit)
                        .body(ConfirmTO("authorization-token", true, 1))
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val response = client.toBlocking().exchange(request, String::class.java)

                response.status shouldBe HttpStatus.OK

                verify(exactly = 0) {
                    notificationService.notify(
                        UserId("5521999999999"),
                        any(),
                        any(),
                        null,
                        CTALink(displayText = "Ajustar valor", url = "app/carteira/limites"),
                        any(),
                    )
                }
            }

            it("não deve notificar usuário da configuração de limite se houver autorizado mais de 3 vezes") {
                every {
                    transactionService.confirm(any(), any(), any())
                } returns TransactionResult.Success(transaction.copy(details = pixTransactionDetails.copy(amount = 999_99L)))

                every { historyStateRepository.findLatest(any()) } returns billComingDueHistoryState()

                val request =
                    HttpRequest.POST("$controllerPath/123/5521999999999", Unit)
                        .body(ConfirmTO("authorization-token", false, 4))
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val response = client.toBlocking().exchange(request, String::class.java)

                response.status shouldBe HttpStatus.OK

                verify(exactly = 0) {
                    notificationService.notify(
                        UserId("5521999999999"),
                        any(),
                        any(),
                        null,
                        CTALink(displayText = "Ajustar valor", url = "app/carteira/limites"),
                        any(),
                    )
                }
            }

            it("não deve notificar usuário da configuração de limite se o valor da transação for maior que o limite máximo") {
                every {
                    transactionService.confirm(any(), any(), any())
                } returns TransactionResult.Success(transaction.copy(details = pixTransactionDetails.copy(amount = 1001_00)))

                every { historyStateRepository.findLatest(any()) } returns billComingDueHistoryState()

                val request =
                    HttpRequest.POST("$controllerPath/123/5521999999999", Unit)
                        .body(ConfirmTO("authorization-token", false, 1))
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val response = client.toBlocking().exchange(request, String::class.java)

                response.status shouldBe HttpStatus.OK

                verify(exactly = 0) {
                    notificationService.notify(
                        UserId("5521999999999"),
                        any(),
                        any(),
                        null,
                        CTALink(displayText = "Ajustar valor", url = "app/carteira/limites"),
                        any(),
                    )
                }
            }
        }

        describe("ao cancelar uma transação") {
            it("deve cancelar se estiver ativa e for do mesmo usuário") {
                every {
                    transactionService.cancel(any(), any())
                } returns TransactionResult.Canceled

                val request =
                    HttpRequest.DELETE("$controllerPath/123/5521999999999", Unit)
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val response = client.toBlocking().exchange(request, String::class.java)

                response.status shouldBe HttpStatus.OK
            }

            it("não deve cancelar se ela não existir") {
                every {
                    transactionService.cancel(any(), any())
                } returns TransactionResult.TransactionNotFound

                val request =
                    HttpRequest.DELETE("$controllerPath/123/5521999999999", Unit)
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val thrown =
                    assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

                thrown.status shouldBe HttpStatus.NOT_FOUND
            }

            it("não deve cancelar se ela não estiver ativa") {
                every {
                    transactionService.cancel(any(), any())
                } returns TransactionResult.IllegalState(TransactionStatus.COMPLETED)

                val request =
                    HttpRequest.DELETE("$controllerPath/123/5521999999999", Unit)
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val thrown =
                    assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

                thrown.status shouldBe HttpStatus.CONFLICT
            }

            it("não deve cancelar se ela não for do mesmo usuário") {
                every {
                    transactionService.cancel(any(), any())
                } returns TransactionResult.IllegalUser(UserId("5521999999999"))

                val request =
                    HttpRequest.DELETE("$controllerPath/123/552188888888", Unit)
                        .basicAuth(clientId, secret)
                        .header(TENANT_KEY, "FRIDAY")

                val thrown =
                    assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

                thrown.status shouldBe HttpStatus.NOT_FOUND
            }
        }

        describe("ao atualizar o estado do pagamento de uma transacao") {
            val requestBody = UpdateTransactionPaymentStatusTO(
                status = TransactionPaymentStatus.SUCCESS,
            )

            val request =
                HttpRequest.POST("$controllerPath/123/5521999999999/updatePaymentStatus", Unit)
                    .body(requestBody)
                    .basicAuth(clientId, secret)
                    .header(TENANT_KEY, "FRIDAY")

            it("deve atualizar se estiver COMPLETED e for do mesmo usuário") {
                every {
                    transactionService.updatePaymentStatus(any(), any(), any())
                } returns TransactionResult.Success(transaction)

                val response = client.toBlocking().exchange(request, String::class.java)

                response.status shouldBe HttpStatus.OK

                val transactionIdSlot = slot<TransactionId>()
                val userIdSlot = slot<UserId>()
                val paymentStatusSlot = slot<TransactionPaymentStatus>()
                verify {
                    transactionService.updatePaymentStatus(capture(transactionIdSlot), capture(userIdSlot), capture(paymentStatusSlot))
                }
                transactionIdSlot.captured.value shouldBe "123"
                userIdSlot.captured.value shouldBe "5521999999999"
                paymentStatusSlot.captured shouldBe requestBody.status
            }

            it("não deve atualizar se ela não existir") {
                every {
                    transactionService.updatePaymentStatus(any(), any(), any())
                } returns TransactionResult.TransactionNotFound

                val thrown =
                    assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

                thrown.status shouldBe HttpStatus.NOT_FOUND
            }

            it("não deve atualizar se ela não estiver COMPLETED") {
                every {
                    transactionService.updatePaymentStatus(any(), any(), any())
                } returns TransactionResult.IllegalState(TransactionStatus.ACTIVE)

                val thrown =
                    assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

                thrown.status shouldBe HttpStatus.CONFLICT
            }

            it("não deve atualizar se ela não for do mesmo usuário") {
                every {
                    transactionService.updatePaymentStatus(any(), any(), any())
                } returns TransactionResult.IllegalUser(UserId("5521999999999"))

                val thrown =
                    assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

                thrown.status shouldBe HttpStatus.NOT_FOUND
            }
        }
    }
}