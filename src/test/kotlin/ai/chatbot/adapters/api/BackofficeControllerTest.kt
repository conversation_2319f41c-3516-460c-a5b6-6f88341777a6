package ai.chatbot.adapters.api

import ai.chatbot.adapters.billPayment.BillTO
import ai.chatbot.adapters.billPayment.ResponseRecipientTO
import ai.chatbot.adapters.waCommCentre.ClientId
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.bill.BillType
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.notification.BillComingDueLastWarnDetailsTO
import ai.chatbot.app.notification.BillComingDueRegularDetailsTO
import ai.chatbot.app.notification.RegisterCompletedTO
import ai.chatbot.app.notification.TemplateInfoProvider
import ai.chatbot.app.notification.WelcomeDetailsTO
import ai.chatbot.app.notification.WelcomeMessageType
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldNotBeEmpty
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV, "test"])
class BackofficeControllerTest(
    embeddedServer: EmbeddedServer,
) : DescribeSpec() {
    private val client = RxHttpClient.create(embeddedServer.url)

    @MockBean(TemplateInfoProvider::class)
    fun templateInfoProvider(): TemplateInfoProvider = templateInfoProvider
    private val templateInfoProvider: TemplateInfoProvider = mockk(relaxed = true)

    @MockBean(TenantService::class)
    fun tenantService(): TenantService = tenantService
    private val tenantService: TenantService = mockk(relaxed = true) {
        every { getConfiguration().internalAuth.identity } returns "internal-auth-identity"
        every { getConfiguration().internalAuth.secret } returns "internla-auth-secret"
        every { getConfiguration().clientId } returns ClientId("test")
        every { getConfiguration().waCommCentre.senderId } returns "test"
    }

    private val controllerPath = "/backoffice/notifications/inspect"
    private val validAuth = "Basic aW50ZXJuYWwtYXV0aC1pZGVudGl0eTppbnRlcm5sYS1hdXRoLXNlY3JldA==" // internal-auth-identity:internla-auth-secret in base64

    init {
        beforeEach {
            clearMocks(templateInfoProvider)
        }
        describe("ao tentar acessar a rota /notifications/inspect") {
            it("deve retornar UNAUTHORIZED quando não estiver autenticado") {
                val notification = BillComingDueRegularDetailsTO(
                    bills = emptyList(),
                    walletName = "Test Wallet",
                )
                val request = HttpRequest.POST(controllerPath, notification)

                val thrown = assertThrows<HttpClientResponseException> {
                    client.toBlocking()
                        .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
                }

                thrown.status shouldBe HttpStatus.UNAUTHORIZED
            }

            it("deve retornar UNAUTHORIZED quando a autenticação básica estiver incorreta") {
                val notification = BillComingDueRegularDetailsTO(
                    bills = emptyList(),
                    walletName = "Test Wallet",
                )
                val request = HttpRequest.POST(controllerPath, notification)
                    .header("Authorization", "Basic dXNlcjpwYXNz") // user:pass in base64

                val thrown = assertThrows<HttpClientResponseException> {
                    client.toBlocking()
                        .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
                }

                thrown.status shouldBe HttpStatus.UNAUTHORIZED
            }

            it("deve retornar sucesso quando autenticado corretamente e buildNotifications retorna sucesso") {
                val testBill = BillTO(
                    id = "BILL-TEST-1",
                    assignor = "Test Assignor",
                    billRecipient = ResponseRecipientTO(name = "Test Recipient"),
                    description = "Test Bill Description",
                    amount = 10000,
                    discount = 0,
                    interest = 0,
                    fine = 0,
                    amountTotal = 10000,
                    billType = BillType.FICHA_COMPENSACAO,
                    paymentLimitTime = "23:59",
                    dueDate = "2024-03-15",
                    subscriptionFee = false,
                    status = "ACTIVE",
                    schedule = null,
                )

                val notification = BillComingDueRegularDetailsTO(
                    bills = listOf(testBill),
                    walletName = "Test Wallet",
                )

                every {
                    templateInfoProvider.getHistoryMessage(any())
                } returns "Test notification message"

                val request = HttpRequest.POST(controllerPath, notification)
                    .header("Authorization", validAuth)

                val response = client.toBlocking().exchange(
                    request,
                    Argument.of(Map::class.java),
                    Argument.of(String::class.java),
                )

                response.status shouldBe HttpStatus.OK
                val body = response.body()
                body shouldBe mapOf(
                    "notificationCount" to 1,
                    "notifications" to listOf<Map<String, String>>(
                        mapOf(
                            "text" to "Test notification message",
                            "type" to "BILLS_COMING_DUE",
                        ),
                    ),
                )
            }

            it("deve retornar erro quando buildNotifications retorna NotificationError") {
                val notification = BillComingDueRegularDetailsTO(
                    bills = emptyList(),
                    walletName = "Test Wallet",
                )

                val request = HttpRequest.POST(controllerPath, notification)
                    .header("Authorization", validAuth)

                val response = client.toBlocking().exchange(
                    request,
                    Argument.of(Map::class.java),
                    Argument.of(String::class.java),
                )

                response.status shouldBe HttpStatus.OK
                val body = response.body()
                body shouldBe mapOf(
                    "status" to "ERROR",
                    "error" to "NO_BILLS_TO_NOTIFY",
                )
            }

            it("deve retornar múltiplas notificações quando buildNotifications retorna lista com mais de uma") {
                val testFirstBill = BillTO(
                    id = "BILL-TEST-1",
                    assignor = "Test Assignor",
                    billRecipient = ResponseRecipientTO(name = "Test Recipient"),
                    description = "Test Bill Description",
                    amount = 10000,
                    discount = 0,
                    interest = 0,
                    fine = 0,
                    amountTotal = 10000,
                    billType = BillType.FICHA_COMPENSACAO,
                    paymentLimitTime = "23:59",
                    dueDate = "2024-03-15",
                    subscriptionFee = false,
                    status = "ACTIVE",
                    schedule = null,
                )
                val testSecondBill = BillTO(
                    id = "BILL-TEST-2",
                    assignor = "Test Assignor 2",
                    billRecipient = ResponseRecipientTO(name = "Test Recipient 2"),
                    description = "Test Bill Description 2",
                    amount = 5000,
                    discount = 0,
                    interest = 0,
                    fine = 0,
                    amountTotal = 5000,
                    billType = BillType.PIX,
                    paymentLimitTime = "23:59",
                    dueDate = "2024-03-15",
                    subscriptionFee = false,
                    status = "WAITING_APPROVAL",
                    schedule = null,
                )
                val testThirdBill = BillTO(
                    id = "BILL-TEST-3",
                    assignor = "Test Assignor 3",
                    billRecipient = ResponseRecipientTO(name = "Test Recipient 3"),
                    description = "Test Bill Description 3",
                    amount = 1000,
                    discount = 0,
                    interest = 0,
                    fine = 0,
                    amountTotal = 1000,
                    billType = BillType.PIX,
                    paymentLimitTime = "23:59",
                    dueDate = "2024-03-15",
                    subscriptionFee = false,
                    status = "WAITING_APPROVAL",
                    schedule = null,
                )

                val notification = BillComingDueRegularDetailsTO(
                    bills = listOf(testFirstBill, testSecondBill, testThirdBill),
                    walletName = "Test Wallet",
                )

                every {
                    templateInfoProvider.getHistoryMessage(any())
                } returns "First notification" andThen "Second notification"

                val request = HttpRequest.POST(controllerPath, notification)
                    .header("Authorization", validAuth)

                val response = client.toBlocking().exchange(
                    request,
                    Argument.of(Map::class.java),
                    Argument.of(String::class.java),
                )

                response.status shouldBe HttpStatus.OK
                val body = response.body()
                body shouldBe mapOf(
                    "notifications" to listOf(
                        mapOf(
                            "text" to "First notification",
                            "type" to "BILLS_COMING_DUE",
                        ),
                        mapOf(
                            "text" to "Second notification",
                            "type" to "WAITING_APPROVAL_BILLS",
                        ),
                    ),
                    "notificationCount" to 2,
                )
            }

            it("deve retornar notificações para BillComingDueLastWarnDetailsTO") {
                val testActiveBill = BillTO(
                    id = "BILL-ACTIVE-1",
                    assignor = "Test Assignor Active",
                    billRecipient = ResponseRecipientTO(name = "Test Recipient Active"),
                    description = "Test Active Bill Description",
                    amount = 15000,
                    discount = 0,
                    interest = 0,
                    fine = 0,
                    amountTotal = 15000,
                    billType = BillType.FICHA_COMPENSACAO,
                    paymentLimitTime = "18:00",
                    dueDate = "2024-03-15",
                    subscriptionFee = false,
                    status = "ACTIVE",
                    schedule = null,
                )
                val testWaitingBill = BillTO(
                    id = "BILL-WAITING-1",
                    assignor = "Test Assignor Waiting",
                    billRecipient = ResponseRecipientTO(name = "Test Recipient Waiting"),
                    description = "Test Waiting Bill Description",
                    amount = 8000,
                    discount = 0,
                    interest = 0,
                    fine = 0,
                    amountTotal = 8000,
                    billType = BillType.PIX,
                    paymentLimitTime = "18:00",
                    dueDate = "2024-03-15",
                    subscriptionFee = false,
                    status = "WAITING_APPROVAL",
                    schedule = null,
                )

                val notification = BillComingDueLastWarnDetailsTO(
                    bills = listOf(testActiveBill, testWaitingBill),
                    walletName = "Test Wallet",
                    hint = "Last chance to pay!",
                )

                val request = HttpRequest.POST(controllerPath, notification)
                    .header("Authorization", validAuth)

                val response = client.toBlocking().exchange(
                    request,
                    Argument.of(Map::class.java),
                    Argument.of(String::class.java),
                )

                response.status shouldBe HttpStatus.OK
                val body = response.body() as Map<String, Any>
                val notifications = body["notifications"] as List<Map<String, Any>>

                // Validate structure and types
                body["notificationCount"] shouldBe 2
                notifications.size shouldBe 2

                val firstNotification = notifications[0]
                firstNotification["type"] shouldBe "BILLS_COMING_DUE_LAST_WARN_EARLY_ACCESS"
                (firstNotification["text"] as String).shouldNotBeEmpty()
                (firstNotification["text"] as String) shouldBe "Oi Inspection User, aqui é o Fred. 🚨Importante: algumas de suas contas vão vencer até às 18:00."

                val secondNotification = notifications[1]
                secondNotification["type"] shouldBe "WAITING_APPROVAL_BILLS"
            }

            it("deve retornar notificação para RegisterCompletedTO") {
                val notification = RegisterCompletedTO

                val request = HttpRequest.POST(controllerPath, notification)
                    .header("Authorization", validAuth)

                val response = client.toBlocking().exchange(
                    request,
                    Argument.of(Map::class.java),
                    Argument.of(String::class.java),
                )

                response.status shouldBe HttpStatus.OK
                val body = response.body()
                body shouldBe mapOf(
                    "notifications" to listOf(
                        mapOf(
                            "text" to "Oi, aqui é o Fred!\n\nSua conta está sendo criada e, assim que estiver tudo pronto, passo aqui para te contar.\n\nEnquanto isso, já salva o meu contato. Vou te avisar sobre novas contas e lembrar vencimentos por aqui.\n\nE sempre que quiser avaliar minhas mensagens, é só reagir com 👍 ou 👎.",
                            "type" to "REGISTRATION_COMPLETION",
                        ),
                    ),
                    "notificationCount" to 1,
                )
            }

            it("deve retornar múltiplas notificações para WelcomeDetailsTO") {
                val notification = WelcomeDetailsTO(
                    type = WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX,
                )

                val request = HttpRequest.POST(controllerPath, notification)
                    .header("Authorization", validAuth)

                val response = client.toBlocking().exchange(
                    request,
                    Argument.of(Map::class.java),
                    Argument.of(String::class.java),
                )

                response.status shouldBe HttpStatus.OK
                val body = response.body()
                body shouldBe mapOf(
                    "notifications" to listOf(
                        mapOf(
                            "text" to "O usuário está realizando o fluxo de boas vindas.",
                            "type" to "SYSTEM_MESSAGE",
                        ),
                        mapOf(
                            "text" to "Inspection User, trago boas notícias: sua conta já está ativa e estou buscando os boletos em seu nome.",
                            "type" to "ONBOARDING_SINGLE_PIX_START",
                        ),
                        mapOf(
                            "type" to "ONBOARDING_START",
                        ),
                    ),
                    "notificationCount" to 3,
                )
            }
        }
    }
}