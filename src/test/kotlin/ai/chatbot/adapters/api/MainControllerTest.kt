package ai.chatbot.adapters.api

import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.config.TENANT_KEY
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.ConversationService
import ai.chatbot.app.conversation.MessageContent
import ai.chatbot.app.conversation.UserMessagePayloadValidationResult
import ai.chatbot.app.tenantConfiguration
import ai.chatbot.app.user.UserId
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV, "test"])
class MainControllerTest(
    embeddedServer: EmbeddedServer,
) : DescribeSpec() {
    private val client = RxHttpClient.create(embeddedServer.url)

    @MockBean(ConversationService::class)
    fun conversationService(): ConversationService = conversationService
    private val conversationService: ConversationService = mockk(relaxed = true)

    @MockBean(TenantService::class)
    fun tenantService(): TenantService = tenantService

    val mockBlipAuth = mockk<TenantConfiguration.BlipAuthConfig> {
        every { secret } returns "CHATBOT_AI_BLIP_SECRET"
    }
    private val tenantService: TenantService = mockk(relaxed = true) {
        every {
            getConfiguration()
        } returns tenantConfiguration()
    }

    private val validUser = "<EMAIL>"
    private val controllerPath = "/chatbot/message"
    private val tenantId = "FRIDAY"

    init {
        describe("ao tentar acessar o controller") {
            it("deve retornar UNAUTHORIZED quando não estiver autenticado") {
                val request = HttpRequest.POST(controllerPath, MessageTO("oi"))
                    .header(TENANT_KEY, tenantId)

                val thrown =
                    assertThrows<HttpClientResponseException> {
                        client.toBlocking()
                            .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
                    }

                thrown.status shouldBe HttpStatus.UNAUTHORIZED
            }

            it("deve retornar UNAUTHORIZED se a senha tiver errada") {
                val request =
                    HttpRequest.POST(controllerPath, MessageTO("oi"))
                        .header(BLIP_SECRET_HEADER, "INVALID_SECRET")
                        .header(BLIP_USER_HEADER, validUser)
                        .header(TENANT_KEY, tenantId)

                val thrown =
                    assertThrows<HttpClientResponseException> {
                        client.toBlocking()
                            .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
                    }

                thrown.status shouldBe HttpStatus.UNAUTHORIZED
            }

            it("deve retornar UNAUTHORIZED se o usuário não estiver presente") {
                val request =
                    HttpRequest.POST(controllerPath, MessageTO("oi"))
                        .header(BLIP_SECRET_HEADER, "INVALID_SECRET")
                        .header(TENANT_KEY, tenantId)

                val thrown =
                    assertThrows<HttpClientResponseException> {
                        client.toBlocking()
                            .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
                    }

                thrown.status shouldBe HttpStatus.UNAUTHORIZED
            }

            it("deve retornar o resultado quando estiver autenticado") {
                every { tenantService.getConfiguration().blipAuth } returns mockBlipAuth
                val request =
                    HttpRequest.POST(controllerPath, MessageTO("oi"))
                        .header(BLIP_SECRET_HEADER, "CHATBOT_AI_BLIP_SECRET")
                        .header(BLIP_USER_HEADER, validUser)
                        .header(TENANT_KEY, tenantId)

                val response =
                    client.toBlocking().exchange(
                        request,
                        Argument.of(String::class.java),
                        Argument.of(String::class.java),
                    )

                coVerify {
                    conversationService.asyncProcessChat(UserId.fromMsisdn(validUser), MessageContent("oi", null), UserMessagePayloadValidationResult.VALID)
                }
                response.status shouldBe HttpStatus.OK
            }
        }
    }
}