package ai.chatbot.adapters.messaging

import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.user.UserId
import io.kotest.core.spec.style.DescribeSpec
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.mockk
import io.mockk.verify
import software.amazon.awssdk.services.sqs.model.Message

class ChatBotWebhookHandlerTest : DescribeSpec() {

    private val conversationHistoryService: ConversationHistoryService = mockk(relaxed = true)

    private lateinit var handler: ChatBotWebhookHandler

    init {
        beforeEach {
            clearAllMocks()

            handler = ChatBotWebhookHandler(
                queue = "test-queue",
                amazonSQS = mockk(),
                configuration =
                SQSMessageHandlerConfiguration(
                    sqsWaitTime = 0,
                    sqsCoolDownTime = 0,
                    visibilityTimeout = 0,
                    dlqEnabled = false,
                    maxNumberOfMessages = 0,
                ),

                conversationHistoryService = conversationHistoryService,
                tenantPropagator = mockk(relaxed = true),
            )
        }

        describe("quando receber um webhook de mensagem enviada") {
            it("não deve fazer nada") {
                val message = buildMessage(
                    """
                        {
                           "type":"text/plain",
                           "content":"Olá",
                           "id":"wamid.HBgNNTUyMTk2NTMxNzI0MBUCABIYIEIxNjBCRTQ2QTY1RkQ3RTY4MjhGRTk5OTk1RTlFOTE2AA==",
                           "from":"<EMAIL>",
                           "to":"<EMAIL>",
                           "metadata":{
                              "#wa.timestamp":"1724879335",
                              "traceparent":"00-8599a8eb6ed22ffb1672fbf5142aae04-6a29f9e67490e66e-01",
                              "#uniqueId":"8d53bc16-d997-4888-bfdd-d8eef5c142de",
                              "#date_processed":"1724879336547",
                              "date_created":"1724879336511",
                              "#tunnel.owner":"<EMAIL>",
                              "#tunnel.originator":"<EMAIL>",
                              "#tunnel.originalFrom":"<EMAIL>/msging-application-router-hosting-business-rd65t",
                              "#tunnel.originalTo":"<EMAIL>/5521965317240%40wa.gw.msging.net",
                              "#envelope.storageDate":"2024-08-28T21:08:56Z"
                           }
                        }
                    """.trimIndent(),
                )

                handler.handleMessage(message)

                verify {
                    conversationHistoryService wasNot called
                }
            }
        }

        describe("quando receber um webhook de reação do usuário") {
            it("deve salvar mensagem de REACTION") {
                val message = buildMessage(
                    """
                        {
                           "type":"application/vnd.lime.reaction+json",
                           "content":{
                              "emoji":{
                                 "values":[
                                    128077
                                 ]
                              },
                              "inReactionTo":{
                                 "id":"83dbd954-9c2c-48fb-8c5f-7799f6e0984a",
                                 "type":"text/plain",
                                 "value":"Oi, Fernando! Como posso te ajudar hoje? Se tiver alguma dúvida ou precisar de ajuda com suas contas, estou aqui para te auxiliar.",
                                 "direction":"sent"
                              }
                           },
                           "id":"wamid.HBgNNTUyMTk2NTMxNzI0MBUCABIYIEQxRDRBQzIxMkNCMzhCMTQ1MzJDNUI3MEE5RUEwREE2AA==",
                           "from":"<EMAIL>",
                           "to":"<EMAIL>",
                           "metadata":{
                              "#wa.timestamp":"1724878440",
                              "traceparent":"00-ba567cbbf66b0880db47bdc62b40d362-cd0f6e9dd6580373-01",
                              "#uniqueId":"ace9194b-8d50-4d1b-b4a6-862333bc7ed3",
                              "#date_processed":"1724878441927",
                              "date_created":"1724878441879",
                              "#tunnel.owner":"<EMAIL>",
                              "#tunnel.originator":"<EMAIL>",
                              "#tunnel.originalFrom":"<EMAIL>/msging-application-router-hosting-business-lttnb",
                              "#tunnel.originalTo":"<EMAIL>/5521965317240%40wa.gw.msging.net",
                              "#envelope.storageDate":"2024-08-28T20:54:01Z"
                           }
                        }
                    """.trimIndent(),
                )

                handler.handleMessage(message)

                verify {
                    conversationHistoryService.createUserReaction(userId = UserId("5521965317240"), content = "\uD83D\uDC4D")
                }
            }
        }
    }

    private fun buildMessage(messageBody: String): Message {
        return Message.builder().body(messageBody).build()
    }
}