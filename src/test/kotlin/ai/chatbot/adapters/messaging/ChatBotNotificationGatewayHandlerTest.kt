package ai.chatbot.adapters.messaging

import DynamoDBUtils.setupDynamoDB
import ai.chatbot.adapters.billPayment.AccountTO
import ai.chatbot.adapters.billPayment.toBillTO
import ai.chatbot.adapters.dynamodb.ChatHistoryDbRepository
import ai.chatbot.adapters.dynamodb.ChatHistoryDynamoDAO
import ai.chatbot.adapters.dynamodb.ChatHistoryStateDbRepository
import ai.chatbot.adapters.dynamodb.ChatHistoryStateDynamoDAO
import ai.chatbot.adapters.waCommCentre.ClientId
import ai.chatbot.app.DYNAMOB_TEST_TABLE_NAME
import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.HistoryStateRepository
import ai.chatbot.app.NotificationService
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.balance
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.bill.PendingBillsService
import ai.chatbot.app.billView
import ai.chatbot.app.billView2
import ai.chatbot.app.billView3
import ai.chatbot.app.config.FeaturesConfig
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.HistoryStateType
import ai.chatbot.app.conversation.InteractionWindow
import ai.chatbot.app.conversation.InteractionWindowError
import ai.chatbot.app.conversation.InteractionWindowService
import ai.chatbot.app.conversation.InteractionWindowType
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.getCustomNotificationServiceMock
import ai.chatbot.app.mockNotificationConfigs
import ai.chatbot.app.notification.BillComingDueLastWarnDetailsTO
import ai.chatbot.app.notification.BillComingDueRegularDetailsTO
import ai.chatbot.app.notification.BuildNotificationResult
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatBotNotificationDetailsTO
import ai.chatbot.app.notification.ChatBotNotificationGatewayTO
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.ChatbotWhatsappTemplatedNotification
import ai.chatbot.app.notification.FridayNotificationContextTemplatesService
import ai.chatbot.app.notification.FridayOnboardingSinglePixNotificationService
import ai.chatbot.app.notification.KnownNotificationTypes
import ai.chatbot.app.notification.KnownTemplateConfigurationKeys
import ai.chatbot.app.notification.NotificationMedia
import ai.chatbot.app.notification.OpenFinanceIncentiveDetailsTO
import ai.chatbot.app.notification.OpenFinanceIncentiveType
import ai.chatbot.app.notification.RawTemplateNotificationConfig
import ai.chatbot.app.notification.RegisterCompletedTO
import ai.chatbot.app.notification.WelcomeDetailsTO
import ai.chatbot.app.notification.WelcomeMessageType
import ai.chatbot.app.pix.PixQRCode
import ai.chatbot.app.prompt.FridayPromptService
import ai.chatbot.app.prompt.TenantPromptService
import ai.chatbot.app.tenantConfiguration
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.getObjectMapper
import arrow.core.left
import arrow.core.right
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.context.ApplicationContext
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.util.Optional
import org.mockito.Mockito.spy
import software.amazon.awssdk.services.sqs.model.Message

class ChatBotNotificationGatewayHandlerTest : DescribeSpec() {
    private val mockedApplicationContext =
        mockk<ApplicationContext> {
            every { getProperty(any<String>(), String::class.java) } returns Optional.of(DYNAMOB_TEST_TABLE_NAME)
        }

    private val tenantService: TenantService = mockk()

    private val dynamoDbEnhancedClient = setupDynamoDB()
    private val historyRepository: HistoryRepository = ChatHistoryDbRepository(ChatHistoryDynamoDAO(dynamoDbEnhancedClient, mockedApplicationContext), tenantService)
    private val historyStateRepository: HistoryStateRepository = ChatHistoryStateDbRepository(ChatHistoryStateDynamoDAO(dynamoDbEnhancedClient, mockedApplicationContext), tenantService)
    private val notificationService: NotificationService = mockk(relaxed = true)
    private val interactionWindowService: InteractionWindowService = mockk()
    private val paymentAdapter: PaymentAdapter = mockk()
    private lateinit var pendingBillsService: PendingBillsService
    private lateinit var conversationHistoryService: ConversationHistoryService
    private lateinit var chatbotNotificationBuilder: ChatbotNotificationBuilder
    private val notificationContextTemplatesService = FridayNotificationContextTemplatesService()

    private val customNotificationService = getCustomNotificationServiceMock(notificationContextTemplatesService = notificationContextTemplatesService)

    private lateinit var handler: ChatBotNotificationGatewayHandler

    private val pixCode =
        "<EMAIL>.br52040000530398654040.015802BR5910JOAO PEDRO6014RIO DE JANEIRO61082022029762100506CecApp6304A5F9"

    private val user =
        User(
            accountId = AccountId(value = "ACCOUNT-ID"),
            id = UserId("*************"),
            name = "Nome",
            accountGroups = emptyList(),
            status = AccountStatus.ACTIVE,
        )

    private val billViews = listOf(billView, billView2, billView3)
    private val walletId = "walletId"
    private val walletName = "walletName"

    private fun buildMessage(
        details: ChatBotNotificationDetailsTO,
        paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
    ): Message {
        val messageBody =
            ChatBotNotificationGatewayTO(
                walletId = "walletId",
                account = AccountTO(id = user.accountId.value, fullName = user.name, msisdn = user.id.value, accountGroups = emptyList(), status = AccountStatus.ACTIVE, paymentStatus = paymentStatus),
                details = details,
            )

        return Message.builder().body(getObjectMapper().writeValueAsString(messageBody)).build()
    }

    private fun buildMessage(body: String): Message {
        return Message.builder().body(body).build()
    }

    private val mockAppBaseUrl = "https://app.friday.ai"
    private val mockTenantName = "friday"
    private val mockFeatures = mockk<FeaturesConfig> {
        every { openFinanceIncentive } returns true
    }

    init {
        beforeEach {
            clearAllMocks()
            every { tenantService.getConfiguration() } returns tenantConfiguration().copy(appBaseUrl = mockAppBaseUrl, features = mockFeatures)
            every { tenantService.getTenantName() } returns mockTenantName

            every { customNotificationService.config() } returns mockNotificationConfigs
            every { interactionWindowService.checkAndCreate(any()) } returns Unit.right()
            every { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()

            pendingBillsService =
                PendingBillsService(
                    historyStateRepository = historyStateRepository,
                    paymentAdapter = paymentAdapter,
                )

            conversationHistoryService =
                ConversationHistoryService(
                    historyRepository = historyRepository,
                    openAIAdapter = mockk(),
                    paymentAdapter = paymentAdapter,
                    notificationService = notificationService,
                    historyStateRepository = historyStateRepository,
                    pendingBillsService = pendingBillsService,
                    promptService = TenantPromptService(
                        prompts = mapOf("friday" to FridayPromptService()),
                        tenantService = mockk(),
                        multiTenantPromptService = mockk(relaxed = true),
                    ),
                )

            chatbotNotificationBuilder = ChatbotNotificationBuilder(
                buildNotificationService = BuildNotificationService(FridayNotificationContextTemplatesService(), tenantService),
                notificationContextTemplatesService = FridayNotificationContextTemplatesService(),
                customNotificationService = customNotificationService,
                tenantService = tenantService,
                onboardingSinglePixNotificationService = FridayOnboardingSinglePixNotificationService(),
            )

            handler =
                ChatBotNotificationGatewayHandler(
                    conversationHistoryService = spy(conversationHistoryService),
                    notificationService = notificationService,
                    queue = "test",
                    amazonSQS = mockk(),
                    configuration =
                    SQSMessageHandlerConfiguration(
                        sqsWaitTime = 0,
                        sqsCoolDownTime = 0,
                        visibilityTimeout = 0,
                        dlqEnabled = false,
                        maxNumberOfMessages = 0,
                    ),
                    onePixPayInstrumentation = mockk(relaxed = true),
                    interactionWindowService = interactionWindowService,
                    paymentAdapter = paymentAdapter,
                    tenantPropagator = mockk(relaxed = true),
                    tenantService = tenantService,
                    chatbotNotificationBuilder = chatbotNotificationBuilder,
                )
        }

        describe("Deve conseguir tratar o payload de multichannel do billpayment") {
            it("deve notificar o usuário corretmente") {
                val messageBody =
                    """
                        {"walletId":"ACCOUNT-ce4d6c82-422a-4fb3-aa00-5c02110bb846","account":{"id":"ACCOUNT-ce4d6c82-422a-4fb3-aa00-5c02110bb846","fullName":"Marlon da Costa Moncores","document":"***********","documentType":"CPF","status":"ACTIVE","msisdn":"+*************","paymentStatus":"UpToDate","subscriptionType":"PIX","accountGroups":["ALPHA","BETA","CREDIT_CARD_STANDARD_PLAN","DEVELOPER"],"sweepingAccount":false},"details":{"@type":"GenericNotificationRawDetailsTO","notificationId":"2015d15b-ac9d-4aa3-a889-c3180ae448a1","receiver":{"msisdn":"+*************"},"accountId":{"value":"ACCOUNT-ce4d6c82-422a-4fb3-aa00-5c02110bb846"},"configurationKey":"barcode-bill-add-waiting-approval","arguments":{"BILL_PAYEE":"NU PAGAMENTOS SA","BILL_DUE_DATE":"10/09","BILL_AMOUNT":"R${'$'} 20,00","BILL_SOURCE":"Caixa Postal","BILL_CREATED_BY":"<EMAIL>","WALLET_NAME":"Marlon da Costa Moncores","BILL_ID":"BILL-608f7e37-0274-412b-a14e-1e4299ed51e5"},"media":null,"clientId":"CHATBOT_AI_TRANSACTION_CLIENT_ID-d502c3c4-0b1b-401f-aa75-e86d9f94cd95"}}
                    """.trimIndent()

                val message = Message.builder().body(messageBody).build()
                val response = handler.handleMessage(message)
                response.shouldDeleteMessage shouldBe true
            }
        }

        describe("quando chegar uma mensagem de bills coming due e o usuário estiver inadimplente") {
            beforeEach {
                handler.handleMessage(
                    buildMessage(
                        details =
                        BillComingDueRegularDetailsTO(
                            bills = billViews.map { it.toBillTO() },
                            walletName = "",
                        ),
                        paymentStatus = AccountPaymentStatus.Overdue,
                    ),
                )
            }
            afterEach {
                conversationHistoryService.clearHistory(
                    user.id,
                    HistoryStateType.BILLS_COMING_DUE,
                    localDate = LocalDate.now(),
                )
            }
            it("deve notificar com o template simples") {
                val state = historyStateRepository.findLatest(userId = user.id)

                val slot = slot<ChatbotRawTemplatedNotification>()
                verify { notificationService.notify(capture(slot)) }

                slot.captured.configurationKey shouldBe KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDueSimple
                val currentState = state.shouldBeTypeOf<BillComingDueHistoryState>()
                currentState.internalStateControl.shouldNotBeNull()
            }
        }

        describe("quando chegar uma mensagem de last warn") {
            afterEach {
                conversationHistoryService.clearHistory(
                    user.id,
                    HistoryStateType.BILLS_COMING_DUE,
                    localDate = LocalDate.now(),
                )
            }

            it("deve adicionar a conta na lista de contas pendentes sem remover as que já estavam") {
                setupBillComingDueMessage(billViews)

                var state = historyStateRepository.findLatest(userId = user.id)
                var currentState = state.shouldBeTypeOf<BillComingDueHistoryState>()
                currentState.walletWithBills.bills.size shouldBe billViews.size

                setupLastWarnMessage(billViews.take(1))

                state = historyStateRepository.findLatest(userId = user.id)
                currentState = state.shouldBeTypeOf<BillComingDueHistoryState>()
                currentState.walletWithBills.bills.size shouldBe billViews.size
            }
        }

        describe("quando chegar uma mensagem de bills coming due") {
            beforeEach {
                clearAllMocks()
                every { tenantService.getConfiguration() } returns tenantConfiguration().copy(appBaseUrl = mockAppBaseUrl, features = mockFeatures)
                every { tenantService.getTenantName() } returns "friday"

                coEvery { paymentAdapter.generateOnePixPay(user.id, any(), any()) } returns PixQRCode(pixCode).right()
                coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user = user, WalletWithBills(bills = billViews, walletId = WalletId(walletId), walletName = walletName)).right()
                every { interactionWindowService.checkAndCreate(any()) } returns Unit.right()
                coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
            }

            afterEach {
                conversationHistoryService.clearHistory(
                    user.id,
                    HistoryStateType.BILLS_COMING_DUE,
                    localDate = LocalDate.now(),
                )
            }

            describe("quando houver somente assinatura") {
                it("nao deve notificar") {
                    val billViewSubscription = billView2.copy(subscriptionFee = true, billDescription = "Assinatura")
                    setupBillComingDueMessage(listOf(billViewSubscription))

                    verify(exactly = 0) { notificationService.notify(any()) }
                }
            }

            describe("quando a mensagem for muito grande") {
                it("deve notificar o usuário com uma mensagem mais simples e salvar no banco") {
                    val billViewLarge = billView.copy(billDescription = "a".repeat(2000))
                    setupBillComingDueMessage(listOf(billViewLarge, billView))

                    val state = historyStateRepository.findLatest(userId = user.id)
                    val slot = slot<ChatbotRawTemplatedNotification>()

                    verify { notificationService.notify(capture(slot)) }
                    slot.captured.configurationKey shouldBe KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDueBasic
                    val currentState = state.shouldBeTypeOf<BillComingDueHistoryState>()
                    currentState.internalStateControl.shouldNotBeNull()
                }
            }

            it("deve notificar o usuário e salvar no banco") {
                setupBillComingDueMessage(billViews)

                val state = historyStateRepository.findLatest(userId = user.id)

                val slot = slot<ChatbotRawTemplatedNotification>()
                verify {
                    notificationService.notify(
                        capture(slot),
                    )
                }
                slot.captured.configurationKey shouldBe KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDue
                val currentState = state.shouldBeTypeOf<BillComingDueHistoryState>()
                currentState.internalStateControl.shouldNotBeNull()
            }

            it("deve salvar as bills ordenadas por vencimento se for a primeira notificação do dia") {
                val billView2Subscription = billView2.copy(subscriptionFee = true, billDescription = "Assinatura")
                var billView3 = billView3.copy(dueDate = LocalDate.now().plusDays(1))
                setupBillComingDueMessage(listOf(billView3, billView, billView2Subscription))
                val state = historyStateRepository.findLatest(userId = user.id)

                val currentState = state.shouldBeTypeOf<BillComingDueHistoryState>()

                val bills = currentState.walletWithBills.bills

                bills.size shouldBe 3
                bills[0].billId shouldBe billView.billId
                bills[1].billId shouldBe billView2Subscription.billId
                bills[2].billId shouldBe billView3.billId

                val slot = slot<ChatbotRawTemplatedNotification>()
                verify {
                    notificationService.notify(capture(slot))
                }
                slot.captured.configurationKey shouldBe KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDue
            }

            it("deve salvar as bills se não for a primeira notificação do dia") {
                var billView3 = billView3.copy(dueDate = LocalDate.now().plusDays(1))
                setupBillComingDueMessage(listOf(billView3, billView, billView2))
                setupBillComingDueMessage(listOf(billView3, billView2))
                val state = historyStateRepository.findLatest(userId = user.id)

                val currentState = state.shouldBeTypeOf<BillComingDueHistoryState>()

                val bills = currentState.walletWithBills.bills

                bills.size shouldBe 2
                bills[0].billId shouldBe billView2.billId
                bills[1].billId shouldBe billView3.billId
            }

            describe("quando chegar uma mensagem de bills coming due novamente") {
                it("não deve notificar o usuário novamente") {
                    every { tenantService.getTenantName() } returns "friday"

                    conversationHistoryService = mockk(relaxed = true)
                    handler =
                        ChatBotNotificationGatewayHandler(
                            conversationHistoryService = conversationHistoryService,
                            notificationService = notificationService,
                            queue = "test",
                            amazonSQS = mockk(),
                            configuration =
                            SQSMessageHandlerConfiguration(
                                sqsWaitTime = 0,
                                sqsCoolDownTime = 0,
                                visibilityTimeout = 0,
                                dlqEnabled = false,
                                maxNumberOfMessages = 0,
                            ),
                            onePixPayInstrumentation = mockk(relaxed = true),
                            interactionWindowService = interactionWindowService,
                            paymentAdapter = paymentAdapter,
                            tenantPropagator = mockk(relaxed = true),
                            tenantService = tenantService,
                            chatbotNotificationBuilder = chatbotNotificationBuilder,
                        )

                    coEvery { conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(any()) } returns
                        BillComingDueHistoryState(
                            user = user,
                            walletWithBills = WalletWithBills(bills = billViews, walletId = WalletId(walletId), walletName = walletName),
                            internalStateControl = InternalStateControl(
                                shouldSynchronizeBeforeCompletion = false,
                                billComingDueNotifiedAt = LocalDateTime.now().minusHours(1),
                            ),
                            balance = balance,
                            startDate = LocalDate.now(),
                            endDate = LocalDate.now(),
                            contacts = emptyList(),
                            lastBillsUpdatedAt = ZonedDateTime.now(),
                            subscription = null,
                        )

                    setupBillComingDueMessage(billViews)
                    verify(exactly = 0) {
                        notificationService.notify(any())
                    }
                }
            }
        }
        describe("quando chegar uma mensagem de boas vindas") {
            describe("e for onboarding single pix pelo chatbot") {
                it("deve notificar o usuário e salvar no histórico") {
                    val offerConfig = KnownTemplateConfigurationKeys.onboardingStart

                    every { customNotificationService.buildRawTemplateNotification(any(), any(), RawTemplateNotificationConfig(offerConfig, KnownNotificationTypes.ONBOARDING_START)) } returns BuildNotificationResult(
                        notification = ChatbotRawTemplatedNotification(
                            configurationKey = KnownTemplateConfigurationKeys.onboardingStart,
                            mobilePhone = user.id.value,
                            accountId = user.accountId,
                            clientId = ClientId("ClientId"),
                        ),
                        historyMessage = "onboarding start",
                        shouldSend = true,
                    )

                    handler.handleMessage(
                        buildMessage(
                            details = WelcomeDetailsTO(type = WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX),
                        ),
                    )

                    val state = historyStateRepository.findLatest(userId = user.id)

                    verify {
                        notificationService.notify(
                            withArg<ChatbotRawTemplatedNotification> {
                                it.configurationKey shouldBe KnownTemplateConfigurationKeys.onboardingSinglePixStart
                            },
                        )

                        notificationService.notify(
                            withArg<ChatbotRawTemplatedNotification> {
                                it.configurationKey shouldBe KnownTemplateConfigurationKeys.onboardingStart
                            },
                            any(),
                        )
                    }

                    val currentState = state.shouldBeTypeOf<BillComingDueHistoryState>()
                    currentState.internalStateControl.shouldNotBeNull()
                }
            }
        }

        describe("quando chegar uma mensagem de cadastro finalizado") {
            it("deve notificar o usuário e salvar no histórico") {
                handler.handleMessage(
                    buildMessage(
                        details = RegisterCompletedTO,
                    ),
                )

                val state = historyStateRepository.findLatest(userId = user.id)

                verify {
                    notificationService.notify(
                        withArg<ChatbotRawTemplatedNotification> {
                            it.configurationKey shouldBe KnownTemplateConfigurationKeys.registrationCompletion
                        },
                    )
                }

                val currentState = state.shouldBeTypeOf<BillComingDueHistoryState>()
                currentState.internalStateControl.shouldNotBeNull()
            }
        }

        describe("quando o usuário tiver uma janela de interação aberta") {
            it("não deve enviar notificação") {
                every { interactionWindowService.checkAndCreate(any()) } returns
                    InteractionWindowError.UserAlreadyHasOpenWindow(
                        InteractionWindow(
                            userId = user.id,
                            type = InteractionWindowType.ONBOARDING_SINGLE_PIX,
                            expiration = getZonedDateTime().plusHours(5),
                        ),
                    ).left()

                verify(exactly = 0) {
                    notificationService.notify(any())
                }
            }
        }

        describe("quando chegar uma mensagem de notificação genérica") {
            it("deve notificar o usuário e salvar no histórico") {
                handler.handleMessage(
                    buildMessage(
                        """
                            {
                               "walletId":"$walletId",
                               "account":{
                                  "id":"${user.accountId.value}",
                                  "fullName":"",
                                  "document":"",
                                  "documentType":"",
                                  "status":"ACTIVE",
                                  "msisdn":"+${user.id.value}",
                                  "paymentStatus":"UpToDate",
                                  "accountGroups":[],
                                  "sweepingAccount":false
                               },
                               "details":{
                                  "@type":"GenericNotificationDetailsTO",
                                  "notification":{
                                     "receiver":{
                                        "msisdn":"+${user.id.value}"
                                     },
                                     "accountId":{
                                        "value":"${user.accountId.value}"
                                     },
                                     "template":{
                                        "value":"test_message_template__1_0_0"
                                     },
                                     "parameters":[
                                        
                                     ],
                                     "quickReplyButtonsWhatsAppParameter":[],
                                     "buttonWhatsAppParameter":{
                                        "value":"app/entrar"
                                     }
                                  },
                                  "historyMessage":"Mensagem para o histórico."
                               }
                            }
                        """.trimIndent(),
                    ),
                )

                val state = historyStateRepository.findLatest(userId = user.id)

                verify {
                    notificationService.notify(
                        withArg<ChatbotWhatsappTemplatedNotification> {
                            it.template.value shouldBe "test_message_template__1_0_0"
                        },
                    )
                }

                val currentState = state.shouldBeTypeOf<BillComingDueHistoryState>()
                currentState.internalStateControl.shouldNotBeNull()
            }

            it("deve notificar o usuário não registrado e salvar no histórico") {
                handler.handleMessage(
                    buildMessage(
                        """
                            {
                               "walletId":"$walletId",
                               "account":{
                                  "id":"",
                                  "fullName":"",
                                  "document":"",
                                  "documentType":"",
                                  "status":"REGISTER_INCOMPLETE",
                                  "msisdn":"+${user.id.value}",
                                  "paymentStatus":"UpToDate",
                                  "accountGroups":[],
                                  "sweepingAccount":false
                               },
                               "details":{
                                  "@type":"GenericNotificationDetailsTO",
                                  "notification":{
                                     "receiver":{
                                        "msisdn":"+${user.id.value}"
                                     },
                                     "accountId":{
                                        "value":"${user.accountId.value}"
                                     },
                                     "template":{
                                        "value":"test_message_template__1_0_0"
                                     },
                                     "parameters":[
                                        
                                     ],
                                     "quickReplyButtonsWhatsAppParameter":[],
                                     "buttonWhatsAppParameter":{
                                        "value":"app/entrar"
                                     }
                                  },
                                  "historyMessage":"Mensagem para o histórico."
                               }
                            }
                        """.trimIndent(),
                    ),
                )

                val state = historyStateRepository.findLatest(userId = user.id)

                verify {
                    notificationService.notify(
                        withArg<ChatbotWhatsappTemplatedNotification> {
                            it.template.value shouldBe "test_message_template__1_0_0"
                        },
                    )
                }

                val currentState = state.shouldBeTypeOf<BillComingDueHistoryState>()
                currentState.internalStateControl.shouldNotBeNull()
            }

            describe("deve ler campo media corretamente") {
                for ((json, expectedMedia) in listOf(
                    Pair("""{ "type": "IMAGE", "url": "https://my.image.url", "imageType": "image/png" }""", NotificationMedia.Image("https://my.image.url", "image/png")),
                    Pair("""{ "type": "VIDEO", "url": "https://my.video.url", "videoType": "video/mp4"  }""", NotificationMedia.Video("https://my.video.url", "video/mp4")),
                    Pair("""{ "type": "DOCUMENT", "url": "https://my.document.url", "filename": "ducumentFilename", "documentType": "application/pdf"  }""", NotificationMedia.Document("https://my.document.url", "ducumentFilename", "application/pdf")),
                )) {
                    it("media ${expectedMedia::class.simpleName}") {
                        val message = """
                            {
                               "walletId":"$walletId",
                               "account":{
                                  "id":"${user.accountId.value}",
                                  "fullName":"",
                                  "document":"",
                                  "documentType":"",
                                  "status":"ACTIVE",
                                  "msisdn":"+${user.id.value}",
                                  "paymentStatus":"UpToDate",
                                  "accountGroups":[],
                                  "sweepingAccount":false
                               },
                               "details":{
                                  "@type":"GenericNotificationDetailsTO",
                                  "notification":{
                                     "receiver":{
                                        "msisdn":"+${user.id.value}"
                                     },
                                     "accountId":{
                                        "value":"${user.accountId.value}"
                                     },
                                     "template":{
                                        "value":"test_message_template__1_0_0"
                                     },
                                     "parameters":[
                                        
                                     ],
                                     "quickReplyButtonsWhatsAppParameter":[],
                                     "buttonWhatsAppParameter":{
                                        "value":"app/entrar"
                                     },
                                     "media": {{media}}
                                  },
                                  "historyMessage":"Mensagem para o histórico."
                               }
                            }
                        """.trimIndent()

                        handler.handleMessage(
                            buildMessage(
                                message.replace("{{media}}", json),
                            ),
                        )

                        verify {
                            notificationService.notify(
                                withArg<ChatbotWhatsappTemplatedNotification> {
                                    it.media shouldBe expectedMedia
                                },
                            )
                        }
                    }
                }
            }
        }

        describe("quando chegar um mensagem de incentivo") {
            describe("e o incentivo estiver desabilitado") {
                it("não deve notificar o usuário") {

                    handler =
                        ChatBotNotificationGatewayHandler(
                            conversationHistoryService = spy(conversationHistoryService),
                            notificationService = notificationService,
                            queue = "test",
                            amazonSQS = mockk(),
                            configuration =
                            SQSMessageHandlerConfiguration(
                                sqsWaitTime = 0,
                                sqsCoolDownTime = 0,
                                visibilityTimeout = 0,
                                dlqEnabled = false,
                                maxNumberOfMessages = 0,
                            ),
                            onePixPayInstrumentation = mockk(relaxed = true),
                            interactionWindowService = interactionWindowService,
                            paymentAdapter = paymentAdapter,
                            tenantPropagator = mockk(relaxed = true),
                            tenantService = tenantService,
                            chatbotNotificationBuilder = chatbotNotificationBuilder,
                        )
                    conversationHistoryService.clearHistory(
                        user.id,
                        HistoryStateType.BILLS_COMING_DUE,
                        localDate = LocalDate.now(),
                    )

                    val message = buildMessage(
                        details = OpenFinanceIncentiveDetailsTO(
                            type = OpenFinanceIncentiveType.CASH_IN,
                            userOptedOut = false,
                            bank = null,
                        ),
                    )

                    handler.handleMessage(
                        message,
                    )

                    verify(exactly = 0) {
                        notificationService.notify(any())
                    }
                }
            }
        }
    }

    private fun setupBillComingDueMessage(bills: List<BillView>) {
        handler.handleMessage(
            buildMessage(
                details =
                BillComingDueRegularDetailsTO(
                    bills = bills.map { it.toBillTO() },
                    walletName = "",
                ),
            ),
        )
    }

    private fun setupLastWarnMessage(bills: List<BillView>) {
        handler.handleMessage(
            buildMessage(
                details =
                BillComingDueLastWarnDetailsTO(
                    bills = bills.map { it.toBillTO() },
                    walletName = "",
                    hint = null,
                ),
            ),
        )
    }
}