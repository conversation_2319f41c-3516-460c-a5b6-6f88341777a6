package ai.chatbot.adapters.messaging

import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.HistoryStateRepository
import ai.chatbot.app.IOpenAIAdapter
import ai.chatbot.app.NotificationService
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.bill.PendingBillsService
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.prompt.TenantPromptService
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.core.spec.style.DescribeSpec
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import software.amazon.awssdk.services.sqs.model.Message

class ChatStateHandlerTest : DescribeSpec() {

    private lateinit var handler: ChatBotStateHandler
    private val userState = BillComingDueHistoryState(
        user = User(
            id = UserId("*************"),
            accountId = AccountId("123"),
            status = AccountStatus.ACTIVE,
            paymentStatus = AccountPaymentStatus.UpToDate,
            name = "Josnei",
            accountGroups = listOf(),
        ),
        walletWithBills = WalletWithBills(
            bills = listOf(),
            walletId = WalletId("123"),
            walletName = "Josnei",
            activeConsents = listOf(),
        ),
        balance = null,
        internalStateControl = InternalStateControl(
            shouldSynchronizeBeforeCompletion = false,
            billComingDueNotifiedAt = null,
        ),
        startDate = getLocalDate(),
        endDate = getLocalDate(),
        contacts = listOf(),
        lastBillsUpdatedAt = getZonedDateTime(),
        subscription = null,
        onboardingState = null,
        alreadyPromotedSweepingAccount = true,
    )

    private val historyStateRepository: HistoryStateRepository = mockk(relaxed = true)
    private val conversationHistoryService: ConversationHistoryService = spyk(
        ConversationHistoryService(
            historyRepository = mockk<HistoryRepository>(relaxed = true),
            historyStateRepository = historyStateRepository,
            openAIAdapter = mockk<IOpenAIAdapter>(relaxed = true),
            paymentAdapter = mockk<PaymentAdapter>(relaxed = true),
            notificationService = mockk<NotificationService>(relaxed = true),
            pendingBillsService = mockk<PendingBillsService>(relaxed = true),
            promptService = mockk<TenantPromptService>(relaxed = true),
        ),
    )

    init {
        beforeEach {
            clearAllMocks()

            handler = ChatBotStateHandler(
                queue = "test-queue",
                amazonSQS = mockk(),
                configuration =
                SQSMessageHandlerConfiguration(
                    sqsWaitTime = 0,
                    sqsCoolDownTime = 0,
                    visibilityTimeout = 0,
                    dlqEnabled = false,
                    maxNumberOfMessages = 0,
                ),

                conversationHistoryService = conversationHistoryService,
                tenantPropagator = mockk(relaxed = true),
            )
        }

        describe("quando receber uma atualização com payload válido") {
            it("deve atualizar o estado do usuário") {
                every { historyStateRepository.findByDate(UserId("*************"), any()) } returns userState
                val message = buildMessage(
                    """
                        {
                           "accountId": "123",
                           "paymentStatus": "UpToDate",
                           "msisdn": "*************",
                           "status": "ACTIVE"
                        }
                    """.trimIndent(),
                )

                handler.handleMessage(message)

                verify {
                    conversationHistoryService.saveHistoryState(any(), any())
                    historyStateRepository.save(any(), any())
                }
            }
        }

        describe("quando receber uma atualização com estado inválido") {
            it("deve chamar saveHistoryState mas não deve salvar quando accountId está vazio") {
                val invalidUserState = userState.copy(
                    user = userState.user.copy(accountId = AccountId("")),
                )
                every { historyStateRepository.findByDate(UserId("*************"), any()) } returns invalidUserState

                val message = buildMessage(
                    """
                        {
                           "accountId": "",
                           "paymentStatus": "UpToDate",
                           "msisdn": "*************",
                           "status": "ACTIVE"
                        }
                    """.trimIndent(),
                )

                handler.handleMessage(message)

                verify {
                    conversationHistoryService.saveHistoryState(any(), any())
                }
                verify(exactly = 0) {
                    historyStateRepository.save(any(), any())
                }
            }
        }
    }

    private fun buildMessage(messageBody: String): Message {
        return Message.builder().body(messageBody).build()
    }
}