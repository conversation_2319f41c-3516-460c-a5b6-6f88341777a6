package ai.chatbot.adapters.messaging

import ai.chatbot.adapters.billPayment.toBillTO
import ai.chatbot.adapters.waCommCentre.ClientId
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.billView
import ai.chatbot.app.billView2
import ai.chatbot.app.billView3
import ai.chatbot.app.config.FeaturesConfig
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.getCustomNotificationServiceMock
import ai.chatbot.app.mockNotificationConfigs
import ai.chatbot.app.notification.BillComingDueLastWarnDetailsTO
import ai.chatbot.app.notification.BillComingDueRegularDetailsTO
import ai.chatbot.app.notification.BuildNotificationResult
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.ChatbotWhatsappTemplatedNotification
import ai.chatbot.app.notification.ConfigurationKey
import ai.chatbot.app.notification.FridayNotificationContextTemplatesService
import ai.chatbot.app.notification.FridayOnboardingSinglePixNotificationService
import ai.chatbot.app.notification.GenericNotificationContentTO
import ai.chatbot.app.notification.GenericNotificationDetailsTO
import ai.chatbot.app.notification.GenericNotificationRawDetailsTO
import ai.chatbot.app.notification.KnownNotificationTypes
import ai.chatbot.app.notification.NotificationMap
import ai.chatbot.app.notification.NotificationParam
import ai.chatbot.app.notification.NotificationTemplate
import ai.chatbot.app.notification.OpenFinanceIncentiveDetailsTO
import ai.chatbot.app.notification.OpenFinanceIncentiveType
import ai.chatbot.app.notification.RawTemplateNotificationConfig
import ai.chatbot.app.notification.ReceiverTO
import ai.chatbot.app.notification.RegisterCompletedTO
import ai.chatbot.app.notification.TemplateInfoService
import ai.chatbot.app.notification.TestPixReminderType
import ai.chatbot.app.notification.TestPixreminderDetailsTO
import ai.chatbot.app.notification.WelcomeDetailsTO
import ai.chatbot.app.notification.WelcomeMessageType
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import arrow.core.Either
import com.theokanning.openai.completion.chat.ChatMessageRole
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDateTime
import kotlin.collections.emptyList

class ChatbotNotificationBuilderTest : DescribeSpec(
    {

        val tenantService: TenantService = mockk()
        val buildNotificationService: BuildNotificationService = mockk()
        val notificationContextTemplatesService = FridayNotificationContextTemplatesService()
        val templateInfoService: TemplateInfoService = mockk()
        val customNotificationService = getCustomNotificationServiceMock(notificationContextTemplatesService, templateInfoService)
        val onboardingSinglePixNotificationService = FridayOnboardingSinglePixNotificationService()

        val builder = ChatbotNotificationBuilder(
            buildNotificationService = buildNotificationService,
            notificationContextTemplatesService = notificationContextTemplatesService,
            customNotificationService = customNotificationService,
            tenantService = tenantService,
            onboardingSinglePixNotificationService = onboardingSinglePixNotificationService,
        )

        val user = User(
            accountId = AccountId("ACCOUNT-ID"),
            id = UserId("*************"),
            name = "Test User",
            accountGroups = emptyList(),
            status = AccountStatus.ACTIVE,
            paymentStatus = AccountPaymentStatus.UpToDate,
        )

        val bills = listOf(billView, billView2, billView3)
        val billTOs = bills.map { it.toBillTO() }
        val mockClientId = ClientId("unit-test")

        val mockTenantConfiguration = mockk<TenantConfiguration> {
            every { clientId } returns mockClientId
            every { messages } returns mockNotificationConfigs
            every { appBaseUrl } returns "https://app.base.url"
            every { features } returns mockk<FeaturesConfig> {
                every { openFinanceIncentive } returns true
            }
        }

        beforeEach {
            clearAllMocks()
            every { tenantService.getConfiguration() } returns mockTenantConfiguration

            every { mockTenantConfiguration.messages } returns mockNotificationConfigs
            every { mockTenantConfiguration.appBaseUrl } returns "https://app.base.url"
            every { mockTenantConfiguration.features } returns mockk<FeaturesConfig> {
                every { openFinanceIncentive } returns true
            }
        }

        describe("buildNotifications") {
            describe("quando details for BillComingDueLastWarnDetailsTO") {
                it("deve construir notificações de último aviso com sucesso") {
                    val details = BillComingDueLastWarnDetailsTO(
                        bills = billTOs,
                        walletName = "Test Wallet",
                        hint = null,
                    )

                    val expectedNotification = ChatbotRawTemplatedNotification(
                        clientId = mockClientId,
                        configurationKey = ConfigurationKey("bills-coming-due-last-warn"),
                        mobilePhone = user.id.value,
                        accountId = user.accountId,
                        arguments = mapOf("USERNAME" to "test", "PAYMENT_LIMIT_TIME" to "2025-08-13 23:59:59"),
                    )

                    every {
                        buildNotificationService.buildBillsComingDueLastWarnNotification(
                            accountId = user.accountId,
                            mobilePhone = user.id.value,
                            userName = user.name,
                            paymentLimitTime = bills.first().paymentLimitTime,
                        )
                    } returns expectedNotification

                    val result = builder.buildNotifications(details, user)

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 1
                        notifications.first().text shouldBe notificationContextTemplatesService.getBillComingDueLastWarn(
                            user.name,
                            bills.first().paymentLimitTime,
                        )
                        notifications.first().notification shouldBe expectedNotification
                    }
                }

                it("deve incluir notificação de aprovação pendente quando contas estão aguardando aprovação") {
                    val configurationKey = ConfigurationKey("waiting-approval-bills")
                    val billsWithWaitingApproval = billTOs.map { it.copy(status = "WAITING_APPROVAL") }
                    val details = BillComingDueLastWarnDetailsTO(
                        bills = billsWithWaitingApproval,
                        walletName = "Test Wallet",
                        hint = null,
                    )

                    val expectedNotification = ChatbotRawTemplatedNotification(
                        clientId = mockClientId,
                        configurationKey = configurationKey,
                        mobilePhone = user.id.value,
                        accountId = user.accountId,
                        arguments = mapOf("TOTAL_BILLS" to "3"),
                    )

                    every {
                        buildNotificationService.buildBillsComingDueLastWarnNotification(
                            accountId = user.accountId,
                            mobilePhone = user.id.value,
                            userName = user.name,
                            paymentLimitTime = bills.first().paymentLimitTime,
                        )
                    } returns expectedNotification

                    val waitingApprovalResult = BuildNotificationResult(
                        notification = ChatbotRawTemplatedNotification(
                            clientId = mockClientId,
                            mobilePhone = user.id.value,
                            accountId = user.accountId,
                            configurationKey = configurationKey,

                        ),
                        historyMessage = "You have 3 bills waiting for approval",
                        shouldSend = true,
                    )

                    every {
                        customNotificationService.buildRawTemplateNotification(
                            user = user,
                            notificationConfig = RawTemplateNotificationConfig(
                                configurationKey = configurationKey,
                                notificationType = KnownNotificationTypes.WAITING_APPROVAL_BILLS,
                            ),
                            params = listOf(
                                NotificationMap(NotificationParam.TOTAL_BILLS, "3"),
                            ),
                        )
                    } returns waitingApprovalResult

                    every {
                        templateInfoService.getHistoryMessage(any<ChatbotRawTemplatedNotification>())
                    } returns waitingApprovalResult.historyMessage

                    val result = builder.buildNotifications(details, user)

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 2
                        notifications[0].notification shouldBe expectedNotification
                        notifications[0].text shouldBe notificationContextTemplatesService.getBillComingDueLastWarn(user.name, billsWithWaitingApproval.first().paymentLimitTime)
                        notifications[1].text shouldBe waitingApprovalResult.historyMessage
                    }
                }
            }

            describe("quando details for BillComingDueRegularDetailsTO") {
                it("deve construir notificações regulares com sucesso") {
                    val configurationKey = ConfigurationKey("bills-coming-due")
                    val details = BillComingDueRegularDetailsTO(
                        bills = billTOs,
                        walletName = "Test Wallet",
                    )

                    val currentState = BillComingDueHistoryState(
                        user = user,
                        walletWithBills = WalletWithBills(
                            bills = emptyList(),
                            totalWaitingApproval = 0,
                            walletId = WalletId("test"),
                            walletName = "test",
                            activeConsents = emptyList(),
                        ),
                        internalStateControl = InternalStateControl(
                            shouldSynchronizeBeforeCompletion = false,
                            billComingDueNotifiedAt = null,
                        ),
                        subscription = null,
                        alreadyPromotedSweepingAccount = false,
                        balance = null,
                        contacts = emptyList(),
                    )

                    val customNotificationResult = BuildNotificationResult(
                        notification = ChatbotRawTemplatedNotification(
                            clientId = mockClientId,
                            configurationKey = ConfigurationKey("bills-coming-due"),
                            mobilePhone = user.id.value,
                            accountId = user.accountId,
                            arguments = mapOf(
                                "USER_NAME" to "Test User",
                                "TEMPLATE_VARIANT" to "3_bills",
                                "BILL_DESCRIPTION_1" to "John Doe (conta da luz)",
                                "AMOUNT_1" to "R$ 96,97",
                                "BILL_DESCRIPTION_2" to "Enzo (Pix para o enzo)",
                                "AMOUNT_2" to "R$ 70.000,00",
                                "BILL_DESCRIPTION_3" to "João Pedro Bretanha (Pix para João)",
                                "AMOUNT_3" to "R$ 7,00",
                            ),
                        ),
                        historyMessage = "Oi Test User, aqui é o Fred. 🚨Importante: algumas de suas contas vão vencer até às 20:00.",
                        shouldSend = true,
                    )

                    every {
                        customNotificationService.buildBillsComingDueCustom(
                            user = user,
                            bills = any(),
                        )
                    } returns customNotificationResult

                    val result = builder.buildNotifications(details, user, currentState)

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 1
                        notifications.first().text shouldBe "Oi Test User, aqui é o Fred. 🚨Importante: algumas de suas contas vão vencer até às 20:00."
                        notifications.first().notification.shouldBeTypeOf<ChatbotRawTemplatedNotification>()
                        with(notifications.first().notification as ChatbotRawTemplatedNotification) {
                            mobilePhone shouldBe user.id.value
                            accountId shouldBe user.accountId
                            configurationKey shouldBe ConfigurationKey("bills-coming-due")
                        }
                    }
                }

                it("deve retornar USER_NOT_ELIGIBLE para usuários inadimplentes com assinatura IN_APP") {
                    val overdueUser = user.copy(paymentStatus = AccountPaymentStatus.Overdue)
                    val details = BillComingDueRegularDetailsTO(
                        bills = billTOs,
                        walletName = "Test Wallet",
                    )

                    val result = builder.buildNotifications(
                        details,
                        overdueUser,
                        subscriptionType = SubscriptionType.IN_APP,
                    )

                    result.shouldBeTypeOf<Either.Left<NotificationError>>()
                    result.mapLeft { error ->
                        error shouldBe NotificationError.USER_NOT_ELIGIBLE
                    }
                }

                it("deve retornar NO_BILLS_TO_NOTIFY quando apenas contas de assinatura existem") {
                    val subscriptionBills = billTOs.map { it.copy(subscriptionFee = true) }
                    val details = BillComingDueRegularDetailsTO(
                        bills = subscriptionBills,
                        walletName = "Test Wallet",
                    )

                    val result = builder.buildNotifications(details, user)

                    result.shouldBeTypeOf<Either.Left<NotificationError>>()
                    result.mapLeft { error ->
                        error shouldBe NotificationError.NO_BILLS_TO_NOTIFY
                    }
                }

                it("deve retornar NO_BILLS_TO_NOTIFY quando lista de bills está vazia") {
                    val details = BillComingDueRegularDetailsTO(
                        bills = emptyList(),
                        walletName = "Test Wallet",
                    )

                    val result = builder.buildNotifications(details, user)

                    result.shouldBeTypeOf<Either.Left<NotificationError>>()
                    result.mapLeft { error ->
                        error shouldBe NotificationError.NO_BILLS_TO_NOTIFY
                    }
                }

                it("deve retornar USER_ALREADY_NOTIFIED quando não há contas novas e tempo insuficiente passou") {
                    val details = BillComingDueRegularDetailsTO(
                        bills = billTOs,
                        walletName = "Test Wallet",
                    )

                    val currentState = BillComingDueHistoryState(
                        user = user,
                        walletWithBills = WalletWithBills(
                            bills = bills,
                            totalWaitingApproval = 0,
                            walletId = WalletId("test"),
                            walletName = "test",
                            activeConsents = emptyList(),
                        ),
                        internalStateControl = InternalStateControl(
                            shouldSynchronizeBeforeCompletion = false,
                            billComingDueNotifiedAt = LocalDateTime.now().minusHours(1),
                        ),
                        subscription = null,
                        alreadyPromotedSweepingAccount = false,
                        balance = null,
                        contacts = emptyList(),
                    )

                    val result = builder.buildNotifications(details, user, currentState)

                    result.shouldBeTypeOf<Either.Left<NotificationError>>()
                    result.mapLeft { error ->
                        error shouldBe NotificationError.USER_ALREADY_NOTIFIED
                    }
                }
            }

            describe("quando details for RegisterCompletedTO") {
                it("deve construir notificação de cadastro concluído com sucesso") {
                    val configurationKey = ConfigurationKey("registration-completion")
                    val details = RegisterCompletedTO

                    val expectedNotification = ChatbotRawTemplatedNotification(
                        clientId = mockClientId,
                        configurationKey = configurationKey,
                        mobilePhone = user.id.value,
                        accountId = user.accountId,
                        arguments = emptyMap(),
                    )

                    every {
                        buildNotificationService.buildRegisterCompletedNotification(
                            accountId = user.accountId,
                            mobilePhone = user.id.value,
                            userName = user.name,
                        )
                    } returns expectedNotification

                    val result = builder.buildNotifications(details, user)

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 1
                        notifications.first().text shouldBe notificationContextTemplatesService.getRegisterCompletedMessage(user.name)
                        notifications.first().notification shouldBe expectedNotification
                    }
                }

                it("deve propagar exceção quando buildNotificationService falha") {
                    val details = RegisterCompletedTO

                    every {
                        buildNotificationService.buildRegisterCompletedNotification(
                            accountId = user.accountId,
                            mobilePhone = user.id.value,
                            userName = user.name,
                        )
                    } throws RuntimeException("Service error")

                    shouldThrow<RuntimeException> {
                        builder.buildNotifications(details, user)
                    }
                }
            }

            describe("quando details for GenericRawNotificationDetailsTO") {
                it("deve construir notificação genérica raw com sucesso") {
                    val details = GenericNotificationRawDetailsTO(
                        notificationId = "test-id",
                        receiver = ReceiverTO(msisdn = user.id.value),
                        configurationKey = "generic-template",
                        arguments = emptyMap(),
                        media = null,
                        clientId = "test-client",
                    )

                    every {
                        customNotificationService.getHistoryMessage(any<ChatbotRawTemplatedNotification>())
                    } returns "Generic notification message"

                    val result = builder.buildNotifications(details, user)

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 1
                        notifications.first().text shouldBe "Generic notification message"
                        // The notification object will be different due to UUID generation, so we check the properties instead
                        notifications.first().notification.shouldBeTypeOf<ChatbotRawTemplatedNotification>()
                        with(notifications.first().notification as ChatbotRawTemplatedNotification) {
                            mobilePhone shouldBe user.id.value
                            accountId shouldBe user.accountId
                            configurationKey shouldBe ConfigurationKey("generic-template")
                            arguments shouldBe emptyMap()
                            media shouldBe null
                        }
                    }
                }
            }

            describe("quando details for GenericNotificationDetailsTO") {
                it("deve construir notificação genérica com sucesso") {
                    val details = GenericNotificationDetailsTO(
                        notification = GenericNotificationContentTO(
                            notificationId = "test-id",
                            receiver = ReceiverTO(msisdn = user.id.value),
                            template = NotificationTemplate("generic_template"),
                            configurationKey = "generic-template",
                        ),
                    )

                    every {
                        customNotificationService.getHistoryMessage(any<ChatbotWhatsappTemplatedNotification>())
                    } returns "Generic templated notification message"

                    val result = builder.buildNotifications(details, user)

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 1
                        notifications.first().text shouldBe "Generic templated notification message"
                        // The notification object will be different due to UUID generation, so we check the properties instead
                        notifications.first().notification.shouldBeTypeOf<ChatbotWhatsappTemplatedNotification>()
                        with(notifications.first().notification as ChatbotWhatsappTemplatedNotification) {
                            mobilePhone shouldBe user.id.value
                            accountId shouldBe user.accountId
                            template shouldBe NotificationTemplate("generic_template")
                            configurationKey shouldBe "generic-template"
                        }
                    }
                }
            }

            describe("quando details for TestPixreminderDetailsTO") {
                it("deve retornar lista vazia para todos os tipos de lembrete") {
                    val details = TestPixreminderDetailsTO(
                        bills = billTOs,
                        reminderType = TestPixReminderType.NEXT_DAY,
                    )

                    val result = builder.buildNotifications(details, user)

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 0
                    }
                }
            }

            describe("quando details for WelcomeDetailsTO") {
                it("deve construir notificação de boas-vindas para onboarding single pix") {
                    val details = WelcomeDetailsTO(
                        type = WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX,
                    )

                    val configurationKey = ConfigurationKey("onboarding-single-pix-start")
                    val expectedNotification = ChatbotRawTemplatedNotification(
                        clientId = ClientId("unit-test"),
                        configurationKey = configurationKey,
                        mobilePhone = user.id.value,
                        accountId = user.accountId,
                        arguments = emptyMap(),
                    )

                    every {
                        buildNotificationService.buildOnboardingSinglePixStartNotification(
                            accountId = user.accountId,
                            mobilePhone = user.id.value,
                            userName = user.name,
                        )
                    } returns expectedNotification

                    val offerResult = BuildNotificationResult(
                        notification = ChatbotRawTemplatedNotification(
                            clientId = ClientId("unit-test"),
                            mobilePhone = user.id.value,
                            accountId = user.accountId,
                            configurationKey = configurationKey,
                            arguments = emptyMap(),
                        ),
                        historyMessage = "Welcome to Friday! Here's an example payment offer.",
                        shouldSend = true,
                    )

                    every {
                        customNotificationService.buildRawTemplateNotification(
                            user = user,
                            params = emptyList(),
                            notificationConfig = RawTemplateNotificationConfig(
                                configurationKey = configurationKey,
                                notificationType = KnownNotificationTypes.ONBOARDING_START,
                            ),
                        )
                    } returns offerResult

                    every {
                        templateInfoService.getHistoryMessage(any<ChatbotRawTemplatedNotification>())
                    } returns offerResult.historyMessage

                    val result = builder.buildNotifications(details, user)

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 3
                        notifications[0].role shouldBe ChatMessageRole.SYSTEM
                        notifications[0].text shouldBe onboardingSinglePixNotificationService.getSinglePixStartContextMessage()
                        notifications[0].notification shouldBe null
                        notifications[1].text shouldBe onboardingSinglePixNotificationService.getSinglePixStartMessage(user.name)
                        notifications[1].notification shouldBe expectedNotification
                        notifications[1].synchronous shouldBe true
                        notifications[2].text shouldBe "Welcome to Friday! Here's an example payment offer."
                        notifications[2].delaySeconds shouldBe 5
                        notifications[2].synchronous shouldBe true
                    }
                }
            }

            describe("quando details for OpenFinanceIncentiveDetailsTO") {
                it("deve construir notificação de incentivo DDA com sucesso") {
                    val bill = billView.toBillTO()
                    val details = OpenFinanceIncentiveDetailsTO(
                        type = OpenFinanceIncentiveType.DDA,
                        bill = bill,
                        userOptedOut = false,
                    )

                    val configurationKey = ConfigurationKey("promote-sweeping-account-dda")

                    val wallet = WalletWithBills(
                        bills = emptyList(),
                        totalWaitingApproval = 0,
                        walletId = WalletId("test"),
                        walletName = "test",
                        activeConsents = emptyList(),
                    )

                    val expectedNotification = ChatbotRawTemplatedNotification(
                        clientId = ClientId("unit-test"),
                        mobilePhone = user.id.value,
                        accountId = user.accountId,
                        configurationKey = configurationKey,
                        arguments = emptyMap(),
                    )

                    every {
                        buildNotificationService.buildPromoteSweepingAccountDDANotification(
                            accountId = user.accountId,
                            mobilePhone = user.id.value,
                            billInfo = any(),
                        )
                    } returns expectedNotification

                    val result = builder.buildNotifications(details, user, walletProvider = { wallet })

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 1
                        notifications.first().notification shouldBe expectedNotification
                        notifications.first().synchronous shouldBe true
                    }
                }

                it("deve construir notificação de incentivo cash-in com sucesso") {
                    val details = OpenFinanceIncentiveDetailsTO(
                        type = OpenFinanceIncentiveType.CASH_IN,
                        userOptedOut = false,
                    )

                    val wallet = WalletWithBills(
                        bills = emptyList(),
                        totalWaitingApproval = 0,
                        walletId = WalletId("test"),
                        walletName = "test",
                        activeConsents = emptyList(),
                    )

                    val configurationKey = ConfigurationKey("promote-sweeping-account-cash-in")

                    val expectedNotification = ChatbotRawTemplatedNotification(
                        clientId = ClientId("unit-test"),
                        mobilePhone = user.id.value,
                        accountId = user.accountId,
                        configurationKey = configurationKey,
                        arguments = emptyMap(),
                    )

                    every {
                        buildNotificationService.buildPromoteSweepingAccountCashInNotification(
                            accountId = user.accountId,
                            mobilePhone = user.id.value,
                        )
                    } returns expectedNotification

                    val result = builder.buildNotifications(details, user, walletProvider = { wallet })

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 1
                        notifications.first().notification shouldBe expectedNotification
                        notifications.first().synchronous shouldBe true
                    }
                }

                it("deve retornar lista vazia quando incentivo está desabilitado") {
                    val mockFeatures = mockk<FeaturesConfig> {
                        every { openFinanceIncentive } returns false
                    }
                    every { mockTenantConfiguration.features } returns mockFeatures

                    val details = OpenFinanceIncentiveDetailsTO(
                        type = OpenFinanceIncentiveType.CASH_IN,
                        userOptedOut = false,
                    )

                    val wallet = WalletWithBills(
                        bills = emptyList(),
                        totalWaitingApproval = 0,
                        walletId = WalletId("test"),
                        walletName = "test",
                        activeConsents = emptyList(),
                    )

                    val result = builder.buildNotifications(details, user, walletProvider = { wallet })

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 0
                    }
                }

                it("deve respeitar configurações específicas do tenant") {
                    val customTenantConfig = mockk<TenantConfiguration> {
                        every { clientId } returns ClientId("custom-tenant")
                        every { messages } returns mockNotificationConfigs
                        every { appBaseUrl } returns "https://custom.app.url"
                        every { features } returns mockk<FeaturesConfig> {
                            every { openFinanceIncentive } returns false
                        }
                    }
                    every { tenantService.getConfiguration() } returns customTenantConfig

                    val details = OpenFinanceIncentiveDetailsTO(
                        type = OpenFinanceIncentiveType.CASH_IN,
                        userOptedOut = false,
                    )

                    val wallet = WalletWithBills(
                        bills = emptyList(),
                        totalWaitingApproval = 0,
                        walletId = WalletId("test"),
                        walletName = "test",
                        activeConsents = emptyList(),
                    )

                    val result = builder.buildNotifications(details, user, walletProvider = { wallet })

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 0
                    }
                }

                it("deve retornar lista vazia quando usuário tem conta sweeping") {
                    val details = OpenFinanceIncentiveDetailsTO(
                        type = OpenFinanceIncentiveType.CASH_IN,
                        userOptedOut = false,
                    )

                    val wallet = WalletWithBills(
                        bills = emptyList(),
                        totalWaitingApproval = 0,
                        walletId = WalletId("test"),
                        walletName = "test",
                        activeConsents = listOf(mockk()),
                    )

                    val result = builder.buildNotifications(details, user, walletProvider = { wallet })

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 0
                    }
                }

                it("deve retornar lista vazia quando usuário optou por sair") {
                    val details = OpenFinanceIncentiveDetailsTO(
                        type = OpenFinanceIncentiveType.CASH_IN,
                        userOptedOut = true,
                    )

                    val wallet = WalletWithBills(
                        bills = emptyList(),
                        totalWaitingApproval = 0,
                        walletId = WalletId("test"),
                        walletName = "test",
                        activeConsents = emptyList(),
                    )

                    val result = builder.buildNotifications(details, user, walletProvider = { wallet })

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                    result.map { notifications ->
                        notifications.size shouldBe 0
                    }
                }

                it("deve lançar exceção para incentivo DDA sem conta") {
                    val details = OpenFinanceIncentiveDetailsTO(
                        type = OpenFinanceIncentiveType.DDA,
                        bill = null,
                        userOptedOut = false,
                    )

                    val wallet = WalletWithBills(
                        bills = emptyList(),
                        totalWaitingApproval = 0,
                        walletId = WalletId("test"),
                        walletName = "test",
                        activeConsents = emptyList(),
                    )

                    // Should throw IllegalArgumentException when DDA incentive is created without a bill
                    val exception: IllegalArgumentException = shouldThrow {
                        builder.buildNotifications(details, user, walletProvider = { wallet })
                    }

                    exception.message shouldBe "dda incentive without bill"
                }

                it("deve processar grandes volumes de bills eficientemente") {
                    val largeBillList = (1..100).map { index ->
                        billView.copy(
                            billId = BillId(index.toString()),
                            billDescription = "Bill $index",
                            amount = index.toLong(),
                        ).toBillTO()
                    }
                    val details = BillComingDueRegularDetailsTO(
                        bills = largeBillList,
                        walletName = "Test Wallet",
                    )

                    val currentState = BillComingDueHistoryState(
                        user = user,
                        walletWithBills = WalletWithBills(
                            bills = emptyList(),
                            totalWaitingApproval = 0,
                            walletId = WalletId("test"),
                            walletName = "test",
                            activeConsents = emptyList(),
                        ),
                        internalStateControl = InternalStateControl(
                            shouldSynchronizeBeforeCompletion = false,
                            billComingDueNotifiedAt = null,
                        ),
                        subscription = null,
                        alreadyPromotedSweepingAccount = false,
                        balance = null,
                        contacts = emptyList(),
                    )

                    val customNotificationResult = BuildNotificationResult(
                        notification = ChatbotRawTemplatedNotification(
                            clientId = mockClientId,
                            configurationKey = ConfigurationKey("chatbot-ai-notify-bills-coming-due"),
                            mobilePhone = user.id.value,
                            accountId = user.accountId,
                            arguments = mapOf("USER_NAME" to "Test User"),
                        ),
                        historyMessage = "Performance test message",
                        shouldSend = true,
                    )

                    every {
                        customNotificationService.buildBillsComingDueCustom(
                            user = user,
                            bills = any(),
                        )
                    } returns customNotificationResult

                    val result = builder.buildNotifications(details, user, currentState)

                    result.shouldBeTypeOf<Either.Right<List<ParsedNotificationTO>>>()
                }
            }
        }
    },
)