package ai.chatbot.adapters.dynamodb

import DynamoDBUtils.setupDynamoDB
import ai.chatbot.app.DYNAMOB_TEST_TABLE_NAME
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.ME_POUPE_ENV
import ai.chatbot.app.prompt.PromptType
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.context.ApplicationContext
import io.mockk.every
import io.mockk.mockk
import java.util.*

class StagingPromptTenantRepositoryTest : DescribeSpec() {
    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val mockedApplicationContext =
        mockk<ApplicationContext> {
            every { getProperty(any<String>(), String::class.java) } returns Optional.of(DYNAMOB_TEST_TABLE_NAME)
        }

    private val repository = StagingPromptTenantRepository(dynamoDbDAO = PromptTenantDynamoDAO(cli = dynamoDbEnhancedClient, mockedApplicationContext))

    init {
        val msisdn = "5521999999"

        describe("testar o banco") {
            it("deve salvar e ler corretamente") {
                repository.save(msisdn, FRIDAY_ENV)

                val promptTenant = repository.find(msisdn)
                promptTenant shouldNotBe null
                promptTenant!!.msisdn shouldBe msisdn
                promptTenant!!.tenant shouldBe FRIDAY_ENV
                promptTenant.type shouldBe PromptType.DEFAULT
                promptTenant.hasInvestmentCampaign shouldBe false
            }

            it("deve atualizar tenant de msisdn que ja existe") {
                repository.save(msisdn, FRIDAY_ENV)
                repository.save(msisdn, ME_POUPE_ENV)

                val promptTenant = repository.find(msisdn)
                promptTenant shouldNotBe null
                promptTenant!!.msisdn shouldBe msisdn
                promptTenant!!.tenant shouldBe ME_POUPE_ENV
            }
        }
    }
}