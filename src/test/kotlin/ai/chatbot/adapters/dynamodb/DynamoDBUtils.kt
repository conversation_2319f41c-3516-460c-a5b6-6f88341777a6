import ai.chatbot.adapters.dynamodb.CHAT_BOT_PARTITION_KEY
import ai.chatbot.adapters.dynamodb.CHAT_BOT_RANGE_KEY
import ai.chatbot.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.chatbot.adapters.dynamodb.LocalDbCreationRule
import ai.chatbot.app.DYNAMOB_TEST_TABLE_NAME
import java.net.URI
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.AttributeDefinition
import software.amazon.awssdk.services.dynamodb.model.CreateTableRequest
import software.amazon.awssdk.services.dynamodb.model.CreateTableResponse
import software.amazon.awssdk.services.dynamodb.model.DeleteTableRequest
import software.amazon.awssdk.services.dynamodb.model.DescribeTableRequest
import software.amazon.awssdk.services.dynamodb.model.GlobalSecondaryIndex
import software.amazon.awssdk.services.dynamodb.model.KeySchemaElement
import software.amazon.awssdk.services.dynamodb.model.KeyType
import software.amazon.awssdk.services.dynamodb.model.Projection
import software.amazon.awssdk.services.dynamodb.model.ProjectionType
import software.amazon.awssdk.services.dynamodb.model.ProvisionedThroughput
import software.amazon.awssdk.services.dynamodb.model.ScalarAttributeType
import software.amazon.awssdk.services.dynamodb.model.TableStatus

object DynamoDBUtils {
    fun setupDynamoDB(): DynamoDbEnhancedClient {
        LocalDbCreationRule.startServer()
        val dynamoDbClient = getDynamoDbClient()

        createChatHistoryTable(dynamoDbClient)

        return DynamoDbEnhancedClient
            .builder()
            .dynamoDbClient(dynamoDbClient).build()
    }

    fun getDynamoDBAsync(port: Int = 8000): DynamoDbEnhancedAsyncClient {
        val cli =
            DynamoDbAsyncClient
                .builder()
                .endpointOverride(URI.create("http://localhost:$port"))
                .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create("abc", "qwe")))
                .region(Region.US_EAST_1)
                .build()

        return DynamoDbEnhancedAsyncClient
            .builder()
            .dynamoDbClient(cli).build()
    }

    fun getDynamoDbClient(): DynamoDbClient =
        DynamoDbClient
            .builder()
            .endpointOverride(URI.create("http://localhost:8000"))
            .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create("abc", "qwe")))
            .region(Region.US_EAST_1)
            .build()

    fun createChatHistoryTable(
        dynamoDbClient: DynamoDbClient? = null,
        dynamoDbAsyncClient: DynamoDbAsyncClient? = null,
    ) {
        createTable(
            dynamoDbClient,
            dynamoDbAsyncClient,
            DYNAMOB_TEST_TABLE_NAME,
            CHAT_BOT_PARTITION_KEY,
            CHAT_BOT_RANGE_KEY,
            "GSIndex1PartitionKey",
            "GSIndex1RangeKey",
            null,
            null,
            null,
            null,
        )
    }

    private fun createTable(
        dynamoDbClient: DynamoDbClient? = null,
        dynamoDbAsyncClient: DynamoDbAsyncClient? = null,
        tableName: String,
        hashKeyName: String,
        rangeKeyName: String,
        gsi1HashKeyName: String,
        gsi1RangeKeyName: String,
        gsi2HashKeyName: String? = null,
        gsi2RangeKeyName: String? = null,
        gsi3HashKeyName: String? = null,
        gsi3RangeKeyName: String? = null,
    ): CreateTableResponse {
        val attributeDefinitions =
            mutableListOf(
                AttributeDefinition.builder().attributeName(hashKeyName).attributeType(ScalarAttributeType.S).build(),
                AttributeDefinition.builder().attributeName(rangeKeyName).attributeType(ScalarAttributeType.S).build(),
                AttributeDefinition.builder().attributeName(gsi1HashKeyName).attributeType(ScalarAttributeType.S).build(),
                AttributeDefinition.builder().attributeName(gsi1RangeKeyName).attributeType(ScalarAttributeType.S).build(),
            )
        gsi2HashKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build(),
            )
        }
        gsi2RangeKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build(),
            )
        }
        gsi3HashKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build(),
            )
        }
        gsi3RangeKeyName?.let {
            attributeDefinitions.add(
                AttributeDefinition.builder().attributeName(it).attributeType(ScalarAttributeType.S).build(),
            )
        }
        return createTable(
            dynamoDbClient,
            dynamoDbAsyncClient,
            tableName,
            hashKeyName,
            rangeKeyName,
            gsi1HashKeyName,
            gsi1RangeKeyName,
            gsi2HashKeyName,
            gsi2RangeKeyName,
            gsi3HashKeyName,
            gsi3RangeKeyName,
            attributeDefinitions,
        )
    }

    private fun createTable(
        dynamoDbClient: DynamoDbClient? = null,
        dynamoDbAsyncClient: DynamoDbAsyncClient? = null,
        tableName: String,
        hashKeyName: String,
        rangeKeyName: String,
        gsi1HashKeyName: String,
        gsi1RangeKeyName: String,
        gsi2HashKeyName: String?,
        gsi2RangeKeyName: String?,
        gsi3HashKeyName: String?,
        gsi3RangeKeyName: String?,
        attributeDefinitions: List<AttributeDefinition>,
    ): CreateTableResponse {
        try {
            val deleteTableRequest = DeleteTableRequest.builder().tableName(tableName).build()
            if (dynamoDbAsyncClient == null) {
                dynamoDbClient!!.deleteTable(deleteTableRequest)
            } else {
                dynamoDbAsyncClient.deleteTable(deleteTableRequest).get()
            }
        } catch (_: Exception) {
        }
        val ks =
            listOf(
                KeySchemaElement.builder().attributeName(hashKeyName).keyType(KeyType.HASH).build(),
                KeySchemaElement.builder().attributeName(rangeKeyName).keyType(KeyType.RANGE).build(),
            )
        val globalSecondaryIndex: MutableList<GlobalSecondaryIndex> = ArrayList()
        addIndex(globalSecondaryIndex, GlobalSecondaryIndexNames.GSIndex1, gsi1HashKeyName, gsi1RangeKeyName)
        addIndex(globalSecondaryIndex, GlobalSecondaryIndexNames.GSIndex2, gsi2HashKeyName, gsi2RangeKeyName)
        addIndex(globalSecondaryIndex, GlobalSecondaryIndexNames.GSIndex3, gsi3HashKeyName, gsi3RangeKeyName)
        val provisionedThroughput =
            ProvisionedThroughput.builder().readCapacityUnits(1000L).writeCapacityUnits(1000L).build()
        val request =
            CreateTableRequest.builder()
                .tableName(tableName)
                .attributeDefinitions(attributeDefinitions)
                .keySchema(ks)
                .globalSecondaryIndexes(globalSecondaryIndex)
                .provisionedThroughput(provisionedThroughput).build()

        val result =
            if (dynamoDbAsyncClient == null) {
                dynamoDbClient!!.createTable(request)
            } else {
                dynamoDbAsyncClient.createTable(request).get()
            }

        val describeTableRequest = DescribeTableRequest.builder().tableName(tableName).build()

        val describeTableResponse =
            if (dynamoDbAsyncClient == null) {
                dynamoDbClient!!.describeTable(describeTableRequest)
            } else {
                dynamoDbAsyncClient.describeTable(describeTableRequest).get()
            }

        while (describeTableResponse.table().tableStatus() != TableStatus.ACTIVE) {
            // wait til table is active or otherwise will not find table
        }
        return result
    }

    private fun addIndex(
        globalSecondaryIndex: MutableList<GlobalSecondaryIndex>,
        index: GlobalSecondaryIndexNames,
        partitionKeyName: String?,
        rangeKeyName: String?,
    ) {
        if (partitionKeyName != null) {
            val gsi =
                listOf(
                    KeySchemaElement.builder().attributeName(partitionKeyName).keyType(KeyType.HASH).build(),
                    KeySchemaElement.builder().attributeName(rangeKeyName).keyType(KeyType.RANGE).build(),
                )
            globalSecondaryIndex.add(
                GlobalSecondaryIndex.builder()
                    .keySchema(gsi)
                    .indexName(index.name)
                    .projection(Projection.builder().projectionType(ProjectionType.ALL).build())
                    .provisionedThroughput(
                        ProvisionedThroughput.builder().readCapacityUnits(1000L).writeCapacityUnits(1000L).build(),
                    ).build(),
            )
        }
    }
}