package ai.chatbot.adapters.dynamodb

import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.local.embedded.DynamoDBEmbedded
import com.amazonaws.services.dynamodbv2.local.main.ServerRunner

class LocalDbCreationRule {
    companion object {
        @JvmStatic
        lateinit var dynamoDB: AmazonDynamoDB

        @JvmStatic
        fun getDynamoDBProxyServer(): AmazonDynamoDB {
            if (!this::dynamoDB.isInitialized) {
                System.setProperty("sqlite4java.library.path", "native-libs")
                val port = "8000"
                val server = ServerRunner.createServerFromCommandLineArgs(arrayOf("-inMemory", "-port", port))
                server!!.start()
                dynamoDB = DynamoDBEmbedded.create().amazonDynamoDB()
            }

            return dynamoDB
        }

        @JvmStatic
        fun startServer() {
            if (!this::dynamoDB.isInitialized) {
                System.setProperty("sqlite4java.library.path", "native-libs")
                val port = "8000"
                val server = ServerRunner.createServerFromCommandLineArgs(arrayOf("-inMemory", "-port", port))
                server!!.start()
                dynamoDB = DynamoDBEmbedded.create().amazonDynamoDB()
            }
        }
    }
}