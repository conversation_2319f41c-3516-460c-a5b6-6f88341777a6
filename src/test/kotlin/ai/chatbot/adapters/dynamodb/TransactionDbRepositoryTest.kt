package ai.chatbot.adapters.dynamodb

import DynamoDBUtils.setupDynamoDB
import ai.chatbot.adapters.api.TransactionPaymentStatus
import ai.chatbot.app.DYNAMOB_TEST_TABLE_NAME
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.transaction.PixTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.transaction.TransactionStatus
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.utils.timestampFormatWithBrazilTimeZone
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.date.shouldBeAfter
import io.kotest.matchers.shouldBe
import io.micronaut.context.ApplicationContext
import io.mockk.every
import io.mockk.mockk
import java.util.Optional

class TransactionDbRepositoryTest : DescribeSpec() {
    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val mockedApplicationContext =
        mockk<ApplicationContext> {
            every { getProperty(any<String>(), String::class.java) } returns Optional.of(DYNAMOB_TEST_TABLE_NAME)
        }

    private val tenantService = mockk<TenantService> {
        every { getTenantName() } returns "friday"
    }

    private val repository = TransactionDbRepository(dynamoDbDAO = TransactionDynamoDAO(cli = dynamoDbEnhancedClient, mockedApplicationContext), tenantService)

    init {
        describe("quando existe uma transacao") {
            val transaction =
                Transaction(
                    id = TransactionId(),
                    groupId = TransactionGroupId(),
                    userId = UserId(value = "userId"),
                    walletId = WalletId(value = "walletId"),
                    status = TransactionStatus.ACTIVE,
                    paymentStatus = TransactionPaymentStatus.UNKNOWN,
                    details =
                    PixTransactionDetails(
                        amount = 1,
                        pixKey = PixKey(value = "<EMAIL>", type = PixKeyType.EMAIL),
                        recipientName = "Recipient",
                        recipientDocument = "***.456.789-**",
                        recipientInstitution = "Banco teste",
                        sweepingAmount = null,
                        sweepingParticipantId = null,
                        qrCode = null,
                    ),
                )

            repository.save(transaction)

            it("deve encontrar pelo id") {
                with(repository.find(transaction.id)!!) {
                    this.id shouldBe transaction.id
                    this.userId shouldBe transaction.userId
                    this.walletId shouldBe transaction.walletId
                    this.status shouldBe transaction.status
                    this.details shouldBe transaction.details
                    this.createdAt.format(timestampFormatWithBrazilTimeZone) shouldBe transaction.createdAt.format(timestampFormatWithBrazilTimeZone)
                    this.updatedAt shouldBeAfter transaction.updatedAt
                }
            }

            it("deve encontrar pelo userId") {
                with(repository.find(transaction.userId).single()) {
                    this.id shouldBe transaction.id
                    this.userId shouldBe transaction.userId
                    this.walletId shouldBe transaction.walletId
                    this.status shouldBe transaction.status
                    this.details shouldBe transaction.details
                    this.createdAt.format(timestampFormatWithBrazilTimeZone) shouldBe transaction.createdAt.format(timestampFormatWithBrazilTimeZone)
                    this.updatedAt shouldBeAfter transaction.updatedAt
                }
            }

            it("deve encontrar pelo userId e status") {
                with(repository.find(transaction.userId, transaction.status).single()) {
                    this.id shouldBe transaction.id
                    this.userId shouldBe transaction.userId
                    this.walletId shouldBe transaction.walletId
                    this.status shouldBe transaction.status
                    this.details shouldBe transaction.details
                    this.createdAt.format(timestampFormatWithBrazilTimeZone) shouldBe transaction.createdAt.format(timestampFormatWithBrazilTimeZone)
                    this.updatedAt shouldBeAfter transaction.updatedAt
                }
            }
        }
    }
}