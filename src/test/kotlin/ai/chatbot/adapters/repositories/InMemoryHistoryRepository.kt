package ai.chatbot.adapters.repositories

import ai.chatbot.adapters.dynamodb.toChatMessageWrapper
import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.classification.ClassificationResult
import ai.chatbot.app.conversation.ChatMessageWrapper
import ai.chatbot.app.conversation.CompletionMessage
import ai.chatbot.app.conversation.ConversationHistory
import ai.chatbot.app.conversation.HistoryStateType
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import io.micronaut.context.annotation.Requires
import io.micronaut.context.annotation.Secondary
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime

@Singleton
@Secondary
@Requires(env = ["test"])
class InMemoryHistoryRepository : HistoryRepository {
    private val history = HashMap<String, ConversationHistory>()

    override fun saveUserMessage(
        userId: UserId,
        message: String,
    ) {
        saveChatMessage(
            userId,
            ChatMessageWrapper(
                MessageType.USER,
                message,
                null,
                timestamp = ZonedDateTime.now(),
            ),
        )
    }

    override fun saveUserReaction(
        userId: UserId,
        reaction: String,
    ) {
        saveChatMessage(
            userId,
            ChatMessageWrapper(
                MessageType.REACTION,
                reaction,
                null,
                timestamp = ZonedDateTime.now(),
            ),
        )
    }

    override fun saveAssistantMessage(
        userId: UserId,
        message: String,
    ) {
        saveChatMessage(userId, ChatMessageWrapper(type = MessageType.ASSISTANT, message = message, completionMessage = null, ZonedDateTime.now()))
    }

    override fun saveAssistantMessage(
        userId: UserId,
        completionMessage: CompletionMessage,
    ) {
        saveChatMessage(userId, completionMessage.toChatMessageWrapper())
    }

    override fun saveSystemMessage(
        userId: UserId,
        message: String,
    ) {
        saveChatMessage(
            userId,
            ChatMessageWrapper(
                MessageType.SYSTEM,
                message,
                null,
                ZonedDateTime.now(),
            ),
        )
    }

    override fun saveClassification(userId: UserId, date: LocalDate, classificationResult: ClassificationResult) {
        val userHistory = findLatest(userId)
        history[userId.value] = userHistory.copy(classification = classificationResult)
    }

    override fun findByDate(date: LocalDate): List<ConversationHistory> {
        TODO("Not yet implemented")
    }

    override fun clearConversationHistory(
        userId: UserId,
        template: HistoryStateType,
        date: LocalDate,
    ) {
        history.remove(userId.value)
    }

    override fun findLatest(userId: UserId): ConversationHistory {
        return history[userId.value] ?: throw IllegalStateException("User history not found")
    }

    override fun find(
        userId: UserId,
        date: LocalDate,
        template: HistoryStateType,
    ): ConversationHistory {
        return history[userId.value] ?: throw IllegalStateException("User history not found")
    }

    override fun create(
        userId: UserId,
        historyStateType: HistoryStateType,
        initialUserMessage: String?,
    ) {
        history[userId.value] =
            ConversationHistory(
                userId = userId,
                createdAt = getLocalDate(),
                messages =
                initialUserMessage?.let {
                    listOf(
                        ChatMessageWrapper(
                            MessageType.USER,
                            it,
                            null,
                            ZonedDateTime.now(),
                        ),
                    )
                } ?: emptyList(),
            )
    }

    private fun saveChatMessage(
        userId: UserId,
        chatMessageWrapper: ChatMessageWrapper,
    ) {
        val userHistory = findLatest(userId)
        history[userId.value] = userHistory.copy(messages = userHistory.messages + chatMessageWrapper)
    }
}