package ai.chatbot.adapters.media

import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainInOrder
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe

class ImageQRCodeProcessorTest : DescribeSpec() {
    private val processor = ImageQRCodeProcessor()

    init {
        describe("ao processar uma imagem") {
            it("deve encontrar um QR code PIX") {
                val imageBytes = loadResourceAsBytes("/media/qr_code_single.jpeg")
                val expectedQRCode = "00020126580014BR.GOV.BCB.PIX013634377405-c606-4da0-b944-00503596d12c520400005303986540525.505802BR5924Marlon da Costa Moncores6009SAO PAULO61080540900062140510testepixqr63044A81"

                val result = processor.processImage(imageBytes)
                result.shouldNotBeEmpty()
                result.size shouldBe 1
                result.first() shouldBe expectedQRCode
            }

            it("deve encontrar múltiplos QR codes") {
                val imageBytes = loadResourceAsBytes("/media/qr_code_multi_type.png")
                val expectedQRCodes = listOf(
                    "www.google.com",
                    "00020126580014BR.GOV.BCB.PIX013634377405-c606-4da0-b944-00503596d12c520400005303986540525.505802BR5924Marlon da Costa Moncores6009SAO PAULO61080540900062140510testepixqr63044A81",
                    "00020126580014BR.GOV.BCB.PIX0136547b7870-2442-4bad-ba00-8f9c845421f952040000530398654040.015802BR5920RAFAEL HAERTEL PERES6007PELOTAS622605221HNOBKbErSuKI69IoEfNgc6304E921",
                )

                val result = processor.processImage(imageBytes)
                result.shouldNotBeEmpty()
                result.size shouldBe 3
                result shouldContainInOrder expectedQRCodes
            }

            it("deve retornar lista vazia quando a imagem não contém QR code") {
                val imageBytes = loadResourceAsBytes("/media/no_qr_code.png")
                val result = processor.processImage(imageBytes)
                result.shouldBeEmpty()
            }

            it("deve retornar lista vazia quando a imagem for inválida") {
                val invalidImageBytes = ByteArray(100) { 0 }
                val result = processor.processImage(invalidImageBytes)
                result.shouldBeEmpty()
            }

            it("deve retornar lista vazia quando a imagem estiver vazia") {
                val emptyImageBytes = ByteArray(0)
                val result = processor.processImage(emptyImageBytes)
                result.shouldBeEmpty()
            }
        }
    }

    private fun loadResourceAsBytes(resourcePath: String): ByteArray {
        return javaClass.getResourceAsStream(resourcePath)?.use { it.readAllBytes() }
            ?: throw IllegalStateException("Resource not found: $resourcePath")
    }
}