package ai.chatbot.adapters.media

import ai.chatbot.app.media.BoletoInfo
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe

class DocumentBarCodeProcessorTest : DescribeSpec() {
    private val processor = DocumentBarCodeProcessor()

    val pdfText =
        "Pagável preferencialmente na Rede Bradesco e Bradesco Expresso\n" +
            "23790448099134353296518014850004399360000048159\n" +
            "Ficha de Compensação Autenticação Mecânica\n" +
            "23790448099134353296519014850002599880000048159\n" +
            "Ficha de Compensação Autenticação Mecânica\n" +
            "Pagável preferencialmente na Rede Bradesco e Bradesco Expresso\n" +
            "23790448099134353296520014850000110160000048159\n" +
            "Ficha de Compensação Autenticação Mecânica\n" +
            "23790448099134353296521014850008910470000048159\n" +
            "Cotação válida somente após liquidação do cheque."

    val pdfFormattedText =
        "Pagável preferencialmente na Rede Bradesco e Bradesco Expresso\n" +
            "23790.44809 91343.532965 18014.850004 3 99360000048159\n" +
            "Ficha de Compensação Autenticação Mecânica\n" +
            "23790.44809 91343.532965 19014.850002 5 99880000048159\n" +
            "Ficha de Compensação Autenticação Mecânica\n" +
            "Pagável preferencialmente na Rede Bradesco e Bradesco Expresso\n" +
            "23790.44809 91343.532965 20014.850000 1 10160000048159\n" +
            "Ficha de Compensação Autenticação Mecânica\n" +
            "23790.44809 91343.532965 21014.850008 9 10470000048159\n" +
            "Cotação válida somente após liquidação do cheque."

    val pdfConcessionariaText =
        "CNPJ:33.938-119/0002-40. Inscrição Estadual: 83.409-738 - Inscrição Municipal: 00.578.495\n" +
            "COMPANHIA DISTRIBUIDORA DE GÁS DO RIO DE JANEIRO - CEG – Rua São Cristóvão, 1200 - São Cristóvão - CEP: 20.940-000 - Rio de Janeiro - RJ.\n" +
            "83600000000-7 70970056000-2 00009007147-3 39042025150-1\n\n" +
            "836000000007709700560002000090071473390420251501"

    init {
        describe("ao processar um PDF com códigos de barras no texto") {
            it("deve encontrar códigos de barras sem formatação") {
                val result = processor.processText(pdfText)
                result.shouldNotBeEmpty()
                result.size shouldBe 4
                result shouldContainExactly listOf(
                    BoletoInfo(codigo = "23790448099134353296518014850004399360000048159"),
                    BoletoInfo(codigo = "23790448099134353296519014850002599880000048159"),
                    BoletoInfo(codigo = "23790448099134353296520014850000110160000048159"),
                    BoletoInfo(codigo = "23790448099134353296521014850008910470000048159"),
                )
            }

            it("deve encontrar códigos de barras com formatação") {
                val result = processor.processText(pdfFormattedText)
                result.shouldNotBeEmpty()
                result.size shouldBe 4
                result shouldContainExactly listOf(
                    BoletoInfo(codigo = "23790448099134353296518014850004399360000048159"),
                    BoletoInfo(codigo = "23790448099134353296519014850002599880000048159"),
                    BoletoInfo(codigo = "23790448099134353296520014850000110160000048159"),
                    BoletoInfo(codigo = "23790448099134353296521014850008910470000048159"),
                )
            }

            it("deve aceitar códigos de concessionária") {
                val result = processor.processText(pdfConcessionariaText)
                result.shouldNotBeEmpty()
                result shouldContainExactly listOf(
                    BoletoInfo(codigo = "836000000007709700560002000090071473390420251501"),
                )
            }

            it("deve retornar lista vazia quando não encontrar códigos") {
                val result = processor.processText("pdf text")
                result.shouldBeEmpty()
            }
        }
    }
}