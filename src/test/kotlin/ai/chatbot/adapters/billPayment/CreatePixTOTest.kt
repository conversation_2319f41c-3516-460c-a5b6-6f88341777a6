package ai.chatbot.adapters.billPayment

import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.withGivenDateTime
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import java.time.ZoneId
import java.time.ZonedDateTime

class CreatePixTOTest : DescribeSpec() {

    init {
        describe("ao agendar pix depois das 21h") {
            it("deve criar request com a data correta") {
                val createPixTO = withGivenDateTime(ZonedDateTime.of(2025, 4, 2, 21, 33, 0, 0, ZoneId.of("America/Sao_Paulo"))) {
                    CreatePixTO(
                        recipient = RequestPixRecipientTO(
                            id = "",
                            name = "",
                            document = "",
                            documentType = "",
                            pixKey = PixKeyRequestTO("", PixKeyType.PHONE),
                            qrCode = "",
                        ),
                        amount = 10_00L,
                        description = "",
                        transactionId = "",
                        sweepingRequest = null,
                        retryTransaction = false,
                    )
                }

                createPixTO.dueDate shouldBe "2025-04-02"
            }
        }
    }
}