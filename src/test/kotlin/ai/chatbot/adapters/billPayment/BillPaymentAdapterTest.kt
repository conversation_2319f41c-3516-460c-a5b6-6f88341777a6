package ai.chatbot.adapters.billPayment

import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.BillType
import ai.chatbot.app.bill.Recipient
import ai.chatbot.app.bill.ScheduledInfo
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.shouldBe
import java.time.LocalDate
import java.time.format.DateTimeFormatter

class BillPaymentAdapterTest : DescribeSpec() {

    init {
        describe("toBillViews") {

            context("quando a lista está vazia") {
                it("deve retornar lista vazia") {
                    val billsTO = emptyList<BillTO>()

                    val result = billsTO.toBillViews()

                    result.shouldBeEmpty()
                }
            }

            context("quando há uma bill na lista") {
                it("deve retornar uma BillView com externalBillId 1") {
                    val dueDate = "2024-03-15"
                    val billTO = BillTO(
                        id = "BILL-123",
                        assignor = "Test Assignor",
                        billRecipient = ResponseRecipientTO(name = "Test Recipient"),
                        description = "Test Description",
                        amount = 10000,
                        discount = 0,
                        interest = 0,
                        fine = 0,
                        amountTotal = 10000,
                        billType = BillType.FICHA_COMPENSACAO,
                        paymentLimitTime = "23:59",
                        dueDate = dueDate,
                        subscriptionFee = false,
                        status = "ACTIVE",
                    )
                    val billsTO = listOf(billTO)

                    val result = billsTO.toBillViews()

                    result.size shouldBe 1
                    result[0].billId shouldBe BillId("BILL-123")
                    result[0].externalBillId shouldBe 1
                    result[0].assignor shouldBe "Test Assignor"
                    result[0].recipient shouldBe Recipient(name = "Test Recipient")
                    result[0].billDescription shouldBe "Test Description"
                    result[0].amount shouldBe 10000
                    result[0].amountTotal shouldBe 10000
                    result[0].billType shouldBe BillType.FICHA_COMPENSACAO
                    result[0].dueDate shouldBe LocalDate.parse(dueDate, DateTimeFormatter.ISO_LOCAL_DATE)
                    result[0].status shouldBe BillStatus.ACTIVE
                }
            }

            context("quando há múltiplas bills na lista") {
                it("deve ordenar por data de vencimento e atribuir externalBillId sequencial") {
                    // arrange
                    val billTO1 = BillTO(
                        id = "BILL-1",
                        assignor = "Assignor 1",
                        billRecipient = null,
                        description = "Description 1",
                        amount = 5000,
                        discount = 0,
                        interest = 0,
                        fine = 0,
                        amountTotal = 5000,
                        billType = BillType.PIX,
                        paymentLimitTime = "23:59",
                        dueDate = "2024-03-20", // data mais tardia
                        subscriptionFee = false,
                        status = "ACTIVE",
                    )

                    val billTO2 = BillTO(
                        id = "BILL-2",
                        assignor = "Assignor 2",
                        billRecipient = ResponseRecipientTO(name = "Recipient 2"),
                        description = "Description 2",
                        amount = 3000,
                        discount = 0,
                        interest = 0,
                        fine = 0,
                        amountTotal = 3000,
                        billType = BillType.FICHA_COMPENSACAO,
                        paymentLimitTime = "20:00",
                        dueDate = "2024-03-15", // data mais cedo
                        subscriptionFee = false,
                        status = "ACTIVE",
                    )

                    val billTO3 = BillTO(
                        id = "BILL-3",
                        assignor = "Assignor 3",
                        billRecipient = ResponseRecipientTO(name = "Recipient 3"),
                        description = "Description 3",
                        amount = 7500,
                        discount = 100,
                        interest = 50,
                        fine = 25,
                        amountTotal = 7475,
                        billType = BillType.PIX,
                        paymentLimitTime = "18:00",
                        dueDate = "2024-03-18", // data do meio
                        subscriptionFee = true,
                        status = "PROCESSING",
                    )

                    val billsTO = listOf(billTO1, billTO2, billTO3)

                    // act
                    val result = billsTO.toBillViews()

                    // assert
                    result.size shouldBe 3

                    // Verificar ordenação por data (mais cedo primeiro)
                    result[0].billId shouldBe BillId("BILL-2") // 2024-03-15
                    result[0].externalBillId shouldBe 1
                    result[0].dueDate shouldBe LocalDate.parse("2024-03-15", DateTimeFormatter.ISO_LOCAL_DATE)

                    result[1].billId shouldBe BillId("BILL-3") // 2024-03-18
                    result[1].externalBillId shouldBe 2
                    result[1].dueDate shouldBe LocalDate.parse("2024-03-18", DateTimeFormatter.ISO_LOCAL_DATE)
                    result[1].subscriptionFee shouldBe true
                    result[1].discount shouldBe 100
                    result[1].interest shouldBe 50
                    result[1].fine shouldBe 25

                    result[2].billId shouldBe BillId("BILL-1") // 2024-03-20
                    result[2].externalBillId shouldBe 3
                    result[2].dueDate shouldBe LocalDate.parse("2024-03-20", DateTimeFormatter.ISO_LOCAL_DATE)
                    result[2].recipient shouldBe null
                }
            }

            context("quando há bills com diferentes status") {
                it("deve converter corretamente os status") {
                    // arrange
                    val billTO1 = createTestBillTO(id = "BILL-1", status = "ACTIVE")
                    val billTO2 = createTestBillTO(id = "BILL-2", status = "PENDING") // deve virar WAITING_APPROVAL
                    val billTO3 = createTestBillTO(id = "BILL-3", status = "PAID")
                    val billTO4 = createTestBillTO(id = "BILL-4", status = null) // deve virar ACTIVE

                    val billsTO = listOf(billTO1, billTO2, billTO3, billTO4)

                    // act
                    val result = billsTO.toBillViews()

                    // assert
                    result.size shouldBe 4
                    result[0].status shouldBe BillStatus.ACTIVE
                    result[1].status shouldBe BillStatus.WAITING_APPROVAL
                    result[2].status shouldBe BillStatus.PAID
                    result[3].status shouldBe BillStatus.ACTIVE
                }
            }

            context("quando há bills com informações de agendamento") {
                it("deve converter corretamente o ScheduledInfo") {
                    // arrange
                    val billTO1 = createTestBillTO(
                        id = "BILL-1",
                        schedule = null,
                    )

                    val billTO2 = createTestBillTO(
                        id = "BILL-2",
                        schedule = BillScheduleTO(
                            date = "2024-03-15",
                            waitingFunds = false,
                            waitingRetry = false,
                        ),
                    )

                    val billTO3 = createTestBillTO(
                        id = "BILL-3",
                        schedule = BillScheduleTO(
                            date = "2024-03-15",
                            waitingFunds = true,
                            waitingRetry = false,
                        ),
                    )

                    val billsTO = listOf(billTO1, billTO2, billTO3)

                    // act
                    val result = billsTO.toBillViews()

                    // assert
                    result.size shouldBe 3
                    result[0].scheduledInfo shouldBe ScheduledInfo.NOT_SCHEDULED
                    result[1].scheduledInfo shouldBe ScheduledInfo.SCHEDULED
                    result[2].scheduledInfo shouldBe ScheduledInfo.WAITING_FUNDS
                }
            }
        }
    }

    private fun createTestBillTO(
        id: String = "BILL-TEST",
        assignor: String = "Test Assignor",
        billRecipient: ResponseRecipientTO? = ResponseRecipientTO(name = "Test Recipient"),
        description: String = "Test Description",
        amount: Long = 10000,
        discount: Long = 0,
        interest: Long = 0,
        fine: Long = 0,
        amountTotal: Long = 10000,
        billType: BillType = BillType.FICHA_COMPENSACAO,
        paymentLimitTime: String = "23:59",
        dueDate: String = "2024-03-15",
        subscriptionFee: Boolean = false,
        status: String? = "ACTIVE",
        schedule: BillScheduleTO? = null,
    ): BillTO {
        return BillTO(
            id = id,
            assignor = assignor,
            billRecipient = billRecipient,
            description = description,
            amount = amount,
            discount = discount,
            interest = interest,
            fine = fine,
            amountTotal = amountTotal,
            billType = billType,
            paymentLimitTime = paymentLimitTime,
            dueDate = dueDate,
            subscriptionFee = subscriptionFee,
            status = status,
            schedule = schedule,
        )
    }
}