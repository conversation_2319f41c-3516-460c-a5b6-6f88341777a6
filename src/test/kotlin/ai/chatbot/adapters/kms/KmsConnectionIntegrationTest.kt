package ai.chatbot.adapters.kms

import ai.chatbot.adapters.messaging.CognitoTokenHandler
import ai.chatbot.app.NotificationService
import ai.chatbot.app.encrypt.EncryptService
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.user.AccountId
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
@MicronautTest
class KmsConnectionIntegrationTest(private val encryptService: EncryptService) {

    private val notificationAdapter = mockk<NotificationService>(relaxed = true)
    private val buildNotificationService = mockk<BuildNotificationService>(relaxed = true)

    private val handler = CognitoTokenHandler(
        queue = "queue",
        amazonSQS = mockk(relaxed = true),
        configuration = mockk(relaxed = true),
        notificationService = notificationAdapter,
        encryptService = encryptService,
        buildNotificationService = buildNotificationService,
        tenantPropagator = mockk(relaxed = true),
    )

    private val keyArn = "arn:aws:kms:us-east-1:************:key/7aea696c-89eb-4a12-8910-9d343d2d495e"

    @Test
    fun `deve conectar no kms com o raw`() {
        val encripted = "AYADeHEdZR2hnH+XEhl/3FhOPbEAgQACABVhd3MtY3J5cHRvLXB1YmxpYy1rZXkAREE1eG9zMEFkOXNiRGxibi9XbmJYV05CSGlXdEpQSmNDZHRiVzdIdi84akxBeDBvVkJzRVAvTHZNbWdIUlJMa1JWQT09AAt1c2VycG9vbC1pZAATdXMtZWFzdC0xXzBWRHFvUmljMQABAAdhd3Mta21zAEthcm46YXdzOmttczp1cy1lYXN0LTE6Njc3MTIyOTgwOTY5OmtleS83YWVhNjk2Yy04OWViLTRhMTItODkxMC05ZDM0M2QyZDQ5NWUAuAECAQB4UPfMpjS0OwIMmJ5icYrUK1FGR9g7FKmg1kZRdkjzvocBaU98iEtl/JaCrmmYEIU6JAAAAH4wfAYJKoZIhvcNAQcGoG8wbQIBADBoBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDPuaQ5inRr+JWN51xwIBEIA7Wyw2PFtNU1Jqiex8w+5MjIJ3VWjvvlrMTIFocnpVszuZmrlDgljL4R6kSrH1ykCl8KOF9kyjzJYFgxkCAAAAAAwAABAAAAAAAAAAAAAAAAAAbQAWas/C7ObbTs0Gh+HUU/////8AAAABAAAAAAAAAAAAAAABAAAABsK7oL8+7TkA/PdgUkBtDXz28cf+7hAAZzBlAjEAlU1ND6958Hv/NfxGkjb4tg6SZB3B1qDVZsni4nrMPlsrWKVOvbbLnoVMJ51IqG7hAjAM9K66A+OtjQBoGAt1BpaFZL/6Ua8SClx4DN/O5OZN0NDxYnXw+5NzbCdYnaDoNCg="
        val encripted2 = "AYADeCr7XmN9J594KyUjXNvrLCgAgQACABVhd3MtY3J5cHRvLXB1YmxpYy1rZXkAREE4VGFaWk1reStsanNTaFQ2Z2lRbWgxWk54OGV0dXlGaVA4b3VoT1E1MUlMT3FHb21QaWJ2KzFkeTR4RXc1VkxNdz09AAt1c2VycG9vbC1pZAATdXMtZWFzdC0xXzBWRHFvUmljMQABAAdhd3Mta21zAEthcm46YXdzOmttczp1cy1lYXN0LTE6Njc3MTIyOTgwOTY5OmtleS83YWVhNjk2Yy04OWViLTRhMTItODkxMC05ZDM0M2QyZDQ5NWUAuAECAQB4UPfMpjS0OwIMmJ5icYrUK1FGR9g7FKmg1kZRdkjzvocBwgLJugikhRnQ1OldU4cGrwAAAH4wfAYJKoZIhvcNAQcGoG8wbQIBADBoBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDMNeo72W+D1behmFZwIBEIA7svVCCgq9pFFgCaldG6Vc9gbtURMaytieBqak6mWnH6RtbAhgxrLJ4dtDaMAKTpEEY9XTsaFDergRmm0CAAAAAAwAABAAAAAAAAAAAAAAAAAA8QuFzoPXZbKZU5zG3UEkkf////8AAAABAAAAAAAAAAAAAAABAAAABlJIK+y6nhBCHbgTpkjhsSasSIwLGrEAZzBlAjA2qk47uiJCUOUdbvrN3usOzNkq9z1+1bkFCLGdl5n8vQy9hCNcbl8M7nRlmGZlQ9wCMQDKQ3m4cM6ecQc5wGeZ3kGhuY335YAODl1oTqfM039Zlivr/CBt3gpN3mM1RHtzCOM="

        println(encryptService.decrypt(encripted))
        println(encryptService.decrypt(encripted2))
    }

    @Test
    fun `deve enviar o texto corretamente`() {
        val messagesAndTokens = listOf(
            """
    {"code":"AYADeHEdZR2hnH+XEhl/3FhOPbEAgQACABVhd3MtY3J5cHRvLXB1YmxpYy1rZXkAREE1eG9zMEFkOXNiRGxibi9XbmJYV05CSGlXdEpQSmNDZHRiVzdIdi84akxBeDBvVkJzRVAvTHZNbWdIUlJMa1JWQT09AAt1c2VycG9vbC1pZAATdXMtZWFzdC0xXzBWRHFvUmljMQABAAdhd3Mta21zAEthcm46YXdzOmttczp1cy1lYXN0LTE6Njc3MTIyOTgwOTY5OmtleS83YWVhNjk2Yy04OWViLTRhMTItODkxMC05ZDM0M2QyZDQ5NWUAuAECAQB4UPfMpjS0OwIMmJ5icYrUK1FGR9g7FKmg1kZRdkjzvocBaU98iEtl/JaCrmmYEIU6JAAAAH4wfAYJKoZIhvcNAQcGoG8wbQIBADBoBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDPuaQ5inRr+JWN51xwIBEIA7Wyw2PFtNU1Jqiex8w+5MjIJ3VWjvvlrMTIFocnpVszuZmrlDgljL4R6kSrH1ykCl8KOF9kyjzJYFgxkCAAAAAAwAABAAAAAAAAAAAAAAAAAAbQAWas/C7ObbTs0Gh+HUU/////8AAAABAAAAAAAAAAAAAAABAAAABsK7oL8+7TkA/PdgUkBtDXz28cf+7hAAZzBlAjEAlU1ND6958Hv/NfxGkjb4tg6SZB3B1qDVZsni4nrMPlsrWKVOvbbLnoVMJ51IqG7hAjAM9K66A+OtjQBoGAt1BpaFZL/6Ua8SClx4DN/O5OZN0NDxYnXw+5NzbCdYnaDoNCg=","source":"CustomSMSSender_Authentication","username":"***********","sub":"c468f478-90d1-7069-a92a-149ec4aa0495","accountId":"ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061","userPoolId":"us-east-1_0VDqoRic1","phoneNumber":"+*************","phoneNumberVerified":true,"email":"<EMAIL>","emailVerified":true,"userStatus":"CONFIRMED","timestamp":"2025-05-12T12:51:41.339Z"}
    """ to "472747",
            """
        {"code":"AYADeCr7XmN9J594KyUjXNvrLCgAgQACABVhd3MtY3J5cHRvLXB1YmxpYy1rZXkAREE4VGFaWk1reStsanNTaFQ2Z2lRbWgxWk54OGV0dXlGaVA4b3VoT1E1MUlMT3FHb21QaWJ2KzFkeTR4RXc1VkxNdz09AAt1c2VycG9vbC1pZAATdXMtZWFzdC0xXzBWRHFvUmljMQABAAdhd3Mta21zAEthcm46YXdzOmttczp1cy1lYXN0LTE6Njc3MTIyOTgwOTY5OmtleS83YWVhNjk2Yy04OWViLTRhMTItODkxMC05ZDM0M2QyZDQ5NWUAuAECAQB4UPfMpjS0OwIMmJ5icYrUK1FGR9g7FKmg1kZRdkjzvocBwgLJugikhRnQ1OldU4cGrwAAAH4wfAYJKoZIhvcNAQcGoG8wbQIBADBoBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDMNeo72W+D1behmFZwIBEIA7svVCCgq9pFFgCaldG6Vc9gbtURMaytieBqak6mWnH6RtbAhgxrLJ4dtDaMAKTpEEY9XTsaFDergRmm0CAAAAAAwAABAAAAAAAAAAAAAAAAAA8QuFzoPXZbKZU5zG3UEkkf////8AAAABAAAAAAAAAAAAAAABAAAABlJIK+y6nhBCHbgTpkjhsSasSIwLGrEAZzBlAjA2qk47uiJCUOUdbvrN3usOzNkq9z1+1bkFCLGdl5n8vQy9hCNcbl8M7nRlmGZlQ9wCMQDKQ3m4cM6ecQc5wGeZ3kGhuY335YAODl1oTqfM039Zlivr/CBt3gpN3mM1RHtzCOM=","source":"CustomSMSSender_Authentication","username":"***********","sub":"c468f478-90d1-7069-a92a-149ec4aa0495","accountId":"ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061","userPoolId":"us-east-1_0VDqoRic1","phoneNumber":"+*************","phoneNumberVerified":true,"email":"<EMAIL>","emailVerified":true,"userStatus":"CONFIRMED","timestamp":"2025-05-12T12:53:19.042Z"}
        """ to "136356",
        )

        messagesAndTokens.forEach { (rawMessage, token) ->
            val response = handler.handleMessage(
                mockk {
                    every { body() } returns rawMessage
                },
            )
            verify {
                buildNotificationService.buildTokenNotification(
                    accountId = AccountId("ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061"),
                    mobilePhone = "+*************",
                    token = token,
                )
            }
            response.shouldDeleteMessage shouldBe true
        }
    }
}