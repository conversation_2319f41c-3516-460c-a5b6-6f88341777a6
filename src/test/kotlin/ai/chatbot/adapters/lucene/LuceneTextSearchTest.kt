package ai.chatbot.adapters.lucene

import ai.chatbot.app.SearchElement
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

class LuceneTextSearchTest {
    private val lucene = LuceneTextSearch()

    private val elements =
        listOf(
            SearchElement("ID-001", mapOf("name" to "<PERSON>")),
            SearchElement("ID-002", mapOf("name" to "<PERSON>")),
            SearchElement("ID-003", mapOf("name" to "<PERSON>", "alias" to "<PERSON><PERSON><PERSON><PERSON>")),
            SearchElement("ID-004", mapOf("name" to "<PERSON>", "alias" to "Trindade")),
            SearchElement("ID-005", mapOf("name" to "<PERSON>", "alias" to "<PERSON>rig<PERSON>")),
            SearchElement("ID-006", mapOf("name" to "<PERSON>")),
            SearchElement("ID-007", mapOf("name" to "<PERSON>")),
            SearchElement("ID-008", mapOf("name" to "<PERSON> <PERSON>")),
        )

    @Test
    fun `deve buscar pelo nome completo`() {
        val result = lucene.search("Fernando Rodrigues", elements)

        result.size shouldBe 1

        result[0] shouldBe "ID-005"
    }

    @Test
    fun `deve buscar com caracteres especiais`() {
        val result = lucene.search("Fernando ! - + ^ [ { } ] ? : Rodrigues", elements)

        result.size shouldBe 1

        result[0] shouldBe "ID-005"
    }

    @Test
    fun `deve buscar pelo ultimo nome`() {
        val result = lucene.search("Doe", elements)

        result.size shouldBe 2

        result shouldContainExactlyInAnyOrder listOf("ID-001", "ID-002")
    }

    @Test
    fun `deve buscar por parte do nome`() {
        val result = lucene.search("Fernando Santos", elements)

        result.size shouldBe 1

        result[0] shouldBe "ID-004"
    }

    @Test
    fun `deve buscar por nome com erro de digitacao`() {
        val result = lucene.search("Jooã", elements)

        result.size shouldBe 1

        result[0] shouldBe "ID-003"
    }

    @Test
    fun `deve buscar pelo apelido`() {
        val result = lucene.search("Trindade", elements)

        result.size shouldBe 1

        result[0] shouldBe "ID-004"
    }

    @Test
    fun `nao deve diferenciar maiuscula de minuscula`() {
        val result = lucene.search("joão", elements)

        result.size shouldBe 1

        result[0] shouldBe "ID-003"
    }

    @Test
    fun `nao deve diferenciar caracter acentuado`() {
        val result = lucene.search("Joao", elements)

        result.size shouldBe 1

        result[0] shouldBe "ID-003"
    }

    @Test
    fun `deve não incluir sugestão caso tenha encontrado a palavra exata`() {
        val result = lucene.search("Alexandre", elements)

        result.size shouldBe 1

        result[0] shouldBe "ID-006"
    }

    @ParameterizedTest
    @ValueSource(strings = ["Felipe", "Filipe", "Filipi"])
    fun `deve conseguir buscar nomes parecidos`(nome: String) {
        val result = lucene.search(nome, elements)

        result.size shouldBe 1

        result[0] shouldBe "ID-008"
    }
}