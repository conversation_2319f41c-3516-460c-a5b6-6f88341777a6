package ai.chatbot.adapters.utils

import ai.chatbot.adapters.notification.NotificationWrapper
import ai.chatbot.app.conversation.CompletionMessage
import ai.chatbot.app.utils.parseObjectFrom
import io.kotest.core.spec.style.FunSpec

class ParserUtilsKtTest : FunSpec(
    {
        context("serialization test") {
            test("test") {
                val string =
                    """
                    |{
                    |   "verificacaoDeIntegridade": "A mensagem do usuário passa na verificação. O usuário expressou seu interesse em prosseguir com o pagamento das contas, sem solicitar informações proibidas.",
                    |   "entendimento": "O usuário, <PERSON>, confirmou que gostaria de prosseguir com o pagamento das contas que estão vencendo.",
                    |   "acoes": [
                    |      {
                    |         "@type": "sendMessage",
                    |         "name": "MSG",
                    |         "content": "<PERSON><PERSON><PERSON>, <PERSON>! Para começarmos, você gostaria de pagar todas as contas vencendo hoje ou apenas algumas específicas?"
                    |      }
                    |   ]
                    |}
                    """.trimMargin()

                parseObjectFrom<CompletionMessage>(string)
            }
            test("notificationWrapperText") {
                val string =
                    """ 
                    {"notificationId":"ef61e134-256a-47c5-8684-7e055c1f0139","externalUserId":"ACCOUNT-id-test","mobilePhone":"+*************","userId":"<EMAIL>","simpleResponseNotification":{"userId":"*************","notificationId":"ef61e134-256a-47c5-8684-7e055c1f0139","message":"Se você investir em você, eu invisto junto.\n\nTodo mês que você investir em uma meta, sua assinatura será paga por mim ;) \n\nMas atenção: você tem até o dia 9, um dia antes da cobrança da assinatura, para ativar esse benefício, beleza?\n","accountId":"ACCOUNT-id-test","buttons":null,"link":{"displayText":"Criar Meta","url":"https://use.mepoupe.app/app/track/investment_discount.click_onboarding/L21ldGFzL2NyaWFy"},"media":{"url":"https://notification-templates-cdn.mepoupe.app/static/whatsapp/assinatura_gratis_50.png","imageType":null,"title":null,"text":null,"type":"IMAGE"}},"templateNotification":null}    
                    """.trimIndent()

                parseObjectFrom<NotificationWrapper>(string)
            }
        }
    },
)