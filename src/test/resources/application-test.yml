micronaut:
  otel:
    enabled: false
  server:
    ssl:
      enabled: false
    netty:
      listeners:
        httpsListener:
          port: -1
          ssl: false
    port: -1
  application:
    name: ai-chatbot
  executors:
    conversation-executor:
      type: fixed
      nThreads: 1
      thread-name-prefix: "conversation-pool-"

chatbot-ai-transaction-auth:
  clientId: "test-client-id"
  secret: "test-passwd"

dynamodb:
  tableName: "TABLE_NAME"

integrations.whatsapp:
  accountId: "****************"
  api-token: xxx

aws:
  transcription:
    enabled: true
openai:
  transcription:
    enabled: true

integrations:
  whatsapp:
    accountId: "****************"
    apiToken: xxx
  billPayment:
    host: "https://api.friday.ai"
    secret: "bill-payment-secret"

internal-auth:
  identity: "internal-auth-identity"
  secret: "internla-auth-secret"

blip-auth:
  secret: "CHATBOT_AI_BLIP_SECRET"

app-base-url: https://use.test.ai

features:
  open-finance-incentive: true

wa-comm-centre:
  enabled: true
  sender-id: CHATBOT-FRIDAY
  backoffice-secret: "secret"
  auth:
    clientId: "CHATBOT_AI_WACOMMCENTRE_FRIDAY"
    secret: "secret"


feature-flags:
  wa-comm-centre:
    enabled: NO_ONE