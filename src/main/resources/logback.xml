<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <timestampPattern>yyyy-MM-dd'T'HH:mm:ss.SSSZ</timestampPattern>
            <jsonGeneratorDecorator class="net.logstash.logback.mask.MaskingJsonGeneratorDecorator">
                <defaultMask>****</defaultMask>
                <path>tokenusuario</path>
            </jsonGeneratorDecorator>
            <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                <exclude>^io.netty.channel.*</exclude>
                <exclude>^io.netty.handler.*</exclude>
                <maxDepthPerThrowable>30</maxDepthPerThrowable>
            </throwableConverter>
        </encoder>
    </appender>

    <!-- adiciona info do span no (traceId, spanId) -->
    <appender name="OTEL" class="io.opentelemetry.instrumentation.logback.mdc.v1_0.OpenTelemetryAppender">
        <addBaggage>true</addBaggage>
        <appender-ref ref="STDOUT"/>
    </appender>
    <root level="INFO">
        <appender-ref ref="OTEL"/>
    </root>

    <!-- desliga o log do open telemetry no java util logging -->
    <!-- OBS: qualquer biblioteca que use o java util logging -->
    <!-- PRECISA desligar o log para nao impactar a performance -->
    <!-- quando a bridge sl4j-to-jul esta habilitada -->
    <logger name="io.opentelemetry" level="OFF"/>
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator"/>
</configuration>
