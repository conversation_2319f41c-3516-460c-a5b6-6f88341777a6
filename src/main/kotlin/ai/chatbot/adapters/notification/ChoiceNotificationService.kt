package ai.chatbot.adapters.notification

import ai.chatbot.app.NotificationService
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.notification.CTALink
import ai.chatbot.app.notification.ChatbotNotification
import ai.chatbot.app.notification.NotificationMedia
import ai.chatbot.app.notification.QuickReplyButton
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.UserId
import io.micronaut.context.annotation.Primary
import jakarta.inject.Singleton

@Singleton
@Primary
class ChoiceNotificationService(
    private val blipNotificationAdapter: BlipNotificationAdapter,
    private val waCommCentreNotificationAdapter: WaCommCentreNotificationAdapter,
    private val tenantService: TenantService,
) : NotificationService {

    private fun getNotificationService(userId: String): NotificationService {
        val waCommCentreEnabled = tenantService.getConfiguration().flags.waCommCentre.check(UserId(userId))
        val sendViaWaCommCentreEnabled = tenantService.getConfiguration().flags.sendViaWaCommCentre.check(UserId(userId))

        return if (waCommCentreEnabled && sendViaWaCommCentreEnabled) {
            waCommCentreNotificationAdapter
        } else {
            blipNotificationAdapter
        }
    }

    override fun notify(notification: ChatbotNotification, delaySeconds: Int?, retry: Boolean) {
        getNotificationService(notification.mobilePhone).notify(notification, delaySeconds, retry)
    }

    override fun notify(
        userId: UserId,
        accountId: AccountId?,
        message: String?,
        quickReplyButtons: List<QuickReplyButton>?,
        ctaLink: CTALink?,
        media: NotificationMedia?,
        delay: Int?,
        retry: Boolean,
    ) {
        getNotificationService(userId.value).notify(
            userId,
            accountId,
            message,
            quickReplyButtons,
            ctaLink,
            media,
            delay,
            retry,
        )
    }
}