package ai.chatbot.adapters.notification

import ai.chatbot.adapters.messaging.ButtonWhatsAppParameterTO
import ai.chatbot.adapters.messaging.ChatbotRawTemplatedNotificationTO
import ai.chatbot.adapters.messaging.ChatbotWhatsappTemplatedNotificationTO
import ai.chatbot.adapters.messaging.NotificationMediaTO
import ai.chatbot.adapters.waCommCentre.WaCommCentreAdapter
import ai.chatbot.app.NotificationService
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.notification.ButtonWhatsAppDeeplinkParameter
import ai.chatbot.app.notification.ButtonWhatsAppParameter
import ai.chatbot.app.notification.ButtonWhatsAppRawParameter
import ai.chatbot.app.notification.ButtonWhatsAppTrackedDeeplinkParameter
import ai.chatbot.app.notification.ButtonWhatsAppWebParameter
import ai.chatbot.app.notification.CTALink
import ai.chatbot.app.notification.ChatbotNotification
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.ChatbotWhatsappSimpleNotification
import ai.chatbot.app.notification.ChatbotWhatsappTemplatedNotification
import ai.chatbot.app.notification.NotificationMedia
import ai.chatbot.app.notification.QuickReplyButton
import ai.chatbot.app.notification.SimpleResponseNotification
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.andAppend
import io.micronaut.context.annotation.Property
import io.micronaut.http.client.HttpClientConfiguration
import io.micronaut.runtime.ApplicationConfiguration
import io.via1.communicationcentre.app.notification.Notification
import io.via1.communicationcentre.app.notification.NotificationChannel
import io.via1.communicationcentre.app.notification.NotificationDocument
import io.via1.communicationcentre.app.notification.NotificationImage
import io.via1.communicationcentre.app.notification.NotificationVideo
import jakarta.inject.Singleton
import java.time.Duration
import java.util.UUID
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

private const val MAX_PROCESSING_RETRIES = 10

@Singleton
class WaCommCentreHttpConfiguration(
    applicationConfiguration: ApplicationConfiguration,
    private val configuration: HttpClientConfiguration,
) : HttpClientConfiguration(applicationConfiguration) {
    init {
        setReadTimeout(Duration.ofSeconds(30))
        setConnectionPoolIdleTimeout(Duration.ofSeconds(10))
    }

    override fun getConnectionPoolConfiguration(): ConnectionPoolConfiguration =
        configuration.connectionPoolConfiguration
}

@Singleton
class WaCommCentreNotificationAdapter(
    private val sqsMessagePublisher: SQSMessagePublisher,
    private val waCommCentreNotificationService: WaCommCentreAdapter,
    @Property(name = "aws.sqs.queues.chatbotRawTemplateDelayQueue") private val rawTemplateDelayQueue: String,
    @Property(name = "aws.sqs.queues.chatbotTemplateDelayQueue") private val templateDelayQueue: String,
    @Property(name = "aws.sqs.queues.chatbotSimpleDelayQueue") private val simpleDelayQueue: String,
    private val tenantService: TenantService,
) : NotificationService {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun notify(notification: ChatbotNotification, delaySeconds: Int?, retry: Boolean) {
        when (notification) {
            is ChatbotWhatsappSimpleNotification -> notify(
                userId = UserId(notification.mobilePhone),
                accountId = notification.accountId,
                message = notification.message,
                quickReplyButtons = notification.quickReplyButtons,
                ctaLink = notification.ctaLink,
                media = notification.media,
            )

            is ChatbotWhatsappTemplatedNotification -> notifyTemplate(notification, delaySeconds, retry = retry)
            is ChatbotRawTemplatedNotification -> notifyRawMessage(notification, delaySeconds, retry)
        }
    }

    private fun notifyRawMessage(notification: ChatbotRawTemplatedNotification, delaySeconds: Int?, retry: Boolean) {
        val logName = "WaCommCentreNotificationAdapter#notifyRawMessage"
        val markers = Markers.append("userId", notification.mobilePhone)
            .andAppend("notification", notification)
            .andAppend("delay", delaySeconds)
            .andAppend("accountId", notification.accountId.value)

        if (delaySeconds != null && delaySeconds > 0) {
            logger.info(markers, "$logName/delay")
            sqsMessagePublisher.sendMessage(rawTemplateDelayQueue, notification.toNotificationTO(), delaySeconds)
            return
        }

        try {
            waCommCentreNotificationService.sendRawMessage(notification = notification)
            logger.info(markers, logName)
        } catch (e: Exception) {
            logger.error(markers, logName, e)

            if (retry) {
                sqsMessagePublisher.sendMessage(rawTemplateDelayQueue, notification.toNotificationTO(), 1)
            } else {
                throw e
            }
        }
    }

    private fun notifyTemplate(notification: ChatbotWhatsappTemplatedNotification, delaySeconds: Int?, retry: Boolean) {
        val logName = "WaCommCentreNotificationAdapter#notifyTemplate"
        val markers = Markers.append("userId", notification.mobilePhone)
            .andAppend("notification", notification)
            .andAppend("delay", delaySeconds)
            .andAppend("accountId", notification.accountId.value)

        if (delaySeconds != null && delaySeconds > 0) {
            logger.info(markers, "$logName/delay")
            sqsMessagePublisher.sendMessage(templateDelayQueue, notification.toNotificationTO(), delaySeconds)
            return
        }

        try {
            waCommCentreNotificationService.sendTemplateMessage(
                msisdn = notification.mobilePhone,
                templateName = notification.template.value,
                configurationKey = notification.configurationKey,
                parameters = notification.parameters,
                buttonLinks = listOfNotNull(notification.buttonWhatsAppParameter?.value),
                buttonPayloads = notification.quickReplyButtonsWhatsAppParameter,
                media = notification.media,
            )
            logger.info(markers, logName)
        } catch (e: Exception) {
            logger.error(markers, logName, e)

            if (retry) {
                sqsMessagePublisher.sendMessage(templateDelayQueue, notification.toNotificationTO(), 1)
            } else {
                throw e
            }
        }
    }

    override fun notify(
        userId: UserId,
        accountId: AccountId?,
        message: String?,
        quickReplyButtons: List<QuickReplyButton>?,
        ctaLink: CTALink?,
        media: NotificationMedia?,
        delay: Int?,
        retry: Boolean,
    ) {
        val logName = "WaCommCentreNotificationAdapter"
        val appBaseUrl = tenantService.getConfiguration().appBaseUrl

        val notification = SimpleResponseNotification(
            userId = userId.value,
            notificationId = UUID.randomUUID().toString(),
            message = message?.replace("""\n""", "\r\n"),
            accountId = accountId?.value,
            buttons = quickReplyButtons,
            link = ctaLink?.withFullUrl(appBaseUrl),
            media = media,
        )

        val markers = Markers.append("userId", userId.value)
            .andAppend("notification", notification)
            .andAppend("delay", delay)
            .andAppend("accountId", accountId?.value ?: "")
            .andAppend("media", media)

        if (delay != null && delay > 0) {
            logger.info(markers, "$logName/delay")
            sqsMessagePublisher.sendMessage(simpleDelayQueue, notification, delay)
            return
        }

        try {
            notifySimple(notification)
            logger.info(markers, logName)
        } catch (e: Exception) {
            logger.error(markers, logName, e)

            if (retry) {
                sqsMessagePublisher.sendMessage(simpleDelayQueue, notification, delay ?: 1)
            } else {
                throw e
            }
        }
    }

    private fun notifySimple(notification: SimpleResponseNotification): String {
        val mobilePhone = "+${notification.userId}"

        val messageId = if (notification.link != null) {
            waCommCentreNotificationService.sendLinkMessage(
                msisdn = mobilePhone,
                content = notification.message ?: "",
                linkTitle = notification.link.displayText,
                url = notification.link.url,
                media = notification.media,
            )
            "link-message-${UUID.randomUUID()}"
        } else if (!notification.buttons.isNullOrEmpty()) {
            waCommCentreNotificationService.sendInteractiveMessage(
                msisdn = mobilePhone,
                content = notification.message ?: "",
                buttons = notification.buttons,
                media = notification.media,
            )
            "interactive-message-${UUID.randomUUID()}"
        } else {
            waCommCentreNotificationService.sendTextMessage(
                msisdn = mobilePhone,
                content = notification.message ?: "",
                media = notification.media,
            )
            "text-message-${UUID.randomUUID()}"
        }

        return messageId
    }

    private fun buildWaCommCentreNotification(notification: ChatbotWhatsappTemplatedNotification): Notification {
        return Notification().apply {
            externalUserId = notification.accountId.value
            mobilePhone = notification.mobilePhone
            template = notification.template.value
            whatsAppParameters = notification.parameters
            buttonWhatsAppParameter = notification.buttonWhatsAppParameter?.value
            quickReplyButtonsWhatsAppParameter = notification.quickReplyButtonsWhatsAppParameter
            quickRepliesStartIndex = notification.quickRepliesStartIndex
            notificationChannel = NotificationChannel.WHATSAPP
            media = notification.media?.let {
                it.toCommCentreNotificationMedia()
            }
        }
    }

    private fun NotificationMedia.toCommCentreNotificationMedia() = when (this) {
        is NotificationMedia.Image -> NotificationImage(url, imageType, title, text)
        is NotificationMedia.Video -> NotificationVideo(url, videoType)
        is NotificationMedia.Document -> NotificationDocument(url, filename, documentType)
    }
}

fun ChatbotRawTemplatedNotification.toNotificationTO() = ChatbotRawTemplatedNotificationTO(
    notificationId = notificationId,
    mobilePhone = mobilePhone,
    accountId = accountId.value,
    clientId = clientId.value,
    configurationKey = configurationKey.value,
    language = language,
    arguments = arguments,
    media = media?.toNotificationMediaTO(),
    notificationType = notificationType?.value,
)

fun NotificationMedia.toNotificationMediaTO() = when (this) {
    is NotificationMedia.Document -> NotificationMediaTO.Document(url = url, filename = filename, documentType = documentType)
    is NotificationMedia.Image -> NotificationMediaTO.Image(url = url, imageType = imageType, title = title, text = text)
    is NotificationMedia.Video -> NotificationMediaTO.Video(url = url, videoType = videoType)
}

fun ChatbotWhatsappTemplatedNotification.toNotificationTO() = ChatbotWhatsappTemplatedNotificationTO(
    notificationId = notificationId,
    mobilePhone = mobilePhone,
    accountId = accountId,
    template = template,
    configurationKey = configurationKey,
    parameters = parameters,
    quickReplyButtonsWhatsAppParameter = quickReplyButtonsWhatsAppParameter,
    quickRepliesStartIndex = quickRepliesStartIndex,
    buttonWhatsAppParameter = buttonWhatsAppParameter?.toButtonWhatsAppParameterTO(),
    media = media?.toNotificationMediaTO(),
)

fun ButtonWhatsAppParameter.toButtonWhatsAppParameterTO() = when (this) {
    is ButtonWhatsAppRawParameter -> ButtonWhatsAppParameterTO.ButtonWhatsAppRawParameterTO(value = value)
    is ButtonWhatsAppWebParameter -> ButtonWhatsAppParameterTO.ButtonWhatsAppWebParameterTO(path = path)
    is ButtonWhatsAppDeeplinkParameter -> ButtonWhatsAppParameterTO.ButtonWhatsAppDeeplinkParameterTO(path = path)
    is ButtonWhatsAppTrackedDeeplinkParameter -> ButtonWhatsAppParameterTO.ButtonWhatsAppTrackedDeeplinkParameterTO(path = path, event = event)
}