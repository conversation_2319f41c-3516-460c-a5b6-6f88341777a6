package ai.chatbot.adapters.notification

import ai.chatbot.app.config.TENANT_KEY
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.getObjectMapper
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue
import software.amazon.awssdk.services.sqs.model.SendMessageRequest

interface MessagePublisher {
    fun sendMessage(
        queueName: String,
        body: Any,
        delaySeconds: Int? = null,
    )
}

@Singleton
class SQSMessagePublisher(
    private val amazonSQS: SqsClient,
    private val tenantService: TenantService,
) : MessagePublisher {
    private val queueUrls = mutableMapOf<String, String>()

    private fun getQueueURL(queueName: String): String {
        return queueUrls.getOrPut(queueName) {
            amazonSQS.getQueueUrl(GetQueueUrlRequest.builder().queueName(queueName).build()).queueUrl()
        }
    }

    override fun sendMessage(
        queueName: String,
        body: Any,
        delaySeconds: Int?,
    ) {
        val queueUrl = getQueueURL(queueName)
        val tenantId = tenantService.getTenantName()

        val queueMessage = getObjectMapper().writeValueAsString(body)
        val sendMessageRequestBuilder =
            SendMessageRequest.builder()
                .queueUrl(queueUrl)
                .messageBody(queueMessage)
                .messageAttributes(
                    mapOf(
                        TENANT_KEY to MessageAttributeValue.builder()
                            .dataType("String")
                            .stringValue(tenantId)
                            .build(),
                    ),
                )

        delaySeconds?.let { sendMessageRequestBuilder.delaySeconds(it) }

        val sendMessageRequest = sendMessageRequestBuilder.build()
        LOG.info(
            Markers.append("queueName", queueName)
                .andAppend("queueMessage", queueMessage)
                .andAppend("tenantId", tenantId),
            "SQSMessagePublisher",
        )
        amazonSQS.sendMessage(sendMessageRequest)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSMessagePublisher::class.java)
    }
}