package ai.chatbot.adapters.notification

import ai.chatbot.app.NotificationService
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.notification.CTALink
import ai.chatbot.app.notification.ChatbotNotification
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.ChatbotWhatsappSimpleNotification
import ai.chatbot.app.notification.ChatbotWhatsappTemplatedNotification
import ai.chatbot.app.notification.NotificationMedia
import ai.chatbot.app.notification.QuickReplyButton
import ai.chatbot.app.notification.SimpleResponseNotification
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.andAppend
import io.micronaut.context.annotation.Property
import io.micronaut.http.client.HttpClientConfiguration
import io.micronaut.runtime.ApplicationConfiguration
import io.via1.communicationcentre.app.integrations.CheckableNotificationService
import io.via1.communicationcentre.app.notification.Button
import io.via1.communicationcentre.app.notification.Notification
import io.via1.communicationcentre.app.notification.NotificationChannel
import io.via1.communicationcentre.app.notification.NotificationDocument
import io.via1.communicationcentre.app.notification.NotificationImage
import io.via1.communicationcentre.app.notification.NotificationStatus
import io.via1.communicationcentre.app.notification.NotificationVideo
import jakarta.inject.Singleton
import java.time.Duration
import java.util.*
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

private const val DELAY_TO_CHECK_MESSAGE_STATUS = 3
private const val MAX_PROCESSING_RETRIES = 10

@Singleton
class BlipHttpConfiguration(
    applicationConfiguration: ApplicationConfiguration,
    private val configuration: HttpClientConfiguration,
) : HttpClientConfiguration(applicationConfiguration) {
    init {
        setReadTimeout(Duration.ofSeconds(30))
        setConnectionPoolIdleTimeout(Duration.ofSeconds(10))
    }

    override fun getConnectionPoolConfiguration(): ConnectionPoolConfiguration =
        configuration.connectionPoolConfiguration
}

@Singleton
class BlipNotificationAdapter(
    private val sqsMessagePublisher: SQSMessagePublisher,
    private val blipNotificationService: io.via1.communicationcentre.app.integrations.NotificationService,
    private val checkableNotificationService: CheckableNotificationService,
    private val tenantService: TenantService,
    @Property(name = "aws.sqs.queues.chatbotVerifyNotification") private val verifyNotificationQueue: String,
    @Property(name = "aws.sqs.queues.chatbotTemplateDelayQueue") private val templateDelayQueue: String,
    @Property(name = "aws.sqs.queues.chatbotSimpleDelayQueue") private val simpleDelayQueue: String,
) : NotificationService {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun notify(notification: ChatbotNotification, delaySeconds: Int?, retry: Boolean) {
        when (notification) {
            is ChatbotWhatsappSimpleNotification -> notify(
                userId = UserId(notification.mobilePhone),
                accountId = notification.accountId,
                message = notification.message,
                quickReplyButtons = notification.quickReplyButtons,
                ctaLink = notification.ctaLink,
                media = notification.media,
            )

            is ChatbotWhatsappTemplatedNotification -> notifyTemplate(notification, delaySeconds)
            is ChatbotRawTemplatedNotification -> throw NotImplementedError("Raw templated notifications are not supported in this adapter. ChatbotAI não sabe resolver o template associado a config.")
        }
    }

    private fun notifyTemplate(notification: ChatbotWhatsappTemplatedNotification, delaySeconds: Int?) {
        val logName = "BlipNotificationAdapter"
        val markers = Markers.append("userId", notification.mobilePhone)
            .andAppend("notification", notification)
            .andAppend("delay", delaySeconds)
            .andAppend("accountId", notification.accountId.value)

        val blipNotification = buildBlipNotification(notification)

        if (delaySeconds != null && delaySeconds > 0) {
            logger.info(markers, "$logName/delay")
            sqsMessagePublisher.sendMessage(templateDelayQueue, notification.toNotificationTO(), delaySeconds)
            return
        }

        val userId = try {
            blipNotificationService.notify(blipNotification)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }

        sendToVerificationQueue(blipNotification, userId)

        logger.info(markers, logName)
    }

    override fun notify(
        userId: UserId,
        accountId: AccountId?,
        message: String?,
        quickReplyButtons: List<QuickReplyButton>?,
        ctaLink: CTALink?,
        media: NotificationMedia?,
        delay: Int?,
        retry: Boolean,
    ) {
        val logName = "BlipNotificationAdapter"
        val appBaseUrl = tenantService.getConfiguration().appBaseUrl

        val notification = SimpleResponseNotification(
            userId = userId.value,
            notificationId = UUID.randomUUID().toString(),
            message = message?.replace("""\n""", "\r\n"),
            accountId = accountId?.value,
            buttons = quickReplyButtons,
            link = ctaLink?.withFullUrl(appBaseUrl),
            media = media,
        )

        val markers = Markers.append("userId", userId.value)
            .andAppend("notification", notification)
            .andAppend("delay", delay)
            .andAppend("accountId", accountId?.value ?: "")
            .andAppend("media", media)

        if (delay != null && delay > 0) {
            logger.info(markers, "$logName/delay")
            sqsMessagePublisher.sendMessage(simpleDelayQueue, notification, delay)
            return
        }

        val blipUserId = try {
            notifySimple(notification)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            sqsMessagePublisher.sendMessage(simpleDelayQueue, notification, delay ?: 1)
            return
        }

        sendToVerificationQueue(notification, accountId, blipUserId)

        logger.info(markers, logName)
    }

    fun verifyStatus(notificationWrapper: NotificationWrapper, attempts: Long): Boolean {
        val markers = Markers.empty()

        val notificationStatus = checkableNotificationService.checkStatus(notificationWrapper.notificationId)
        markers.andAppend("currentStatus", notificationStatus.status).andAppend("reason", notificationStatus.reason)
            .andAppend("retryable", notificationStatus.retryable)
            .andAppend("accountId", notificationWrapper.externalUserId)
            .andAppend("userId", notificationWrapper.mobilePhone)
            .andAppend("notificationId", notificationWrapper.notificationId)
            .andAppend("template", notificationWrapper.templateNotification?.template)

        val maxRetriesReached = attempts >= MAX_PROCESSING_RETRIES
        if (maxRetriesReached) {
            markers.andAppend("maxRetriesReached", true)
        }

        val shouldDeleteMessage =
            when (notificationStatus.status!!) {
                NotificationStatus.SUCCESS -> true

                NotificationStatus.PROCESSING, NotificationStatus.UNKNOWN -> maxRetriesReached

                NotificationStatus.FAILED -> {
                    if (notificationStatus.retryable) {
                        markers.andAppend("retrying", true)
                        sendNotification(notificationWrapper)
                    }
                    !notificationStatus.retryable || maxRetriesReached
                }
            }

        logger.info(markers, "BlipNotificationAdapter#verifyStatus")

        return shouldDeleteMessage
    }

    private fun sendNotification(notificationWrapper: NotificationWrapper): String {
        val userId = if (notificationWrapper.templateNotification != null) {
            blipNotificationService.notify(notificationWrapper.templateNotification)
        } else if (notificationWrapper.simpleResponseNotification != null) {
            notifySimple(notificationWrapper.simpleResponseNotification)
        } else {
            ""
        }

        return userId
    }

    private fun notifySimple(notification: SimpleResponseNotification): String {
        val logName = "BlipNotificationAdapter#notifySimple"
        val markers = Markers.append("userId", notification.userId)
            .andAppend("notification", notification)
            .andAppend("accountId", notification.accountId ?: "")
            .andAppend("media", notification.media)
            .andAppend("emptyButtons", notification.buttons?.isEmpty())
            .andAppend("nullButtons", notification.buttons == null)

        val mobilePhone = "+${notification.userId}"
        val messageId = if (notification.link != null) {
            markers.andAppend("sender", "sendLinkResponse")
            logger.info(markers, logName)
            blipNotificationService.sendLinkResponse(
                notification.message,
                notification.link.displayText,
                notification.link.url,
                mobilePhone,
                notification.notificationId,
                notification.media?.let {
                    it.toCommCentreNotificationMedia()
                },
            )
        } else if (!notification.buttons.isNullOrEmpty()) {
            markers.andAppend("sender", "sendInteractiveResponse")
            logger.info(markers, logName)
            blipNotificationService.sendInteractiveResponse(
                notification.message,
                notification.buttons.map { Button(it.text, it.payload) },
                mobilePhone,
                notification.notificationId,
                notification.media?.let {
                    it.toCommCentreNotificationMedia()
                },
            )
        } else if (notification.media != null) {
            markers.andAppend("sender", "sendSimpleResponseWithMedia")
            logger.info(markers, logName)
            blipNotificationService.sendSimpleResponseWithMedia(
                notification.media.toCommCentreNotificationMedia(),
                mobilePhone,
                notification.notificationId,
            )
        } else {
            markers.andAppend("sender", "sendSimpleResponse")
            logger.info(markers, logName)
            blipNotificationService.sendSimpleResponse(
                notification.message,
                mobilePhone,
                notification.notificationId,
            )
        }

        return messageId
    }

    private fun buildBlipNotification(notification: ChatbotWhatsappTemplatedNotification): Notification {
        return Notification().apply {
            externalUserId = notification.accountId.value
            mobilePhone = notification.mobilePhone
            template = notification.template.value
            whatsAppParameters = notification.parameters
            buttonWhatsAppParameter = notification.buttonWhatsAppParameter?.value
            quickReplyButtonsWhatsAppParameter = notification.quickReplyButtonsWhatsAppParameter
            quickRepliesStartIndex = notification.quickRepliesStartIndex
            notificationChannel = NotificationChannel.WHATSAPP
            media = notification.media?.let {
                it.toCommCentreNotificationMedia()
            }
        }
    }

    private fun NotificationMedia.toCommCentreNotificationMedia() = when (this) {
        is NotificationMedia.Image -> NotificationImage(url, imageType, title, text)
        is NotificationMedia.Video -> NotificationVideo(url, videoType)
        is NotificationMedia.Document -> NotificationDocument(url, filename, documentType)
    }

    private fun sendToVerificationQueue(notification: Notification, userId: String) = sqsMessagePublisher.sendMessage(
        verifyNotificationQueue,
        NotificationWrapper(
            notificationId = notification.notificationId,
            mobilePhone = notification.mobilePhone,
            externalUserId = notification.externalUserId,
            templateNotification = notification,
            userId = userId,
        ),
        delaySeconds = DELAY_TO_CHECK_MESSAGE_STATUS,
    )

    private fun sendToVerificationQueue(notification: SimpleResponseNotification, accountId: AccountId?, userId: String) = sqsMessagePublisher.sendMessage(
        verifyNotificationQueue,
        NotificationWrapper(
            notificationId = notification.notificationId,
            mobilePhone = "+${notification.userId}",
            externalUserId = accountId?.value,
            simpleResponseNotification = notification,
            userId = userId,
        ),
        delaySeconds = DELAY_TO_CHECK_MESSAGE_STATUS,
    )
}

data class NotificationWrapper(
    val notificationId: String?,
    val externalUserId: String?,
    val mobilePhone: String?,
    val userId: String,
    val simpleResponseNotification: SimpleResponseNotification? = null,
    val templateNotification: Notification? = null,
)