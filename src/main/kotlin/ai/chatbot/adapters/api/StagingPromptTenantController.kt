package ai.chatbot.adapters.api

import ai.chatbot.adapters.dynamodb.StagingPromptTenantRepository
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.ME_POUPE_ENV
import ai.chatbot.app.MOTOROLA_ENV
import ai.chatbot.app.Staging
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.HistoryStateType
import ai.chatbot.app.prompt.PromptType
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.andAppend
import io.micronaut.http.HttpResponse
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Put
import net.logstash.logback.marker.Markers.append
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Controller("/staging")
@Staging
class StagingPromptTenantController(
    private val promptTenantRepository: StagingPromptTenantRepository,
    private val conversationHistoryService: ConversationHistoryService,
) {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    @Put("prompt-tenant")
    fun saveTenant(
        @Body body: PromptTenantTO,
    ): MutableHttpResponse<*> {
        val logName = "StagingPromptTenantController#saveTenant"
        val markers = append("body", body)

        return try {
            checkTenant(body.tenant)

            val type = body.type ?: PromptType.DEFAULT
            val hasInvestmentCampaignText = body.investmentCampaign ?: false

            promptTenantRepository.save(body.msisdn, body.tenant, type, hasInvestmentCampaignText)

            runCatching {
                HistoryStateType.values().forEach {
                    conversationHistoryService.clearHistory(UserId(body.msisdn), it, getLocalDate())
                }
            }
            logger.info(markers, logName)
            HttpResponse.noContent<Unit>()
        } catch (ex: IllegalArgumentException) {
            logger.warn(markers, logName, ex)
            HttpResponse.badRequest(
                mapOf(
                    "message" to "Tenant invalido",
                    "error" to ex.message,
                ),
            )
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError(
                mapOf(
                    "message" to "Erro ao salvar a configuracao de prompt tenant",
                    "error" to ex.message,
                ),
            )
        }
    }

    private fun checkTenant(tenant: String) {
        if (tenant.isNullOrEmpty()) {
            throw IllegalArgumentException("Tenant is null or empty")
        }
        if (!tenant.equals(FRIDAY_ENV) && !tenant.equals(ME_POUPE_ENV) && !tenant.equals(MOTOROLA_ENV)) {
            throw IllegalArgumentException("Tenant is invalid")
        }
    }

    @Get("prompt-tenant/msisdn/{msisdn}")
    fun getTenant(
        @PathVariable msisdn: String,
    ): MutableHttpResponse<*> {
        val logName = "StagingPromptTenantController#getTenant"
        val markers = append("msisdn", msisdn)

        return try {
            val promptTenant = promptTenantRepository.find(msisdn)
            markers.andAppend("promptTenant", promptTenant)

            if (promptTenant == null) {
                HttpResponse.notFound<Unit>()
            } else {
                val promptTenantTO = PromptTenantTO(promptTenant.msisdn, promptTenant.tenant, promptTenant.type, promptTenant.hasInvestmentCampaign)
                logger.info(markers, logName)
                HttpResponse.ok(promptTenantTO)
            }
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)

            HttpResponse.serverError(
                mapOf(
                    "message" to "Erro ao salvar a configuracao de prompt tenant",
                    "error" to ex.message,
                ),
            )
        }
    }
}

data class PromptTenantTO(
    val msisdn: String,
    val tenant: String,
    val type: PromptType? = null,
    val investmentCampaign: Boolean? = null,
)