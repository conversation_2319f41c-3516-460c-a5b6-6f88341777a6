package ai.chatbot.adapters.api

import ai.chatbot.app.NotificationService
import ai.chatbot.app.TransactionService
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.SweepingLimitType
import ai.chatbot.app.notification.ButtonWhatsAppDeeplinkParameter
import ai.chatbot.app.notification.CTALink
import ai.chatbot.app.transaction.AddBoletoTransactionDetails
import ai.chatbot.app.transaction.MarkAsPaidOrIgnoreBillsTransactionDetails
import ai.chatbot.app.transaction.PixTransactionDetails
import ai.chatbot.app.transaction.ScheduleBillsTransactionDetails
import ai.chatbot.app.transaction.SendPixCodeTransactionDetails
import ai.chatbot.app.transaction.SweepingTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionDetails
import ai.chatbot.app.transaction.TransactionErrorReason
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.transaction.TransactionResult.Canceled
import ai.chatbot.app.transaction.TransactionResult.IllegalState
import ai.chatbot.app.transaction.TransactionResult.IllegalUser
import ai.chatbot.app.transaction.TransactionResult.Success
import ai.chatbot.app.transaction.TransactionResult.TransactionError
import ai.chatbot.app.transaction.TransactionResult.TransactionNotFound
import ai.chatbot.app.transaction.TransactionStatus
import ai.chatbot.app.transaction.TransactionType
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.formatToZonedDateTime
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.notifyTransactionProcessing
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.context.annotation.Property
import io.micronaut.core.async.publisher.Publishers
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Filter
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import net.logstash.logback.marker.Markers.append
import org.reactivestreams.Publisher
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Filter(value = ["/transaction/**"])
class TransactionControllerFilter(
    @Property(name = "chatbot-ai-transaction-auth.clientId") private val clientId: String,
    @Property(name = "chatbot-ai-transaction-auth.secret") private val secret: String,
) : HttpServerFilter {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun doFilter(
        request: HttpRequest<*>,
        chain: ServerFilterChain,
    ): Publisher<MutableHttpResponse<*>> {
        if (BasicAuthValidator.validate(request, clientId, secret)) {
            return chain.proceed(request)
        }

        logger.warn(append("authorization", request.headers["authorization"]), "TransactionControllerFilter")
        return Publishers.just(HttpResponse.status<Any>(HttpStatus.UNAUTHORIZED))
    }
}

@Controller("/transaction")
class TransactionController(
    private val transactionService: TransactionService,
    private val conversationHistoryService: ConversationHistoryService,
    private val notificationService: NotificationService,
) {

    private val MAX_AMOUNT_TO_NOTIFY_SET_LIMIT = 1_000_00L

    @Get("/{transactionId}")
    fun findTransaction(
        @PathVariable transactionId: String,
    ): HttpResponse<*> {
        val logName = "TransactionController#findTransaction"
        val markers = append("transactionId", transactionId)
        return try {
            val transaction = transactionService.find(TransactionId(transactionId)) ?: return HttpResponse.notFound<Unit>()
            markers.andAppend("transaction", transaction)
            logger.info(markers, logName)
            HttpResponse.ok(transaction.toTransactionTO())
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError("Não foi possível encontrar a transação")
        }
    }

    @Get("/group/{transactionGroupId}/{msisdn}")
    fun findTransactions(
        @PathVariable transactionGroupId: String,
        @PathVariable msisdn: String,
    ): HttpResponse<*> {
        val logName = "TransactionController#findTransactions"
        val markers = append("transactionGroupId", transactionGroupId)
            .andAppend("msisdn", msisdn)
        val userId = UserId.fromMsisdn(msisdn)

        return try {
            val transactions = transactionService.find(userId, TransactionGroupId(transactionGroupId)).filter {
                it.isAuthorizable()
            }
            markers.andAppend("transactions", transactions)
            logger.info(markers, logName)
            HttpResponse.ok(transactions.map { it.toTransactionTO() })
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError("Não foi possível encontrar a transação")
        }
    }

    @Post("/{transactionId}/{msisdn}")
    fun confirmTransaction(
        @PathVariable transactionId: String,
        @PathVariable msisdn: String,
        @Body body: ConfirmTO,
    ): HttpResponse<*> {
        val logName = "TransactionController#confirmTransaction"
        val userId = UserId.fromMsisdn(msisdn)

        val markers = append("transactionId", transactionId).andAppend("body", body).andAppend("userId", userId.value)

        return try {
            val transactionResult = transactionService.confirm(TransactionId(transactionId), userId, authorizationToken = body.authorizationToken)
            markers.andAppend("transactionResult", transactionResult)

            if (transactionResult is Success) {
                try {
                    val state = conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(userId)
                    notifyTransactionProcessing(state.user, notificationService, conversationHistoryService)
                    notifySetLimit(transactionResult, body, state)
                    conversationHistoryService.createSystemMessage(userId, "Transação confirmada com sucesso")
                } catch (e: Exception) {
                    logger.warn(markers, "$logName/couldNotFindState", e)
                }

                logger.info(markers, logName)
            } else {
                logger.warn(markers, logName)
            }

            when (transactionResult) {
                is Success -> HttpResponse.ok()
                TransactionNotFound -> HttpResponse.notFound()
                is IllegalUser -> HttpResponse.notFound()
                is IllegalState -> HttpResponse.status<Unit>(HttpStatus.CONFLICT)
                is TransactionError -> {
                    when (transactionResult.reason) {
                        is TransactionErrorReason.GenericReason -> {
                            HttpResponse.serverError("Não foi possível confirmar a transação")
                        }
                        is TransactionErrorReason.SweepingLimitExceeded -> {
                            val response = when (transactionResult.reason.limitType) {
                                SweepingLimitType.DAILY -> ResponseTO(code = "40031", message = "Limite diário excedido")
                                SweepingLimitType.WEEKLY -> ResponseTO(code = "40032", message = "Limite semanal excedido")
                                SweepingLimitType.MONTHLY -> ResponseTO(code = "40033", message = "Limite mensal excedido")
                                SweepingLimitType.YEARLY -> ResponseTO(code = "40034", message = "Limite anual excedido")
                                SweepingLimitType.GLOBAL -> ResponseTO(code = "40035", message = "Limite global excedido")
                                SweepingLimitType.TRANSACTION -> ResponseTO(code = "40036", message = "Limite por transação excedido")
                                SweepingLimitType.UNKNOWN -> ResponseTO(code = "40037", message = "Limite excedido")
                            }
                            HttpResponse.unprocessableEntity<ResponseTO>().body(response)
                        }
                    }
                }
                else -> HttpResponse.serverError("Não foi possível confirmar a transação")
            }
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError("Não foi possível confirmar a transação")
        }
    }

    @Delete("/{transactionId}/{msisdn}")
    fun cancelTransaction(
        @PathVariable transactionId: String,
        @PathVariable msisdn: String,
    ): HttpResponse<*> {
        val logName = "TransactionController#cancelTransaction"
        val markers = append("transactionId", transactionId)
        return try {
            val transactionResult = transactionService.cancel(TransactionId(transactionId), UserId.fromMsisdn(msisdn))
            markers.andAppend("transactionResult", transactionResult)

            if (transactionResult is Canceled) {
                logger.info(markers, logName)
            } else {
                logger.warn(markers, logName)
            }

            when (transactionResult) {
                Canceled -> HttpResponse.ok()
                TransactionNotFound -> HttpResponse.notFound()
                is IllegalUser -> HttpResponse.notFound()
                is IllegalState -> HttpResponse.status<Unit>(HttpStatus.CONFLICT)
                else -> HttpResponse.serverError("Não foi possível cancelar a transação")
            }
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError("Não foi possível cancelar a transação")
        }
    }

    @Post("/{transactionId}/{msisdn}/updatePaymentStatus")
    fun updatePaymentStatus(
        @PathVariable transactionId: String,
        @PathVariable msisdn: String,
        @Body body: UpdateTransactionPaymentStatusTO,
    ): HttpResponse<*> {
        val logName = "TransactionController#updatePaymentStatus"
        val userId = UserId.fromMsisdn(msisdn)

        val markers = append("transactionId", transactionId).andAppend("body", body).andAppend("userId", userId.value)

        return try {
            val transactionResult = transactionService.updatePaymentStatus(TransactionId(transactionId), userId, paymentStatus = body.status)
            markers.andAppend("transactionResult", transactionResult)

            if (transactionResult is Success) {
                logger.info(markers, logName)
            } else {
                logger.warn(markers, logName)
            }

            when (transactionResult) {
                is Success -> HttpResponse.ok()
                TransactionNotFound -> HttpResponse.notFound()
                is IllegalUser -> HttpResponse.notFound()
                is IllegalState -> HttpResponse.status<Unit>(HttpStatus.CONFLICT)
                is TransactionError -> HttpResponse.serverError("Não foi possível atualizar a transação")
                else -> HttpResponse.serverError("Não foi possível atualizar a transação")
            }
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError("Não foi possível atualizar a transação")
        }
    }

    private fun notifySetLimit(result: Success, confirmTO: ConfirmTO, state: BillComingDueHistoryState) {
        val userId = result.transaction.userId

        if (
            confirmTO.hasSetLimit ||
            confirmTO.transactionsAuthorized > 3 ||
            getTotalPaid(result.transaction.details, state) > MAX_AMOUNT_TO_NOTIFY_SET_LIMIT
        ) {
            return
        }

        val message = """
            Seu próximo pagamento por WhatsApp pode ser ainda mais rápido! 

            Ajuste o valor diário para pagar contas e fazer Pix direto por WhatsApp, sem precisar autorizar no App.
        """.trimIndent()

        val link = CTALink(
            displayText = "Ajustar valor",
            url = ButtonWhatsAppDeeplinkParameter("carteira/limites").value,
        )

        notificationService.notify(
            userId = userId,
            accountId = state.user.accountId,
            message = message,
            ctaLink = link,
        )

        conversationHistoryService.createAssistantMessage(userId, message)
    }

    private fun getTotalPaid(details: TransactionDetails, state: BillComingDueHistoryState): Long {
        return when (details) {
            is PixTransactionDetails -> details.amount
            is ScheduleBillsTransactionDetails -> state.walletWithBills.bills.filter { it.billId in details.bills }.fold(0L) { acc, billView -> acc + billView.amountTotal }
            is SweepingTransactionDetails -> state.walletWithBills.bills.filter { it.billId in details.bills }.fold(0L) { acc, billView -> acc + billView.amountTotal }
            is MarkAsPaidOrIgnoreBillsTransactionDetails, is AddBoletoTransactionDetails, is SendPixCodeTransactionDetails -> 0
        }
    }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(TransactionController::class.java)
    }
}

fun Transaction.isAuthorizable() = when (details) {
    is PixTransactionDetails -> true
    is ScheduleBillsTransactionDetails -> true
    is SweepingTransactionDetails -> true
    is MarkAsPaidOrIgnoreBillsTransactionDetails -> false
    is AddBoletoTransactionDetails -> false
    is SendPixCodeTransactionDetails -> false
}

fun Transaction.toTransactionTO() = TransactionTO(
    id = id,
    groupId = groupId.value,
    status = status,
    details = when (details) {
        is PixTransactionDetails -> details.toTransactionDetailsTO()
        is ScheduleBillsTransactionDetails -> details.toTransactionDetailsTO()
        is SweepingTransactionDetails -> details.toTransactionDetailsTO()
        is MarkAsPaidOrIgnoreBillsTransactionDetails, is AddBoletoTransactionDetails, is SendPixCodeTransactionDetails -> throw IllegalStateException("Transaction does not have a DetailsTO")
    },
    createdAt = formatToZonedDateTime(createdAt),
    updatedAt = formatToZonedDateTime(updatedAt),
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class TransactionTO(
    val id: TransactionId,
    val groupId: String,
    val status: TransactionStatus,
    val details: TransactionDetailsTO,
    val createdAt: String,
    val updatedAt: String,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class TransactionDetailsTO(
    val type: TransactionType,
    val totalAmount: Long? = null,
    val sweepingAmount: Long? = null,
    val sweepingParticipantId: String?,
    val bills: List<String>? = null,
    val pixKey: String? = null,
    val name: String? = null,
    val document: String? = null,
    val institution: String? = null,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class ConfirmTO(
    val authorizationToken: String,
    val hasSetLimit: Boolean,
    val transactionsAuthorized: Int,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class UpdateTransactionPaymentStatusTO(
    val status: TransactionPaymentStatus,
)

enum class TransactionPaymentStatus {
    UNKNOWN, SUCCESS, FAILED
}

fun PixTransactionDetails.toTransactionDetailsTO() = TransactionDetailsTO(
    type = type,
    totalAmount = amount,
    pixKey = qrCode?.value ?: pixKey.value,
    name = recipientName,
    document = recipientDocument,
    institution = recipientInstitution,
    sweepingParticipantId = sweepingParticipantId?.value,
)

fun SweepingTransactionDetails.toTransactionDetailsTO() = TransactionDetailsTO(
    type = type,
    sweepingAmount = amount,
    bills = bills.map { it.value },
    sweepingParticipantId = sweepingParticipantId.value,
)

fun ScheduleBillsTransactionDetails.toTransactionDetailsTO() = TransactionDetailsTO(
    type = type,
    bills = bills.map { it.value },
    sweepingParticipantId = null,
)

data class ResponseTO(
    val code: String,
    val message: String,
)