package ai.chatbot.adapters.api

import ai.chatbot.adapters.billPayment.BillPaymentHttpClient
import ai.chatbot.adapters.billPayment.getHttpClient
import ai.chatbot.adapters.messaging.ChatbotNotificationBuilder
import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.NotificationService
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.HistoryStateType
import ai.chatbot.app.conversation.InteractionWindowService
import ai.chatbot.app.conversation.UserConversationHistoryNotFound
import ai.chatbot.app.encrypt.EncryptService
import ai.chatbot.app.instrumentation.OnePixPayMetric
import ai.chatbot.app.job.ChatBotDailyLogService
import ai.chatbot.app.job.DailyLogQuery
import ai.chatbot.app.metrics.push
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ButtonWhatsAppDeeplinkParameter
import ai.chatbot.app.notification.CTALink
import ai.chatbot.app.notification.ChatBotNotificationDetailsTO
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.ChatbotWhatsappSimpleNotification
import ai.chatbot.app.notification.ChatbotWhatsappTemplatedNotification
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.FormattedBillInfo
import ai.chatbot.app.notification.KnownNotificationTypes
import ai.chatbot.app.notification.KnownTemplateConfigurationKeys
import ai.chatbot.app.notification.NotificationContextTemplatesService
import ai.chatbot.app.notification.NotificationMap
import ai.chatbot.app.notification.NotificationMedia
import ai.chatbot.app.notification.NotificationMediaType
import ai.chatbot.app.notification.NotificationParam
import ai.chatbot.app.notification.NotificationTemplate
import ai.chatbot.app.notification.OnboardingSinglePixNotificationService
import ai.chatbot.app.notification.OpenFinanceIncentiveDetailsTO
import ai.chatbot.app.notification.RawTemplateNotificationConfig
import ai.chatbot.app.notification.TextNotificationConfig
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.formattedAmount
import ai.chatbot.app.utils.getObjectMapper
import ai.chatbot.app.utils.parseObjectFrom
import ai.chatbot.app.utils.timeSince
import arrow.core.Either
import arrow.core.getOrElse
import io.micronaut.core.async.publisher.Publishers
import io.micronaut.http.HttpHeaders
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Filter
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Produces
import io.micronaut.http.annotation.Put
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import java.time.LocalDate
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers.append
import org.reactivestreams.Publisher
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Filter(value = ["/backoffice/**"])
class BackofficeControllerFilter(
    private val tenantService: TenantService,
    private val clients: Map<String, BillPaymentHttpClient>,
) : HttpServerFilter {
    override fun doFilter(
        request: HttpRequest<*>,
        chain: ServerFilterChain,
    ): Publisher<MutableHttpResponse<*>> {
        val internalAuth = tenantService.getConfiguration().internalAuth

        val logName = "BackofficeControllerFilter#doFilter"
        val authorization = request.headers[HttpHeaders.AUTHORIZATION]
        val markers = append("path", request.path)

        if (request.path.contains("/admin")) {
            try {
                val httpRequest = HttpRequest.POST("/backoffice/validateAuth", Unit)
                    .bearerAuth(authorization.removePrefix("Bearer "))
                    .header(HttpHeaders.USER_AGENT, "RxHttpClient")

                val response = clients.getHttpClient(tenantService.getTenantName()).exchange(httpRequest).firstOrError().blockingGet()

                if (response.status != HttpStatus.OK) {
                    logger.warn(markers.andAppend("validateStatus", response.status()), logName)
                    return Publishers.just(HttpResponse.status<Any>(HttpStatus.FORBIDDEN))
                }
            } catch (e: HttpClientResponseException) {
                logger.warn(markers.andAppend("validateStatus", e.status.code).andAppend("authorizationHeader", authorization), logName)
                return Publishers.just(HttpResponse.status<Any>(HttpStatus.FORBIDDEN))
            } catch (e: Exception) {
                logger.error(markers.andAppend("authorizationHeader", authorization), logName, e)
                return Publishers.just(HttpResponse.status<Any>(HttpStatus.INTERNAL_SERVER_ERROR))
            }

            logger.info(markers.andAppend("role", "ADMIN"), logName)
            return chain.proceed(request)
        }

        if (BasicAuthValidator.validate(request, internalAuth.identity, internalAuth.secret)) {
            logger.info(markers.andAppend("role", "BACKOFFICE"), logName)
            return chain.proceed(request)
        }

        logger.warn(markers, logName)
        return Publishers.just(HttpResponse.status<Any>(HttpStatus.UNAUTHORIZED))
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BackofficeControllerFilter::class.java)
    }
}

@Controller("/backoffice")
class BackofficeController(
    private val historyRepository: HistoryRepository,
    private val historyService: ConversationHistoryService,
    private val chatBotDailyLogService: ChatBotDailyLogService,
    private val notificationService: NotificationService,
    private val buildNotificationService: BuildNotificationService,
    private val interactionWindowService: InteractionWindowService,
    private val notificationContextTemplatesService: NotificationContextTemplatesService,
    private val customNotificationService: CustomNotificationService,
    private val paymentAdapter: PaymentAdapter,
    private val onboardingSinglePixNotificationService: OnboardingSinglePixNotificationService,
    private val encryptService: EncryptService,
    private val chatbotNotificationBuilder: ChatbotNotificationBuilder,
) {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    @Put("/history")
    fun messagesHistory(
        @Body body: GetHistoryTO,
    ): MutableHttpResponse<*> {
        val logName = "BackofficeController#messagesHistory"
        val markers = append("body", body).andAppend("userId", body.msisdn)
        return try {
            val history =
                historyRepository.find(
                    userId = UserId.fromMsisdn(body.msisdn),
                    date = LocalDate.parse(body.date),
                    template = body.type,
                )
            logger.info(markers, logName)
            HttpResponse.ok(getObjectMapper().writeValueAsString(history))
        } catch (ex: UserConversationHistoryNotFound) {
            HttpResponse.ok("message" to "usuário não tem histórico")
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError(
                mapOf(
                    "message" to "Não foi possível processar a mensagem",
                    "error" to ex.message,
                ),
            )
        }
    }

    @Post("/history/clear")
    fun clearHistory(
        @Body body: GetHistoryTO,
    ): MutableHttpResponse<*> {
        val logName = "BackofficeController#clearHistory"
        val markers = append("body", body).andAppend("userId", body.msisdn)
        return try {
            historyService.clearHistory(
                userId = UserId.fromMsisdn(body.msisdn),
                localDate = LocalDate.parse(body.date),
                template = body.type,
            )

            logger.info(markers, logName)

            HttpResponse.ok<Unit>()
        } catch (ex: UserConversationHistoryNotFound) {
            HttpResponse.ok("message" to "usuário não tem histórico")
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError(
                mapOf(
                    "message" to "Não foi possível processar a mensagem",
                    "error" to ex.message,
                ),
            )
        }
    }

    @Post("/interactionWindow/close")
    fun closeInteractionWindow(
        @Body body: CloseInteractionWindowTO,
    ): MutableHttpResponse<*> {
        val logName = "BackofficeController#closeInteractionWindow"
        val markers = append("body", body).andAppend("userId", body.msisdn)
        return try {
            interactionWindowService.close(UserId(body.msisdn), body.sendQueuedMessages)

            logger.info(markers, logName)

            HttpResponse.ok<Unit>()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError(
                mapOf(
                    "message" to "Não foi possível processar a mensagem",
                    "error" to ex.message,
                ),
            )
        }
    }

    @Post("/notification/{message}")
    fun notifyMessage(
        @Body rawBody: String,
        @PathVariable message: String,
    ): HttpResponse<*> {
        try {
            when (message) {
                "chatbot_ai_notify_bills_coming_due_last_warn" -> sendBillComingDueLastWarn(rawBody)
                "chatbot_ai_notify_outdated_pending_bills" -> sendBillOutdatedPending(rawBody)
                "chatbot_ai_notify_bills_coming_due_custom" -> sendBillComingDueCustom(rawBody)
                "chatbot_ai_promote_sweeping_account_mark_as_paid" -> sendPromoteSweepingAccountMarkAsPaid(rawBody)
                "chatbot_ai_promote_sweeping_account_cash_in" -> sendPromoteSweepingAccountCashIn(rawBody)
                "chatbot_ai_promote_sweeping_account_dda" -> sendPromoteSweepingAccountDDA(rawBody)
                "chatbot_ai_promote_sweeping_account_know_more" -> sendPromoteSweepingAccountKnowMore(rawBody)
                "chatbot_ai_promote_sweeping_account_opt_out" -> sendPromoteSweepingAccountOptOut(rawBody)
                "ai_chatbot_onboarding_pix_promote_sweeping_account" -> sendPromoteSweepingAccountOnboardingSinglePix(rawBody)
                "chatbot_ai_simple_notification_media" -> sendSimpleNotificationWithMedia(rawBody)
                "chatbot_ai_template_notification_media" -> sendTemplateWithMedia(rawBody)
                "ai_chatbot_onboarding_start" -> sendOnboardingStart(rawBody)
                "chatbot_ai_notify_bills_awaiting_coming_due" -> chatbotNotifyBillsAwaitingComingDue(rawBody)
                "custom" -> sendCustom(rawBody)
                else -> throw Exception("Invalid message")
            }
        } catch (e: Exception) {
            logger.error(
                append("rawBody", rawBody),
                "BackofficeController#notifyMessage",
                e,
            )
            return HttpResponse.serverError<Unit>()
        }

        return HttpResponse.ok("")
    }

    @Post("/notification/custom/{template}")
    fun notifyCustom(
        @Body body: Map<String, String>,
        @PathVariable template: String,
    ): HttpResponse<*> {
        try {
            val config = when (template) {
                "authorizePix" -> RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.authorizePix, KnownNotificationTypes.AUTHORIZE_PIX)
                else -> throw Exception("Invalid template")
            }

            val mobilePhone = body["mobilePhone"] ?: throw Exception("body missing mobilePhone")
            val accountId = AccountId(body["accountId"] ?: throw Exception("body missing accountId"))

            val model = body.entries
                .filter { it.key !in listOf("mobilePhone", "accountId") }
                .map { NotificationMap(NotificationParam.valueOf(it.key), it.value) }

            customNotificationService.send(
                user = User(
                    accountId = accountId,
                    id = UserId(mobilePhone),
                    name = "Test user",
                    accountGroups = listOf(),
                    status = AccountStatus.ACTIVE,
                    paymentStatus = AccountPaymentStatus.UpToDate,
                ),
                notificationConfig = config,
                params = model,
            )
        } catch (e: Exception) {
            logger.error(
                append("body", body),
                "BackofficeController#notifyCustom",
                e,
            )
            return HttpResponse.serverError<Unit>()
        }

        return HttpResponse.ok("")
    }

    @Post("/notifications/inspect")
    fun inspectNotification(@Body chatbotNotification: ChatBotNotificationDetailsTO): HttpResponse<*> {
        val logName = "BackofficeController#inspectNotification"
        val markers = append("notification", chatbotNotification)

        return try {
            val user = fakeUser()

            val result = chatbotNotificationBuilder.buildNotifications(
                details = chatbotNotification,
                user = user,
                walletProvider = if (chatbotNotification is OpenFinanceIncentiveDetailsTO) {
                    { fakeWallet() }
                } else {
                    null
                },
            )

            when (result) {
                is Either.Left -> {
                    logger.warn(markers.andAppend("error", result.value.name), logName)
                    HttpResponse.ok(
                        mapOf(
                            "status" to "ERROR",
                            "error" to result.value.name,
                        ),
                    )
                }

                is Either.Right -> {
                    logger.info(markers.andAppend("notificationsCount", result.value.size), logName)
                    HttpResponse.ok(
                        mapOf(
                            "notifications" to result.value.map { notification ->
                                mapOf(
                                    "text" to notification.text,
                                    "type" to when (val notif = notification.notification) {
                                        is ChatbotRawTemplatedNotification -> notif.notificationType?.value ?: "UNKNOWN"
                                        is ChatbotWhatsappTemplatedNotification -> "TEMPLATED"
                                        is ChatbotWhatsappSimpleNotification -> "SIMPLE"
                                        null -> "SYSTEM_MESSAGE"
                                        else -> "UNKNOWN"
                                    },
                                )
                            },
                            "notificationCount" to result.value.size,
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            HttpResponse.serverError(
                mapOf(
                    "message" to "Não foi possível inspecionar conversão da notificação",
                    "error" to e.message,
                ),
            )
        }
    }

    @Post("/metrics/pre-populate")
    fun prepopulateMetrics(): HttpResponse<*> {
        // TODO: deveria ter uma forma inteligente de carregar todas as implementações
        OnePixPayMetric.RequestedToNotifyUser.push()
        OnePixPayMetric.NotifiedUser.push()
        OnePixPayMetric.UserQuickRepliedPayAll.push()
        OnePixPayMetric.UserQuickRepliedMarkAllAsPaid.push()
        OnePixPayMetric.UserRequestedToPayBills.All.push()
        OnePixPayMetric.UserRequestedToPayBills.Some.push()
        OnePixPayMetric.BillsChangedAfterInteraction.MarkAsPaid.push()
        OnePixPayMetric.BillsChangedAfterInteraction.SendPixCode.push()
        OnePixPayMetric.BillsChangedAfterInteraction.SendSubscriptionPixCode.push()
        OnePixPayMetric.BillsChangedAfterInteraction.TransactionConfirm.push()
        OnePixPayMetric.BillsChangedAfterInteraction.TransactionCancel.push()
        OnePixPayMetric.BillsChangedAfterInteraction.MarkReminderAsDone.push()
        OnePixPayMetric.SentOnePixPayCodeToUser.push()
        OnePixPayMetric.RequestedToMarkAsPaid.push()

        return HttpResponse.noContent<Unit>()
    }

    @Post("/onboarding/resume/{userId}")
    fun resumeOnboarding(
        @PathVariable("userId") msisdn: String,
    ): HttpResponse<*> {
        val logName = "BackofficeController#resumeOnboarding"
        val markers = append("userId", msisdn)

        val userId = UserId(msisdn)

        try {
            val today = getLocalDate()
            val userAndWallet = runBlocking {
                paymentAdapter.findPendingBills(userId, today, today)
            }.getOrElse {
                logger.error(markers.andAppend("error", it), logName)
                return HttpResponse.serverError("error finding user")
            }

            val user = userAndWallet.user

            val state = try {
                historyService.findLatestConversationHistoryState<BillComingDueHistoryState>(user.id)
            } catch (e: UserConversationHistoryNotFound) {
                historyService.setupEmptyHistoryState(
                    userId = user.id,
                    initialAssistantMessage = null,
                    initialUserAndWallet = UserAndWallet(
                        user = user,
                        wallet = userAndWallet.wallet,
                    ),
                )
            }

            historyService.createSystemMessage(userId, onboardingSinglePixNotificationService.getSinglePixStartContextMessage())
            customNotificationService.send(
                user = user,
                notificationConfig = RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.onboardingStart, KnownNotificationTypes.ONBOARDING_START),
                params = listOf(),
            )

            return HttpResponse.ok<Unit>()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return HttpResponse.serverError(e.message)
        }
    }

    private fun sendBillComingDueCustom(
        rawBody: String,
    ) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        val fakeUser =
            User(
                accountId = AccountId(value = body.accountId),
                id = UserId(value = body.mobilePhone),
                name = body.userName,
                accountGroups = listOf(),
                status = AccountStatus.ACTIVE,
                paymentStatus = body.paymentStatus ?: AccountPaymentStatus.UpToDate,
            )

        val notification = customNotificationService.buildBillsComingDueCustom(
            user = fakeUser,
            bills =
            body.formattedBillsMessage.map {
                FormattedBillInfo(
                    description = it,
                    amount = (100..1000L).random().formattedAmount(),
                )
            },
        )

        notificationService.notify(notification.notification)
    }

    private fun sendBillOutdatedPending(
        rawBody: String,
        paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
    ) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        val fakeUser = User(accountId = AccountId(value = body.accountId), id = UserId(value = body.mobilePhone), name = body.userName, accountGroups = listOf(), status = AccountStatus.ACTIVE, paymentStatus = paymentStatus)
        val notification =
            buildNotificationService.buildOutdatedPendingBillsNotification(
                user = fakeUser,
                formattedBillsMessage = body.formattedBillsMessage,
                payLoadDate = body.payLoadDate,
            )

        notificationService.notify(
            userId = fakeUser.id,
            accountId = fakeUser.accountId,
            message = notification.message,
            quickReplyButtons = notification.quickReplyButtons,
        )
    }

    private fun sendBillComingDueLastWarn(
        rawBody: String,
        paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
    ) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        val fakeUser = User(accountId = AccountId(value = body.accountId), id = UserId(value = body.mobilePhone), name = body.userName, accountGroups = listOf("ALPHA", "DEVELOPER"), status = AccountStatus.ACTIVE, paymentStatus = paymentStatus)
        val notification =
            buildNotificationService.buildBillsComingDueLastWarnNotification(
                payLoadDate = body.payLoadDate,
                accountId = fakeUser.accountId,
                mobilePhone = body.mobilePhone,
                userName = fakeUser.name,
                paymentLimitTime = "23:59",
            )

        notificationService.notify(notification)
    }

    private fun sendPromoteSweepingAccountMarkAsPaid(
        rawBody: String,
        paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
    ) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        val fakeUser = User(accountId = AccountId(value = body.accountId), id = UserId(value = body.mobilePhone), name = body.userName, accountGroups = listOf("ALPHA", "DEVELOPER"), status = AccountStatus.ACTIVE, paymentStatus = paymentStatus)
        val notification =
            buildNotificationService.buildPromoteSweepingAccountMarkAsPaidNotification(
                accountId = fakeUser.accountId,
                mobilePhone = body.mobilePhone,
            )

        notificationService.notify(notification)
    }

    private fun sendPromoteSweepingAccountCashIn(
        rawBody: String,
        paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
    ) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        val fakeUser = User(accountId = AccountId(value = body.accountId), id = UserId(value = body.mobilePhone), name = body.userName, accountGroups = listOf("ALPHA", "DEVELOPER"), status = AccountStatus.ACTIVE, paymentStatus = paymentStatus)
        val notification =
            buildNotificationService.buildPromoteSweepingAccountCashInNotification(
                accountId = fakeUser.accountId,
                mobilePhone = body.mobilePhone,
            )

        notificationService.notify(notification)
    }

    private fun sendPromoteSweepingAccountDDA(
        rawBody: String,
        paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
    ) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        val fakeUser = User(accountId = AccountId(value = body.accountId), id = UserId(value = body.mobilePhone), name = body.userName, accountGroups = listOf("ALPHA", "DEVELOPER"), status = AccountStatus.ACTIVE, paymentStatus = paymentStatus)
        val notification =
            buildNotificationService.buildPromoteSweepingAccountDDANotification(
                accountId = fakeUser.accountId,
                mobilePhone = body.mobilePhone,
                billInfo = FormattedBillInfo("Nome da Conta", "R$ 123,45"),
            )

        notificationService.notify(notification)
    }

    private fun sendTemplateWithMedia(
        rawBody: String,
    ) {
        val body = parseObjectFrom<NotifyTemplateWithMediaTO>(rawBody)
        val media = detectMedia(body.media)

        val templateNotification = ChatbotWhatsappTemplatedNotification(
            mobilePhone = body.mobilePhone,
            accountId = AccountId(body.accountId),
            template = NotificationTemplate(body.template),
            configurationKey = body.configurationKey,
            parameters = body.parameters,
            quickReplyButtonsWhatsAppParameter = body.quickReplyButtonsWhatsAppParameter,
            quickRepliesStartIndex = body.quickRepliesStartIndex,
            buttonWhatsAppParameter = body.buttonWhatsAppParameter?.let { ButtonWhatsAppDeeplinkParameter(body.buttonWhatsAppParameter) },
            media = media,
        )

        notificationService.notify(templateNotification)
    }

    private fun sendSimpleNotificationWithMedia(
        rawBody: String,
    ) {
        val body = parseObjectFrom<NotifyResponseWithMediaTO>(rawBody)
        val media = detectMedia(body.media)

        notificationService.notify(
            userId = UserId(value = body.mobilePhone),
            accountId = AccountId(value = body.accountId),
            message = null,
            media = media,
            delay = 0,
        )
    }

    private fun detectMedia(media: MediaTO): NotificationMedia {
        return when (media.mediaType) {
            NotificationMediaType.DOCUMENT -> NotificationMedia.Document(
                url = media.mediaPublicUrl,
                filename = media.filename ?: "no_name",
                documentType = media.mimeType,
            )

            NotificationMediaType.IMAGE -> NotificationMedia.Image(
                url = media.mediaPublicUrl,
                imageType = media.mimeType,
                title = if (media is ResponseMediaTO) {
                    media.title
                } else {
                    null
                },
                text = if (media is ResponseMediaTO) {
                    media.text
                } else {
                    null
                },
            )

            NotificationMediaType.VIDEO -> NotificationMedia.Video(
                url = media.mediaPublicUrl,
                videoType = media.mimeType,
            )
        }
    }

    private fun sendPromoteSweepingAccountOnboardingSinglePix(
        rawBody: String,
        paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
    ) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        val fakeUser = User(accountId = AccountId(value = body.accountId), id = UserId(value = body.mobilePhone), name = body.userName, accountGroups = listOf("ALPHA", "DEVELOPER"), status = AccountStatus.ACTIVE, paymentStatus = paymentStatus)
        customNotificationService.send(fakeUser, listOf(), customNotificationService.config().onboardingPromoteSweepingAccount ?: throw IllegalStateException("onboardingPromoteSweepingAccount config not found"))
    }

    private fun sendOnboardingStart(
        rawBody: String,
        paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
    ) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        val fakeUser = User(accountId = AccountId(value = body.accountId), id = UserId(value = body.mobilePhone), name = body.userName, accountGroups = listOf("ALPHA", "DEVELOPER"), status = AccountStatus.ACTIVE, paymentStatus = paymentStatus)
        customNotificationService.send(fakeUser, listOf(), RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.onboardingStart, KnownNotificationTypes.ONBOARDING_START))
    }

    private fun chatbotNotifyBillsAwaitingComingDue(
        rawBody: String,
    ) {
        val body = parseObjectFrom<ChatbotNotifyBillsAwaitingComingDue>(rawBody)
        val fakeUser = User(accountId = AccountId(value = "FAKE_ACCOUNT_ID"), id = UserId(value = body.mobilePhone), name = "Fake Username", accountGroups = listOf("ALPHA", "DEVELOPER"), status = AccountStatus.ACTIVE, paymentStatus = AccountPaymentStatus.UpToDate)

        val config = if (body.totalWaitingApproveBills == 1) {
            KnownTemplateConfigurationKeys.waitingApprovalBillsSingular
        } else {
            KnownTemplateConfigurationKeys.waitingApprovalBillsPlural
        }

        val notification = customNotificationService.buildRawTemplateNotification(
            user = fakeUser,
            notificationConfig = RawTemplateNotificationConfig(config, KnownNotificationTypes.WAITING_APPROVAL_BILLS),
            params = listOf(
                NotificationMap(NotificationParam.TOTAL_BILLS, body.totalWaitingApproveBills.toString()),
            ),
        )

        notificationService.notify(notification.notification, 0)
    }

    private fun sendCustom(
        rawBody: String,
        paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
    ) {
        val body = parseObjectFrom<NotifyCustomTO>(rawBody)
        val fakeUser = User(accountId = AccountId(value = body.accountId), id = UserId(value = body.mobilePhone), name = "Fulano", accountGroups = listOf("ALPHA", "DEVELOPER"), status = AccountStatus.ACTIVE, paymentStatus = paymentStatus)
        customNotificationService.send(fakeUser, body.buildParamMap(), body.config)
    }

    private fun sendPromoteSweepingAccountKnowMore(rawBody: String) =
        sendNotificationMessage(
            rawBody = rawBody,
            message = notificationContextTemplatesService.getPromoteSweepingAccountKnowMoreMessage(),
            link = CTALink(
                displayText = "Conectar com meu banco",
                url = ButtonWhatsAppDeeplinkParameter("open-finance").value,
            ),
        )

    private fun sendPromoteSweepingAccountOptOut(rawBody: String) =
        sendNotificationMessage(
            rawBody = rawBody,
            message = notificationContextTemplatesService.getPromoteSweepingAccountOptOutMessage(),
        )

    private fun sendNotificationMessage(
        rawBody: String,
        paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
        message: String,
        link: CTALink? = null,
    ) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        val fakeUser = User(accountId = AccountId(value = body.accountId), id = UserId(value = body.mobilePhone), name = body.userName, accountGroups = listOf("ALPHA", "DEVELOPER"), status = AccountStatus.ACTIVE, paymentStatus = paymentStatus)
        notificationService.notify(
            userId = fakeUser.id,
            accountId = fakeUser.accountId,
            message = message,
            ctaLink = link,
        )
    }

    @Get("/historyByDate/{date}")
    fun messagesHistoryByDate(
        @PathVariable date: String,
    ): MutableHttpResponse<*> {
        val localDate = LocalDate.parse(date)
        val logName = "BackofficeController#messagesHistoryByDate"
        return try {
            chatBotDailyLogService.conversationsReportByDate(localDate)
            HttpResponse.ok("Email enviado para o canal")
        } catch (ex: Exception) {
            logger.error(logName, ex)
            HttpResponse.serverError(
                mapOf(
                    "message" to "Não foi possível processar a mensagem",
                    "error" to ex.message,
                ),
            )
        }
    }

    @Post("/dailyLog/send/{userId}/{date}")
    fun sendDailyLog(
        @PathVariable date: String,
        @PathVariable userId: String,
    ): MutableHttpResponse<*> {
        val localDate = LocalDate.parse(date)
        val logName = "BackofficeController#sendDailyLog"
        return try {
            chatBotDailyLogService.processConversation(localDate, UserId(userId), prefix = "(Backoffice)", shouldSend = true)
            HttpResponse.ok("Email enviado para o canal")
        } catch (ex: Exception) {
            logger.error(logName, ex)
            HttpResponse.serverError(
                mapOf(
                    "message" to "Não foi possível processar a mensagem",
                    "error" to ex.message,
                ),
            )
        }
    }

    @Produces(value = [MediaType.TEXT_HTML])
    @Get("/dailyLog/send/{userId}/{date}")
    fun getDailyLog(
        @PathVariable date: String,
        @PathVariable userId: String,
    ): MutableHttpResponse<*> {
        val localDate = LocalDate.parse(date)
        val logName = "BackofficeController#getDailyLog"
        val markers = append("date", date).andAppend("userId", userId)

        return try {
            val result = chatBotDailyLogService.processConversation(localDate, UserId(userId), shouldSend = false)
            HttpResponse.ok(result.emailBody)
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError(
                mapOf(
                    "message" to "Não foi possível processar a mensagem",
                    "error" to ex.message,
                ),
            )
        }
    }

    @Produces(value = [MediaType.TEXT_HTML])
    @Post("/admin/dailyLog/query")
    fun queryDailyLogs(
        @Body body: DailyLogQueryTO,
    ): MutableHttpResponse<*> {
        val logName = "BackofficeController#queryDailyLog"
        val markers = append("body", body)

        val startTime = getZonedDateTime()

        return try {
            val conversationsResult = body.conversations?.let { to ->
                to.map {
                    val (userId, date) = it.toPair()
                    chatBotDailyLogService.processConversation(
                        date = date,
                        userId = UserId(userId),
                        prefix = null,
                        forceClassify = false,
                        shouldSend = false,
                    )
                }
            } ?: emptyList()

            val queryResult = body.query?.let { chatBotDailyLogService.processQuery(it.toQuery()) } ?: emptyList()

            val result = conversationsResult + queryResult

            logger.info(markers.andAppend("responseTime", timeSince(startTime)), logName)

            return when (result.size) {
                0 -> HttpResponse.noContent<Unit>()
                1 -> HttpResponse.ok(result.single().emailBody)
                else -> HttpResponse.ok(multipleConversationsTemplate(result.joinToString("\n") { multipleConversationsWrapper(it.subject, it.emailBody) }))
            }
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError(
                mapOf(
                    "message" to "Não foi possível realizar query",
                    "error" to ex.message,
                ),
            )
        }
    }

    @Post("/decrypt")
    fun decrypt(
        @Body body: DecryptTO,
    ): MutableHttpResponse<*> {
        val logName = "BackofficeController#decrypt"
        return try {
            val content = encryptService.decrypt(body.message)
            return HttpResponse.ok(content)
        } catch (ex: Exception) {
            logger.error(logName, ex)
            throw ex
        }
    }

    companion object {
        private fun multipleConversationsTemplate(content: String) = """
            <html>
                <head>
                    <style>
                        .collapsible { background-color: #ddd; color: #444; cursor: pointer; padding: 18px; width: 100%; border: none; text-align: left; outline: none; font-size: 15px; margin-top: 10px; }
                        .active, .collapsible:hover { background-color: #eee; }
                        .content { display: none; overflow: hidden; border: groove; padding: 10px; }
                    </style>
                </head>
                <body>
                    $content
                    <script>
                        var coll = document.getElementsByClassName("collapsible");
                        var i;

                        for (i = 0; i < coll.length; i++) {
                            coll[i].addEventListener("click", function() {
                                this.classList.toggle("active");
                                var content = this.nextElementSibling;
                                if (content.style.display === "block") {
                                content.style.display = "none";
                                } else {
                                content.style.display = "block";
                                }
                            });
                        }
                    </script>
                </body>
            </html>
        """.trimIndent()

        private fun multipleConversationsWrapper(title: String, content: String) = """<button type="button" class="collapsible">$title</button> <div class="content"> $content </div> """
    }
}

data class DecryptTO(
    val message: String,
)

data class ChatbotNotifyBillsAwaitingComingDue(
    val totalWaitingApproveBills: Int,
    val mobilePhone: String,
)

data class NotifyMessageTO(
    val mobilePhone: String,
    val accountId: String,
    val userName: String,
    val formattedBillsMessage: List<String>,
    val billsCount: Int,
    val payLoadDate: LocalDate,
    val firstNotification: Boolean,
    val paymentStatus: AccountPaymentStatus? = AccountPaymentStatus.UpToDate,
)

data class NotifyCustomTO(
    val mobilePhone: String,
    val accountId: String,
    val config: TextNotificationConfig,
    val params: Map<String, String>,
) {
    fun buildParamMap(): List<NotificationMap> = params.map { (key, value) ->
        NotificationMap(NotificationParam.valueOf(key), value)
    }
}

data class NotifyTemplateWithMediaTO(
    val mobilePhone: String,
    val accountId: String,
    val template: String,
    val configurationKey: String?,
    val parameters: List<String> = listOf(),
    val quickReplyButtonsWhatsAppParameter: List<String> = emptyList(),
    val quickRepliesStartIndex: Int? = null,
    val buttonWhatsAppParameter: String? = null,
    val media: TemplateMediaTO,
)

sealed interface MediaTO {
    val mediaType: NotificationMediaType
    val mimeType: String
    val mediaPublicUrl: String
    val filename: String?
}

data class TemplateMediaTO(
    override val mediaType: NotificationMediaType,
    override val mimeType: String,
    override val mediaPublicUrl: String,
    override val filename: String?,
) : MediaTO

data class NotifyResponseWithMediaTO(
    val mobilePhone: String,
    val accountId: String,
    val media: ResponseMediaTO,
)

data class ResponseMediaTO(
    override val mediaType: NotificationMediaType,
    override val mimeType: String,
    override val mediaPublicUrl: String,
    override val filename: String?,
    val title: String?,
    val text: String?,
) : MediaTO

data class GetHistoryTO(val msisdn: String, val date: String, val type: HistoryStateType)

data class CloseInteractionWindowTO(val msisdn: String, val sendQueuedMessages: Boolean)

data class EncryptRequestTO(
    val data: String,
)

data class DailyLogQueryTO(
    val query: QueryTO? = null,
    val conversations: List<ConversationTO>? = null,
) {
    data class QueryTO(
        val date: String,
        val limit: Int? = null,
        val queries: List<List<DailyLogQuery.Rule>>,
    ) {
        fun toQuery() = DailyLogQuery(
            date = LocalDate.parse(date, dateFormat),
            limit = limit,
            queries = queries,
        )
    }

    data class ConversationTO(
        val userId: String,
        val date: String,
    ) {
        fun toPair() = userId to LocalDate.parse(date, dateFormat)
    }
}

private fun fakeUser(): User {
    return User(
        accountId = AccountId("INSPECTION_ACCOUNT_ID"),
        id = UserId.fromMsisdn("*************"),
        name = "Inspection User",
        accountGroups = emptyList(),
        status = AccountStatus.ACTIVE,
        paymentStatus = AccountPaymentStatus.UpToDate,
    )
}

private fun fakeWallet(): WalletWithBills {
    return WalletWithBills(
        bills = emptyList(),
        totalWaitingApproval = 0,
        walletId = WalletId("inspection"),
        walletName = "inspection",
        activeConsents = emptyList(),
    )
}