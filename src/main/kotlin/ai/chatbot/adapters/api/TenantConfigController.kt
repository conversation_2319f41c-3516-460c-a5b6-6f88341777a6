package ai.chatbot.adapters.api

import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.config.TenantService
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import kotlin.random.Random
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.slf4j.MDC

@Controller("/tenant")
open class TenantConfigController(
    private val tenantService: TenantService,
    private val tenantPropagator: TenantPropagator,
) {
    @Get("/")
    fun getTenantConfiguration(): HttpResponse<*> {
        val tenantConfig = tenantService.getTenantName()
        return HttpResponse.ok(tenantConfig)
    }

    @Get("/feature-flags")
    fun getFeatureFlag(@PathVariable featureFlagName: String): HttpResponse<*> {
        val featureFlag = tenantService.getConfiguration().flags
        return HttpResponse.ok(featureFlag)
    }

    @Get("/coroutine/{tenantId}")
    suspend fun testCoroutine(@PathVariable tenantId: String): HttpResponse<String> {
        tenantPropagator.executeWithTenant(tenant = tenantId) {
            runBlocking {
                testeMdcPropagation(tenantId, "testNormal")
            }
        }

        tenantPropagator.executeWithTenantWithSuspend(tenant = tenantId) {
            testeMdcPropagation(tenantId, "testSuspend")
        }

        return HttpResponse.ok("Teste de propagação de tenant em corotinas concluído!")
    }

    suspend fun testeMdcPropagation(tenant: String, method: String) {
        println("Antes da corrotina: $method $tenant = ${MDC.get("X-TENANT-ID")}")
        delay(Random.nextInt() % 100L)
        println("Depois do corrotina: $method $tenant = ${MDC.get("X-TENANT-ID")}")

        noSuspendFunction(tenant, method)
    }

    fun noSuspendFunction(tenant: String, method: String) {
        println("Antes da função não suspensa: $method $tenant = ${MDC.get("X-TENANT-ID")}")
        Thread.sleep(100) // Força uma possível troca de thread
        println("Depois da função não suspensa: $method $tenant = ${MDC.get("X-TENANT-ID")}")
    }
}