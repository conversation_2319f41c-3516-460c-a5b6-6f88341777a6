package ai.chatbot.adapters.api

import ai.chatbot.app.user.UserId
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.exceptions.HttpStatusException
import java.util.Base64

const val AUTHENTICADED_USER = "username"
const val BLIP_SECRET_HEADER = "X-BLIP-SECRET"
const val BLIP_USER_HEADER = "X-USER-IDENTIFIER"

class BasicAuthValidator {
    companion object {
        fun validate(
            request: HttpRequest<*>,
            internalIdentity: String,
            internalSecret: String,
        ): Bo<PERSON>an {
            try {
                decodeAuthorizationHeader(request)?.let { requestAuth ->
                    if (requestAuth == "$internalIdentity:$internalSecret") {
                        return true
                    }
                }
                return false
            } catch (ex: Exception) {
                return false
            }
        }

        fun executeBlipAuthentication(
            request: HttpRequest<*>,
            internalSecret: String,
        ): Bo<PERSON>an {
            try {
                if (request.headers[BLIP_SECRET_HEADER] == internalSecret) {
                    request.setAttribute(AUTHENTICADED_USER, request.headers[BLIP_USER_HEADER])
                    return true
                }
                return false
            } catch (ex: Exception) {
                return false
            }
        }

        private fun decodeAuthorizationHeader(request: HttpRequest<*>): String? {
            val authorization = request.headers["authorization"]
            return if (authorization.startsWith("Basic ")) {
                return authorization.removePrefix("Basic ").base64Decode()
            } else {
                null
            }
        }
    }
}

private fun String.base64Decode(): String {
    return String(Base64.getDecoder().decode(this))
}

fun HttpRequest<*>.getAuthenticatedUser(): UserId {
    val userName = this.attributes.get(AUTHENTICADED_USER, String::class.java).orElseThrow { HttpStatusException(HttpStatus.FORBIDDEN, "User is not authenticated") }
    return UserId.fromMsisdn(userName)
}