package ai.chatbot.adapters.api

import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.ConversationService
import ai.chatbot.app.conversation.InterceptAction
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.MessageContent
import ai.chatbot.app.conversation.UserMessageValidationService
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.utils.andAppend
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.github.javaparser.utils.StringEscapeUtils
import io.micronaut.core.async.publisher.Publishers
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Filter
import io.micronaut.http.annotation.Post
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import java.time.Duration
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers.append
import org.reactivestreams.Publisher
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Filter(value = ["/chatbot/**"])
class ChatbotControllerFilter(
    private val tenantService: TenantService,
) :
    HttpServerFilter {
    override fun doFilter(
        request: HttpRequest<*>,
        chain: ServerFilterChain,
    ): Publisher<MutableHttpResponse<*>> {
        if (BasicAuthValidator.executeBlipAuthentication(request, tenantService.getConfiguration().blipAuth.secret)) {
            return chain.proceed(request)
        }
        return Publishers.just(HttpResponse.status<Any>(HttpStatus.UNAUTHORIZED))
    }
}

const val PAYLOAD_HEADER = "X-ACTION-PAYLOAD"

@Controller("/chatbot")
class MainController(
    private val openAiService: ConversationService,
    private val userMessageValidationService: UserMessageValidationService,
    private val tenantService: TenantService,
) {
    @Post("/message")
    suspend fun message(
        @Body body: MessageTO,
        request: HttpRequest<*>,
    ): MutableHttpResponse<String>? {
        val userId = request.getAuthenticatedUser()
        val initial = ZonedDateTime.now()
        val logName = "MessageReceived"
        val markers = append("body", body).andAppend("userId", userId.value)

        if (tenantService.getConfiguration().flags.waCommCentre.check(userId)) {
            logger.info(markers, "$logName/disabled")
            return HttpResponse.ok("ok")
        }

        logger.info(markers.andAppend("timeInitial", initial), "$logName#start")

        val buttonPayload = body.date?.let {
            if (it.contains("\\")) { // Is escaped JSON
                StringEscapeUtils.unescapeJava(it)
            } else {
                it
            }
        }

        return try {
            val interceptAction =
                if (body.action.isNullOrBlank()) {
                    null
                } else {
                    InterceptAction(
                        type = InterceptMessagePayloadType.valueOf(body.action),
                        transactionId = body.transactionId?.let { TransactionId(it) },
                        payload = buttonPayload,
                        amount = if (body.amount.isNullOrBlank()) null else body.amount.toLong(),
                    )
                }

            val payloadValidationResult = userMessageValidationService.validatePayload(buttonPayload, interceptAction)
            markers.andAppend("payloadDateValidationResult", payloadValidationResult)
            openAiService.asyncProcessChat(userId, MessageContent(body.message), payloadValidationResult, interceptAction)
            val final = ZonedDateTime.now()
            val duration = Duration.between(initial, final)
            logger.info(
                markers.andAppend("timeFinal", final).andAppend("timeTaken", duration).andAppend("timeTakenMillis", duration.toMillis()),
                "$logName#end",
            )
            HttpResponse.ok("ok")
        } catch (ex: Exception) {
            val final = ZonedDateTime.now()
            val duration = Duration.between(initial, final)
            logger.error(markers.andAppend("timeFinal", final).andAppend("timeTaken", duration).andAppend("timeTakenMillis", duration.toMillis()), logName, ex)
            HttpResponse.serverError("Não foi possível processar a mensagem")
        }
    }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(MainController::class.java)
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class MessageTO(val message: String, val date: String? = null, val action: String? = null, val transactionId: String? = null, val amount: String? = null)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MessagePayloadTO(val payload: String, val action: String? = null)