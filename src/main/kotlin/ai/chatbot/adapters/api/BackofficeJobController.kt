package ai.chatbot.adapters.api

import ai.chatbot.app.job.JobManager
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.andAppend
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.TaskScheduler
import jakarta.inject.Named
import java.time.Duration
import java.util.*
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/backoffice/job")
class BackofficeJobController(
    private val jobManager: Optional<JobManager>,
    @param:Named(TaskExecutors.SCHEDULED) private val taskScheduler: TaskScheduler,
) {
    @Post("/{jobName}/execute/")
    fun executeJob(@PathVariable jobName: String): HttpResponse<*> {
        val markers = Markers.append("jobName", jobName)

        val abstractJob = jobManager.get().listJobs().single { it.jobName == jobName }
        markers.andAppend("previousLastStartTime", abstractJob.lastStartTime)

        val now = getZonedDateTime()

        taskScheduler.schedule(Duration.ZERO, abstractJob)

        while (abstractJob.lastStartTime?.isBefore(now) != false) {
            Thread.sleep(500)
        }
        markers.andAppend("currentLastStartTime", abstractJob.lastStartTime)

        LOG.info(markers, "BackofficeJobController#executeJob")
        return HttpResponse.ok<Unit>()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeJobController::class.java)
    }
}