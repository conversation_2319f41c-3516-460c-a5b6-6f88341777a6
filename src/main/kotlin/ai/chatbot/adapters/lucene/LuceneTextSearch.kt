package ai.chatbot.adapters.lucene

import ai.chatbot.app.SearchElement
import ai.chatbot.app.TextSearchAdapter
import jakarta.inject.Singleton
import java.io.Reader
import org.apache.commons.codec.language.RefinedSoundex
import org.apache.lucene.analysis.CharArraySet
import org.apache.lucene.analysis.LowerCaseFilter
import org.apache.lucene.analysis.StopFilter
import org.apache.lucene.analysis.StopwordAnalyzerBase
import org.apache.lucene.analysis.TokenStream
import org.apache.lucene.analysis.miscellaneous.ASCIIFoldingFilter
import org.apache.lucene.analysis.phonetic.PhoneticFilter
import org.apache.lucene.analysis.standard.StandardTokenizer
import org.apache.lucene.document.Document
import org.apache.lucene.document.Field
import org.apache.lucene.document.StoredField
import org.apache.lucene.document.TextField
import org.apache.lucene.index.DirectoryReader
import org.apache.lucene.index.IndexWriter
import org.apache.lucene.index.IndexWriterConfig
import org.apache.lucene.queryparser.classic.MultiFieldQueryParser
import org.apache.lucene.queryparser.classic.QueryParser
import org.apache.lucene.search.IndexSearcher
import org.apache.lucene.store.ByteBuffersDirectory

const val MAX_RESULTS = 10

@Singleton
class LuceneTextSearch : TextSearchAdapter {
    override fun search(
        text: String,
        elements: List<SearchElement>,
    ): List<String> {
        // add a \ before special characters + - && || ! ( ) { } [ ] ^ " ~ * ? : \ to avoid parse errors
        val escapedText = text.replace("([+\\-!(){}\\[\\]^\"~*?:\\\\])".toRegex(), "\\\\$1")

        val analyzer = CustomAnalyzer()

        val directory = ByteBuffersDirectory()

        val directoryWriter = IndexWriter(directory, IndexWriterConfig(analyzer))

        directoryWriter.addDocuments(elements.map { it.toDocument() })

        directoryWriter.flush()
        directoryWriter.close()

        val parser = MultiFieldQueryParser(elements.extractFieldNames().toTypedArray(), analyzer)
        parser.fuzzyMinSim = 1f
        parser.fuzzyPrefixLength = 2
        parser.defaultOperator = QueryParser.AND_OPERATOR

        val standardQuery = parser.parse(escapedText)
        val fuzzyQuery = parser.parse(escapedText.replace(" ", "~ ") + "~")

        val indexReader = DirectoryReader.open(directory)
        val dirSearcher = IndexSearcher(indexReader)

        var topDocs = dirSearcher.search(standardQuery, MAX_RESULTS)

        if (topDocs.totalHits.value == 0L) {
            topDocs = dirSearcher.search(fuzzyQuery, MAX_RESULTS)
        }

        return topDocs.scoreDocs.map {
            val document = dirSearcher.doc(it.doc)
            document["id"]
        }.also {
            indexReader.close()
        }
    }

    private fun List<SearchElement>.extractFieldNames(): Set<String> {
        val names = mutableSetOf<String>()

        this.forEach {
            it.fields.keys.forEach { key ->
                names.add(key)
            }
        }

        return names
    }

    private fun SearchElement.toDocument(): Document {
        val doc = Document()

        doc.add(StoredField("id", id))
        fields.entries.forEach {
            doc.add(TextField(it.key, it.value, Field.Store.YES))
        }

        return doc
    }
}

data class CustomAnalyzer(val maxTokenLength: Int = 255) : StopwordAnalyzerBase(CharArraySet.EMPTY_SET) {
    override fun createComponents(fieldName: String): TokenStreamComponents {
        val tokenizer = StandardTokenizer()
        tokenizer.maxTokenLength = maxTokenLength

        return TokenStreamComponents(
            { r: Reader? ->
                tokenizer.maxTokenLength = maxTokenLength
                tokenizer.setReader(r)
            },
            PhoneticFilter(
                StopFilter(createBaseTokenStream(tokenizer), stopwords),
                RefinedSoundex(),
                true,
            ),
        )
    }

    override fun normalize(
        fieldName: String,
        input: TokenStream,
    ): TokenStream {
        return createBaseTokenStream(input)
    }

    private fun createBaseTokenStream(input: TokenStream) = LowerCaseFilter(ASCIIFoldingFilter(input))
}