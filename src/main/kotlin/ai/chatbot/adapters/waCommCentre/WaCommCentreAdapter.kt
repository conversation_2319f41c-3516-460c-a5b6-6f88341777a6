package ai.chatbot.adapters.waCommCentre

import ai.chatbot.app.config.TenantService
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.NotificationMedia
import ai.chatbot.app.notification.QuickReplyButton
import ai.chatbot.app.notification.TemplateInfo
import ai.chatbot.app.notification.TemplateInfoProvider
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.getObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.util.*
import kotlin.String
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class WaCommCentreAdapter(
    @Property(name = "integrations.wa-comm-centre.url") private val waCommCentreUrl: String,
    private val tenantService: TenantService,
    private val httpClient: RxHttpClient,
) : TemplateInfoProvider {

    private val logger = LoggerFactory.getLogger(this::class.java.name)

    fun sendTextMessage(msisdn: String, content: String, media: NotificationMedia?) {
        val markers = Markers.append("msisdn", msisdn).andAppend("messageContent", content).andAppend("media", media)

        val message = mapOf(
            "@type" to "TextMessage",
            "phoneNumber" to msisdn,
            "clientId" to mapOf("value" to tenantService.getConfiguration().waCommCentre.senderId),
            "id" to mapOf("value" to "CHATBOT-${UUID.randomUUID()}"),
            "messageType" to "SIMPLE_TEXT",
            "message" to content,
            "media" to media?.toTO(),
        )

        try {
            sendSyncRequest(message)
            logger.info(markers, "WaCommCentreMessaging#sendTextMessageSync")
        } catch (e: Exception) {
            logger.error(markers, "WaCommCentreMessaging#sendTextMessageSync", e)
            throw e
        }
    }

    fun sendTemplateMessage(msisdn: String, templateName: String, parameters: List<String>, configurationKey: String?, buttonLinks: List<String>, buttonPayloads: List<String>, media: NotificationMedia?) {
        val markers = Markers.append("msisdn", msisdn)
            .andAppend("template", templateName)
            .andAppend("configurationKey", configurationKey)
            .andAppend("params", parameters)
            .andAppend("buttonsPayloads", buttonPayloads)
            .andAppend("buttonsLinks", buttonLinks)
            .andAppend("media", media)

        val linkButtons = buttonLinks.mapIndexed { index, url ->
            mapOf(
                "index" to (index).toString(),
                "url" to url,
            )
        }

        val quickReplyButtons = buttonPayloads.mapIndexed { index, payload ->
            mapOf(
                "index" to (buttonLinks.size + index).toString(),
                "payload" to payload,
            )
        }

        val message = buildMap {
            put("@type", "TemplateMessage")
            put("phoneNumber", msisdn)
            put("clientId", mapOf("value" to tenantService.getConfiguration().waCommCentre.senderId))
            put("id", mapOf("value" to "CHATBOT-${UUID.randomUUID()}"))
            put("templateName", templateName)
            put("configurationKey", configurationKey)
            put("arguments", parameters)
            put("linkArguments", linkButtons)
            put("buttonArguments", quickReplyButtons)
            put("media", media?.toTO())
        }

        try {
            sendSyncRequest(message)
            logger.info(markers, "WaCommCentreMessaging#sendTemplateMessage")
        } catch (e: Exception) {
            logger.error(markers, "WaCommCentreMessaging#sendTemplateMessage", e)
            throw e
        }
    }

    fun sendLinkMessage(msisdn: String, content: String, linkTitle: String, url: String, media: NotificationMedia?) {
        val markers = Markers.append("msisdn", msisdn)
            .andAppend("messageContent", content)
            .andAppend("linkTitle", linkTitle)
            .andAppend("url", url)
            .andAppend("media", media)

        val message = mapOf(
            "@type" to "LinkMessage",
            "phoneNumber" to msisdn,
            "clientId" to mapOf("value" to tenantService.getConfiguration().waCommCentre.senderId),
            "id" to mapOf("value" to "CHATBOT-${UUID.randomUUID()}"),
            "message" to content,
            "uri" to url,
            "uriTitle" to linkTitle,
            "media" to media?.toTO(),
        )

        try {
            sendSyncRequest(message)
            logger.info(markers, "WaCommCentreMessaging#sendLinkMessage")
        } catch (e: Exception) {
            logger.error(markers, "WaCommCentreMessaging#sendLinkMessage", e)
            throw e
        }
    }

    fun sendInteractiveMessage(msisdn: String, content: String, buttons: List<QuickReplyButton>, media: NotificationMedia?) {
        val markers = Markers.append("msisdn", msisdn)
            .andAppend("messageContent", content)
            .andAppend("buttons", buttons)
            .andAppend("media", media)

        val message = mapOf(
            "@type" to "InteractiveMessage",
            "phoneNumber" to msisdn,
            "clientId" to mapOf("value" to tenantService.getConfiguration().waCommCentre.senderId),
            "id" to mapOf("value" to "CHATBOT-${UUID.randomUUID()}"),
            "message" to content,
            "buttons" to buttons.map { button ->
                mapOf(
                    "text" to button.text,
                    "payload" to button.payload,
                )
            },
            "media" to media?.toTO(),
        )

        try {
            sendSyncRequest(message)
            logger.info(markers, "WaCommCentreMessaging#sendInteractiveMessage")
        } catch (e: Exception) {
            logger.error(markers, "WaCommCentreMessaging#sendInteractiveMessage", e)
            throw e
        }
    }

    private fun sendSyncRequest(message: Any) {
        val request = HttpRequest.POST(
            "$waCommCentreUrl/message/send",
            message,
        )

        httpClient.exchange(request).firstOrError().blockingGet()
    }

    private fun getMessageTextContent(message: Any): String {
        val logName = "WaCommCentreAdapter#getMessageTextContent"
        val markers = Markers.append("messageBody", message)

        try {
            val request = HttpRequest.POST(
                "$waCommCentreUrl/message/messageTextContent",
                message,
            )

            return httpClient.exchange(request, Argument.STRING)
                .firstOrError().blockingGet().body().also {
                    markers.andAppend("response", it)
                    logger.info(markers, logName)
                }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    private fun NotificationMedia.toTO(): Map<String, Any> {
        return when (this) {
            is NotificationMedia.Image -> mapOf("@type" to "Image", "url" to url)
            is NotificationMedia.Video -> mapOf("@type" to "Video", "url" to url)
            is NotificationMedia.Document -> mapOf("@type" to "Document", "url" to url, "filename" to filename)
        }
    }

    override fun fetch(template: String): TemplateInfo {
        val clientId = tenantService.getConfiguration().waCommCentre.senderId
        val request = HttpRequest.GET<TemplateInfo>("$waCommCentreUrl/template/$clientId/$template")

        val httpResponse: HttpResponse<String> = httpClient
            .exchange(request, Argument.STRING, Argument.STRING)
            .blockingFirst()

        return getObjectMapper().readValue(httpResponse.body())
    }

    override fun getHistoryMessage(rawNotificationMessage: ChatbotRawTemplatedNotification): String {
        val message = rawNotificationToWaCommCentrePayload(rawNotificationMessage)

        return getMessageTextContent(message)
    }

    override fun assertIfMessageContentIsValid(notification: ChatbotRawTemplatedNotification) {
        try {
            logger.info(
                Markers.append("notification", notification),
                "WaCommCentreMessaging#assertIfMessageContentIsValid",
            )
            getMessageTextContent(rawNotificationToWaCommCentrePayload(notification))
        } catch (e: Exception) {
            throw InvalidNotificationMessageException("Conteúdo inválido para notificação do tipo ${notification.configurationKey}", e)
        }
    }

    fun sendRawMessage(notification: ChatbotRawTemplatedNotification) {
        val markers = Markers.append("msisdn", notification.mobilePhone)
            .andAppend("configurationKey", notification.configurationKey.value)
            .andAppend("params", notification.arguments)
            .andAppend("media", notification.media)
            .andAppend("notificationType", notification.notificationType?.value)

        val message = rawNotificationToWaCommCentrePayload(notification)

        try {
            sendSyncRequest(message)
            logger.info(markers, "WaCommCentreMessaging#sendRawMessage")
        } catch (e: Exception) {
            logger.error(markers, "WaCommCentreMessaging#sendRawMessage", e)
            throw e
        }
    }

    private fun rawNotificationToWaCommCentrePayload(rawNotificationMessage: ChatbotRawTemplatedNotification): Map<String, Any?> = buildMap {
        put("@type", "RawTemplateMessage")
        put("phoneNumber", rawNotificationMessage.mobilePhone)
        put("clientId", mapOf("value" to tenantService.getConfiguration().waCommCentre.senderId))
        put("id", mapOf("value" to "CHATBOT-${UUID.randomUUID()}"))
        put("configurationKey", rawNotificationMessage.configurationKey.value)
        put("arguments", rawNotificationMessage.arguments)
        put("media", rawNotificationMessage.media?.toTO())
        put("notificationType", rawNotificationMessage.notificationType?.value)
    }
}

internal data class TemplateNotificationConfigTO(
    val configurationKey: String?,
    val template: String?,
    val params: List<String>?,
    val link: TemplateNotificationConfigTOLink?,
    val quickReplies: List<TemplateNotificationConfigTOQuickReply>?,
    val mediaUrl: String?,
    val mediaType: String?,
)

internal data class TemplateNotificationConfigTOLink(
    val text: String?,
    val url: String?,
    val event: String?,
)

internal data class TemplateNotificationConfigTOQuickReply(
    val text: String?,
    val action: String?,
    val payload: String?,
    val transactionId: String?,
)

class InvalidNotificationMessageException(
    message: String,
    cause: Throwable? = null,
) : RuntimeException(message, cause)