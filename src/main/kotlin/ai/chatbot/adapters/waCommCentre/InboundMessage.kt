package ai.chatbot.adapters.waCommCentre

import ai.chatbot.app.file.StoredObject
import com.fasterxml.jackson.annotation.JsonIgnoreProperties

data class InboundMessage(
    val clientId: ClientId,
    val phoneNumberId: PhoneNumberId,
    val displayPhoneNumber: String,
    val id: MessageId,
    val from: String,
    val timestamp: String,
    val content: InboundMessageContent,
    val type: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class InboundMessageContent(
    val text: String,
    val payload: String? = null,
    val forwarded: Boolean = false,
    val media: InboundMessageMediaContent? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class InboundMessageMediaContent(
    val type: String,
    val storedObject: StoredObject?,
    val mimeType: String,
)

data class ClientId(val value: String)
data class PhoneNumberId(val value: String)
data class MessageId(val value: String)