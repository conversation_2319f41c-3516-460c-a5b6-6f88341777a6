package ai.chatbot.adapters.waCommCentre

import ai.chatbot.app.TransactionService
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.ConversationService
import ai.chatbot.app.conversation.InterceptAction
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.MessageContent
import ai.chatbot.app.conversation.UserMessageValidationService
import ai.chatbot.app.media.BoletoInfo
import ai.chatbot.app.media.MediaProcessorService
import ai.chatbot.app.transaction.AddBoletoTransactionDetails
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.transcription.TranscriptionService
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.parseObjectFrom
import jakarta.inject.Singleton
import java.time.Duration
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers.append
import org.slf4j.Logger
import org.slf4j.LoggerFactory

interface IncomingMessageProcessor {
    suspend fun process(message: InboundMessage)
}

@Singleton
class DefaultIncomingMessageProcessor(
    private val userMessageValidationService: UserMessageValidationService,
    private val conversationService: ConversationService,
    private val transcriptionService: TranscriptionService,
    private val mediaProcessorService: MediaProcessorService,
    private val transactionService: TransactionService,
    private val tenantService: TenantService,
) : IncomingMessageProcessor {
    val logName = "DefaultIncomingMessageProcessor"

    override suspend fun process(message: InboundMessage) {
        if (!tenantService.getConfiguration().waCommCentre.enabled) {
            return
        }

        val userId = UserId(message.from)
        val initial = ZonedDateTime.now()
        val markers = append("message", message).andAppend("userId", userId.value)

        return try {
            val processedMediaResult = processMessageMedia(message)

            val messageContent = MessageContent(message.content.text, processedMediaResult)
            markers.andAppend("text", messageContent.content).andAppend("transcript", messageContent.isTranscript)

            val interceptMultipleBoletoAction = if (messageContent.noQrCodeFound && messageContent.isMediaBarCode && messageContent.barCodes.isNotEmpty()) {
                val transaction = transactionService.create(userId, AddBoletoTransactionDetails(barCodes = messageContent.barCodes))

                InterceptAction(
                    type = InterceptMessagePayloadType.VALIDATE_BOLETO,
                    transactionId = transaction.id,
                    payload = getLocalDate().format(dateFormat),
                )
            } else {
                null
            }

            val interceptAction = interceptMultipleBoletoAction ?: message.content.payload?.let {
                val payload = parseObjectFrom<MessagePayload>(it)
                InterceptAction(
                    type = InterceptMessagePayloadType.valueOf(payload.action),
                    transactionId = payload.transactionId?.let { TransactionId(it) },
                    payload = payload.payload,
                )
            }

            markers.andAppend("interceptAction", interceptAction)

            val payloadValidationResult = userMessageValidationService.validatePayload(interceptAction?.payload, interceptAction)
            markers.andAppend("payloadDateValidationResult", payloadValidationResult)

            conversationService.asyncProcessChat(userId, messageContent, payloadValidationResult, interceptAction)

            val final = ZonedDateTime.now()
            val duration = Duration.between(initial, final)
            logger.info(
                markers.andAppend("timeFinal", final).andAppend("timeTaken", duration).andAppend("timeTakenMillis", duration.toMillis()),
                logName,
            )
        } catch (ex: Exception) {
            val final = ZonedDateTime.now()
            val duration = Duration.between(initial, final)
            logger.error(markers.andAppend("timeFinal", final).andAppend("timeTaken", duration).andAppend("timeTakenMillis", duration.toMillis()), logName, ex)
        }
    }

    private suspend fun processMessageMedia(message: InboundMessage): ProcessedMediaResult? {
        val media = message.content.media
        if (media?.storedObject == null) {
            return null
        }

        when (media.type) {
            "image" -> {
                val mediaAnalysis = mediaProcessorService.process(media.storedObject)
                return ProcessedMediaResult(
                    mediaType = MediaType.IMAGE,
                    qrCodeValues = mediaAnalysis.qrCodeValues,
                    boletoInfo = mediaAnalysis.boletos,
                    textExtraction = mediaAnalysis.text,
                )
            }

            "document" -> {
                val mediaAnalysis = mediaProcessorService.process(media.storedObject)
                return ProcessedMediaResult(
                    mediaType = MediaType.DOCUMENT,
                    qrCodeValues = mediaAnalysis.qrCodeValues,
                    boletoInfo = mediaAnalysis.boletos,
                    textExtraction = mediaAnalysis.text,
                )
            }

            "audio" -> {
                return ProcessedMediaResult(
                    mediaType = MediaType.AUDIO,
                    transcription = transcriptionService.transcribe(media.storedObject),
                )
            }

            else -> {
                logger.warn("Tipo de mídia desconhecido: ${media.type}")
                return null
            }
        }
    }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(DefaultIncomingMessageProcessor::class.java)
    }
}

data class ProcessedMediaResult(
    val mediaType: MediaType,
    val transcription: String? = null,
    val textExtraction: String? = null,
    val qrCodeValues: List<String>? = null,
    val boletoInfo: List<BoletoInfo>? = null,
)

enum class MediaType {
    IMAGE,
    DOCUMENT,
    AUDIO,
}

data class MessagePayload(
    val action: String,
    val transactionId: String? = null,
    val payload: String? = null,
)