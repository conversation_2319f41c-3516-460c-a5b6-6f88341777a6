package ai.chatbot.adapters.dynamodb

import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.DynamoDbClient

@Factory
class AmazonDynamoDBFactory(
    @Property(name = "aws.region") private val dynamodbRegion: String,
) {
    @Singleton
    fun getDynamoDbClient(): DynamoDbClient = DynamoDbClient.builder().region(Region.of(dynamodbRegion)).build()

    @Singleton
    fun getDynamoDbAsyncClient(): DynamoDbAsyncClient =
        DynamoDbAsyncClient.builder().region(Region.of(dynamodbRegion)).build()

    @Singleton
    fun dynamoDbEnhancedClient(): DynamoDbEnhancedClient {
        return DynamoDbEnhancedClient.builder()
            .dynamoDbClient(getDynamoDbClient())
            .build()
    }

    @Singleton
    fun dynamoDbEnhancedAsyncClient(): DynamoDbEnhancedAsyncClient {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(getDynamoDbAsyncClient())
            .build()
    }
}