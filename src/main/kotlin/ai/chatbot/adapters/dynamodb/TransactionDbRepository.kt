package ai.chatbot.adapters.dynamodb

import ai.chatbot.adapters.api.TransactionPaymentStatus
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.TransactionRepository
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.media.BoletoInfo
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixQRCode
import ai.chatbot.app.transaction.AddBoletoTransactionDetails
import ai.chatbot.app.transaction.MarkAsPaidOrIgnoreBillsTransactionDetails
import ai.chatbot.app.transaction.PixTransactionDetails
import ai.chatbot.app.transaction.ScheduleBillsTransactionDetails
import ai.chatbot.app.transaction.SendPixCodeTransactionDetails
import ai.chatbot.app.transaction.SweepingTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionDetails
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.transaction.TransactionStatus
import ai.chatbot.app.transaction.TransactionType
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.utils.getObjectMapper
import ai.chatbot.app.utils.parseObjectFrom
import ai.chatbot.app.utils.timestampFormatWithBrazilTimeZone
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import io.micronaut.context.ApplicationContext
import io.micronaut.context.annotation.Primary
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class TransactionDynamoDAO(cli: DynamoDbEnhancedClient, context: ApplicationContext) : AbstractDynamoDAO<TransactionEntity>(TransactionEntity::class.java, context, cli)

@Singleton
@Primary
class TransactionDbRepository(private val dynamoDbDAO: TransactionDynamoDAO, private val tenantService: TenantService) : TransactionRepository {
    override fun save(transaction: Transaction) {
        val entity =
            TransactionEntity().apply {
                this.partitionKey = buildPartitionKey()
                this.rangeKey = transaction.id.value
                this.index1PartitionKey = buildIndex1PartitionKey(transaction.userId)
                this.index1RangeKey = "TRANSACTION#${transaction.status}"
                this.userId = transaction.userId.value
                this.walletId = transaction.walletId.value
                this.status = transaction.status
                this.paymentStatus = transaction.paymentStatus
                this.type = transaction.details.type
                this.details = getObjectMapper().writeValueAsString(transaction.details.toEntity())
                this.authorizationToken = transaction.authorizationToken
                this.createdAt = transaction.createdAt.format(timestampFormatWithBrazilTimeZone)
                this.updatedAt = ZonedDateTime.now().format(timestampFormatWithBrazilTimeZone)
                this.transactionGroupId = transaction.groupId.value
            }
        dynamoDbDAO.save(entity)
    }

    override fun find(userId: UserId): List<Transaction> {
        val entity = dynamoDbDAO.findByPrimaryKeyOnIndex(GlobalSecondaryIndexNames.GSIndex1, buildIndex1PartitionKey(userId))

        return entity.map { it.toTransaction() }
    }

    override fun find(
        userId: UserId,
        status: TransactionStatus,
    ): List<Transaction> {
        val entity = dynamoDbDAO.findByPrimaryKeyOnIndex(GlobalSecondaryIndexNames.GSIndex1, buildIndex1PartitionKey(userId), "TRANSACTION#$status")

        return entity.map { it.toTransaction() }
    }

    override fun find(transactionId: TransactionId): Transaction? {
        val entity = dynamoDbDAO.findByPrimaryKey(buildTransactionKey(transactionId))
        return entity?.toTransaction()
    }

    private fun buildTransactionKey(transactionId: TransactionId) = object : AbstractKey<TransactionEntity>(partitionKey = buildPartitionKey(), sortKey = transactionId.value) {}

    private fun buildPartitionKey(): String = "TRANSACTION" + "#" + tenantService.getTenantName()

    private fun buildIndex1PartitionKey(userId: UserId): String = userId.value + "#" + tenantService.getTenantName()

    private fun TransactionDetails.toEntity(): TransactionDetailsEntity<*> {
        return when (this) {
            is PixTransactionDetails ->
                PixTransactionDetailsEntity(
                    amount = amount,
                    sweepingAmount = sweepingAmount,
                    sweepingParticipantId = sweepingParticipantId?.value,
                    pixKey = pixKey.toEntity(),
                    recipientName = recipientName,
                    recipientDocument = recipientDocument,
                    recipientInstitution = recipientInstitution,
                    qrCode = qrCode?.value,
                )

            is SweepingTransactionDetails -> SweepingTransactionDetailsEntity(
                amount = amount,
                bills = bills.map { it.value },
                sweepingParticipantId = sweepingParticipantId?.value,
            )

            is ScheduleBillsTransactionDetails -> ScheduleBillsTransactionDetailsEntity(
                bills = bills.map { it.value },
            )

            is MarkAsPaidOrIgnoreBillsTransactionDetails -> MarkAsPaidOrIgnoreBillsTransactionDetailsEntity(
                billsToMarkAsPaid = billsToMarkAsPaid.map { it.value },
                billsToIgnore = billsToIgnore.map { it.value },
            )

            is AddBoletoTransactionDetails -> AddBoletoTransactionDetailsEntity(
                barCodes = barCodes.map { it.codigo to it.vencimento },
            )

            is SendPixCodeTransactionDetails -> SendPixCodeTransactionDetailsEntity(
                bills = bills.map { it.value },
            )
        }
    }

    private fun TransactionEntity.toTransaction() =
        Transaction(
            id = TransactionId(value = this.rangeKey),
            userId = UserId(value = this.userId),
            walletId = WalletId(value = this.walletId),
            status = this.status,
            paymentStatus = this.paymentStatus,
            details = parseObjectFrom<TransactionDetailsEntity<*>>(details).toDomain(TransactionId(value = this.rangeKey)),
            authorizationToken = this.authorizationToken,
            createdAt = ZonedDateTime.parse(this.createdAt, timestampFormatWithBrazilTimeZone),
            updatedAt = ZonedDateTime.parse(this.updatedAt, timestampFormatWithBrazilTimeZone),
            groupId = transactionGroupId?.let { TransactionGroupId(it) } ?: TransactionGroupId(),
        )

    private fun PixKey.toEntity() =
        PixKeyEntity(
            key = value,
            type = type,
        )
}

@DynamoDbBean
class TransactionEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = CHAT_BOT_PARTITION_KEY)
    lateinit var partitionKey: String // TRANSACTION # TENANT

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = CHAT_BOT_RANGE_KEY)
    lateinit var rangeKey: String // TRANSACTION_ID

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var index1PartitionKey: String // USER_ID # TENANT

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    lateinit var index1RangeKey: String // TRANSACTION#STATE

    @get:DynamoDbAttribute("UserId")
    lateinit var userId: String

    @get:DynamoDbAttribute("WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "Type")
    lateinit var type: TransactionType

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: TransactionStatus

    @get:DynamoDbAttribute(value = "PaymentStatus")
    var paymentStatus: TransactionPaymentStatus = TransactionPaymentStatus.UNKNOWN

    @get:DynamoDbAttribute(value = "AuthorizationToken")
    var authorizationToken: String? = null

    @get:DynamoDbAttribute(value = "Details")
    lateinit var details: String

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDbAttribute(value = "TransactionGroupId")
    var transactionGroupId: String? = null
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY)
sealed interface TransactionDetailsEntity<T : TransactionDetails> {
    fun toDomain(transactionId: TransactionId): T
}

@JsonTypeName("PIX")
@JsonIgnoreProperties(ignoreUnknown = true)
data class PixTransactionDetailsEntity(
    val amount: Long,
    val sweepingAmount: Long?,
    val sweepingParticipantId: String?,
    val pixKey: PixKeyEntity,
    val recipientName: String? = null,
    val recipientDocument: String? = null,
    val recipientInstitution: String? = null,
    val qrCode: String? = null,
) : TransactionDetailsEntity<PixTransactionDetails> {
    override fun toDomain(transactionId: TransactionId): PixTransactionDetails =
        PixTransactionDetails(
            amount = amount,
            sweepingAmount = sweepingAmount,
            sweepingParticipantId = sweepingParticipantId?.let { SweepingParticipantId(it) },
            pixKey = pixKey.toPixKey(),
            recipientName = recipientName ?: pixKey.key, // Compatibilidade com transactions antigas que não tinham esses campos
            recipientDocument = recipientDocument ?: "",
            recipientInstitution = recipientInstitution ?: "",
            qrCode = qrCode?.let { PixQRCode(it) },
        )
}

@JsonTypeName("SWEEPING")
data class SweepingTransactionDetailsEntity(
    val amount: Long,
    val bills: List<String>,
    val sweepingParticipantId: String?,
) : TransactionDetailsEntity<SweepingTransactionDetails> {
    override fun toDomain(transactionId: TransactionId): SweepingTransactionDetails =
        SweepingTransactionDetails(
            amount = amount,
            bills = bills.map { BillId(it) },
            sweepingParticipantId = sweepingParticipantId?.let { SweepingParticipantId(it) } ?: SweepingParticipantId("INVALID-CHATBOT-${transactionId.value}"),
        )
}

@JsonTypeName("SEND_PIX_CODE")
data class SendPixCodeTransactionDetailsEntity(
    val bills: List<String>,
) : TransactionDetailsEntity<SendPixCodeTransactionDetails> {
    override fun toDomain(transactionId: TransactionId): SendPixCodeTransactionDetails =
        SendPixCodeTransactionDetails(
            bills = bills.map { BillId(it) },
        )
}

@JsonTypeName("SCHEDULE_BILLS")
data class ScheduleBillsTransactionDetailsEntity(
    val bills: List<String>,
) : TransactionDetailsEntity<ScheduleBillsTransactionDetails> {
    override fun toDomain(transactionId: TransactionId): ScheduleBillsTransactionDetails =
        ScheduleBillsTransactionDetails(
            bills = bills.map { BillId(it) },
        )
}

@JsonTypeName("MARK_AS_PAID_OR_IGNORE_BILLS")
data class MarkAsPaidOrIgnoreBillsTransactionDetailsEntity(
    val billsToMarkAsPaid: List<String>,
    val billsToIgnore: List<String>,
) : TransactionDetailsEntity<MarkAsPaidOrIgnoreBillsTransactionDetails> {
    override fun toDomain(transactionId: TransactionId): MarkAsPaidOrIgnoreBillsTransactionDetails =
        MarkAsPaidOrIgnoreBillsTransactionDetails(
            billsToMarkAsPaid = billsToMarkAsPaid.map { BillId(it) },
            billsToIgnore = billsToIgnore.map { BillId(it) },
        )
}

@JsonTypeName("ADD_BOLETO")
data class AddBoletoTransactionDetailsEntity(
    val barCodes: List<Pair<String, String?>>,
) : TransactionDetailsEntity<AddBoletoTransactionDetails> {
    override fun toDomain(transactionId: TransactionId): AddBoletoTransactionDetails =
        AddBoletoTransactionDetails(
            barCodes = barCodes.map { BoletoInfo(it.first, it.second) },
        )
}