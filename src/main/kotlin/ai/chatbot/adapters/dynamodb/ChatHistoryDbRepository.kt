package ai.chatbot.adapters.dynamodb

import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.classification.ClassificationResult
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.Action
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.ChatMessageWrapper
import ai.chatbot.app.conversation.CompletionMessage
import ai.chatbot.app.conversation.ConversationHistory
import ai.chatbot.app.conversation.HistoryStateType
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.conversation.UserConversationHistoryNotFound
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.formatToZonedDateTime
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.parseDate
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.parseToZonedDateTime
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.getObjectMapper
import ai.chatbot.app.utils.parseListFrom
import com.fasterxml.jackson.module.kotlin.readValue
import io.micronaut.context.annotation.Primary
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.AttributeConverter
import software.amazon.awssdk.enhanced.dynamodb.AttributeValueType
import software.amazon.awssdk.enhanced.dynamodb.EnhancedType
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey
import software.amazon.awssdk.services.dynamodb.model.AttributeValue

const val INDEX_1 = "GSIndex1"
const val HISTORY_DATE_REGISTER = "HISTORY_DATE"

@Singleton
@Primary
class ChatHistoryDbRepository(private val dynamoDbDAO: ChatHistoryDynamoDAO, private val tenantService: TenantService) : HistoryRepository {
    private val logger = LoggerFactory.getLogger(ChatHistoryDbRepository::class.java)

    override fun findLatest(userId: UserId): ConversationHistory {
        return getUserHistory(userId)?.toConversationHistory() ?: throw UserConversationHistoryNotFound.UserConversationNotFound
    }

    override fun findByDate(date: LocalDate): List<ConversationHistory> {
        return getHistoryByDate(date).map { it.toConversationHistory() }
    }

    override fun find(
        userId: UserId,
        date: LocalDate,
        template: HistoryStateType,
    ): ConversationHistory {
        return getUserHistory(userId, date, template)?.toConversationHistory() ?: throw UserConversationHistoryNotFound.UserConversationNotFound
    }

    override fun saveUserMessage(
        userId: UserId,
        message: String,
    ) {
        saveMessage(
            userId,
            chatMessage =
            ChatMessageWrapper(
                MessageType.USER,
                message,
                null,
                ZonedDateTime.now(),
            ),
        )
    }

    override fun saveUserReaction(
        userId: UserId,
        reaction: String,
    ) {
        saveMessage(
            userId,
            chatMessage =
            ChatMessageWrapper(
                MessageType.REACTION,
                reaction,
                null,
                ZonedDateTime.now(),
            ),
        )
    }

    override fun saveAssistantMessage(
        userId: UserId,
        message: String,
    ) {
        saveMessage(userId, ChatMessageWrapper(type = MessageType.ASSISTANT, message = message, completionMessage = null, ZonedDateTime.now()))
    }

    override fun saveAssistantMessage(
        userId: UserId,
        completionMessage: CompletionMessage,
    ) {
        saveMessage(userId, completionMessage.toChatMessageWrapper())
    }

    override fun saveSystemMessage(
        userId: UserId,
        message: String,
    ) {
        saveMessage(
            userId,
            ChatMessageWrapper(
                MessageType.SYSTEM,
                message,
                null,
                ZonedDateTime.now(),
            ),
        )
    }

    private fun saveMessage(
        userId: UserId,
        chatMessage: ChatMessageWrapper,
    ) {
        val userHistory = getUserHistory(userId) ?: throw UserConversationHistoryNotFound.UserConversationNotFound

        dynamoDbDAO.save(
            userHistory.apply {
                messages = userHistory.messages + ChatMessageWrapperEntity(role = chatMessage.type, message = chatMessage.message, completionMessage = chatMessage.completionMessage, timestamp = chatMessage.timestamp?.let { formatToZonedDateTime(it) })
            },
        )
    }

    override fun saveClassification(
        userId: UserId,
        date: LocalDate,
        classificationResult: ClassificationResult,
    ) {
        val userHistory = getUserHistory(userId, date) ?: throw UserConversationHistoryNotFound.UserConversationNotFound

        dynamoDbDAO.save(
            userHistory.apply { classification = getObjectMapper().writeValueAsString(classificationResult) },
        )
    }

    override fun clearConversationHistory(
        userId: UserId,
        template: HistoryStateType,
        date: LocalDate,
    ) {
        val logName = "ChatHistoryDbRepository#clearConversationHistory"
        val markers = Markers.append("userId", userId.value)

        val partitionKey = buildPartitionKey(userId)
        markers.andAppend("partitionKey", partitionKey)

        val rangeKey = "${template.name}#${date.format(dateFormat)}"
        markers.andAppend("rangeKey", rangeKey)

        dynamoDbDAO.delete(partitionKey, rangeKey)
        logger.info(markers, logName)
    }

    override fun create(
        userId: UserId,
        historyStateType: HistoryStateType,
        initialUserMessage: String?,
    ) {
        val logName = "ChatHistoryDbRepository#create"
        val markers = Markers.append("userId", userId.value)

        val now = getZonedDateTime()
        val newPartitionKey = buildPartitionKey(userId)
        markers.andAppend("partitionKey", newPartitionKey)

        val newRangeKey = "${historyStateType.name}#${now.toLocalDate().format(dateFormat)}"
        markers.andAppend("rangeKey", newRangeKey)

        dynamoDbDAO.save(
            ChatHistoryEntity().apply {
                partitionKey = newPartitionKey
                rangeKey = newRangeKey
                messages = initialUserMessage?.let {
                    listOf(
                        ChatMessageWrapperEntity(
                            MessageType.USER,
                            it,
                            null,
                            formatToZonedDateTime(now),
                        ),
                    )
                } ?: emptyList()
                template = HistoryStateType.BILLS_COMING_DUE
                index1HashKey = buildIndex1HashKey(now.toLocalDate())
                index1RangeKey = userId.value
            },
        )

        logger.info(markers, logName)
    }

    private fun buildPartitionKey(userId: UserId): String = userId.value + "#" + tenantService.getTenantName()

    private fun buildIndex1HashKey(date: LocalDate): String = "$HISTORY_DATE_REGISTER#${tenantService.getTenantName()}#${date.format(dateFormat)}"

    private fun getUserHistory(
        userId: UserId,
        date: LocalDate = getLocalDate(),
        historyStateType: HistoryStateType = HistoryStateType.BILLS_COMING_DUE,
    ): ChatHistoryEntity? { // FIXME: quando adicionar mais templates,não pode ter o default
        return dynamoDbDAO.findByPrimaryKey(key = ChatHistoryKey(buildPartitionKey(userId), "${historyStateType.name}#${date.format(dateFormat)}"))
    }

    private fun getHistoryByDate(date: LocalDate = getLocalDate()): List<ChatHistoryEntity> {
        return dynamoDbDAO.findByPrimaryKeyOnIndex(index = GlobalSecondaryIndexNames.GSIndex1, partitionKey = buildIndex1HashKey(date))
    }
}

data class ChatHistoryKey(override val partitionKey: String, override val sortKey: String) : AbstractKey<ChatHistoryEntity>(partitionKey = partitionKey, sortKey = sortKey)

fun CompletionMessage.toChatMessageWrapper(): ChatMessageWrapper {
    val message =
        acoes.firstOrNull { it.name == ActionType.MSG }?.let {
            when (it) {
                is Action.SendMessage -> it.content
                else -> null
            }
        }
    return ChatMessageWrapper(type = MessageType.ASSISTANT, message = message, completionMessage = this, timestamp = ZonedDateTime.now())
}

@DynamoDbBean
class ChatHistoryEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = CHAT_BOT_PARTITION_KEY)
    lateinit var partitionKey: String // user identifier # TENANT

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = CHAT_BOT_RANGE_KEY)
    lateinit var rangeKey: String // TEMPLATE#DATE

    @get:DynamoDbAttribute(value = "Messages")
    @get:DynamoDbConvertedBy(value = ChatMessageConverter::class)
    lateinit var messages: List<ChatMessageWrapperEntity>

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    var index1HashKey: String? = null // HISTORY_DATE#TENANT#DATE

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    var index1RangeKey: String? = null // user identifier

    @get:DynamoDbAttribute(value = "Classification")
    var classification: String? = null // Daily log classification

    @get:DynamoDbAttribute(value = "Template")
    var template: HistoryStateType = HistoryStateType.BILLS_COMING_DUE
}

private fun ChatHistoryEntity.toConversationHistory(): ConversationHistory {
    return ConversationHistory(
        userId = UserId(partitionKey.split("#").first()),
        createdAt = parseDate(rangeKey.split("#").last()),
        messages =
        messages.map {
            ChatMessageWrapper(
                type = it.role,
                message = it.message,
                completionMessage = it.completionMessage,
                timestamp = it.timestamp?.let { date -> parseToZonedDateTime(date) },
            )
        },
        classification = classification?.let {
            try {
                getObjectMapper().readValue<ClassificationResult>(it)
            } catch (e: Exception) {
                null
            }
        },
    )
}

class ChatMessageConverter : AttributeConverter<List<ChatMessageWrapperEntity>> {
    private val objectMapper = getObjectMapper()

    override fun transformFrom(chatMessage: List<ChatMessageWrapperEntity>): AttributeValue {
        return AttributeValue.builder().s(objectMapper.writeValueAsString(chatMessage)).build()
    }

    override fun transformTo(attributeValue: AttributeValue?): List<ChatMessageWrapperEntity> {
        return attributeValue?.s()?.let { parseListFrom(it) }
            ?: throw IllegalArgumentException("Failed to convert to a list of ChatMessage")
    }

    override fun type(): EnhancedType<List<ChatMessageWrapperEntity>> = EnhancedType.listOf(ChatMessageWrapperEntity::class.java)

    override fun attributeValueType(): AttributeValueType = AttributeValueType.S
}

data class ChatMessageWrapperEntity(
    val role: MessageType, // Se chama "role" por motivos de retrocompatibilidade
    val message: String?,
    val completionMessage: CompletionMessage?,
    val timestamp: String?,
)