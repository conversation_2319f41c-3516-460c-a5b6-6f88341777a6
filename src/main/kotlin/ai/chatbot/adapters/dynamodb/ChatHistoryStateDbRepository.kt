package ai.chatbot.adapters.dynamodb

import ai.chatbot.adapters.billPayment.BillStatus
import ai.chatbot.app.HistoryStateRepository
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.SweepingConsentPeriodicLimitUsage
import ai.chatbot.app.SweepingConsentPeriodicUsage
import ai.chatbot.app.SweepingParticipant
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.BillType
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.bill.Recipient
import ai.chatbot.app.bill.ScheduledInfo
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.contact.ContactId
import ai.chatbot.app.contact.LastUsed
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.HistoryState
import ai.chatbot.app.conversation.HistoryStateType
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.OnboardingState
import ai.chatbot.app.conversation.Subscription
import ai.chatbot.app.conversation.SubscriptionPaymentStatus
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.conversation.UserConversationHistoryNotFound
import ai.chatbot.app.payment.Balance
import ai.chatbot.app.payment.Forecast
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.getObjectMapper
import ai.chatbot.app.utils.parseListFrom
import ai.chatbot.app.utils.parseObjectFrom
import ai.chatbot.app.utils.timestampFormatWithBrazilTimeZone
import io.micronaut.context.ApplicationContext
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.AttributeConverter
import software.amazon.awssdk.enhanced.dynamodb.AttributeValueType
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.EnhancedType
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey
import software.amazon.awssdk.services.dynamodb.model.AttributeValue

private const val CHAT_HISTORY_STATE = "CHAT_HISTORY_STATE"

private val DEFAULT_UNKNOWN_USER =
    User(
        accountId = AccountId(value = ""),
        id = UserId(value = ""),
        name = "",
        accountGroups = listOf(),
        status = AccountStatus.ACTIVE,
    )

@Singleton
class ChatHistoryStateDynamoDAO(
    cli: DynamoDbEnhancedClient,
    context: ApplicationContext,
) : AbstractDynamoDAO<ChatHistoryStateEntity>(ChatHistoryStateEntity::class.java, context, cli)

@Singleton
class ChatHistoryStateDbRepository(
    private val dynamoDbDAO: ChatHistoryStateDynamoDAO,
    private val tenantService: TenantService,
) : HistoryStateRepository {

    private val logger = LoggerFactory.getLogger(ChatHistoryStateDbRepository::class.java)

    override fun save(
        userId: UserId,
        state: HistoryState,
    ) {
        when (state) {
            is BillComingDueHistoryState -> {
                logger.info(
                    append("userId", userId.value)
                        .andAppend("activeConsents", state.walletWithBills.activeConsents.size)
                        .andAppend("balance", state.balance?.current)
                        .andAppend("bills", state.walletWithBills.bills.size),
                    "ChatHistoryStateDbRepository#save",
                )
                dynamoDbDAO.save(
                    ChatHistoryStateEntity().apply {
                        partitionKey = buildPartitionKey(userId)
                        rangeKey = "$CHAT_HISTORY_STATE#${getLocalDate().format(dateFormat)}"
                        bills = state.walletWithBills.bills.map {
                            it.toBillViewEntity()
                        }
                        totalWaitingApproval = state.walletWithBills.totalWaitingApproval
                        activeConsents = state.walletWithBills.activeConsents.map {
                            it.toActiveConsentsEntity()
                        }
                        internalStateControl = state.internalStateControl.toInternalStateControlEntity()
                        balance = state.balance?.toBalanceEntity()
                        currentWalletId = state.walletWithBills.walletId?.value
                        currentWalletName = state.walletWithBills.walletName
                        user = state.user.toUserDbEntity()
                        startDate = state.startDate.format(dateFormat)
                        endDate = state.endDate.format(dateFormat)
                        contacts = state.contacts.map { it.toContactEntity() }
                        lastBillsUpdatedAt = state.lastBillsUpdatedAt.format(timestampFormatWithBrazilTimeZone)
                        subscription = state.subscription?.toSubscriptionEntity()
                        onboarding = state.onboardingState?.toOnboardingEntity()
                        alreadyPromotedSweepingAccount = state.alreadyPromotedSweepingAccount
                    },
                )
            }
        }
    }

    override fun findLatest(userId: UserId): HistoryState = dynamoDbDAO.findByPrimaryKey(key = ChatStateHistoryKey(buildPartitionKey(userId), "$CHAT_HISTORY_STATE#${getLocalDate().format(dateFormat)}"))?.toHistoryState() ?: throw UserConversationHistoryNotFound.UserConversationStateNotFound

    override fun findByDate(userId: UserId, date: LocalDate): HistoryState = dynamoDbDAO.findByPrimaryKey(key = ChatStateHistoryKey(buildPartitionKey(userId), "$CHAT_HISTORY_STATE#${date.format(dateFormat)}"))?.toHistoryState() ?: throw UserConversationHistoryNotFound.UserConversationStateNotFound
    override fun delete(
        userId: UserId,
        date: LocalDate,
    ) {
        logger.info(
            append("userId", userId.value)
                .andAppend("date", date.format(dateFormat)),
            "ChatHistoryStateDbRepository#delete",
        )
        dynamoDbDAO.delete(buildPartitionKey(userId), "$CHAT_HISTORY_STATE#${date.format(dateFormat)}")
    }

    private fun buildPartitionKey(userId: UserId): String {
        logger.info(append("userId", userId.value).andAppend("tenant", tenantService.getTenantName()), "ChatHistoryStateDbRepository#buildPartitionKey")

        return userId.value + "#" + tenantService.getTenantName()
    }
}

@DynamoDbBean
class ChatHistoryStateEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = CHAT_BOT_PARTITION_KEY)
    lateinit var partitionKey: String // user identifier # TENANT

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = CHAT_BOT_RANGE_KEY)
    lateinit var rangeKey: String // CHAT_HISTORY_STATE#DATE

    @get:DynamoDbAttribute(value = "Bills")
    @get:DynamoDbConvertedBy(value = BillViewsConverter::class)
    var bills: List<BillViewEntity>? = null

    @get:DynamoDbAttribute(value = "TotalWaitingApproval")
    var totalWaitingApproval: Int = 0

    @get:DynamoDbAttribute(value = "ActiveSweepingConsents")
    @get:DynamoDbConvertedBy(value = SweepingConsentsConverter::class)
    var activeConsents: List<SweepingConsentEntity>? = null

    @get:DynamoDbAttribute(value = "Contacts")
    @get:DynamoDbConvertedBy(value = ContactsConverter::class)
    var contacts: List<ContactEntity>? = null

    @get:DynamoDbAttribute(value = "Balance")
    var balance: BalanceEntity? = null

    @get:DynamoDbAttribute(value = "InternalStateControl")
    var internalStateControl: InternalStateControlEntity? = null

    @get:DynamoDbAttribute(value = "CurrentWalletId")
    var currentWalletId: String? = null

    @get:DynamoDbAttribute(value = "CurrentWalletName")
    var currentWalletName: String? = null

    @get:DynamoDbAttribute(value = "User")
    var user: UserEntity? = null

    @get:DynamoDbAttribute(value = "Template")
    var template: HistoryStateType = HistoryStateType.BILLS_COMING_DUE

    @get:DynamoDbAttribute(value = "StartDate")
    var startDate: String = getLocalDate().format(dateFormat)

    @get:DynamoDbAttribute(value = "EndDate")
    var endDate: String = getLocalDate().format(dateFormat)

    @get:DynamoDbAttribute(value = "lastBillsUpdatedAt")
    var lastBillsUpdatedAt: String = getZonedDateTime().format(timestampFormatWithBrazilTimeZone)

    @get:DynamoDbAttribute(value = "subscription")
    var subscription: SubscriptionEntity? = null

    @get:DynamoDbAttribute(value = "onboarding")
    var onboarding: OnboardingEntity? = null

    @get:DynamoDbAttribute(value = "alreadyPromotedSweepingAccount")
    var alreadyPromotedSweepingAccount: Boolean? = null
}

@DynamoDbBean
class UserEntity {
    lateinit var accountId: String
    lateinit var id: String
    lateinit var name: String
    lateinit var accountGroups: List<String>
    var paymentStatus: AccountPaymentStatus? = null
    var status: AccountStatus? = null
}

@DynamoDbBean
class OnboardingEntity {
    var hasAcceptedExamplePayment: Boolean = false
    var pixKeyValue: String? = null
    var pixKeyType: PixKeyType? = null
    var billView: String? = null

    fun toOnboardingState(): OnboardingState {
        val onboardingEntity = this

        return OnboardingState(
            hasAcceptedExamplePayment = onboardingEntity.hasAcceptedExamplePayment,
            pixKeyType = onboardingEntity.pixKeyType,
            pixKeyValue = onboardingEntity.pixKeyValue,
            billView = onboardingEntity.billView?.let { parseObjectFrom(it) },
        )
    }
}

data class ContactEntity(
    val id: String,
    val alias: String? = null,
    val name: String,
    val pixKeys: List<PixKeyEntity>,
    val lastUsed: LastUsedEntity? = null,
)

data class LastUsedEntity(
    val pixKey: PixKeyEntity? = null,
)

data class PixKeyEntity(
    val key: String,
    val type: PixKeyType,
)

data class SweepingConsentEntity(
    val participantId: String,
    val participantName: String,
    val participantShortName: String? = null,
    val transactionLimit: Long,
    val lastSuccessfulCashIn: String? = null,
    val periodicUsage: SweepingConsentPeriodicUsageEntity? = null,
) {
    fun toSweepingConsent() = SweepingConsent(
        participant = SweepingParticipant(
            id = SweepingParticipantId(participantId),
            name = participantName,
            shortName = participantShortName ?: participantName,
        ),
        transactionLimit = transactionLimit,
        lastSuccessfulCashIn = lastSuccessfulCashIn?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) },
        periodicUsage = periodicUsage?.toDomain(),
    )
}

data class SweepingConsentPeriodicUsageEntity(
    val daily: SweepingConsentPeriodicLimitUsageEntity,
    val weekly: SweepingConsentPeriodicLimitUsageEntity,
    val monthly: SweepingConsentPeriodicLimitUsageEntity,
    val yearly: SweepingConsentPeriodicLimitUsageEntity,
    val totalLimit: Long,
    val totalUsed: Long,
) {
    fun toDomain() = SweepingConsentPeriodicUsage(
        daily = daily.toDomain(),
        weekly = weekly.toDomain(),
        monthly = monthly.toDomain(),
        yearly = yearly.toDomain(),
        totalLimit = totalLimit,
        totalUsed = totalUsed,
    )
}

data class SweepingConsentPeriodicLimitUsageEntity(
    val amountLimit: Long,
    val amountUsed: Long,
    val quantityLimit: Long,
    val quantityUsed: Long,
) {
    fun toDomain() = SweepingConsentPeriodicLimitUsage(
        amountLimit = amountLimit,
        amountUsed = amountUsed,
        quantityLimit = quantityLimit,
        quantityUsed = quantityUsed,
    )
}

data class BillViewEntity(
    val billId: BillId,
    val externalBillId: Int,
    val assignor: String?,
    val recipient: String?,
    val billDescription: String,
    val amount: Long,
    val discount: Long,
    val interest: Long,
    val fine: Long,
    val amountTotal: Long,
    val billType: BillType,
    val scheduledInfo: ScheduledInfo?,
    val paymentLimitTime: String?,
    val dueDate: String,
    val subscriptionFee: Boolean,
    val status: String? = null,
)

@DynamoDbBean
data class BalanceEntity(
    var walletId: String? = null,
    var current: Long? = null,
    var openAmountToday: Long? = null,
    var openAmountInSevenDays: Long? = null,
    var openAmountInFifteenDays: Long? = null,
    var openAmountMonth: Long? = null,
    var openAndScheduledAmountForNextMonth: Long? = null,
    var onlyScheduledAmountToday: Long? = null,
    var onlyScheduledAmountInSevenDays: Long? = null,
    var onlyScheduledAmountInFifteenDays: Long? = null,
    var onlyScheduledAmountMonth: Long? = null,
    var onlyScheduledAmountForNextMonth: Long? = null,
) {
    fun toBalance(): Balance =
        Balance(
            walletId = walletId?.let { WalletId(walletId!!) } ?: throw IllegalArgumentException("WalletId is null"),
            current = current?.let { current } ?: throw IllegalArgumentException("Current is null"),
            onlyScheduled =
            Forecast(
                amountToday = onlyScheduledAmountToday?.let { onlyScheduledAmountToday } ?: throw IllegalArgumentException("onlyScheduledAmountToday is null"),
                amountInSevenDays = onlyScheduledAmountInSevenDays?.let { onlyScheduledAmountInSevenDays } ?: throw IllegalArgumentException("onlyScheduledAmountInSevenDays is null"),
                amountInFifteenDays = onlyScheduledAmountInFifteenDays?.let { onlyScheduledAmountInFifteenDays } ?: throw IllegalArgumentException("onlyScheduledAmountInFifteenDays is null"),
                amountMonth = onlyScheduledAmountMonth?.let { onlyScheduledAmountMonth } ?: throw IllegalArgumentException("onlyScheduledAmountMonth is null"),
                amountForNextMonth = onlyScheduledAmountForNextMonth?.let { onlyScheduledAmountForNextMonth } ?: throw IllegalArgumentException("onlyScheduledAmountForNextMonth is null"),
            ),
            open =
            Forecast(
                amountToday = openAmountToday?.let { openAmountToday } ?: throw IllegalArgumentException("openAmountToday is null"),
                amountInSevenDays = openAmountInSevenDays?.let { openAmountInSevenDays } ?: throw IllegalArgumentException("openAmountInSevenDays is null"),
                amountInFifteenDays = openAmountInFifteenDays?.let { openAmountInFifteenDays } ?: throw IllegalArgumentException("openAmountInFifteenDays is null"),
                amountMonth = openAmountMonth?.let { openAmountMonth } ?: throw IllegalArgumentException("openAmountMonth is null"),
                amountForNextMonth = openAndScheduledAmountForNextMonth?.let { openAndScheduledAmountForNextMonth } ?: throw IllegalArgumentException("openAndScheduledAmountForNextMonth is null"),
            ),
        )
}

class BillViewsConverter : AttributeConverter<List<BillViewEntity>> {
    private val objectMapper = getObjectMapper()

    override fun transformFrom(billViews: List<BillViewEntity>): AttributeValue = AttributeValue.builder().s(objectMapper.writeValueAsString(billViews)).build()

    override fun transformTo(attributeValue: AttributeValue?): List<BillViewEntity> =
        attributeValue?.s()?.let { parseListFrom(it) }
            ?: throw IllegalArgumentException("Failed to convert to a list of bill view")

    override fun type(): EnhancedType<List<BillViewEntity>> = EnhancedType.listOf(BillViewEntity::class.java)

    override fun attributeValueType(): AttributeValueType = AttributeValueType.S
}

class SweepingConsentsConverter : AttributeConverter<List<SweepingConsentEntity>> {
    private val objectMapper = getObjectMapper()

    override fun transformFrom(domain: List<SweepingConsentEntity>): AttributeValue = AttributeValue.builder().s(objectMapper.writeValueAsString(domain)).build()

    override fun transformTo(attributeValue: AttributeValue?): List<SweepingConsentEntity> =
        attributeValue?.s()?.let { parseListFrom(it) }
            ?: throw IllegalArgumentException("Failed to convert to a list of sweeping consent")

    override fun type(): EnhancedType<List<SweepingConsentEntity>> = EnhancedType.listOf(SweepingConsentEntity::class.java)

    override fun attributeValueType(): AttributeValueType = AttributeValueType.S
}

class ContactsConverter : AttributeConverter<List<ContactEntity>> {
    private val objectMapper = getObjectMapper()

    override fun transformFrom(contacts: List<ContactEntity>): AttributeValue = AttributeValue.builder().s(objectMapper.writeValueAsString(contacts)).build()

    override fun transformTo(attributeValue: AttributeValue?): List<ContactEntity> =
        attributeValue?.s()?.let { parseListFrom(it) }
            ?: throw IllegalArgumentException("Failed to convert to a list of contacts")

    override fun type(): EnhancedType<List<ContactEntity>> = EnhancedType.listOf(ContactEntity::class.java)

    override fun attributeValueType(): AttributeValueType = AttributeValueType.S
}

@DynamoDbBean
class InternalStateControlEntity(
    var shouldSynchronizeBeforeCompletion: Boolean? = null,
    var billComingDueNotifiedAt: LocalDateTime? = null,
)

@DynamoDbBean
class SubscriptionEntity {
    var fee: Long? = null
    lateinit var dueDate: String
    lateinit var paymentStatus: SubscriptionPaymentStatus
    lateinit var type: SubscriptionType

    fun toSubscription(): Subscription =
        Subscription(
            fee = fee,
            dueDate = LocalDate.parse(dueDate, dateFormat),
            paymentStatus = paymentStatus,
            type = type,
        )
}

data class ChatStateHistoryKey(
    override val partitionKey: String,
    override val sortKey: String,
) : AbstractKey<ChatHistoryStateEntity>(partitionKey = partitionKey, sortKey = sortKey)

fun ChatHistoryStateEntity.toHistoryState(): HistoryState =
    when (template) {
        HistoryStateType.BILLS_COMING_DUE -> {
            BillComingDueHistoryState(
                walletWithBills = WalletWithBills(
                    bills = bills?.map { it.toBillView() } ?: emptyList(),
                    totalWaitingApproval = totalWaitingApproval,
                    walletId = currentWalletId?.let { WalletId(it) },
                    walletName = currentWalletName,
                    activeConsents = activeConsents?.map {
                        it.toSweepingConsent()
                    } ?: emptyList(),
                ),
                internalStateControl = internalStateControl?.let { InternalStateControl(it.shouldSynchronizeBeforeCompletion ?: false, it.billComingDueNotifiedAt) } ?: InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
                balance = balance?.toBalance(),
                user = user?.toUser() ?: DEFAULT_UNKNOWN_USER, // Compatibilidade para leitura de dados antigos
                startDate = LocalDate.parse(startDate, dateFormat),
                endDate = LocalDate.parse(endDate, dateFormat),
                contacts = contacts?.map { it.toContact() } ?: emptyList(),
                lastBillsUpdatedAt = ZonedDateTime.parse(lastBillsUpdatedAt, timestampFormatWithBrazilTimeZone),
                subscription = subscription?.toSubscription(),
                onboardingState = onboarding?.toOnboardingState(),
                alreadyPromotedSweepingAccount = alreadyPromotedSweepingAccount ?: false,
            )
        }
    }

private fun UserEntity.toUser(): User =
    User(
        accountId = AccountId(accountId),
        id = UserId(id),
        name = name,
        accountGroups = accountGroups,
        paymentStatus = paymentStatus ?: AccountPaymentStatus.UpToDate,
        status = status ?: AccountStatus.ACTIVE,
    )

private fun User.toUserDbEntity(): UserEntity =
    UserEntity().apply {
        accountId = <EMAIL>
        id = <EMAIL>
        name = <EMAIL>
        accountGroups = <EMAIL>
        paymentStatus = <EMAIL>
        status = <EMAIL>
    }

private fun Balance.toBalanceEntity(): BalanceEntity =
    BalanceEntity(
        current = current,
        openAmountToday = open.amountToday,
        openAmountInSevenDays = open.amountInSevenDays,
        openAmountInFifteenDays = open.amountInFifteenDays,
        openAmountMonth = open.amountMonth,
        openAndScheduledAmountForNextMonth = open.amountForNextMonth,
        onlyScheduledAmountToday = onlyScheduled.amountToday,
        onlyScheduledAmountInSevenDays = onlyScheduled.amountInSevenDays,
        onlyScheduledAmountInFifteenDays = onlyScheduled.amountInFifteenDays,
        onlyScheduledAmountMonth = onlyScheduled.amountMonth,
        onlyScheduledAmountForNextMonth = onlyScheduled.amountForNextMonth,
        walletId = walletId.value,
    )

private fun BillView.toBillViewEntity(): BillViewEntity =
    BillViewEntity(
        billId = billId,
        assignor = assignor,
        recipient = recipient?.name,
        billDescription = billDescription,
        amount = amount,
        discount = discount,
        interest = interest,
        fine = fine,
        amountTotal = amountTotal,
        billType = billType,
        scheduledInfo = scheduledInfo,
        paymentLimitTime = paymentLimitTime,
        dueDate = dueDate.format(DateTimeFormatter.ISO_DATE),
        externalBillId = externalBillId,
        subscriptionFee = subscriptionFee,
        status = status.name,
    )

private fun SweepingConsent.toActiveConsentsEntity() = SweepingConsentEntity(
    participantId = participant.id.value,
    participantName = participant.name,
    participantShortName = participant.shortName,
    transactionLimit = transactionLimit,
    lastSuccessfulCashIn = lastSuccessfulCashIn?.format(DateTimeFormatter.ISO_DATE_TIME),
    periodicUsage = periodicUsage?.toEntity(),
)

private fun InternalStateControl.toInternalStateControlEntity(): InternalStateControlEntity =
    InternalStateControlEntity().apply {
        shouldSynchronizeBeforeCompletion = <EMAIL>
        billComingDueNotifiedAt = <EMAIL>
    }

private fun BillViewEntity.toBillView(): BillView =
    BillView(
        billId = billId,
        assignor = assignor,
        recipient = recipient?.let { Recipient(it) },
        billDescription = billDescription,
        amount = amount,
        discount = discount,
        interest = interest,
        fine = fine,
        amountTotal = amountTotal,
        billType = billType,
        scheduledInfo = scheduledInfo ?: ScheduledInfo.NOT_SCHEDULED,
        paymentLimitTime = paymentLimitTime ?: "20:00", // FIXME - criar script para popular na entidade e remover o default
        dueDate = LocalDate.parse(dueDate, DateTimeFormatter.ISO_DATE),
        externalBillId = externalBillId,
        subscriptionFee = subscriptionFee,
        status = status?.let { BillStatus.valueOf(it) } ?: BillStatus.ACTIVE,
    )

private fun ContactEntity.toContact(): Contact =
    Contact(
        id = ContactId(id),
        alias = alias,
        name = name,
        pixKeys = pixKeys.map { PixKey(value = it.key, type = it.type) },
        lastUsed = lastUsed?.pixKey?.let { LastUsed(PixKey(value = it.key, type = it.type)) },
    )

private fun Contact.toContactEntity(): ContactEntity =
    ContactEntity(
        id = id.value,
        alias = alias,
        name = name,
        pixKeys = pixKeys.map { PixKeyEntity(key = it.value, type = it.type) },
        lastUsed = lastUsed?.pixKey?.let { LastUsedEntity(pixKey = PixKeyEntity(key = it.value, type = it.type)) },
    )

fun PixKeyEntity.toPixKey(): PixKey =
    PixKey(
        value = key,
        type = type,
    )

private fun Subscription.toSubscriptionEntity(): SubscriptionEntity {
    val subscription = this
    return SubscriptionEntity().apply {
        fee = subscription.fee
        paymentStatus = subscription.paymentStatus
        dueDate = subscription.dueDate.format(dateFormat)
        type = subscription.type
    }
}

private fun OnboardingState.toOnboardingEntity(): OnboardingEntity {
    val onboarding = this
    return OnboardingEntity().apply {
        hasAcceptedExamplePayment = onboarding.hasAcceptedExamplePayment
        pixKeyValue = onboarding.pixKeyValue
        pixKeyType = onboarding.pixKeyType
        billView = getObjectMapper().writeValueAsString(onboarding.billView)
    }
}

private fun SweepingConsentPeriodicUsage.toEntity() = SweepingConsentPeriodicUsageEntity(
    daily = daily.toEntity(),
    weekly = weekly.toEntity(),
    monthly = monthly.toEntity(),
    yearly = yearly.toEntity(),
    totalLimit = totalLimit,
    totalUsed = totalUsed,
)

private fun SweepingConsentPeriodicLimitUsage.toEntity() = SweepingConsentPeriodicLimitUsageEntity(
    amountLimit = amountLimit,
    amountUsed = amountUsed,
    quantityLimit = quantityLimit,
    quantityUsed = quantityUsed,
)