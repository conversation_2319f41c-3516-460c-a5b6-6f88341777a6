package ai.chatbot.adapters.dynamodb

import io.micronaut.context.ApplicationContext
import kotlin.jvm.optionals.getOrElse
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable
import software.amazon.awssdk.enhanced.dynamodb.Key
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import software.amazon.awssdk.enhanced.dynamodb.model.PutItemEnhancedRequest
import software.amazon.awssdk.enhanced.dynamodb.model.PutItemEnhancedResponse
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest
import software.amazon.awssdk.services.dynamodb.model.ReturnValue

abstract class AbstractDynamoDAO<T>(type: Class<T>, context: ApplicationContext, cli: DynamoDbEnhancedClient) {
    private val table: DynamoDbTable<T> = cli.table(context.getProperty("dynamodb.tableName", String::class.java).getOrElse { throw IllegalStateException("DynamoDb table name not found") }, TableSchema.fromBean(type))

    fun save(item: T) = table.putItem(item)

    fun saveWithResponse(item: T): PutItemEnhancedResponse<T> =
        table.putItemWithResponse(
            PutItemEnhancedRequest.builder<T>(item!!::class.java).item(item).returnValues(ReturnValue.ALL_OLD).build(),
        )

    fun findByPrimaryKey(key: AbstractKey<T>): T? = table.getItem(key)

    fun findBeginsWithOnScanKey(
        partitionKey: String,
        sortKey: String,
    ): List<T> =
        table.queryTableOnHashKeyAndRangeKeyBeginsWith(partitionKey, sortKey)

    fun findByPrimaryKeyOnIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
    ): List<T> =
        table.getItemsByIndex(index, partitionKey, sortKey)

    fun findByPrimaryKeyOnIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
    ): List<T> =
        table.getItemsByIndexOnlyOnPrimaryKey(index, partitionKey)

    fun findLessThanOnIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
    ): List<T> =
        table.getItemsByIndexSortLessThan(index, partitionKey, sortKey)

    fun findByPartitionKeyOnMaxRangeKey(partitionKey: String): T? {
        return table.getItemByPartitionKey(partitionKey).singleOrNull()
    }

    fun findByPartitionKeyOnRangeKeyBetween(partitionKey: String, minRangeKey: String, maxRangeKey: String): List<T> {
        return table.getItemsByPartitionKeySortBetween(partitionKey, minRangeKey, maxRangeKey)
    }

    fun findByPartitionKey(partitionKey: String): List<T> {
        return table.getItemByPartitionKey(partitionKey)
    }

    fun delete(
        partitionKey: String,
        sortKey: String,
    ): T =
        table.deleteItem(Key.builder().partitionValue(partitionKey).sortValue(sortKey).build())

    private fun <T> DynamoDbTable<T>.getItem(key: AbstractKey<T>): T? =
        this.item(key.partitionKey, key.sortKey)

    private fun <T> DynamoDbTable<T>.getItemsByPartitionKeySortBetween(
        partitionKey: String,
        sortKeyMin: String,
        sortKeyMax: String,
    ): List<T> =
        this.query(
            QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortBetween(keyByPartitionKeyAndSortKey(partitionKey, sortKeyMin), keyByPartitionKeyAndSortKey(partitionKey, sortKeyMax)))
                .build(),
        )
            .flatMap { it.items() }

    private fun <T> DynamoDbTable<T>.getItemsByIndex(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
    ): List<T> =
        this.index(index.name)
            .query(partitionKey, sortKey)
            .flatMap { it.items() }

    private fun <T> DynamoDbTable<T>.getItemsByIndexSortLessThan(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
        sortKey: String,
    ): List<T> =
        this.index(index.name)
            .query(
                QueryEnhancedRequest.builder()
                    .queryConditional(QueryConditional.sortLessThan(keyByPartitionKeyAndSortKey(partitionKey, sortKey)))
                    .build(),
            )
            .flatMap { it.items() }

    private fun <T> DynamoDbTable<T>.getItemByPartitionKey(
        partitionKey: String,
    ): List<T> =
        this.query(
            QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(keyByPartitionKey(partitionKey))).scanIndexForward(false).limit(1)
                .build(),
        ).first().items()

    private fun <T> DynamoDbTable<T>.getItemsByIndexOnlyOnPrimaryKey(
        index: GlobalSecondaryIndexNames,
        partitionKey: String,
    ): List<T> =
        this.index(index.name)
            .query(
                QueryEnhancedRequest.builder()
                    .queryConditional(QueryConditional.keyEqualTo(keyByPartitionKey(partitionKey))).scanIndexForward(false).limit(1)
                    .build(),
            )
            .flatMap { it.items() }

    private fun <T> DynamoDbTable<T>.item(
        partitionKey: String,
        sortKey: String,
    ) =
        this.getItem(keyByPartitionKeyAndSortKey(partitionKey, sortKey))

    private fun <T> DynamoDbIndex<T>.query(
        partitionKey: String,
        sortKey: String,
    ) =
        this.query(queryByPartitionKeyAndSortKey(partitionKey, sortKey))

    private fun queryByPartitionKeyAndSortKey(
        partitionKey: String,
        sortKey: String,
    ) =
        QueryEnhancedRequest.builder()
            .queryConditional(conditionalByPartitionKeyAndSortKey(partitionKey, sortKey))
            .build()

    private fun conditionalByPartitionKeyAndSortKey(
        partitionKey: String,
        sortKey: String,
    ) =
        QueryConditional.keyEqualTo(keyByPartitionKeyAndSortKey(partitionKey, sortKey))

    private fun keyByPartitionKeyAndSortKey(
        partitionKey: String,
        sortKey: String,
    ) =
        Key.builder()
            .partitionValue(partitionKey)
            .sortValue(sortKey)
            .build()

    private fun keyByPartitionKey(partitionKey: String) =
        Key.builder()
            .partitionValue(partitionKey)
            .build()

    private fun <T> DynamoDbTable<T>.queryTableOnHashKeyAndRangeKeyBeginsWith(
        partitionKey: String?,
        scanKey: String?,
    ): List<T> {
        return this.query(
            QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.sortBeginsWith { it.partitionValue(partitionKey).sortValue(scanKey) }).build(),
        ).flatMap { it.items() }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(AbstractDynamoDAO::class.java)
    }
}

abstract class AbstractKey<T>(
    open val partitionKey: String,
    open val sortKey: String,
)