package ai.chatbot.adapters.dynamodb

import ai.chatbot.app.DailyLogSummaryRepository
import ai.chatbot.app.classification.ConversationAction
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.job.DailyLogErrorSummary
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.dateTimeFormat
import ai.chatbot.app.utils.getObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.micronaut.context.ApplicationContext
import io.micronaut.context.annotation.Primary
import jakarta.inject.Singleton
import java.time.LocalDate
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class DailyLogSummaryDynamoDAO(cli: DynamoDbEnhancedClient, context: ApplicationContext) : AbstractDynamoDAO<DailyLogSummaryEntity>(DailyLogSummaryEntity::class.java, context, cli)

@Singleton
@Primary
class DailyLogSummaryDbRepository(private val dynamoDbDAO: DailyLogSummaryDynamoDAO, private val tenantService: TenantService) : DailyLogSummaryRepository {
    override fun save(summary: DailyLogErrorSummary) {
        val entity =
            DailyLogSummaryEntity().apply {
                this.partitionKey = buildPartitionKey()
                this.rangeKey = summary.date.format(dateFormat)
                this.date = summary.date.format(dateFormat)
                this.version = summary.version
                this.actions = summary.actions.map { getObjectMapper().writeValueAsString(it) }
                this.totalConversations = summary.totalConversations
                this.totalActions = summary.totalActions
                this.createdAt = getZonedDateTime().format(dateTimeFormat)
            }

        dynamoDbDAO.save(entity)
    }

    override fun find(startDate: LocalDate, endDate: LocalDate): List<DailyLogErrorSummary> {
        val entity = dynamoDbDAO.findByPartitionKeyOnRangeKeyBetween(
            buildPartitionKey(),
            startDate.format(dateFormat),
            endDate.format(dateFormat),
        )

        return entity.map { it.toDailyLogSummary() }
    }

    private fun DailyLogSummaryEntity.toDailyLogSummary(): DailyLogErrorSummary {
        return DailyLogErrorSummary(
            actions = actions.map {
                val parsedAction: ConversationAction = getObjectMapper().readValue(it)
                parsedAction
            },
            date = LocalDate.parse(date, dateFormat),
            version = version,
            totalConversations = totalConversations,
            totalActions = totalActions,
        )
    }

    private fun buildPartitionKey(): String = PARTITION_KEY_PREFIX + "#" + tenantService.getTenantName()

    companion object {
        const val PARTITION_KEY_PREFIX = "DAILY_LOG_SUMMARY"
    }
}

@DynamoDbBean
class DailyLogSummaryEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = CHAT_BOT_PARTITION_KEY)
    lateinit var partitionKey: String // DAILY_LOG_SUMMARY # TENANT

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = CHAT_BOT_RANGE_KEY)
    lateinit var rangeKey: String // date

    @get:DynamoDbAttribute("Date")
    lateinit var date: String

    @get:DynamoDbAttribute("Actions")
    lateinit var actions: List<String>

    @get:DynamoDbAttribute(value = "Version")
    lateinit var version: String

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "TotalConversations")
    var totalConversations: Int = 0

    @get:DynamoDbAttribute(value = "TotalActions")
    var totalActions: Int = 0
}