package ai.chatbot.adapters.dynamodb

import ai.chatbot.app.Staging
import ai.chatbot.app.prompt.PromptType
import io.micronaut.context.ApplicationContext
import io.micronaut.context.annotation.Primary
import jakarta.inject.Singleton
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

const val PROMPT_TENANT = "PROMPT_TENANT"

@Singleton
@Primary
@Staging
class StagingPromptTenantRepository(private val dynamoDbDAO: PromptTenantDynamoDAO) {

    fun save(
        msisdn: String,
        tenant: String,
        type: PromptType? = PromptType.DEFAULT,
        hasInvestmentCampaign: Boolean? = false,
    ) {
        dynamoDbDAO.save(
            PromptTenantEntity().apply {
                partitionKey = msisdn
                rangeKey = PROMPT_TENANT
                this.tenant = tenant
                this.type = type
                this.hasInvestmentCampaign = hasInvestmentCampaign
            },
        )
    }

    fun find(
        msisdn: String,
    ): PromptTenant? {
        val tenantEntity = dynamoDbDAO.findByPrimaryKey(key = PromptTenantKey(msisdn, PROMPT_TENANT))
        return tenantEntity?.let {
            PromptTenant(tenantEntity.partitionKey, tenantEntity.tenant, tenantEntity.type, tenantEntity.hasInvestmentCampaign)
        }
    }
}

data class PromptTenantKey(override val partitionKey: String, override val sortKey: String) : AbstractKey<PromptTenantEntity>(partitionKey = partitionKey, sortKey = sortKey)

@Singleton
class PromptTenantDynamoDAO(cli: DynamoDbEnhancedClient, context: ApplicationContext) : AbstractDynamoDAO<PromptTenantEntity>(PromptTenantEntity::class.java, context, cli)

class PromptTenant(
    val msisdn: String,
    val tenant: String,
    val type: PromptType? = PromptType.DEFAULT,
    val hasInvestmentCampaign: Boolean? = false,
)

@DynamoDbBean
class PromptTenantEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = CHAT_BOT_PARTITION_KEY)
    lateinit var partitionKey: String // msisdn

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = CHAT_BOT_RANGE_KEY)
    lateinit var rangeKey: String

    @get:DynamoDbAttribute(value = "Tenant")
    lateinit var tenant: String

    @get:DynamoDbAttribute(value = "Type")
    var type: PromptType? = PromptType.DEFAULT

    @get:DynamoDbAttribute(value = "HasInvestmentCampaign")
    var hasInvestmentCampaign: Boolean? = null
}