package ai.chatbot.adapters.dynamodb

import ai.chatbot.app.InteractionWindowRepository
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.InteractionWindow
import ai.chatbot.app.conversation.InteractionWindowState
import ai.chatbot.app.conversation.InteractionWindowType
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.getObjectMapper
import ai.chatbot.app.utils.parseObjectFrom
import ai.chatbot.app.utils.timestampFormatWithBrazilTimeZone
import io.micronaut.context.ApplicationContext
import io.micronaut.context.annotation.Primary
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import kotlin.collections.map
import kotlin.jvm.java
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

const val INTERACTION_WINDOW_REGISTER = "INTERACTION_WINDOW"

@Singleton
class InteractionWindowDynamoDAO(
    cli: DynamoDbEnhancedClient,
    context: ApplicationContext,
) : AbstractDynamoDAO<InteractionWindowEntity>(InteractionWindowEntity::class.java, context, cli)

@Singleton
@Primary
class InteractionWindowDbRepository(private val dynamoDbDAO: InteractionWindowDynamoDAO, private val tenantService: TenantService) : InteractionWindowRepository {
    override fun findByUserId(userId: UserId): InteractionWindow? {
        return dynamoDbDAO.findBeginsWithOnScanKey(
            partitionKey = buildPartitionKey(userId),
            sortKey = INTERACTION_WINDOW_REGISTER,
        ).map {
            it.toInteractionWindow()
        }.singleOrNull()
    }

    override fun findAllByStateAndExpirationBefore(state: InteractionWindowState, expiration: ZonedDateTime): List<InteractionWindow> {
        val tenantEntities = dynamoDbDAO.findLessThanOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = buildIndex1PartitionKey(state),
            sortKey = expiration.format(timestampFormatWithBrazilTimeZone),
        )

        return tenantEntities.map { it.toInteractionWindow() }
    }

    override fun save(window: InteractionWindow) {
        val entity = InteractionWindowEntity().apply {
            partitionKey = buildPartitionKey(window.userId)
            rangeKey = INTERACTION_WINDOW_REGISTER
            index1HashKey = buildIndex1PartitionKey(window.state)
            index1RangeKey = window.expiration.format(timestampFormatWithBrazilTimeZone)
            userId = window.userId.value
            expiration = window.expiration.format(timestampFormatWithBrazilTimeZone)
            type = window.type
            state = window.state
            notificationQueue = window.notificationQueue.map { getObjectMapper().writeValueAsString(it) }
        }

        dynamoDbDAO.save(entity)
    }

    override fun delete(window: InteractionWindow) {
        dynamoDbDAO.delete(buildPartitionKey(window.userId), INTERACTION_WINDOW_REGISTER)
    }

    private fun buildPartitionKey(id: UserId): String = id.value + "#${tenantService.getTenantName()}"

    private fun buildIndex1PartitionKey(state: InteractionWindowState): String = "STATE#${state.name}#${tenantService.getTenantName()}"
}

@DynamoDbBean
class InteractionWindowEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = CHAT_BOT_PARTITION_KEY)
    lateinit var partitionKey: String // userId # TENANT

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = CHAT_BOT_RANGE_KEY)
    lateinit var rangeKey: String // INTERACTION_WINDOW

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var index1HashKey: String // STATE#state # TENANT

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    lateinit var index1RangeKey: String // expiration

    @get:DynamoDbAttribute(value = "UserId")
    lateinit var userId: String

    @get:DynamoDbAttribute(value = "Type")
    lateinit var type: InteractionWindowType

    @get:DynamoDbAttribute(value = "State")
    lateinit var state: InteractionWindowState

    @get:DynamoDbAttribute(value = "Expiration")
    lateinit var expiration: String

    @get:DynamoDbAttribute(value = "NotificationQueue")
    lateinit var notificationQueue: List<String>
}

private fun InteractionWindowEntity.toInteractionWindow(): InteractionWindow {
    return InteractionWindow(
        userId = UserId(this.userId),
        expiration = this.expiration.let { ZonedDateTime.parse(it, timestampFormatWithBrazilTimeZone) },
        type = this.type,
        state = this.state,
        notificationQueue = this.notificationQueue.map { parseObjectFrom(it) },
    )
}