package ai.chatbot.adapters.media

import ai.chatbot.app.media.RemoteMediaProcessor
import io.via1.communicationcentre.app.integrations.MachineLearningParser
import io.via1.communicationcentre.app.parser.ParsedBill
import jakarta.inject.Singleton
import java.io.ByteArrayInputStream
import javax.imageio.ImageIO
import org.slf4j.LoggerFactory

@Singleton
open class RemoteBarCodeMediaProcessor(
    private val barCodeReader: MachineLearningParser,
) : RemoteMediaProcessor {
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun process(mediaBytes: ByteArray): Pair<ParsedBill?, String>? {
        try {
            val bufferedImage = ImageIO.read(ByteArrayInputStream(mediaBytes))
            val (parsedBill, text) = barCodeReader.parseContentWithText(bufferedImage)

            return (
                parsedBill?.also {
                    it.dueDate = it.dueDate ?: ""
                    it.digitableLine = it.digitableLine ?: ""
                } to text
                )
        } catch (e: Exception) {
            logger.warn("RemoteBarCodeMediaProcessor#process", e)
            return null
        }
    }
}