package ai.chatbot.adapters.media

import ai.chatbot.app.media.BoletoInfo
import ai.chatbot.app.media.TextProcessor
import jakarta.inject.Singleton
import java.util.regex.Pattern
import org.slf4j.LoggerFactory

@Singleton
open class DocumentBarCodeProcessor : TextProcessor {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val name = "DocumentBarCodeProcessor"

    private val space = "[-., \\|]*"
    private val knownPatterns = arrayOf(
        Pattern.compile("8[0-9]{10}$space[0-9]$space([0-9]{11}$space[0-9]$space){3}"),
        Pattern.compile("([0-9]{11}$space[0-9]$space){4}"),
        Pattern.compile("([0-9]{5}$space){3}[0-9]{6}$space[0-9]{5}$space[0-9]{6}$space[0-9]$space[0-9]{14}"),
        Pattern.compile("[0-9]{9}$space[0-9]{1}$space[0-9]{10}$space[0-9]{1}$space[0-9]{10}$space[0-9]{1}$space[0-9]$space[0-9]{14}"),
    )

    override fun processText(text: String): List<BoletoInfo> {
        val boletos = mutableListOf<BoletoInfo>()

        try {
            knownPatterns.forEach { pattern ->
                val matcher = pattern.matcher(text)

                while (matcher.find()) {
                    val code = matcher.group()

                    val normalizedCode = code.replace(Regex("[-.\\s]"), "")

                    if (isValidBarcode(normalizedCode)) {
                        boletos.add(BoletoInfo(codigo = normalizedCode))
                    }
                }
            }

            return boletos.distinct()
        } catch (e: Exception) {
            logger.warn("$name#processText", e)
            return emptyList()
        }
    }

    private fun isValidBarcode(barcode: String): Boolean {
        return barcode.all { it.isDigit() } &&
            (
                (barcode.length < 48 && !barcode.startsWith("8")) ||
                    (barcode.length == 48 && barcode.startsWith("8"))
                )
    }
}