package ai.chatbot.adapters.media

import ai.chatbot.app.media.ImageProcessor
import com.google.zxing.BarcodeFormat
import com.google.zxing.BinaryBitmap
import com.google.zxing.DecodeHintType
import com.google.zxing.MultiFormatReader
import com.google.zxing.RGBLuminanceSource
import com.google.zxing.common.HybridBinarizer
import com.google.zxing.multi.GenericMultipleBarcodeReader
import jakarta.inject.Singleton
import java.io.ByteArrayInputStream
import javax.imageio.ImageIO
import org.slf4j.LoggerFactory

@Singleton
open class ImageQRCodeProcessor : ImageProcessor {
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun processImage(imageBytes: ByteArray): List<String> {
        return try {
            val image = ImageIO.read(ByteArrayInputStream(imageBytes))
            val width = image.width
            val height = image.height

            val pixels = IntArray(width * height)
            image.getRGB(0, 0, width, height, pixels, 0, width)

            val luminanceSource = RGBLuminanceSource(width, height, pixels)
            val binarizer = HybridBinarizer(luminanceSource)
            val binaryBitmap = BinaryBitmap(binarizer)

            val reader = MultiFormatReader()

            val hints = mapOf(
                DecodeHintType.POSSIBLE_FORMATS to listOf(BarcodeFormat.QR_CODE),
                DecodeHintType.TRY_HARDER to true,
            )
            reader.setHints(hints)
            val multiReader = GenericMultipleBarcodeReader(reader)

            val results = multiReader.decodeMultiple(binaryBitmap)

            results
                .sortedBy { result -> result.resultPoints?.minOfOrNull { it.y } ?: 0f } // ordenando de cima pra baixo
                .map { it.text }
                .filter { it.isNotEmpty() }
                .distinct()
        } catch (e: Exception) {
            logger.warn("ImageQRCodeProcessor#processImage", e)
            emptyList()
        }
    }
}