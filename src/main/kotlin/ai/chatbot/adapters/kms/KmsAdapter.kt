package ai.chatbot.adapters.kms

import ai.chatbot.app.encrypt.EncryptService
import com.amazonaws.encryptionsdk.AwsCrypto
import com.amazonaws.encryptionsdk.CommitmentPolicy
import com.amazonaws.encryptionsdk.kmssdkv2.KmsMasterKeyProvider
import jakarta.inject.Singleton
import java.util.Base64

@Singleton
class KmsAdapter : EncryptService {

    override fun decrypt(encryptedBase64: String): String {
        val kmsKeyProvider = KmsMasterKeyProvider.builder().buildDiscovery()

        val crypto = AwsCrypto.builder()
            .withCommitmentPolicy(CommitmentPolicy.RequireEncryptAllowDecrypt)
            .build()

        val ciphertext = Base64.getDecoder().decode(encryptedBase64)
        val result = crypto.decryptData(kmsKeyProvider, ciphertext)
        return result.result.decodeToString()
    }
}