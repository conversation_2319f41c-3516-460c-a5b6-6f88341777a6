package ai.chatbot.adapters.awstranscribe

import ai.chatbot.app.file.StoredObject
import ai.chatbot.app.transcription.TranscriptionAdapter
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.getObjectMapper
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpRequest
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.lang.Thread.sleep
import java.util.UUID
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.transcribe.TranscribeClient
import software.amazon.awssdk.services.transcribe.model.GetTranscriptionJobRequest
import software.amazon.awssdk.services.transcribe.model.NotFoundException
import software.amazon.awssdk.services.transcribe.model.StartTranscriptionJobRequest
import software.amazon.awssdk.services.transcribe.model.TranscriptionJob
import software.amazon.awssdk.services.transcribe.model.TranscriptionJobStatus

@Singleton
@Requires(property = "aws.transcription.enabled", value = "true")
class AWSTranscriptionAdapter(
    private val transcribeClient: TranscribeClient,
    private val httpClient: RxHttpClient,
    @Property(name = "aws.transcription.language") private val language: String,
) : TranscriptionAdapter {
    private val logger = LoggerFactory.getLogger(AWSTranscriptionAdapter::class.java)

    override val name: String = "aws"

    override suspend fun transcribe(storedObject: StoredObject): String? {
        val jobName = "transcription-${UUID.randomUUID()}"

        val mediaUri = storedObject.toS3Path()

        val request = StartTranscriptionJobRequest.builder()
            .transcriptionJobName(jobName)
            .languageCode(language)
            .media { it.mediaFileUri(mediaUri) }
            .build()

        transcribeClient.startTranscriptionJob(request)

        val maxRetries = 100
        val interval = 100L

        repeat(maxRetries) { index ->
            val job = currentJob(jobName)
            if (job != null && job.transcriptionJobStatus() == TranscriptionJobStatus.COMPLETED) {
                val fileUri = job.transcript().transcriptFileUri()
                return fetchTranscriptionFromUrl(fileUri)
            }
            logger.info(append("jobStatus", job?.transcriptionJobStatus()).andAppend("jobName", jobName).andAppend("tentativa", index), "AWSTranscriptionAdapter#transcribe")
            sleep(interval)
        }
        return null
    }

    private fun currentJob(jobName: String): TranscriptionJob? {
        val request = GetTranscriptionJobRequest.builder()
            .transcriptionJobName(jobName)
            .build()

        return try {
            transcribeClient.getTranscriptionJob(request).transcriptionJob()
        } catch (e: NotFoundException) {
            null
        }
    }

    private fun fetchTranscriptionFromUrl(jsonUrl: String): String? {
        val request = HttpRequest.GET<Unit>(jsonUrl)

        val call = httpClient.retrieve(request, String::class.java)
        val response = call.firstOrError().blockingGet()
        logger.info(append("response", response).andAppend("jsonUrl", jsonUrl), "AWSTranscriptionAdapter#fetchTranscriptionFromUrl")

        val root = getObjectMapper().readTree(response)
        val transcript = root["results"]
            ?.get("transcripts")
            ?.firstOrNull()
            ?.get("transcript")
            ?.asText()
        return transcript
    }
}