package ai.chatbot.adapters.billPayment

import ai.chatbot.app.Staging
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.payment.Balance
import ai.chatbot.app.payment.Forecast
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import arrow.core.Either
import arrow.core.right
import io.micronaut.context.annotation.Primary
import jakarta.inject.Singleton

@Singleton
@Primary
@Staging
open class StagingBillPaymentAdapter(
    configuration: BillPaymentAdapterConfiguration,
    httpClients: Map<String, BillPaymentHttpClient>,
    tenantService: TenantService,
) : BillPaymentAdapter(configuration = configuration, clients = httpClients, tenantService = tenantService) {

    override suspend fun getBalanceAndForecast(userId: UserId, walletId: WalletId?): Either<PaymentAdapterError, Balance> {
        return Balance(
            current = 1_000L,
            open = Forecast(0L, 0L, 0L, 0L, 0L),
            onlyScheduled = Forecast(0L, 0L, 0L, 0L, 0L),
            walletId = walletId ?: WalletId("WALLET-ID"),
        ).right()
    }
}