package ai.chatbot.adapters.billPayment

import ai.chatbot.app.config.EachTenant
import ai.chatbot.app.config.TenantConfiguration
import io.micronaut.context.annotation.Factory
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.net.URL

class BillPaymentHttpClient(
    val httpClient: RxHttpClient,
)

fun Map<String, BillPaymentHttpClient>.getHttpClient(tenantName: String) = this[tenantName.lowercase()]?.httpClient
    ?: throw IllegalArgumentException("HttpClient not found for tenant: $tenantName")

@Factory
class BillPaymentHttpClientFactory(
    private val configuration: BillPaymentHttpConfiguration,
) {
    @EachTenant
    fun createHttpClient(tenantConfiguration: TenantConfiguration): BillPaymentHttpClient {
        val host = tenantConfiguration.integrations.billPayment.host
        val rxHttpClient = RxHttpClient.create(URL(host), configuration)
        return BillPaymentHttpClient(rxHttpClient)
    }
}