package ai.chatbot.adapters.billPayment

import ai.chatbot.adapters.api.ResponseTO
import ai.chatbot.app.AvailableLimitResponse
import ai.chatbot.app.AvailableLimitType
import ai.chatbot.app.CreateBillResult
import ai.chatbot.app.ManualEntryId
import ai.chatbot.app.ManualEntryType
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.PixLimitStatus
import ai.chatbot.app.PixValidationResult
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.SweepingConsentPeriodicLimitUsage
import ai.chatbot.app.SweepingConsentPeriodicUsage
import ai.chatbot.app.SweepingParticipant
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.SweepingRequest
import ai.chatbot.app.bill.BarCode
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.BillType
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.bill.Recipient
import ai.chatbot.app.bill.ScheduledInfo
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.contact.ContactId
import ai.chatbot.app.contact.LastUsed
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.conversation.Subscription
import ai.chatbot.app.conversation.SubscriptionPaymentStatus
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.conversation.SweepingLimitType
import ai.chatbot.app.conversation.SweepingTransferErrorReason
import ai.chatbot.app.event.UserEvent
import ai.chatbot.app.event.UserEventMessage
import ai.chatbot.app.payment.Balance
import ai.chatbot.app.payment.Forecast
import ai.chatbot.app.pix.AssistantPaymentLimit
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.pix.PixQRCode
import ai.chatbot.app.pix.isValid
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserActivity
import ai.chatbot.app.user.UserActivityType
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getEpochMili
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.brazilTimeZone
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.dateTimeFormat
import ai.chatbot.app.utils.measure
import ai.chatbot.app.utils.parseObjectFrom
import ai.chatbot.app.utils.timeSince
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.annotation.Introspected
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.HttpVersion
import io.micronaut.http.MediaType.APPLICATION_JSON_TYPE
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.HttpClientConfiguration
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.client.exceptions.ReadTimeoutException
import io.micronaut.http.client.exceptions.ResponseClosedException
import io.micronaut.http.uri.UriBuilder
import io.micronaut.retry.annotation.Retryable
import io.micronaut.runtime.ApplicationConfiguration
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.netty.handler.codec.http.HttpHeaderNames
import jakarta.inject.Singleton
import jakarta.validation.Valid
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Positive
import java.math.BigInteger
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

private const val descriptionRegex = "^[^'|]{0,140}\$"
private const val descriptionErrorMessage =
    "Description size must be up to 140 length and can't include | or ' character"

@Singleton
class BillPaymentHttpConfiguration(
    applicationConfiguration: ApplicationConfiguration,
    val configuration: HttpClientConfiguration,
) : HttpClientConfiguration(applicationConfiguration) {
    init {
        setReadTimeout(Duration.ofSeconds(65))
        httpVersion = HttpVersion.HTTP_1_1
    }

    override fun getConnectionPoolConfiguration(): ConnectionPoolConfiguration =
        configuration.connectionPoolConfiguration

    override fun getMaxContentLength(): Int = 20971520
}

@Singleton
open class BillPaymentAdapter(
    private val configuration: BillPaymentAdapterConfiguration,
    private val tenantService: TenantService,
    private val clients: Map<String, BillPaymentHttpClient>,
) : PaymentAdapter {
    @Retryable(includes = [ResponseClosedException::class, ReadTimeoutException::class, HttpServerErrorException::class])
    override suspend fun generatePixQRCode(
        userId: UserId,
        amount: Long,
        walletId: WalletId,
        message: String,
    ): Either<PaymentAdapterError, PixQRCode> {
        val markers = Markers.append("userId", userId.value).andAppend("amount", amount)
        val logName = "BillPaymentAdapter#generatePixQRCode"
        val payload =
            PixQRCodeRequestTO(
                amount = amount,
                walletId = walletId.value,
                message = message,
            )
        return try {
            val httpRequest = HttpRequest.POST(configuration.pixQRCodePath, payload).withDefaultHeaders(userId)
            val call = getHttpClient().retrieve(httpRequest, PixQRCodeResponseTO::class.java)
            val response = call.firstOrError().blockingGet()
            PixQRCode(response.qrCode)
                .also {
                    logger.info(markers.andAppend("qrCode", it.value), logName)
                }.right()
        } catch (ex: Exception) {
            return resolveException(ex, markers, logName).left()
        }
    }

    @Retryable(includes = [ResponseClosedException::class, ReadTimeoutException::class, HttpServerErrorException::class])
    override suspend fun generateOnePixPay(
        userId: UserId,
        bills: List<BillId>,
        walletId: WalletId,
    ): Either<PaymentAdapterError, PixQRCode> {
        val billIds = bills.map { it.value }

        val markers = Markers.append("userId", userId.value).andAppend("bills", billIds)
        val logName = "BillPaymentAdapter#generateOnePixPay"
        val payload =
            OnePixPayTO(
                bills = billIds,
                walletId = walletId.value,
            )
        return try {
            val httpRequest = HttpRequest.POST(configuration.onePixPayPath, payload).withDefaultHeaders(userId)
            val call = getHttpClient().retrieve(httpRequest, PixQRCodeResponseTO::class.java)
            val response = call.firstOrError().blockingGet()
            PixQRCode(response.qrCode)
                .also {
                    logger.info(markers.andAppend("qrCode", it.value), logName)
                }.right()
        } catch (ex: Exception) {
            return resolveException(ex, markers, logName).left()
        }
    }

    override suspend fun createOnboardingSinglePix(keyType: String?, keyValue: String?, userId: UserId): Either<PaymentAdapterError, BillView> {
        val markers = Markers.append("keyType", keyType).andAppend("keyValue", keyValue).andAppend("userId", userId.value)
        val logName = "BillPaymentAdapter#createOnboardingTestPix"

        val payload = if (keyType == null || keyValue == null) {
            CreateOnboardingTestPixTO(key = null)
        } else {
            CreateOnboardingTestPixTO(key = OnboardingTestPixKeyTO(keyValue = keyValue, keyType = PixKeyType.valueOf(keyType)))
        }

        val requestTime = getEpochMili()

        return try {
            val httpRequest = HttpRequest.POST(configuration.onboardingTestPixPath, payload).withDefaultHeaders(userId)
            val call = getHttpClient().exchange(httpRequest, Argument.of(BillTO::class.java), Argument.of(OnboardingTestPixResponseTO::class.java))
            val response = call.firstOrError().blockingGet()

            logger.info(markers.andAppend("status", response.status).andAppend("elapsedTime", getEpochMili() - requestTime), logName)

            response.body().toBillView(1).right()
        } catch (ex: HttpClientResponseException) {
            logger.error(markers.andAppend("status", ex.status).andAppend("response", ex.response), logName, ex)

            val errorBody = ex.response.getBody(OnboardingTestPixResponseTO::class.java)

            markers.andAppend("elapsedTime", getEpochMili() - requestTime)

            if (errorBody.isPresent) {
                markers.andAppend("responseBody", errorBody)

                logger.warn(markers, logName)

                when (errorBody.get().code) {
                    "4008" -> PaymentAdapterError.OnboardingSinglePixAlreadyPaid.left()
                    "4009" -> PaymentAdapterError.PixKeyNotFound.left()
                    "4010" -> PaymentAdapterError.PixFromDifferentOwner.left()
                    "4011" -> PaymentAdapterError.OnboardingSinglePixCreateError.left()
                    else -> PaymentAdapterError.ServerError(ex).left()
                }
            } else {
                logger.error(markers, logName, ex)
                PaymentAdapterError.ServerError(ex).left()
            }
        } catch (ex: Exception) {
            markers.andAppend("elapsedTime", getEpochMili() - requestTime)
            logger.error(markers, logName, ex)
            PaymentAdapterError.ServerError(ex).left()
        }
    }

    @Retryable(includes = [ResponseClosedException::class, ReadTimeoutException::class])
    override fun addBill(
        userId: UserId,
        digitable: String,
        dueDate: String?,
        dryRun: Boolean,
    ): Either<PaymentAdapterError, CreateBillResult> {
        val logName = "BillPaymentAdapter#addBill"
        val markers = Markers.append("digitable", digitable).andAppend("userId", userId.value)

        try {
            val barCode = BarCode.ofDigitable(digitable)
            val body = CreateBillTO(
                digitableLine = barCode.digitable,
                dueDate = dueDate,
            )

            val httpRequest = HttpRequest.POST("${configuration.addBillPath}?dryRun=$dryRun", body).withDefaultHeaders(userId)
            val call = getHttpClient().retrieve(httpRequest, CreateBillResultTO::class.java)
            val response = call.firstOrError().blockingGet()

            logger.info(markers.andAppend("response", response), logName)

            return response.toBillValidationResult().right()
        } catch (ex: HttpClientResponseException) {
            val errorBody = ex.response.getBody(String::class.java)

            if (errorBody.isPresent) {
                markers.andAppend("responseBody", errorBody)
            }
            val error = parseObjectFrom<PaymentAdapterErrorTO>(errorBody.get())

            logger.warn(markers, logName)

            return when (error.code) {
                "4001" -> PaymentAdapterError.BillNotPayable.left()
                "4002" -> PaymentAdapterError.BillAlreadyPaid.left()
                "4003" -> PaymentAdapterError.BillPaymentLimitExpired.left()
                "4004" -> PaymentAdapterError.BillBarcodeNotFound.left()
                "4005" -> PaymentAdapterError.BillEmptyAmount.left()
                "4006" -> PaymentAdapterError.BillAlreadyExists.left()
                "4007" -> PaymentAdapterError.UnableToValidateBill.left()
                "5001" -> PaymentAdapterError.ServerError(ex).left()
                else -> {
                    resolveException(ex, markers, logName).left()
                }
            }
        }
    }

    override fun checkBills(userId: UserId, billId: List<BillId>): Either<PaymentAdapterError, List<BillView>> {
        val markers = Markers.append("userId", userId.value).andAppend("billId", billId)
        val logName = "BillPaymentAdapter#checkBills"
        val payload = CheckBillsTO(bills = billId.map { it.value })
        return try {
            val httpRequest = HttpRequest.POST(configuration.checkBillsPath, payload).withDefaultHeaders(userId)
            val call = getHttpClient().retrieve(httpRequest, ListBillsTO::class.java)
            val response = call.firstOrError().blockingGet()
            response.bills.mapIndexed { index, billTO -> billTO.toBillView(index) }.right()
        } catch (ex: Exception) {
            resolveException(ex, markers, logName).left()
        }
    }

    override suspend fun scheduleBills(userId: UserId, sweepingRequest: SweepingRequest?, walletId: WalletId, bills: List<BillId>, authorizationToken: String?, scheduleToDueDate: Boolean): Either<PaymentAdapterError, Unit> {
        val billIds = bills.map { it.value }
        val markers = Markers.append("userId", userId.value)
            .andAppend("sweepingRequest", sweepingRequest)
            .andAppend("walletId", walletId.value)
            .andAppend("bills", billIds)
            .andAppend("authorizationToken", authorizationToken)
        val logName = "BillPaymentAdapter#scheduleBills"
        val payload =
            ChatbotScheduleBillsTO(
                sweepingRequest = sweepingRequest?.toSweepingRequestTO(),
                bills = billIds,
                walletId = walletId.value,
                authorizationToken = authorizationToken,
                scheduleToDueDate = scheduleToDueDate,
            )
        markers.andAppend("payload", payload)

        val requestTime = getEpochMili()

        return try {
            val httpRequest = HttpRequest.POST(configuration.scheduleBillsPath, payload).withDefaultHeaders(userId)
            val call = getHttpClient().exchange(httpRequest, Argument.of(Unit::class.java), Argument.STRING)
            val response = call.firstOrError().blockingGet()

            logger.info(markers.andAppend("status", response.status).andAppend("elapsedTime", getEpochMili() - requestTime), logName)
            if (response.status != HttpStatus.NO_CONTENT) {
                throw IllegalStateException("Unexpected status code: ${response.status}")
            }

            Unit.right()
        } catch (ex: HttpClientResponseException) {
            val errorBody = ex.response.getBody(String::class.java)

            markers.andAppend("elapsedTime", getEpochMili() - requestTime).andAppend("status", ex.status)

            if (errorBody.isPresent) {
                markers.andAppend("responseBody", errorBody)
            }

            logger.warn(markers, logName)

            val error = parseObjectFrom<PaymentAdapterErrorTO>(errorBody.get())

            return when (error.code) {
                "4001", "4002" -> PaymentAdapterError.BillNotActiveError.left()
                "4003" -> PaymentAdapterError.SweepingTransferError(SweepingTransferErrorReason.GenericReason(error.message.orEmpty())).left()
                "40031" -> SweepingLimitType.DAILY.toSweepingTransferError().left()
                "40032" -> SweepingLimitType.WEEKLY.toSweepingTransferError().left()
                "40033" -> SweepingLimitType.MONTHLY.toSweepingTransferError().left()
                "40034" -> SweepingLimitType.YEARLY.toSweepingTransferError().left()
                "40035" -> SweepingLimitType.GLOBAL.toSweepingTransferError().left()
                "40036" -> SweepingLimitType.TRANSACTION.toSweepingTransferError().left()
                "40037" -> SweepingLimitType.UNKNOWN.toSweepingTransferError().left()
                "4004" -> PaymentAdapterError.AssistantLimitExceeded.left()
                "5001" -> PaymentAdapterError.ErrorSchedulingBill.left()
                else -> {
                    resolveException(ex, markers, logName).left()
                }
            }
        } catch (ex: Exception) {
            resolveException(ex, markers, logName).left()
        }
    }

    @Retryable(includes = [ResponseClosedException::class, ReadTimeoutException::class])
    override suspend fun markBillsAsPaid(
        userId: UserId,
        bills: List<BillId>,
        walletId: WalletId?,
    ): Either<PaymentAdapterError, Unit> {
        val markers = Markers.append("userId", userId.value).andAppend("bills", bills.map { it.value }).andAppend("walletId", walletId?.value)
        val logName = "BillPaymentAdapter#markBillsAsPaid"
        val payload = BillsAndWalletTO(bills = bills.map { it.value }, walletId = walletId?.value)

        markers.andAppend("payload", payload)
        return try {
            val httpRequest = HttpRequest.POST(configuration.markBillsAsPaid, payload).withDefaultHeaders(userId)
            val call = getHttpClient().exchange(httpRequest, Argument.of(Unit::class.java), Argument.STRING)
            val response = call.firstOrError().blockingGet()

            logger.info(markers.andAppend("response", response), logName)
            if (response.status != HttpStatus.NO_CONTENT) {
                throw IllegalStateException("Unexpected status code: ${response.status}")
            }

            Unit.right()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return resolveException(ex, markers, logName).left()
        }
    }

    @Retryable(includes = [ResponseClosedException::class, ReadTimeoutException::class])
    override suspend fun markReminderAsDone(
        userId: UserId,
        reminderId: String,
    ): Either<PaymentAdapterError, Unit> {
        val markers = Markers.append("userId", userId.value).andAppend("reminderId", reminderId)
        val logName = "BillPaymentAdapter#markReminderAsDone"
        val payload = MarkReminderAsDoneTO(reminderId = reminderId)

        markers.andAppend("payload", payload)
        return try {
            val httpRequest = HttpRequest.POST(configuration.markReminderAsDone, payload).withDefaultHeaders(userId)
            val call = getHttpClient().exchange(httpRequest, Argument.of(Unit::class.java), Argument.STRING)
            val response = call.firstOrError().blockingGet()

            logger.info(markers.andAppend("response", response), logName)
            if (response.status != HttpStatus.NO_CONTENT) {
                throw IllegalStateException("Unexpected status code: ${response.status}")
            }

            Unit.right()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return PaymentAdapterError.ReminderError.left()
        }
    }

    @Retryable(includes = [ResponseClosedException::class, ReadTimeoutException::class])
    override suspend fun ignoreBills(
        userId: UserId,
        bills: List<BillId>,
        walletId: WalletId?,
    ): Either<PaymentAdapterError, Unit> {
        val markers = Markers.append("userId", userId.value).andAppend("bills", bills.map { it.value }).andAppend("walletId", walletId?.value)
        val logName = "BillPaymentAdapter#ignoreBills"
        val payload = BillsAndWalletTO(bills = bills.map { it.value }, walletId = walletId?.value)

        markers.andAppend("payload", payload)
        return try {
            val httpRequest = HttpRequest.POST(configuration.ignoreBills, payload).withDefaultHeaders(userId)
            val call = getHttpClient().exchange(httpRequest, Argument.of(Unit::class.java), Argument.STRING)
            val response = call.firstOrError().blockingGet()

            logger.info(markers.andAppend("response", response), logName)
            if (response.status != HttpStatus.NO_CONTENT) {
                throw IllegalStateException("Unexpected status code: ${response.status}")
            }

            Unit.right()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return resolveException(ex, markers, logName).left()
        }
    }

    @Retryable(includes = [ResponseClosedException::class, ReadTimeoutException::class])
    override fun pixValidation(
        key: PixKey,
        userId: UserId,
        amount: Long,
        walletId: WalletId,
    ): Either<PaymentAdapterError, PixValidationResult> {
        val logName = "BillPaymentAdapter#pixValidation"
        val markers = Markers.append("pixKey", key.value).andAppend("pixKeyType", key.type)

        return if (!key.isValid()) {
            logger.warn(markers.andAppend("reason", "Invalid pix key"), logName)
            PaymentAdapterError.InvalidPixKey.left()
        } else {
            try {
                val body = ValidatePixTO(
                    keyType = key.type,
                    keyValue = key.value,
                    walletId = walletId.value,
                    amount = amount,
                )

                val httpRequest = HttpRequest.POST(configuration.validatePix, body).withDefaultHeaders(userId)
                val call = getHttpClient().retrieve(httpRequest, PixKeyResponseTO::class.java)
                val response = call.firstOrError().blockingGet()

                logger.info(markers.andAppend("response", response), logName)

                response.toPixValidationResult().right()
            } catch (ex: HttpClientResponseException) {
                return when (ex.status) {
                    HttpStatus.BAD_REQUEST -> PaymentAdapterError.InvalidPixKey.left()
                    HttpStatus.NOT_FOUND -> PaymentAdapterError.PixKeyNotFound.left()
                    HttpStatus.INTERNAL_SERVER_ERROR -> PaymentAdapterError.ServerError(ex).left()
                    else -> resolveException(ex, markers, logName).left()
                }
            }
        }
    }

    @Retryable(includes = [ResponseClosedException::class, ReadTimeoutException::class])
    override fun getContacts(
        userId: UserId,
        walletId: WalletId?,
    ): Either<PaymentAdapterError, List<Contact>> {
        val logName = "BillPaymentAdapter#getContacts"
        val markers = Markers.append("userId", userId.value).andAppend("walletId", walletId)
        return try {
            val walletIdParam = walletId?.let { "?walletId=${it.value}" } ?: ""
            val httpRequest = HttpRequest.GET<ContactsListTO>("${configuration.listContacts}?$walletIdParam").withDefaultHeaders(userId)
            val call = getHttpClient().retrieve(httpRequest, ContactsListTO::class.java)
            val response = call.firstOrError().blockingGet()
            val contacts =
                response.contacts.map {
                    Contact(
                        id = ContactId(value = it.id),
                        alias = it.alias,
                        name = it.name,
                        pixKeys = it.pixKeys.map { pixKey -> PixKey(pixKey.value, pixKey.type) },
                        lastUsed = it.lastUsed?.pixKey?.let { pixKey -> LastUsed(pixKey = PixKey(value = pixKey.value, type = pixKey.type)) },
                    )
                }
            contacts.right()
        } catch (ex: Exception) {
            resolveException(ex, markers, logName).left()
        }
    }

    override suspend fun pixTransaction(
        userId: UserId,
        walletId: WalletId,
        amount: Long,
        sweepingRequest: SweepingRequest?,
        key: PixKey,
        transactionId: TransactionId,
        authorizationToken: String?,
        qrCode: String?,
        retryTransaction: Boolean,
    ): Either<PaymentAdapterError, Unit> {
        val markers = Markers.append("userId", userId.value)
            .andAppend("walletId", walletId.value)
            .andAppend("amount", amount)
            .andAppend("sweepingRequest", sweepingRequest)
            .andAppend("key", key.value)
            .andAppend("transactionId", transactionId.value)
            .andAppend("authorizationToken", authorizationToken)
            .andAppend("qrCode", qrCode)
            .andAppend("retryTransaction", retryTransaction)
        val logName = "BillPaymentAdapter#pixTransaction"
        val pixKey = key.value
        val recipient = if (qrCode != null) {
            RequestPixRecipientTO(
                name = null,
                document = null,
                documentType = null,
                pixKey = null,
                qrCode = qrCode,
            )
        } else {
            RequestPixRecipientTO(
                name = null,
                document = null,
                documentType = null,
                pixKey = PixKeyRequestTO(value = pixKey, type = key.type),
            )
        }

        val payload = CreatePixTO(
            recipient = recipient,
            amount = amount,
            sweepingRequest = sweepingRequest?.toSweepingRequestTO(),
            description = "",
            transactionId = transactionId.value,
            authorizationToken = authorizationToken,
            retryTransaction = retryTransaction,
        )
        markers.andAppend("payload", payload)

        val startTime = getZonedDateTime()

        return try {
            val httpRequest = HttpRequest.POST(configuration.sendPix, payload).withDefaultHeaders(userId)
            val call = getHttpClient().exchange(httpRequest, Argument.of(Unit::class.java), Argument.STRING)
            val response = call.firstOrError().blockingGet()

            markers.andAppend("timeTaken", timeSince(startTime))

            logger.info(markers.andAppend("response", response), logName)
            if (response.status != HttpStatus.NO_CONTENT) {
                throw IllegalStateException("Unexpected status code: ${response.status}")
            }

            Unit.right()
        } catch (ex: HttpClientResponseException) {
            markers.andAppend("timeTaken", timeSince(startTime))
            val errorBody = ex.response.getBody(String::class.java)
            if (errorBody.isPresent) {
                markers.andAppend("responseBody", errorBody)

                val error = try {
                    parseObjectFrom<PaymentAdapterErrorTO>(errorBody.get())
                } catch (e: Exception) {
                    logger.error(markers.andAppend("statusCode", ex.status.code).andAppend("errorBody", errorBody.get()), "$logName/failedToDecodeErrorBody")
                    throw e
                }

                logger.warn(markers, logName)

                return when (error.code) {
                    "4003" -> PaymentAdapterError.SweepingTransferError(SweepingTransferErrorReason.GenericReason(error.message.orEmpty())).left()
                    "40031" -> SweepingLimitType.DAILY.toSweepingTransferError().left()
                    "40032" -> SweepingLimitType.WEEKLY.toSweepingTransferError().left()
                    "40033" -> SweepingLimitType.MONTHLY.toSweepingTransferError().left()
                    "40034" -> SweepingLimitType.YEARLY.toSweepingTransferError().left()
                    "40035" -> SweepingLimitType.GLOBAL.toSweepingTransferError().left()
                    "40036" -> SweepingLimitType.TRANSACTION.toSweepingTransferError().left()
                    "40037" -> SweepingLimitType.UNKNOWN.toSweepingTransferError().left()
                    "4004" -> PaymentAdapterError.AssistantLimitExceeded.left()
                    "4005" -> PaymentAdapterError.PixLimitExceeded.left()
                    "4007" -> {
                        // Bill already exists
                        logger.warn(markers, "$logName/billAlreadyExists")
                        Unit.right()
                    }

                    else -> {
                        resolveException(ex, markers, logName).left()
                    }
                }
            } else {
                return resolveException(ex, markers, logName).left()
            }
        } catch (ex: Exception) {
            markers.andAppend("timeTaken", timeSince(startTime))
            resolveException(ex, markers, logName).left()
        }
    }

    override fun retrySweepingTransfer(
        userId: UserId,
        walletId: WalletId,
        amount: Long,
        participantId: SweepingParticipantId?,
    ): Either<SweepingTransferErrorReason, String> {
        val markers = Markers.append("userId", userId.value)
            .andAppend("amount", amount)
            .andAppend("participantId", participantId?.value)
        val logName = "BillPaymentAdapter#retrySweepingTransfer"

        val payload = RetrySweepingTransferTO(
            amount = amount,
            participantId = participantId?.value,
        )
        markers.andAppend("payload", payload)

        return try {
            val httpRequest = HttpRequest.POST(configuration.retrySweepingTransfer, payload).withDefaultHeaders(userId)
            val call = getHttpClient().exchange(httpRequest, Argument.STRING, Argument.STRING)
            val response = call.firstOrError().blockingGet()

            logger.info(markers.andAppend("response", response), logName)

            response.body().right()
        } catch (ex: HttpClientResponseException) {
            val errorBody = ex.response.getBody(String::class.java)
            if (errorBody.isPresent) {
                markers.andAppend("responseBody", errorBody)
                logger.error(markers, logName, ex)

                val error = parseObjectFrom<PaymentAdapterErrorTO>(errorBody.get())

                when (error.code) {
                    "4003" -> SweepingTransferErrorReason.GenericReason(error.message.orEmpty())
                    "40031" -> SweepingLimitType.DAILY.toSweepingTransferErrorReason()
                    "40032" -> SweepingLimitType.WEEKLY.toSweepingTransferErrorReason()
                    "40033" -> SweepingLimitType.MONTHLY.toSweepingTransferErrorReason()
                    "40034" -> SweepingLimitType.YEARLY.toSweepingTransferErrorReason()
                    "40035" -> SweepingLimitType.GLOBAL.toSweepingTransferErrorReason()
                    "40036" -> SweepingLimitType.TRANSACTION.toSweepingTransferErrorReason()
                    "40037" -> SweepingLimitType.UNKNOWN.toSweepingTransferErrorReason()
                    else -> SweepingTransferErrorReason.GenericReason(ex.message.orEmpty())
                }
            } else {
                SweepingTransferErrorReason.GenericReason(ex.message.orEmpty())
            }.left()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            SweepingTransferErrorReason.GenericReason(ex.message.orEmpty()).left()
        }
    }

    private fun SweepingLimitType.toSweepingTransferError() = PaymentAdapterError.SweepingTransferError(this.toSweepingTransferErrorReason())
    private fun SweepingLimitType.toSweepingTransferErrorReason() = SweepingTransferErrorReason.LimitExceeded(this)

    @Retryable(includes = [ResponseClosedException::class, ReadTimeoutException::class])
    override fun getAssistantLimit(
        userId: UserId,
        walletId: WalletId,
    ): Either<PaymentAdapterError, AssistantPaymentLimit> {
        val markers = Markers.append("userId", userId.value).andAppend("walletId", walletId.value)
        val logName = "BillPaymentAdapter#getAssistantLimit"

        return try {
            val uri =
                UriBuilder
                    .of(configuration.getPixLimitPath)
                    .expand(mutableMapOf("walletId" to walletId.value))
                    .toString()
            val httpRequest =
                HttpRequest.GET<AssistantPaymentLimit>(uri).withDefaultHeaders(userId)
            val call = getHttpClient().exchange(httpRequest, Argument.of(AssistantPaymentLimit::class.java), Argument.STRING)
            val response = call.firstOrError().blockingGet()

            logger.info(markers.andAppend("response", response.body()), logName)

            response.body().right()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return resolveException(ex, markers, logName).left()
        }
    }

    override fun getUserActivities(userId: UserId, vararg activities: UserActivityType): Either<PaymentAdapterError, List<UserActivity>> {
        val markers = Markers.append("userId", userId.value).andAppend("activities", activities)
        val logName = "BillPaymentAdapter#getUserActivities"

        return try {
            val httpRequest = HttpRequest.POST(
                configuration.getSystemActivitiesPath,
                SystemActivityRequestTO(
                    systemActivityTypes = activities.map { it.name },
                ),
            ).withDefaultHeaders(userId)

            val call = getHttpClient().exchange(httpRequest, Argument.of(SystemActivityResponseTO::class.java), Argument.STRING)
            val response = call.firstOrError().blockingGet()

            logger.info(markers.andAppend("response", response.body()), logName)

            response.body().systemActivities.map { it.toUserActivity() }.right()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return PaymentAdapterError.ServerError(ex).left()
        }
    }

    override fun setUserActivity(userId: UserId, userActivity: UserActivity): Either<PaymentAdapterError, Unit> {
        val markers = Markers.append("userId", userId.value).andAppend("userActivity", userActivity)
        val logName = "BillPaymentAdapter#setUserActivity"

        return try {
            val httpRequest = HttpRequest.POST(
                configuration.setSystemActivityPath,
                SystemActivityTO(
                    systemActivityType = userActivity.type.name,
                    value = userActivity.value,
                ),
            ).withDefaultHeaders(userId)

            val call = getHttpClient().exchange(httpRequest, Argument.STRING, Argument.STRING)
            val response = call.firstOrError().blockingGet()

            logger.info(markers.andAppend("status", response.status()), logName)

            Unit.right()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return PaymentAdapterError.ServerError(ex).left()
        }
    }

    @Retryable(includes = [ResponseClosedException::class, ReadTimeoutException::class, HttpServerErrorException::class])
    override suspend fun getBalanceAndForecast(
        userId: UserId,
        walletId: WalletId?, // TODO remove this parameter
    ): Either<PaymentAdapterError, Balance> {
        val markers = Markers.append("userId", userId.value)
        val logName = "BillPaymentAdapter#getBalance"
        return try {
            val (response, elapsed) = measure {
                val httpRequest =
                    HttpRequest.GET<Unit>(configuration.balanceAndForecast).withDefaultHeaders(userId).headers { headers ->
                        if (walletId != null) {
                            headers.add("X-WALLET-ID", walletId.value)
                        }
                    }
                val call = getHttpClient().retrieve(httpRequest, WalletBalanceForecastTO::class.java)
                call.firstOrError().blockingGet()
            }

            logger.info(markers.andAppend("balanceAndForecast", response).andAppend("timeTaken", elapsed), logName)

            Balance(
                walletId = WalletId(response.walletId),
                current = response.amount,
                open =
                Forecast(
                    amountToday = response.open.amountToday,
                    amountInSevenDays = response.open.amountWeek,
                    amountInFifteenDays = response.open.amountFifteenDays,
                    amountMonth = response.open.amountMonth,
                    amountForNextMonth = response.open.amountNextMonth,
                ),
                onlyScheduled =
                Forecast(
                    amountToday = response.scheduled.amountToday,
                    amountInSevenDays = response.scheduled.amountWeek,
                    amountInFifteenDays = response.scheduled.amountFifteenDays,
                    amountMonth = response.scheduled.amountMonth,
                    amountForNextMonth = response.scheduled.amountNextMonth,
                ),
            ).right()
        } catch (ex: Exception) {
            return resolveException(ex, markers, logName).left()
        }
    }

    override fun getOpenFinanceBalances(userId: UserId, walletId: WalletId, participantIds: List<SweepingParticipantId>): Either<PaymentAdapterError, Map<SweepingParticipantId, Long?>> {
        val markers = Markers.append("userId", userId.value)
            .andAppend("walletId", walletId.value)
            .andAppend("participantIds", participantIds.map { it.value })
        val logName = "BillPaymentAdapter#getOpenFinanceBalances"
        return try {
            val (response, elapsed) = measure {
                val httpRequest =
                    HttpRequest.PUT(
                        configuration.openFinanceBalancesPath,
                        OFBalancesRequest(
                            walletId = walletId.value,
                            participantIds = participantIds.map { it.value },
                        ),
                    ).withDefaultHeaders(userId)
                val call = getHttpClient().retrieve(httpRequest, OFBalancesResponse::class.java)
                call.firstOrError().blockingGet()
            }

            logger.info(markers.andAppend("responseBody", response).andAppend("timeTaken", elapsed), logName)

            response.balances.associate {
                SweepingParticipantId(it.participantId) to it.amount
            }.right()
        } catch (ex: Exception) {
            return resolveException(ex, markers, logName).left()
        }
    }

    override fun validateAvailableLimit(userId: UserId, walletId: WalletId, amount: Long, type: AvailableLimitType): Either<PaymentAdapterError, AvailableLimitResponse> {
        val markers = Markers.append("userId", userId.value)
            .andAppend("walletId", walletId.value)
            .andAppend("amount", amount)
            .andAppend("type", type)
        val logName = "BillPaymentAdapter#validateAvailableLimit"
        return try {
            val (response, elapsed) = measure {
                val httpRequest =
                    HttpRequest.POST(
                        configuration.validateAvailableLimitPath,
                        AvailableLimitTO(
                            walletId = walletId.value,
                            amount = amount,
                            type = type,
                        ),
                    ).withDefaultHeaders(userId)
                val call = getHttpClient().retrieve(httpRequest, ResponseTO::class.java)
                call.firstOrError().blockingGet()
            }

            logger.info(markers.andAppend("responseBody", response).andAppend("timeTaken", elapsed), logName)

            when (response.code) {
                "0" -> AvailableLimitResponse.OK
                "4004" -> AvailableLimitResponse.ASSISTANT_LIMIT_EXCEEDED
                "4006" -> AvailableLimitResponse.PIX_LIMIT_EXCEEDED
                else -> throw IllegalArgumentException(response.code)
            }.right()
        } catch (ex: Exception) {
            return resolveException(ex, markers, logName).left()
        }
    }

    @Retryable(includes = [ResponseClosedException::class, ReadTimeoutException::class])
    override suspend fun findPendingBills(
        userId: UserId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): Either<PaymentAdapterError, UserAndWallet> {
        val markers = Markers.append("userId", userId.value)
        val logName = "BillPaymentAdapter#findPendingBills"
        return try {
            val url =
                UriBuilder
                    .of(configuration.listPendingBills)
                    .queryParam("startDate", startDate.format(dateFormat))
                    .queryParam("endDate", endDate.format(dateFormat))
                    .build()

            val (response, elapsed) = measure {
                val httpRequest = HttpRequest.GET<Unit>(url).withDefaultHeaders(userId)
                val call = getHttpClient().retrieve(httpRequest, AccountAndWalletsTO::class.java)
                call.firstOrError().blockingGet()
            }

            val firstWalletBills = response.wallets?.firstOrNull()
            logger.info(markers.andAppend("bills", firstWalletBills?.bills).andAppend("timeTaken", elapsed), logName)

            UserAndWallet(
                user = User(
                    accountId = AccountId(value = response.account.id),
                    id = userId,
                    name = response.account.fullName,
                    accountGroups = response.account.accountGroups,
                    status = response.account.status,
                ),
                wallet = firstWalletBills?.let { wallet ->
                    val (activeBills, waitingApprovalBills) = wallet.bills.partition { BillStatus.fromString(it.status) != BillStatus.WAITING_APPROVAL }
                    WalletWithBills(
                        bills = activeBills.toBillViews(),
                        totalWaitingApproval = waitingApprovalBills.size,
                        walletId = WalletId(wallet.walletId),
                        walletName = wallet.walletName,
                        activeConsents = wallet.activeConsents.map { it.toSweepingConsent() },
                    )
                } ?: WalletWithBills(
                    bills = emptyList(),
                    totalWaitingApproval = 0,
                    walletId = null,
                    walletName = null,
                ),
            ).right()
        } catch (ex: Exception) {
            return resolveException(ex, markers, logName).left()
        }
    }

    override fun getSubscriptionFee(userId: UserId): Either<PaymentAdapterError, Long> {
        val markers = Markers.append("userId", userId.value)
        val logName = "BillPaymentAdapter#getSubscriptionFee"
        return try {
            val httpRequest = HttpRequest.GET<Unit>(configuration.subscriptionFeePath).withDefaultHeaders(userId)
            val call = getHttpClient().retrieve(httpRequest, SubscriptionFeeTO::class.java)
            val response = call.firstOrError().blockingGet()

            logger.info(markers.andAppend("fee", response.fee), logName)
            response.fee.right()
        } catch (ex: Exception) {
            return resolveException(ex, markers, logName).left()
        }
    }

    override fun getActiveSweepingConsents(userId: UserId, walletId: WalletId?): Either<PaymentAdapterError, List<SweepingConsent>> {
        val markers = Markers.append("userId", userId.value)
        val logName = "BillPaymentAdapter#getActiveSweepingConsents"
        return try {
            val url = if (walletId != null && walletId.value.isNotEmpty()) {
                "${configuration.getActiveSweepingConsentsPath}?walletId=${walletId.value}"
            } else {
                configuration.getActiveSweepingConsentsPath
            }
            markers.andAppend("url", url)

            val (response, elapsed) = measure {
                val httpRequest = HttpRequest.GET<Unit>(url).withDefaultHeaders(userId)
                val call = getHttpClient().retrieve(httpRequest, CheckSweepingConsentTO::class.java)
                call.firstOrError().blockingGet()
            }

            logger.info(markers.andAppend("response", response).andAppend("timeTaken", elapsed), logName)
            response.activeConsents.map {
                it.toSweepingConsent()
            }.right()
        } catch (ex: Exception) {
            return resolveException(ex, markers, logName).left()
        }
    }

    private fun SweepingConsentTO.toSweepingConsent() = SweepingConsent(
        participant = SweepingParticipant(
            id = SweepingParticipantId(participantId),
            name = participantName,
            shortName = participantShortName,
        ),
        transactionLimit = transactionLimit,
        lastSuccessfulCashIn = lastSuccessfulCashIn?.let { LocalDateTime.parse(it, dateTimeFormat).atZone(brazilTimeZone) },
        periodicUsage = periodicUsage.toDomain(),
    )

    override fun validatePixQrCode(qrCode: String, amount: Long, userId: UserId): Either<PaymentAdapterError, PixValidationResult> {
        val markers = Markers.append("qrCode", qrCode).andAppend("userId", userId.value)
        val logName = "BillPaymentAdapter#validatePixQrCode"
        try {
            val httpRequest = HttpRequest.POST(
                configuration.validatePixQrCodePath,
                PixQRCodeValidateTO(
                    qrCodeValue = qrCode,
                    amount = amount,
                ),
            ).withDefaultHeaders(userId)
            val call = getHttpClient().retrieve(httpRequest, PixQrCodeDetailsResultTO::class.java)
            return call.firstOrError().blockingGet().toPixValidationResult().right()
        } catch (ex: Exception) {
            return resolveException(ex, markers, logName).left()
        }
    }

    override fun getSubscription(userId: UserId): Either<PaymentAdapterError, Subscription?> {
        val markers = Markers.append("userId", userId.value)
        val logName = "BillPaymentAdapter#getSubscription"
        return try {
            val httpRequest = HttpRequest.GET<Unit>(configuration.subscriptionPath).withDefaultHeaders(userId)
            val call = getHttpClient().retrieve(httpRequest, SubscriptionResponseTO::class.java)
            val response = call.firstOrError().blockingGet()

            // TODO: ler tipo da assinatura da resposta do bill-payment
            val subscription = response.subscription?.let { Subscription(fee = it.fee, paymentStatus = SubscriptionPaymentStatus.valueOf(it.paymentStatus), dueDate = LocalDate.parse(it.dueDate), type = it.type) }

            logger.info(markers.andAppend("subscription", subscription), logName)
            subscription.right()
        } catch (ex: Exception) {
            return resolveException(ex, markers, logName).left()
        }
    }

    @Retryable(includes = [ResponseClosedException::class, ReadTimeoutException::class])
    override fun createManualEntry(
        userId: UserId,
        title: String,
        description: String,
        amount: Long,
        type: ManualEntryType,
        dueDate: LocalDate,
    ): Either<PaymentAdapterError, ManualEntryId> {
        val markers = Markers.append("userId", userId.value)
            .andAppend("manualEntryTitle", title)
            .andAppend("amount", amount)
            .andAppend("type", type)
            .andAppend("dueDate", dueDate)
        val logName = "BillPaymentAdapter#createManualEntry"

        val payload = ManualEntryTO(
            title = title,
            description = description,
            amount = amount,
            type = type,
            dueDate = dueDate.format(dateFormat),
        )

        markers.andAppend("payload", payload)

        return try {
            val httpRequest = HttpRequest.POST(configuration.createManualEntryPath, payload).withDefaultHeaders(userId)
            val call = getHttpClient().exchange(httpRequest, Argument.of(CreateManualEntryResponseTO::class.java), Argument.STRING)
            val response = call.firstOrError().blockingGet()

            if (response.status != HttpStatus.CREATED) {
                throw IllegalStateException("Unexpected status code: ${response.status}")
            }

            val manualEntryId = ManualEntryId(response.body().manualEntryId)

            logger.info(markers.andAppend("response", response).andAppend("manualEntryId", manualEntryId.value), logName)

            manualEntryId.right()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return resolveException(ex, markers, logName).left()
        }
    }

    @Retryable(includes = [ResponseClosedException::class, ReadTimeoutException::class])
    override fun removeManualEntry(
        userId: UserId,
        manualEntryId: ManualEntryId,
    ): Either<PaymentAdapterError, Unit> {
        val markers = Markers.append("userId", userId.value).andAppend("manualEntryId", manualEntryId.value)
        val logName = "BillPaymentAdapter#removeManualEntry"

        return try {
            val httpRequest = HttpRequest.DELETE<Unit>(configuration.removeManualEntryPath.replace("{manualEntryId}", manualEntryId.value)).withDefaultHeaders(userId)
            val call = getHttpClient().exchange(httpRequest)
            val response = call.firstOrError().blockingGet()

            if (response.status != HttpStatus.OK) {
                throw IllegalStateException("Unexpected status code: ${response.status}")
            }

            logger.info(markers, logName)

            Unit.right()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return resolveException(ex, markers, logName).left()
        }
    }

    private fun <T> MutableHttpRequest<T>.withDefaultHeaders(userId: UserId): MutableHttpRequest<T> =
        this
            .basicAuth(userId.value, tenantService.getConfiguration().integrations.billPayment.secret)
            .header(HttpHeaderNames.USER_AGENT, "HTTP Client")
            .contentType(APPLICATION_JSON_TYPE)
            .accept(APPLICATION_JSON_TYPE)

    private fun resolveException(
        ex: Exception,
        markers: LogstashMarker,
        logName: String,
    ): PaymentAdapterError {
        logger.error(markers, logName, ex)

        if (ex is HttpClientResponseException) {
            val errorBody = ex.response.getBody(String::class.java)
            if (errorBody.isPresent) {
                markers.andAppend("responseBody", errorBody)
            }

            if (ex.status.code == 400) {
                if (errorBody.isPresent) {
                    val errorBodyParsed =
                        try {
                            parseObjectFrom<PaymentAdapterErrorTO>(errorBody.get())
                        } catch (e: Exception) {
                            logger.error(markers.andAppend("statusCode", ex.status.code).andAppend("errorBody", errorBody.get()), "$logName/failedToDecodeErrorBody")
                            throw e
                        }

                    return errorBodyParsed.toPaymentAdapterError()
                }
            }

            if (ex.status.code == 401) {
                return PaymentAdapterError.UserNotFound
            }
        }

        return PaymentAdapterError.ServerError(ex)
    }

    fun sendUserEvent(accountId: AccountId, event: UserEvent, metadata: Map<String, String> = emptyMap()) {
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("event", event.eventName)
            .andAppend("metadata", metadata)
        val logName = "BillPaymentAdapter#sendUserEvent"

        try {
            val userEventMessage = UserEventMessage(
                entityId = accountId.value,
                event = event.eventName,
                timestamp = getZonedDateTime().format(dateTimeFormat.withZone(brazilTimeZone)),
                metadata = metadata,
            )

            val internalAuth = tenantService.getConfiguration().internalAuth
            val httpRequest = HttpRequest.POST("/user-events/publish", userEventMessage)
                .basicAuth(internalAuth.identity, internalAuth.secret) // FIXME não tenho certeza se deve ser o internal auth

            val call = getHttpClient().exchange(httpRequest, Argument.of(Unit::class.java))
            call.firstOrError().blockingGet()

            logger.info(markers, logName)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
        }
    }

    private fun getHttpClient(): RxHttpClient = clients.getHttpClient(tenantService.getTenantName())

    companion object {
        private val logger = LoggerFactory.getLogger(BillPaymentAdapter::class.java)
    }
}

private fun PaymentAdapterErrorTO.toPaymentAdapterError(): PaymentAdapterError =
    when (code) {
        "4001" -> PaymentAdapterError.NotFoundError
        "4002" -> PaymentAdapterError.SufficientBalanceError
        "4005" -> PaymentAdapterError.UnknownFinancialInstitution
        "40031" -> PaymentAdapterError.AtLeastOneBillRequired
        "40032" -> PaymentAdapterError.SufficientBalanceError
        "40033" -> PaymentAdapterError.OnlyActiveOrScheduledBillsAreAllowed
        "40034" -> PaymentAdapterError.SingleWalletRequired
        "40035" -> PaymentAdapterError.PixQrCodeValidationError
        "40036" -> PaymentAdapterError.PixQrCodeInvalid
        "40037" -> PaymentAdapterError.BillNotActiveError
        "40039" -> PaymentAdapterError.PixQrCodeAmountNotFound
        else -> {
            PaymentAdapterError.ServerError(IllegalStateException("Unknown code: $code"))
        }
    }

fun BillTO.toBillView(externalBillId: Int): BillView =
    BillView(
        billId = BillId(id),
        externalBillId = externalBillId,
        assignor = assignor,
        recipient = billRecipient?.name?.let { Recipient(billRecipient.name) },
        billDescription = description,
        amount = amount,
        discount = discount,
        interest = interest,
        fine = fine,
        amountTotal = amountTotal,
        billType = billType,
        scheduledInfo =
        when {
            schedule == null -> ScheduledInfo.NOT_SCHEDULED
            schedule.waitingFunds -> ScheduledInfo.WAITING_FUNDS
            else -> ScheduledInfo.SCHEDULED
        },
        paymentLimitTime = paymentLimitTime,
        dueDate = LocalDate.parse(dueDate, DateTimeFormatter.ISO_LOCAL_DATE),
        subscriptionFee = subscriptionFee,
        status = BillStatus.fromString(status),
    )

fun List<BillTO>.toBillViews(): List<BillView> =
    this.sortedWith(compareBy<BillTO> { it.dueDate }.thenBy { it.id }).mapIndexed { index, bill -> bill.toBillView(index + 1) }

fun BillView.toBillTO(): BillTO =
    BillTO(
        id = billId.value,
        assignor = assignor,
        billRecipient = recipient?.let { ResponseRecipientTO(name = it.name) },
        description = billDescription,
        amount = amount,
        discount = discount,
        interest = interest,
        fine = fine,
        amountTotal = amountTotal,
        billType = billType,
        paymentLimitTime = paymentLimitTime,
        dueDate = dueDate.format(DateTimeFormatter.ISO_LOCAL_DATE),
        schedule =
        when (scheduledInfo) {
            ScheduledInfo.NOT_SCHEDULED -> null
            ScheduledInfo.SCHEDULED -> BillScheduleTO(date = dueDate.format(DateTimeFormatter.ISO_LOCAL_DATE), waitingFunds = false, waitingRetry = false)
            ScheduledInfo.WAITING_FUNDS -> BillScheduleTO(date = dueDate.format(DateTimeFormatter.ISO_LOCAL_DATE), waitingFunds = true, waitingRetry = false)
        },
        subscriptionFee = subscriptionFee,
        status = status.name,
    )

@JsonIgnoreProperties(ignoreUnknown = true)
data class BillTO(
    val id: String,
    val assignor: String? = null,
    val billRecipient: ResponseRecipientTO?,
    val description: String,
    val amount: Long,
    val discount: Long,
    val interest: Long = 0,
    val fine: Long = 0,
    val amountTotal: Long,
    val billType: BillType,
    val paymentLimitTime: String,
    val schedule: BillScheduleTO? = null,
    val dueDate: String,
    val subscriptionFee: Boolean,
    val status: String?,
    /*val createdOn: String,
    val recipient: ResponseRecipientTO?,
    val barcode: String? = null,
    val paidDate: String? = null,
    var overdue: Boolean? = null,
    val source: BillSourceTO,
    val payer: PayerTO? = null,
    val warningCode: String,
    val amountCalculationModel: String? = null,
    val recurrence: RecurrenceResponseTO? = null,
    val externalId: ExternalBillId? = null,
    val effectiveDueDate: String,
    val fichaCompensacaoType: FichaCompensacaoType? = null,
    val amountPaid: Long? = null,
    val possibleDuplicateBills: List<PossibleDuplicateBillTO>,
    val markedAsPaidBy: String? = null,
    val trackingOutdated: Boolean = false,
    val securityValidationResult: String? = null,
    val availablePaymentMethods: List<PaymentMethodType>,
    val transactionCorrelationId: String? = null,
    val batchSchedulingId: String? = null,
    val internalSettlement: Boolean = true,
    val paymentDetails: PaymentDetailsTO? = null,*/
) {
    fun checkWaitingApproval(): Boolean {
        return BillStatus.fromString(status) == BillStatus.WAITING_APPROVAL
    }
}

enum class BillStatus {
    WAITING_APPROVAL,
    ACTIVE,
    ALREADY_PAID,

    IGNORED,
    PROCESSING,
    PAID,
    NOT_PAYABLE,
    MOVED,
    PAID_ON_PARTNER,
    WAITING_BENEFICIARY_UPDATE,
    ;

    companion object {
        fun fromString(value: String?): BillStatus {
            if (value?.uppercase() == "PENDING") {
                return WAITING_APPROVAL
            }
            return value?.let {
                valueOf(it.uppercase())
            } ?: ACTIVE
        }
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class BillScheduleTO(
    val date: String,
    val waitingFunds: Boolean,
    val waitingRetry: Boolean,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ResponseRecipientTO(
    val name: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class AccountAndWalletsTO(
    val account: AccountTO,
    val wallets: List<WalletBillsComingDueToNotifyTO>?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SubscriptionFeeTO(
    val fee: Long,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SubscriptionResponseTO(
    val subscription: SubscriptionTO? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CreateManualEntryResponseTO(
    val manualEntryId: String,
)

data class SubscriptionTO(
    val fee: Long,
    val paymentStatus: String,
    val dueDate: String,
    val type: SubscriptionType,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class WalletBillsComingDueToNotifyTO(
    val walletName: String?,
    val walletId: String,
    val bills: List<BillTO>,
    val activeConsents: List<SweepingConsentTO>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ListBillsTO(
    val bills: List<BillTO>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class AccountTO(
    val id: String,
    val status: AccountStatus = AccountStatus.ACTIVE,
    val paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
    val subscriptionType: SubscriptionType = SubscriptionType.PIX,
    val fullName: String,
    val msisdn: String,
    val accountGroups: List<String>,
)

data class PixQRCodeResponseTO(
    val qrCode: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PaymentAdapterErrorTO(
    val code: String,
    val message: String? = null,
)

data class OnePixPayTO(
    val bills: List<String>,
    val walletId: String,
)

data class PixQRCodeRequestTO(
    val amount: Long,
    val walletId: String,
    val message: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class WalletBalanceForecastTO(
    val amount: Long,
    val assistantTransferAmountAvailable: Long,
    val walletId: String = "",
    val open: BalanceForecastTO,
    val scheduled: BalanceForecastTO,
    val dates: BalanceForecastDatesTO,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BalanceForecastDatesTO(
    val today: String = "",
    val week: String = "",
    val fifteenDays: String = "",
    val thirtyDays: String = "",
    val month: String = "",
    val nextMonth: String = "",
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BillsAndWalletTO(
    val bills: List<String>,
    val walletId: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MarkReminderAsDoneTO(
    val reminderId: String,
)

data class CreatePixTO(
    @field:Valid val recipient: RequestPixRecipientTO,
    @field:Positive(message = "Amount must be natural number") val amount: Long,
    @field:Pattern(regexp = descriptionRegex, message = descriptionErrorMessage) val description: String,
    @field:Pattern(
        regexp = "^\\d{4}-\\d{2}-\\d{2}$",
        message = "Due date should be format yyyy-MM-dd",
    ) val dueDate: String = getZonedDateTime().format(dateFormat),
    val transactionId: String,
    val authorizationToken: String? = null,
    val sweepingRequest: SweepingRequestTO?,
    val retryTransaction: Boolean,
)

@Introspected
data class RequestPixRecipientTO(
    val id: String? = null,
    val name: String?,
    @field:Pattern(
        regexp = "^\\d{11}|\\d{14}$",
        message = "document must be 11 digits for CPF or 14 digits for CNPJ",
    ) val document: String?,
    @field:Pattern(regexp = "CPF|CNPJ", message = "Document type must be CPF or CNPJ") val documentType: String?,
    val pixKey: PixKeyRequestTO? = null,
    val qrCode: String? = null,
)

data class PixKeyRequestTO(
    val value: String,
    val type: PixKeyType,
)

data class ValidatePixTO(
    val walletId: String,
    val keyType: PixKeyType,
    val keyValue: String,
    val amount: Long,
)

data class PixKeyResponseTO(
    val code: String,
    val pixDetails: PixKeyDetailsTO,
) {
    fun toPixValidationResult(): PixValidationResult {
        return PixValidationResult(
            keyType = pixDetails.keyType,
            keyValue = pixDetails.keyValue,
            recipientInstitution = pixDetails.recipientInstitution,
            recipientDocument = pixDetails.recipientDocument,
            recipientName = pixDetails.recipientName,
            pixLimitStatus = when (code) {
                "0" -> PixLimitStatus.AVAILABLE
                "4004" -> PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT
                "4006" -> PixLimitStatus.EXCEEDS_DAILY_LIMIT
                else -> throw IllegalStateException("Invalid pix validation response code: $code")
            },
        )
    }
}

data class PixKeyDetailsTO(
    val keyType: PixKeyType,
    val keyValue: String,
    val recipientInstitution: String,
    val recipientDocument: String,
    val recipientName: String,
)

data class ContactsListTO(
    val contacts: List<ContactTO>,
)

data class CreateOnboardingTestPixTO(
    val key: OnboardingTestPixKeyTO?,
)

data class OnboardingTestPixKeyTO(
    val keyType: PixKeyType,
    val keyValue: String,
)

data class OnboardingTestPixResponseTO(
    val code: String,
    val message: String,
)

data class ContactTO(
    val id: String,
    val alias: String? = null,
    val name: String,
    val pixKeys: List<PixKey>,
    val lastUsed: LastUsedTO?,
)

data class LastUsedTO(
    val pixKey: SavedPixKeyTO? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SavedPixKeyTO(
    val value: String,
    val type: PixKeyType,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BalanceForecastTO(
    val amountToday: Long,
    val amountWeek: Long,
    val amountFifteenDays: Long,
    val amountMonth: Long,
    val amountNextMonth: Long,
)

@ConfigurationProperties("integrations.billPayment")
class BillPaymentAdapterConfiguration
@ConfigurationInject
constructor(
    val pixQRCodePath: String,
    val onePixPayPath: String,
    val listPendingBills: String,
    val balanceAndForecast: String,
    val markBillsAsPaid: String,
    val markReminderAsDone: String,
    val ignoreBills: String,
    val sendPix: String,
    val retrySweepingTransfer: String,
    val validatePix: String,
    val listContacts: String,
    val subscriptionFeePath: String,
    val subscriptionPath: String,
    val getPixLimitPath: String,
    val syncSendMessagePath: String,
    val scheduleBillsPath: String,
    val getActiveSweepingConsentsPath: String,
    val onboardingTestPixPath: String,
    val getSystemActivitiesPath: String,
    val setSystemActivityPath: String,
    val validatePixQrCodePath: String,
    val addBillPath: String,
    val checkBillsPath: String,
    val createManualEntryPath: String,
    val removeManualEntryPath: String,
    val openFinanceBalancesPath: String,
    val validateAvailableLimitPath: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CheckSweepingConsentTO(
    val activeConsents: List<SweepingConsentTO>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SweepingConsentTO(
    val participantId: String,
    val participantName: String,
    val participantShortName: String,
    val transactionLimit: Long,
    val lastSuccessfulCashIn: String? = null,
    val periodicUsage: SweepingConsentPeriodicUsageTO,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SweepingConsentPeriodicUsageTO(
    val consentId: String,
    val daily: SweepingConsentPeriodicLimitUsageTO,
    val weekly: SweepingConsentPeriodicLimitUsageTO,
    val monthly: SweepingConsentPeriodicLimitUsageTO,
    val yearly: SweepingConsentPeriodicLimitUsageTO,
    val totalLimit: Long,
    val totalUsed: Long,
) {
    fun toDomain() = SweepingConsentPeriodicUsage(
        daily = daily.toDomain(),
        weekly = weekly.toDomain(),
        monthly = monthly.toDomain(),
        yearly = yearly.toDomain(),
        totalLimit = totalLimit,
        totalUsed = totalUsed,
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class SweepingConsentPeriodicLimitUsageTO(
    val amountLimit: Long,
    val amountUsed: Long,
    val quantityLimit: Long,
    val quantityUsed: Long,
) {
    fun toDomain() = SweepingConsentPeriodicLimitUsage(
        amountLimit = amountLimit,
        amountUsed = amountUsed,
        quantityLimit = quantityLimit,
        quantityUsed = quantityUsed,
    )
}

data class ChatbotScheduleBillsTO(
    val walletId: String,
    val bills: List<String>,
    val authorizationToken: String? = null,
    val sweepingRequest: SweepingRequestTO?,
    val scheduleToDueDate: Boolean,
) {
    val amount: Long? = sweepingRequest?.amount // FIXME remover quando subir o bill payment
}

data class SweepingRequestTO(
    val transactionId: String,
    val amount: Long,
    val participantId: String?,
    val retry: Boolean,
)

data class SystemActivityRequestTO(
    val systemActivityTypes: List<String>,
)

data class SystemActivityResponseTO(
    val systemActivities: List<SystemActivityTO>,
)

data class SystemActivityTO(
    val systemActivityType: String,
    val value: Boolean,
) {
    fun toUserActivity(): UserActivity {
        return UserActivity(
            type = UserActivityType.valueOf(systemActivityType),
            value = value,
        )
    }
}

data class PixQRCodeValidateTO(
    val qrCodeValue: String,
    val amount: Long,
)

data class PixQrCodeDetailsResultTO(
    val pixKeyDetails: PixKeyQrCodeDetailsTO,
    val e2e: String,
    val qrCodeInfo: PixQrCodeDataTO? = null,
    val code: String,
    val amount: Long,
) {
    fun toPixValidationResult(): PixValidationResult {
        return PixValidationResult(
            keyType = pixKeyDetails.key.type,
            keyValue = pixKeyDetails.key.value,
            recipientInstitution = pixKeyDetails.holder.institutionName,
            recipientDocument = pixKeyDetails.owner.document,
            recipientName = pixKeyDetails.owner.name,
            e2e = e2e,
            amount = amount,
            pixLimitStatus = when (code) {
                "0" -> PixLimitStatus.AVAILABLE
                "4004" -> PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT
                "4006" -> PixLimitStatus.EXCEEDS_DAILY_LIMIT
                else -> throw IllegalStateException("Invalid pix validation response code: $code")
            },
        )
    }
}

data class PixKeyHolderTO(
    val accountNo: BigInteger,
    val accountDv: String,
    val ispb: String,
    val institutionName: String,
    val accountType: AccountType,
    val routingNo: Long,
)

data class PixQrCodeDataTO(
    val type: PixQrCodeType,
    val info: String,
    val additionalInfo: Map<String, String>?,
    val pixId: String?,
    val fixedAmount: Long?,
    val expiration: ZonedDateTime?,
    val originalAmount: Long?,
)

enum class AccountType {
    CHECKING, SAVINGS, SALARY, PAYMENT
}

enum class PixQrCodeType { DYNAMIC, STATIC }

data class PixKeyOwnerTO(
    val name: String,
    val document: String,
)

data class PixKeyQrCodeDetailsTO(
    val key: PixKey,
    val holder: PixKeyHolderTO,
    val owner: PixKeyOwnerTO,
)

private class HttpServerErrorException : HttpClientResponseException("Unknown HTTP Server Error", HttpResponse.serverError<Unit>())

private fun SweepingRequest.toSweepingRequestTO() = SweepingRequestTO(
    transactionId = transactionId.value,
    amount = amount,
    participantId = participantId?.value,
    retry = retry,
)

data class RetrySweepingTransferTO(
    val amount: Long,
    val participantId: String?,
)

data class CreateBillTO(
    val digitableLine: String,
    val dueDate: String?,
    val description: String = "",
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CreateBillResultTO(
    val billId: String,
    val amount: Long,
    val dueDate: String,
    val assignor: String,
    val billType: BillType,
)

private fun CreateBillResultTO.toBillValidationResult() = CreateBillResult(
    dueDate = LocalDate.parse(dueDate, dateFormat),
    amount = amount,
    assignor = assignor,
    billType = billType,
    billId = BillId(billId),
)

data class CheckBillsTO(
    val bills: List<String>,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class ManualEntryTO(
    val title: String,
    val description: String,
    val amount: Long,
    val type: ManualEntryType,
    val dueDate: String,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class OFBalancesRequest(
    val walletId: String,
    val participantIds: List<String>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFBalancesResponse(
    val balances: List<OFBalanceTO>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class OFBalanceTO(
    val participantId: String,
    val amount: Long?,
)

data class AvailableLimitTO(
    val walletId: String,
    val amount: Long,
    val type: AvailableLimitType,
)