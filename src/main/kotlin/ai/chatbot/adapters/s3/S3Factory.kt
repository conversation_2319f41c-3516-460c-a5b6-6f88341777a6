package ai.chatbot.adapters.s3

import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client

@Factory
class S3Factory(@Property(name = "aws.region") private val awsRegion: String) {

    @Singleton
    @Primary
    fun getS3Factory() = buildS3Client(Region.of(awsRegion))

    private fun buildS3Client(region: Region): S3Client {
        return S3Client.builder()
            .region(region)
            .build()
    }
}