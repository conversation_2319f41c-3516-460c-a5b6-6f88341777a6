package ai.chatbot.adapters

import ai.chatbot.app.utils.andAppend
import io.micronaut.context.annotation.Requires
import io.micronaut.core.async.publisher.Publishers
import io.micronaut.core.util.StringUtils
import io.micronaut.http.HttpRequest
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Filter
import io.micronaut.http.client.ProxyHttpClient
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import io.micronaut.http.filter.ServerFilterPhase
import io.micronaut.http.uri.UriBuilder
import kotlin.jvm.optionals.getOrNull
import net.logstash.logback.marker.Markers
import org.reactivestreams.Publisher
import org.slf4j.LoggerFactory

private const val PREFIX = "/chatbot/proxy"

@Requires(property = "app.proxy.enabled", value = "true")
@Filter("$PREFIX/**")
class ProxyFilter(
    private val client: ProxyHttpClient,
) : HttpServerFilter {

    private val logger = LoggerFactory.getLogger(ProxyFilter::class.java)

    override fun doFilter(
        request: HttpRequest<*>,
        chain: ServerFilterChain,
    ): Publisher<MutableHttpResponse<*>> {
        val marker = Markers.append("authorization", request.userPrincipal.getOrNull()?.name)

        return Publishers.map(
            client.proxy(
                request.mutate()
                    .uri { b: UriBuilder ->
                        b.apply {
                            scheme(request.headers.get("x-proxy-scheme") ?: "http")
                            host(
                                request.headers.get("x-proxy-host")
                                    ?: throw IllegalArgumentException("x-proxy-host is required"),
                            )
                            replacePath(
                                StringUtils.prependUri(
                                    "/",
                                    request.path.substring(PREFIX.length),
                                ),
                            )
                        }
                    }.headers {
                        it.remove("authorization")
                        request.headers.get("x-proxy-authorization")?.let { auth ->
                            it.add("authorization", auth)
                        }
                    },
            ),
        ) { response: MutableHttpResponse<*> ->
            logger.info(marker.andAppend("status", response.status), "ProxyFilter")
            response
        }
    }

    override fun getOrder() = ServerFilterPhase.FIRST.order()
}