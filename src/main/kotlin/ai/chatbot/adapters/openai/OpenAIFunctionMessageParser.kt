package ai.chatbot.adapters.openai

import ai.chatbot.app.ManualEntryType
import ai.chatbot.app.conversation.Action
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ChatMessageWrapper
import ai.chatbot.app.conversation.CompletionMessage
import ai.chatbot.app.conversation.HistoryState
import ai.chatbot.app.conversation.MarkBillsAsPaidOrIgnoreBillsAction
import ai.chatbot.app.conversation.OnboardingSinglePixAction
import ai.chatbot.app.conversation.Subscription
import ai.chatbot.app.conversation.SubscriptionPaymentStatus
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.media.BoletoInfo
import ai.chatbot.app.prompt.Prompts
import ai.chatbot.app.prompt.Prompts.ENTENDIMENTO
import ai.chatbot.app.prompt.Prompts.MAKE_PAYMENT
import ai.chatbot.app.prompt.Prompts.MANUAL_ENTRY
import ai.chatbot.app.prompt.Prompts.MARK_BILLS_AS_PAID_OR_IGNORE_BILLS
import ai.chatbot.app.prompt.Prompts.NOOP
import ai.chatbot.app.prompt.Prompts.ONBOARDING_SINGLE_PIX
import ai.chatbot.app.prompt.Prompts.PIX_TRANSACTION
import ai.chatbot.app.prompt.Prompts.REFRESH_BALANCE_AND_FORECASTS
import ai.chatbot.app.prompt.Prompts.REMINDER
import ai.chatbot.app.prompt.Prompts.SAVE_NOT_SUPPORTED_FEATURE_YET
import ai.chatbot.app.prompt.Prompts.SEARCH_CONTACTS
import ai.chatbot.app.prompt.Prompts.SEND_MESSAGE
import ai.chatbot.app.prompt.Prompts.SEND_PENDING_BILLS_PERIOD
import ai.chatbot.app.prompt.Prompts.VALIDATE_BOLETO
import ai.chatbot.app.prompt.Prompts.VERIFICACAO_DE_INTEGRIDADE
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.getObjectMapper
import ai.chatbot.app.utils.parseObjectFrom
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonPropertyDescription
import com.theokanning.openai.completion.chat.ChatFunction
import com.theokanning.openai.completion.chat.ChatMessage
import com.theokanning.openai.completion.chat.ChatMessageRole
import com.theokanning.openai.service.FunctionExecutor
import jakarta.inject.Singleton
import java.time.Duration
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Singleton
class OpenAIFunctionMessageParser {
    private val functionExecutor = buildFunctionExecutor()

    fun listFunctions(): List<ChatFunction> = functionExecutor.functions

    fun parseChatMessage(chatMessage: ChatMessage): CompletionMessage =
        if (chatMessage.functionCall != null) {
            val functionCallResponse = functionExecutor.executeAndConvertToMessageHandlingExceptions(chatMessage.functionCall)

            val completionMessage = try {
                parseObjectFrom<CompletionMessage>(functionCallResponse.content)
            } catch (e: Exception) {
                logger.error(Markers.append("functionCallresponse", functionCallResponse.content), "OpenAIFunctionMessageParser#parseChatMessage", e)
                throw e
            }

            completionMessage
        } else if (chatMessage.content != null) {
            CompletionMessage(
                verificacaoDeIntegridade = "",
                entendimento = "",
                acoes =
                listOf(
                    Action.SendMessage(decodeUnicode(chatMessage.content)),
                ),
            )
        } else {
            throw IllegalStateException("functionCall e content não foram encontrados")
        }

    fun processFunction(functionMessagePayload: FunctionMessagePayload): CompletionMessage {
        val actions = listOfNotNull(
            functionMessagePayload.sendResponse?.let { message -> Action.SendMessage(decodeUnicode(message)) },
            functionMessagePayload.saveNotSupportedFeatureYet?.let { message -> Action.NotSupportedFeature(content = listOf(message)) },
            functionMessagePayload.refreshBalanceAndForecasts?.let { Action.RefreshBalance },
            functionMessagePayload.sendPendingBillsPeriod?.let { Action.SendPendingBillsPeriod(it.startDate, it.endDate, it.amount) },
            functionMessagePayload.pixTransaction?.let { it.amount?.let { amount -> Action.PixTransaction(amount.toActionAmount(), key = it.key, type = it.type, ignoreLastUsed = it.ignoreLastUsed) } },
            functionMessagePayload.markBillsAsPaidOrIgnoreBills?.let { Action.MarkAsPaidOrIgnore(MarkBillsAsPaidOrIgnoreBillsAction(billsToMarkAsPaid = it.billsToMarkAsPaid, billsToIgnoreOrRemove = it.billsToIgnoreOrRemove)) },
            functionMessagePayload.noop?.let { Action.Noop },
            functionMessagePayload.makePayment?.let { Action.MakePayment(it.bills) },
            functionMessagePayload.onboardingSinglePix?.let { Action.OnboardingSinglePix(OnboardingSinglePixAction(action = it.action, key = it.key, type = it.type)) },
            functionMessagePayload.validateBoleto?.let { Action.ValidateBoleto(barcodes = it.boletos.map { boleto -> BoletoInfo(boleto.barcode, boleto.dueDate) }) },
            functionMessagePayload.createManualEntry?.let { Action.CreateManualEntry(type = ManualEntryType.EXTERNAL_PAYMENT, dueDate = getLocalDate().format(dateFormat), title = it.title, amount = it.amount.toActionAmount()) },
            functionMessagePayload.createReminder?.let { Action.CreateManualEntry(type = ManualEntryType.REMINDER, dueDate = it.date, title = it.title, amount = it.amount.toActionAmount()) },
//                functionMessagePayload.getContext?.let { Action.GetContext(AdditionalContext.valueOf(it.context), it.subject) },
        )

        // Quantas actions que bloqueiam sendResponse
        val actionCount = actions.count { it.name.blockMessge }

        // Filtrar action de sendResponse se houver alguma action "real"
        val filteredActions = if (actionCount == 0) {
            actions
        } else {
            actions.filter { it.name != ActionType.MSG }
        }

        return CompletionMessage(
            verificacaoDeIntegridade = functionMessagePayload.verificacaoDeIntegridade ?: "",
            entendimento = functionMessagePayload.entendimento ?: "",
            acoes = filteredActions,
        )
    }

    fun buildChatMessageList(
        prompt: String,
        historyState: HistoryState? = null,
        messages: List<ChatMessageWrapper>,
        systemMessageToCompletion: String?,
    ): List<ChatMessage> {
        val messagesList = if (historyState == null) {
            listOf(ChatMessage(ChatMessageRole.SYSTEM.value(), prompt)) + messages.toChatMessage()
        } else {
            listOf(ChatMessage(ChatMessageRole.SYSTEM.value(), prompt)) + historyState.toChatMessageBeforeChat() + messages.toChatMessage() + historyState.toChatMessageAfterChat()
        }

        return systemMessageToCompletion?.let {
            messagesList + ChatMessage(ChatMessageRole.SYSTEM.value(), it)
        } ?: messagesList
    }

    private fun buildFunctionExecutor(): FunctionExecutor {
        val multiFunction =
            ChatFunction
                .builder()
                .name(Prompts.MULTI_FUNCTION_NAME)
                .description(
                    Prompts.MULTI_FUNCTION_DESCRIPTION,
                ).executor(FunctionMessagePayload::class.java) { messagePayload ->
                    processFunction(messagePayload)
                }.build()
        return FunctionExecutor(listOf(multiFunction))
    }

    private fun decodeUnicode(input: String): String {
        val regex = Regex("&#(\\d+);|\\\\u(\\p{XDigit}{4})")

        return regex.replace(input) { matchResult ->
            when {
                matchResult.groupValues[1].isNotEmpty() -> {
                    val charCode = matchResult.groupValues[1].toInt()
                    charCode.toChar().toString()
                }
                matchResult.groupValues[2].isNotEmpty() -> {
                    val unicodeHex = matchResult.groupValues[2]
                    val unicodeInt = unicodeHex.toInt(16)
                    unicodeInt.toChar().toString()
                }
                else -> ""
            }
        }
    }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(OpenAIFunctionMessageParser::class.java)
    }
}

class SearchContactsFunctionMessagePayload {
    @JsonPropertyDescription(VERIFICACAO_DE_INTEGRIDADE)
    var verificacaoDeIntegridade: String? = null

    @JsonPropertyDescription(ENTENDIMENTO)
    var entendimento: String? = null

    @JsonPropertyDescription(SEARCH_CONTACTS)
    var searchContacts: OpenAIAdapter.SearchContactsCompletionResult? = null
}

data class SearchContactsResult(
    @JsonPropertyDescription("[id] do contato encontrado. Caso não encontre, ou encontre mais de um contato, deve ser nulo.")
    val contactId: String?,
    @JsonPropertyDescription("Mensagem a ser enviada ao usuário caso não encontre o contato, ou encontre mais de um contato.")
    val message: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
class FunctionMessagePayload {
    @JsonPropertyDescription(VERIFICACAO_DE_INTEGRIDADE)
    var verificacaoDeIntegridade: String? = null

    @JsonPropertyDescription(ENTENDIMENTO)
    var entendimento: String? = null

    @JsonPropertyDescription(SEND_MESSAGE)
    var sendResponse: String? = null

    @JsonPropertyDescription(REFRESH_BALANCE_AND_FORECASTS)
    var refreshBalanceAndForecasts: Boolean? = null

    @JsonPropertyDescription(SEND_PENDING_BILLS_PERIOD)
    var sendPendingBillsPeriod: SendPendingBillsPeriod? = null

    @JsonPropertyDescription(PIX_TRANSACTION)
    var pixTransaction: PixTransactionTO? = null

    @JsonPropertyDescription(MAKE_PAYMENT)
    var makePayment: MakePaymentTO? = null

    @JsonPropertyDescription(VALIDATE_BOLETO)
    var validateBoleto: ValidateBoletoTO? = null

    @JsonPropertyDescription(SAVE_NOT_SUPPORTED_FEATURE_YET)
    var saveNotSupportedFeatureYet: String? = null

    @JsonPropertyDescription(MARK_BILLS_AS_PAID_OR_IGNORE_BILLS)
    var markBillsAsPaidOrIgnoreBills: MarkBillsAsPaidOrIgnoreBills? = null

    @JsonPropertyDescription(NOOP)
    var noop: Boolean? = null

    @JsonPropertyDescription(ONBOARDING_SINGLE_PIX)
    var onboardingSinglePix: OnboardingSinglePix? = null

    @JsonPropertyDescription(MANUAL_ENTRY)
    var createManualEntry: CreateManualEntryTO? = null

    @JsonPropertyDescription(REMINDER)
    var createReminder: CreateReminderTO? = null

//    @JsonPropertyDescription(GET_CONTEXT)
//    var getContext: GetContextTO? = null
}

class PixTransactionTO {
    @JsonPropertyDescription(Prompts.AMOUNT)
    var amount: Double? = null

    @JsonPropertyDescription("Chave pix do destinatário")
    var key: String = ""

    @JsonPropertyDescription("Tipo da chave pix [ELEVEN_DIGIT, CNPJ, EMAIL, EVP, CPF, PHONE, CONTACT, COPY_PASTE]")
    var type: String = "CONTACT"

    @JsonPropertyDescription("[true] caso o usuário não queira utilizar a ultima chave pix utilizada para o contato, senão [false]")
    var ignoreLastUsed: Boolean = false
}

class MakePaymentTO {
    @JsonPropertyDescription("Lista de ids das contas a serem pagas")
    lateinit var bills: List<Int>
}

class CreateManualEntryTO {
    @JsonPropertyDescription("Título do lançamento manual")
    var title: String = ""

    @JsonPropertyDescription(Prompts.AMOUNT)
    var amount: Double = 0.0
}

class CreateReminderTO {
    @JsonPropertyDescription("Título do lembrete")
    var title: String = ""

    @JsonPropertyDescription("Data que o usuário será lembrado no formato yyyy-mm-dd")
    var date: String = ""

    @JsonPropertyDescription(Prompts.AMOUNT)
    var amount: Double = 0.0
}

class GetContextTO {
    @JsonPropertyDescription("Contexto adicional [SUBSCRIPTION]")
    lateinit var context: String

    @JsonPropertyDescription("Pergunta extraída do contexto adicional")
    lateinit var subject: String
}

class SendPendingBillsPeriod {
    @JsonPropertyDescription(Prompts.SendPendingBills.START_DATE)
    var startDate: String = getLocalDate().format(dateFormat)

    @JsonPropertyDescription(Prompts.SendPendingBills.END_DATE)
    var endDate: String = getLocalDate().format(dateFormat)

    var amount: Boolean = false
}

class BoletoTO {
    @JsonPropertyDescription("Linha digitável ou código de barras do boleto")
    lateinit var barcode: String

    @JsonPropertyDescription("Vencimento do boleto")
    var dueDate: String? = null
}

class ValidateBoletoTO {
    @JsonPropertyDescription("Lista de Boletos")
    lateinit var boletos: List<BoletoTO>
}

class MarkBillsAsPaidOrIgnoreBills {
    @JsonPropertyDescription("Lista de ids das contas a serem marcadas como pagas")
    var billsToMarkAsPaid: List<Int> = emptyList()

    @JsonPropertyDescription("Lista de ids das contas a serem ignoradas ou removidas")
    var billsToIgnoreOrRemove: List<Int> = emptyList()
}

class OnboardingSinglePix {
    @JsonPropertyDescription("Nome da próxima ação do fluxo de boas vindas single pix [ACCEPT, CONFIRM, PIX, SKIP]")
    var action: String = ""

    @JsonPropertyDescription("Chave pix do destinatário. Apenas se a [action] for [PIX}")
    var key: String? = null

    @JsonPropertyDescription("Tipo da chave pix [ELEVEN_DIGIT, CNPJ, EMAIL, EVP, CPF, PHONE]. Apenas se a [action] for [PIX]")
    var type: String? = null
}

private class UserStateBeforeChatTO

private class UserStateAfterChatTO {
    var pendingBillsState: Any? = null
    var startDate: String? = null
    var endDate: String? = null
    var userName: String? = null
    var userStatus: String? = null
    var minutesSinceLastBillsUpdate: Long? = null
    var subscriptionState: SubscriptionTO? = null
    var sweepingAccount: Boolean = false
}

private fun HistoryState.toChatMessageBeforeChat(): ChatMessage {
    val userState = UserStateBeforeChatTO()
    when (this) {
        is BillComingDueHistoryState ->
            userState.apply {
            }
    }

    return ChatMessage(
        ChatMessageRole.SYSTEM.value(),
        getObjectMapper().writeValueAsString(userState),
    )
}

private data class BillTO(
    val id: Int,
    val dueDate: String,
    val informacaoDoAgendamento: String,
    val paymentLimitTime: String,
)

private fun HistoryState.toChatMessageAfterChat(): ChatMessage {
    val userState = UserStateAfterChatTO()
    when (this) {
        is BillComingDueHistoryState -> {
            userState.apply {
                userName = user.name
                userStatus = user.status.toChat()
                startDate = startDate?.format(dateFormat)
                endDate = endDate?.format(dateFormat)
                pendingBillsState = null
                minutesSinceLastBillsUpdate = Duration.between(lastBillsUpdatedAt, ZonedDateTime.now()).toMinutes()
                subscriptionState = subscription?.toSubscriptionTO()
                sweepingAccount = walletWithBills.hasSweepingAccount
            }
        }
    }

    return ChatMessage(
        ChatMessageRole.SYSTEM.value(),
        getObjectMapper().writeValueAsString(userState),
    )
}

private fun AccountStatus.toChat(): String =
    when (this) {
        AccountStatus.BLOCKED,
        AccountStatus.ACTIVE,
        -> "Ativo" // Não informar pro GPT que usuário está bloqueado, não deveria ter diferença no comportamento de BLOCKED e ACTIVE
        AccountStatus.UNDER_REVIEW,
        AccountStatus.UNDER_EXTERNAL_REVIEW,
        -> "Em revisão"
        AccountStatus.APPROVED -> "Aprovado"
        AccountStatus.REGISTER_INCOMPLETE -> "Cadastro incompleto"
        AccountStatus.NOT_REGISTERED -> "Não cadastrado"
        else -> throw IllegalStateException("Status não mapeado")
    }

private data class SubscriptionTO(
    val fee: Long?,
    val paymentStatus: String,
    val dueDate: String,
    val type: SubscriptionType,
)

private fun Subscription.toSubscriptionTO(): SubscriptionTO {
    val payment =
        when {
            isOverDue() -> "Assinatura está vencida e pendente de pagamento"
            paymentStatus == SubscriptionPaymentStatus.PAID && !needToBePaidToday() -> "Assinatura está em dia"
            needToBePaidToday() -> "Sua assinatura está em dia mas existe um pagamento pendente para hoje"
            else -> "Não consegui recuperar informações da assinatura"
        }
    return SubscriptionTO(fee = fee, paymentStatus = payment, dueDate = dueDate.format(dateFormat), type = type)
}

private fun List<ChatMessageWrapper>.toChatMessage(): List<ChatMessage> =
    mapNotNull {
        if (it.message != null) {
            ChatMessage(
                it.type.role.value(),
                it.message,
            )
        } else {
            null
        }
    }.takeLast(40)

private fun Double.toActionAmount(): Long = this.toBigDecimal().multiply(100.toBigDecimal()).toLong()