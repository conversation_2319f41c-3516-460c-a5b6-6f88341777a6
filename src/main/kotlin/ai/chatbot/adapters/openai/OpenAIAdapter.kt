package ai.chatbot.adapters.openai

import ai.chatbot.app.IOpenAIAdapter
import ai.chatbot.app.classification.ClassificationResult
import ai.chatbot.app.classification.ConversationAction
import ai.chatbot.app.classification.ConversationActionSource
import ai.chatbot.app.classification.ConversationActionStatus
import ai.chatbot.app.classification.ConversationTag
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ChatMessageWrapper
import ai.chatbot.app.conversation.CompletionMessage
import ai.chatbot.app.conversation.HistoryState
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.job.DailyLogErrorSummary
import ai.chatbot.app.prompt.Prompts
import ai.chatbot.app.prompt.dailyLogPrompt
import ai.chatbot.app.prompt.dailyLogSummaryPrompt
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.includeActionsInPrompt
import ai.chatbot.app.utils.includeConversationInPrompt
import ai.chatbot.app.utils.parseObjectFrom
import com.fasterxml.jackson.core.JsonParseException
import com.fasterxml.jackson.databind.exc.MismatchedInputException
import com.theokanning.openai.completion.chat.ChatCompletionRequest
import com.theokanning.openai.completion.chat.ChatCompletionResult
import com.theokanning.openai.completion.chat.ChatFunction
import com.theokanning.openai.completion.chat.ChatMessage
import com.theokanning.openai.service.OpenAiService
import io.micronaut.context.annotation.Property
import io.micronaut.retry.annotation.Retryable
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.net.SocketTimeoutException
import kotlin.time.ExperimentalTime
import kotlin.time.measureTimedValue
import net.logstash.logback.marker.Markers
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Singleton
open class OpenAIAdapter(
    @Property(name = "openai.model") private val defaultModel: String,
    @Property(name = "openai.betaModel") private val betaModel: String,
    private val aiService: OpenAiService,
    @Named("DailyLog") private val dailyLogAiService: OpenAiService,
    private val messageParser: OpenAIFunctionMessageParser,
) : IOpenAIAdapter {
    @OptIn(ExperimentalTime::class)
    @Retryable(includes = [SocketTimeoutException::class, MismatchedInputException::class], attempts = "3")
    override fun createChatCompletion(
        prompt: String,
        historyState: HistoryState,
        messages: List<ChatMessageWrapper>,
        systemMessageToCompletion: String?,
        retry: Boolean,
    ): CompletionMessage {
        val logName = "OpenAIAdapter#createChatCompletion"
        val markers = Markers.empty()
        val history = messageParser.buildChatMessageList(prompt, historyState, messages, systemMessageToCompletion)
        try {
            val (functions, functionCall) =
                if (systemMessageToCompletion == null) {
                    val functionCall = ChatCompletionRequest.ChatCompletionRequestFunctionCall(Prompts.MULTI_FUNCTION_NAME)
                    val functions = messageParser.listFunctions()
                    Pair(functions, functionCall)
                } else {
                    Pair(null, null)
                }

            val model = betaModel
            val (result, timeTaken) = measureTimedValue { generateCompletion(history = history, model = model, functions = functions, functionCall = functionCall) }
            val completion = result.choices.first().message
            markers.andAppend("completion", completion)
                .andAppend("usage", result.usage)
                .andAppend("totalMessages", history.size)
                .andAppend("lastMessage", messages.lastOrNull())
                .andAppend("systemMessageToCompletion", systemMessageToCompletion)
                .andAppend("timeTaken", timeTaken.inWholeMilliseconds)
                .andAppend("model", model)
                .andAppend("userId", historyState.getUserId().value)
                .andAppend("userGroups", getUserGroups(historyState))

            val completionMessage = messageParser.parseChatMessage(completion)

            markers.andAppend("completionMessage", completionMessage)
            logger.info(markers, logName)
            return completionMessage
        } catch (e: JsonParseException) {
            if (retry) {
                return createChatCompletion(prompt, historyState, messages, systemMessageToCompletion, false)
            } else {
                logger.error(markers, logName, e)
                throw e
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    override fun createLogCompletion(
        messages: List<ChatMessageWrapper>,
        retry: Boolean,
        state: BillComingDueHistoryState,
    ): LogCompletionResult {
        var rawJson = ""

        try {
            val history = messageParser.buildChatMessageList(includeConversationInPrompt(dailyLogPrompt, messages), historyState = null, emptyList(), systemMessageToCompletion = null)
            val completion = generateDailyLogCompletion(history = history, model = betaModel)

            // Às vezes o ChatGPT formata o json em markdown
            val json = completion.choices.first().message.content.replace("```json", "").replace("```", "")
            rawJson = json

            val classificationResult = processDailyLog(parseObjectFrom<ClassificationResult>(json), state)

            return LogCompletionResult(classificationResult = classificationResult, promptTokens = completion.usage.promptTokens, completionTokens = completion.usage.completionTokens)
        } catch (e: JsonParseException) {
            return if (retry) {
                val retryMessages = messages + ChatMessageWrapper(MessageType.SYSTEM, "Você não gerou uma resposta com um JSON válido tente novamente.", null, null)
                createLogCompletion(retryMessages, false, state)
            } else {
                logger.error("OpenAIAdapter#createLogCompletion", e)
                LogCompletionResult(classificationResult = ClassificationResult(tags = listOf(ConversationTag.JSON_PARSE_ERROR), entendimento = mapOf(ConversationTag.JSON_PARSE_ERROR to "Não conseguiu fazer o parse do JSON: $rawJson")), promptTokens = 0, completionTokens = 0)
            }
        } catch (e: Exception) {
            logger.error("OpenAIAdapter#createLogCompletion", e)
            return LogCompletionResult(classificationResult = ClassificationResult(tags = listOf(ConversationTag.JSON_PARSE_ERROR), entendimento = mapOf()), promptTokens = 0, completionTokens = 0)
        }
    }

    override fun createSummaryCompletion(
        summaries: List<DailyLogErrorSummary>,
        retry: Boolean,
    ): SummaryCompletionResult {
        try {
            val history = messageParser.buildChatMessageList(includeActionsInPrompt(dailyLogSummaryPrompt, summaries), historyState = null, emptyList(), systemMessageToCompletion = null)
            val completion = generateDailyLogCompletion(history = history, model = betaModel)

            // Às vezes o ChatGPT formata o json em markdown
            val json = completion.choices.first().message.content.replace("```json", "").replace("```", "")

            val result = parseObjectFrom<SummaryResult>(json)

            return SummaryCompletionResult(summaryResult = result, promptTokens = completion.usage.promptTokens, completionTokens = completion.usage.completionTokens)
        } catch (e: JsonParseException) {
            return if (retry) {
                createSummaryCompletion(summaries, false)
            } else {
                logger.error("OpenAIAdapter#createSummaryCompletion", e)
                throw e
            }
        } catch (e: Exception) {
            logger.error("OpenAIAdapter#createSummaryCompletion", e)
            throw e
        }
    }

    data class SearchContactsCompletionResult(val searchContactsResult: SearchContactsResult)

    data class LogCompletionResult(val classificationResult: ClassificationResult, val promptTokens: Long, val completionTokens: Long)

    data class SummaryCompletionResult(val summaryResult: SummaryResult, val promptTokens: Long, val completionTokens: Long)

    data class SummaryResult(
        val naoImplementadas: List<String>,
        val entendimentoNaoImplementadas: String,
        val naoAtendidas: List<String>,
        val entendimentoNaoAtendidas: String,
    )

    private fun getPostProcessingTags(actions: List<ConversationAction>, state: BillComingDueHistoryState): List<ConversationTag> {
        val requestsFulfilled = if (actions.any {
                it.status in listOf(ConversationActionStatus.FAILED, ConversationActionStatus.NOT_SUPPORTED) ||
                    (it.fonte == ConversationActionSource.USER && it.status == ConversationActionStatus.IGNORED)
            }
        ) {
            ConversationTag.USUARIO_NAO_ATENDIDO
        } else if (actions.any { it.status == ConversationActionStatus.DONE }) {
            ConversationTag.USUARIO_ATENDIDO
        } else {
            null
        }

        val featureRequests = actions.firstOrNull { it.status == ConversationActionStatus.NOT_SUPPORTED }?.let { ConversationTag.FEATURE_REQUEST }

        val unregisteredUser = if (state.user.status == AccountStatus.NOT_REGISTERED) {
            ConversationTag.NON_USER
        } else {
            null
        }

        return listOfNotNull(requestsFulfilled, featureRequests, unregisteredUser)
    }

    private fun processDailyLog(it: ClassificationResult, state: BillComingDueHistoryState): ClassificationResult {
        val extraTags = getPostProcessingTags(it.acoes, state)

        val tags = if (it.tags.isEmpty()) {
            extraTags + ConversationTag.OUTROS
        } else {
            it.tags + extraTags
        }

        return it.copy(tags = tags)
    }

    fun generateCompletion(
        history: List<ChatMessage>,
        model: String = betaModel,
        functions: List<ChatFunction>? = null,
        functionCall: ChatCompletionRequest.ChatCompletionRequestFunctionCall? = null,
    ): ChatCompletionResult {
        val request = buildRequest(model, history, functions, functionCall)

        return aiService.createChatCompletion(request)
    }

    fun generateDailyLogCompletion(
        history: List<ChatMessage>,
        model: String = betaModel,
        functions: List<ChatFunction>? = null,
        functionCall: ChatCompletionRequest.ChatCompletionRequestFunctionCall? = null,
    ): ChatCompletionResult {
        val request = buildRequest(model, history, functions, functionCall)

        return dailyLogAiService.createChatCompletion(request)
    }

    private fun buildRequest(
        model: String,
        history: List<ChatMessage>,
        functions: List<ChatFunction>?,
        functionCall: ChatCompletionRequest.ChatCompletionRequestFunctionCall?,
    ): ChatCompletionRequest? {
        val request =
            ChatCompletionRequest
                .builder()
                .model(model)
                .messages(history)
                .functions(functions)
                .functionCall(functionCall)
                .temperature(0.0)
                .n(1) // chat completion choices
                .maxTokens(2048)
                .logitBias(HashMap())
                .build()
        return request
    }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(OpenAIAdapter::class.java)
    }
}

private fun getUserGroups(state: HistoryState): List<String> {
    return when (state) {
        is BillComingDueHistoryState -> state.user.accountGroups
    }
}