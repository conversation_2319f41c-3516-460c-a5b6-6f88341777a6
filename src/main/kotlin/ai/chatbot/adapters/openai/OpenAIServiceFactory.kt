package ai.chatbot.adapters.openai

import com.theokanning.openai.service.OpenAiService
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Property
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.Duration

@Factory
class OpenAIServiceFactory(
    @Property(name = "openai.token") private val token: String,
    @Property(name = "openai.daily-log.token") private val dailyLogToken: String,
    @Property(name = "openai.connectionTimeout") private val connectionTimeout: Long,
) {
    @Primary
    @Singleton
    fun createOpenAiService(): OpenAiService {
        return OpenAiService(token, Duration.ofSeconds(connectionTimeout))
    }

    @Named("DailyLog")
    @Singleton
    fun createDailyLogOpenAiService(): OpenAiService {
        return OpenAiService(dailyLogToken, Duration.ofSeconds(connectionTimeout))
    }
}