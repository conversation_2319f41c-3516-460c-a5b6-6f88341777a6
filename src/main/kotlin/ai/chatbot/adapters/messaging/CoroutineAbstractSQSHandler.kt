package ai.chatbot.adapters.messaging

import ai.chatbot.app.config.TENANT_KEY
import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.utils.andAppend
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

abstract class CoroutineAbstractSQSHandler(
    private val tenantPropagator: TenantPropagator,
    amazonSQS: SqsClient,
    configuration: MessageHandlerConfiguration,
    val queueName: String,
    consumers: Int = 1,
) : AbstractSQSHandler(tenantPropagator, amazonSQS, configuration, queueName, consumers = consumers) {
    private val coroutineScope = CoroutineScope(Dispatchers.IO)
    private val channel = Channel<Pair<Message, String>>(Channel.BUFFERED)

    init {
        for (i in 0 until consumers) {
            coroutineScope.launch {
                startConsumer(i)
            }
        }
    }

    suspend fun startConsumer(id: Int) {
        for (msg in channel) {
            val message = msg.first
            val queueUrl = msg.second
            val markers =
                Markers.append("queueMessage", message)
                    .andAppend("queueUrl", queueUrl)
                    .andAppend("consumerId", id)

            val tenantId = message.messageAttributes()[TENANT_KEY]?.stringValue()

            logger.info(markers.andAppend("tenantId", tenantId), "processing message")

            tenantPropagator.executeWithTenant(tenantId) {
                tryProcessMessage(message, queueUrl)
            }
        }
    }

    override fun process(
        consumerId: Int,
        messages: List<Message>,
        queueUrl: String,
    ) {
        runBlocking {
            messages.forEach { message ->
                channel.send(Pair(message, queueUrl))
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(AbstractSQSHandler::class.java)
    }
}