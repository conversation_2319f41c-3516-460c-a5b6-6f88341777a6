package ai.chatbot.adapters.messaging

import ai.chatbot.adapters.notification.BlipNotificationAdapter
import ai.chatbot.adapters.notification.NotificationWrapper
import ai.chatbot.adapters.waCommCentre.ClientId
import ai.chatbot.app.NotificationService
import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.event.UserEvent
import ai.chatbot.app.notification.ButtonWhatsAppDeeplinkParameter
import ai.chatbot.app.notification.ButtonWhatsAppParameter
import ai.chatbot.app.notification.ButtonWhatsAppRawParameter
import ai.chatbot.app.notification.ButtonWhatsAppTrackedDeeplinkParameter
import ai.chatbot.app.notification.ButtonWhatsAppWebParameter
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.ChatbotWhatsappTemplatedNotification
import ai.chatbot.app.notification.ConfigurationKey
import ai.chatbot.app.notification.NotificationMedia
import ai.chatbot.app.notification.NotificationMediaType
import ai.chatbot.app.notification.NotificationTemplate
import ai.chatbot.app.notification.NotificationType
import ai.chatbot.app.notification.SimpleResponseNotification
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.parseObjectFrom
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.util.*
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.MessageSystemAttributeName

@Singleton
@Requires(notEnv = ["test"])
open class BlipVerificationHandler(
    tenantPropagator: TenantPropagator,
    @Property(name = "aws.sqs.queues.chatbotVerifyNotification") queue: String,
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val blipNotificationAdapter: BlipNotificationAdapter,
) : CoroutineAbstractSQSHandler(tenantPropagator = tenantPropagator, queueName = queue, amazonSQS = amazonSQS, configuration = configuration, consumers = 1) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Trace
    @NewSpan("BlipVerificationHandler#handleMessage")
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val logName = "BlipVerificationHandler#handleMessage"
        val body = m.body()
        val notificationWrapper = parseObjectFrom<NotificationWrapper>(body)
        val attempts = m.receiveCount()

        val shouldDeleteMessage = blipNotificationAdapter.verifyStatus(notificationWrapper, attempts)

        logger.info(Markers.append("notification", notificationWrapper).andAppend("attempts", attempts), logName)
        return SQSHandlerResponse(shouldDeleteMessage)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(Markers.append("event", m.body()), "BlipVerificationHandler#handleError", e)
        return SQSHandlerResponse(false)
    }

    private fun Message.receiveCount(): Long {
        return attributes()[MessageSystemAttributeName.APPROXIMATE_RECEIVE_COUNT]?.toLong() ?: 0
    }
}

@Singleton
@Requires(notEnv = ["test"])
open class DelayedChatbotWhatsappTemplatedNotificationHandler(
    tenantPropagator: TenantPropagator,
    @Property(name = "aws.sqs.queues.chatbotTemplateDelayQueue") queue: String,
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val notificationService: NotificationService,
) : CoroutineAbstractSQSHandler(tenantPropagator = tenantPropagator, queueName = queue, amazonSQS = amazonSQS, configuration = configuration, consumers = 1) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Trace
    @NewSpan("DelayedChatbotWhatsappTemplatedNotificationHandler#handleMessage")
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val logName = "DelayedChatbotWhatsappTemplatedNotificationHandler#handleMessage"

        val notification = parseObjectFrom<ChatbotWhatsappTemplatedNotificationTO>(m.body())

        val markers = Markers.append("notification", notification)

        try {
            notificationService.notify(notification.toNotification(), delaySeconds = null, retry = false)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return SQSHandlerResponse(false)
        }

        logger.info(markers, logName)
        return SQSHandlerResponse(true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(Markers.append("event", m.body()), "DelayedChatbotWhatsappTemplatedNotificationHandler#handleError", e)
        return SQSHandlerResponse(false)
    }
}

@Singleton
@Requires(notEnv = ["test"])
open class DelayedChatbotRawTemplatedNotificationHandler(
    tenantPropagator: TenantPropagator,
    @Property(name = "aws.sqs.queues.chatbotRawTemplateDelayQueue") queue: String,
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val notificationService: NotificationService,
) : CoroutineAbstractSQSHandler(tenantPropagator = tenantPropagator, queueName = queue, amazonSQS = amazonSQS, configuration = configuration, consumers = 1) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Trace
    @NewSpan("DelayedChatbotRawTemplatedNotificationHandler#handleMessage")
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val logName = "DelayedChatbotRawTemplatedNotificationHandler#handleMessage"

        val notification = parseObjectFrom<ChatbotRawTemplatedNotificationTO>(m.body())

        val markers = Markers.append("notification", notification)

        try {
            notificationService.notify(notification.toNotification(), delaySeconds = null, retry = false)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return SQSHandlerResponse(false)
        }

        logger.info(markers, logName)
        return SQSHandlerResponse(true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(Markers.append("event", m.body()), "DelayedChatbotRawTemplatedNotificationHandler#handleError", e)
        return SQSHandlerResponse(false)
    }
}

@Singleton
@Requires(notEnv = ["test"])
open class SimpleMessageDelayHandler(
    tenantPropagator: TenantPropagator,
    @Property(name = "aws.sqs.queues.chatbotSimpleDelayQueue") queue: String,
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val notificationService: NotificationService,
) : CoroutineAbstractSQSHandler(tenantPropagator = tenantPropagator, queueName = queue, amazonSQS = amazonSQS, configuration = configuration, consumers = 1) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Trace
    @NewSpan("SimpleMessageDelayHandler#handleMessage")
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val logName = "SimpleMessageDelayHandler#handleMessage"

        val notification = parseObjectFrom<SimpleResponseNotification>(m.body())

        val markers = Markers.append("notification", notification)

        try {
            notificationService.notify(
                userId = UserId(notification.userId),
                accountId = notification.accountId?.let { AccountId(it) },
                message = notification.message,
                quickReplyButtons = notification.buttons,
                ctaLink = notification.link,
                media = notification.media,
                delay = null,
                retry = false,
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return SQSHandlerResponse(false)
        }

        logger.info(markers, logName)
        return SQSHandlerResponse(true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(Markers.append("event", m.body()), "SimpleMessageDelayHandler#handleError", e)
        return SQSHandlerResponse(false)
    }
}

data class ChatbotWhatsappTemplatedNotificationTO(
    val notificationId: String = UUID.randomUUID().toString(),
    val mobilePhone: String,
    val accountId: AccountId,
    val template: NotificationTemplate,
    val configurationKey: String?,
    val parameters: List<String> = listOf(),
    val quickReplyButtonsWhatsAppParameter: List<String> = emptyList(),
    val quickRepliesStartIndex: Int?, // FIXME = null,
    val buttonWhatsAppParameter: ButtonWhatsAppParameterTO? = null,
    val media: NotificationMediaTO? = null,
) {
    fun toNotification() = ChatbotWhatsappTemplatedNotification(
        notificationId = notificationId,
        mobilePhone = mobilePhone,
        accountId = accountId,
        template = template,
        configurationKey = configurationKey,
        parameters = parameters,
        quickReplyButtonsWhatsAppParameter = quickReplyButtonsWhatsAppParameter,
        quickRepliesStartIndex = quickRepliesStartIndex,
        buttonWhatsAppParameter = buttonWhatsAppParameter?.toDomain(),
        media = media?.toNotificationMedia(),
    )
}

data class ChatbotRawTemplatedNotificationTO(
    val notificationId: String = UUID.randomUUID().toString(),
    val mobilePhone: String,
    val accountId: String,
    val clientId: String,
    val configurationKey: String,
    val language: String? = "pt_BR",
    val arguments: Map<String, String> = emptyMap(),
    val media: NotificationMediaTO? = null,
    val notificationType: String? = null,
) {
    fun toNotification() = ChatbotRawTemplatedNotification(
        notificationId = notificationId,
        mobilePhone = mobilePhone,
        accountId = AccountId(accountId),
        clientId = ClientId(clientId),
        configurationKey = ConfigurationKey(configurationKey),
        language = language,
        arguments = arguments,
        media = media?.toNotificationMedia(),
        notificationType = notificationType?.let { NotificationType(it) },
    )
}

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type",
    visible = true,
)
@JsonSubTypes(
    JsonSubTypes.Type(value = NotificationMedia.Document::class, name = "DOCUMENT"),
    JsonSubTypes.Type(value = NotificationMedia.Image::class, name = "IMAGE"),
    JsonSubTypes.Type(value = NotificationMedia.Video::class, name = "VIDEO"),
)
sealed class NotificationMediaTO(val type: NotificationMediaType) {
    data class Document(val url: String, val filename: String, val documentType: String?) : NotificationMediaTO(NotificationMediaType.DOCUMENT) {
        override fun toNotificationMedia() = NotificationMedia.Document(url = url, filename = filename, documentType = documentType)
    }

    data class Image(val url: String, val imageType: String? = null, val title: String? = null, val text: String? = null) : NotificationMediaTO(NotificationMediaType.IMAGE) {
        override fun toNotificationMedia() = NotificationMedia.Image(url = url, imageType = imageType, title = title, text = text)
    }

    data class Video(val url: String, val videoType: String?) : NotificationMediaTO(NotificationMediaType.VIDEO) {
        override fun toNotificationMedia() = NotificationMedia.Video(url = url, videoType = videoType)
    }

    abstract fun toNotificationMedia(): NotificationMedia
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY)
sealed interface ButtonWhatsAppParameterTO {
    val value: String

    @JsonTypeName("RAW")
    data class ButtonWhatsAppRawParameterTO(override val value: String) : ButtonWhatsAppParameterTO {
        override fun toDomain() = ButtonWhatsAppRawParameter(value)
    }

    @JsonTypeName("WEB")
    data class ButtonWhatsAppWebParameterTO(val path: String) : ButtonWhatsAppParameterTO {
        override val value = "web/$path"
        override fun toDomain() = ButtonWhatsAppWebParameter(path)
    }

    @JsonTypeName("DEEP_LINK")
    data class ButtonWhatsAppDeeplinkParameterTO(val path: String) : ButtonWhatsAppParameterTO {
        override val value = "app/$path"
        override fun toDomain() = ButtonWhatsAppDeeplinkParameter(path)
    }

    @JsonTypeName("TRACKED_DEEP_LINK")
    data class ButtonWhatsAppTrackedDeeplinkParameterTO(val path: String, val event: UserEvent) : ButtonWhatsAppParameterTO {
        override val value = "app/track/${event.name}/${String(encoder.encode("/$path".toByteArray()))}"
        override fun toDomain() = ButtonWhatsAppTrackedDeeplinkParameter(path, event)

        companion object {
            private val encoder = Base64.getUrlEncoder()
        }
    }

    fun toDomain(): ButtonWhatsAppParameter
}