package ai.chatbot.adapters.messaging

import ai.chatbot.adapters.waCommCentre.InboundMessage
import ai.chatbot.adapters.waCommCentre.IncomingMessageProcessor
import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.utils.parseObjectFrom
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.util.concurrent.ExecutorService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requirements(
    Requires(notEnv = ["test"]),
    Requires(property = "wa-comm-centre.enabled", value = "true"),
)
open class IncomingMessageHandler(
    private val tenantPropagator: TenantPropagator,
    private val tenantService: TenantService,
    amazonSQS: SqsClient,
    private val incomingMessageProcessor: IncomingMessageProcessor,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = "aws.sqs.queues.waCommCentreIncomingMessageQueue") queueName: String,
    @Named("message-handler") executorService: ExecutorService,
) : CoroutineAbstractSQSHandler(tenantPropagator, queueName = queueName, amazonSQS = amazonSQS, configuration = configuration, consumers = 1) {
    private val logger = LoggerFactory.getLogger(IncomingMessageHandler::class.java)
    private val logName = "IncomingMessageHandler"

    private val dispatcher = executorService.asCoroutineDispatcher()

    @OptIn(DelicateCoroutinesApi::class)
    @NewSpan("InboundMessageSqsHandler#handleMessage")
    @Trace
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val inboundMessage = parseObjectFrom<InboundMessage>(m.body())
        val markers = Markers.append("response", inboundMessage)
        val tenant = tenantService.getTenantName()

        CoroutineScope(dispatcher).launch {
            tenantPropagator.executeWithTenantWithSuspend(tenant) {
                incomingMessageProcessor.process(inboundMessage)
            }
        }

        logger.info(markers, "$logName/success")
        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(m: Message, e: Exception): SQSHandlerResponse {
        logger.error("$logName#handleError", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }
}