package ai.chatbot.adapters.messaging

import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration
import software.amazon.awssdk.core.retry.RetryPolicy
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sqs.SqsClient

@Factory
class SqsFactory(
    @Property(name = "aws.region") private val sqsRegion: String,
) {
    @Singleton
    fun getSqsFactory() = buildSqsClient(Region.of(sqsRegion))

    private fun buildSqsClient(region: Region): SqsClient {
        return SqsClient.builder()
            .region(region)
            .overrideConfiguration(
                ClientOverrideConfiguration.builder()
                    .retryPolicy(RetryPolicy.builder().numRetries(25).build()).build(),
            )
            .build()
    }
}