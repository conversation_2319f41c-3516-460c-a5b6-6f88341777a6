package ai.chatbot.adapters.messaging

import ai.chatbot.adapters.billPayment.toBillViews
import ai.chatbot.app.NotificationService
import ai.chatbot.app.OnePixPayInstrumentation
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InteractionWindowError
import ai.chatbot.app.conversation.InteractionWindowService
import ai.chatbot.app.conversation.UserConversationHistoryNotFound
import ai.chatbot.app.notification.BillComingDueLastWarnDetailsTO
import ai.chatbot.app.notification.BillComingDueRegularDetailsTO
import ai.chatbot.app.notification.BuildNotificationResult
import ai.chatbot.app.notification.ChatBotNotificationGatewayTO
import ai.chatbot.app.notification.ChatbotNotification
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.buildUnregisteredUserAndWallet
import ai.chatbot.app.utils.parseObjectFrom
import arrow.core.getOrElse
import com.theokanning.openai.completion.chat.ChatMessageRole
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.LocalDateTime
import java.util.Locale
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["test"])
open class ChatBotNotificationGatewayHandler(
    tenantPropagator: TenantPropagator,
    private val conversationHistoryService: ConversationHistoryService,
    private val notificationService: NotificationService,
    private val onePixPayInstrumentation: OnePixPayInstrumentation,
    private val interactionWindowService: InteractionWindowService,
    private val paymentAdapter: PaymentAdapter,
    private val chatbotNotificationBuilder: ChatbotNotificationBuilder,
    @Property(name = "aws.sqs.queues.chatBotNotificationGateway") queue: String,
    private val tenantService: TenantService,
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
) : CoroutineAbstractSQSHandler(tenantPropagator, queueName = queue, amazonSQS = amazonSQS, configuration = configuration, consumers = 10) {

    @Trace
    @NewSpan("ChatBotNotificationGatewayHandler#handleMessage")
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val logName = "ChatBotNotificationGatewayHandler#handleMessage"
        val messageBody = m.body()
        val markers = Markers.append("messageBody", messageBody)

        try {
            markers.andAppend("tenantId", tenantService.getTenantName())

            val event = parseObjectFrom<ChatBotNotificationGatewayTO>(m.body())
            markers.andAppend("event", event)
                .andAppend("chatBotMessageType", event.details::class.java.simpleName)

            interactionWindowService.checkAndCreate(event).mapLeft {
                if (it is InteractionWindowError.UserAlreadyHasOpenWindow) {
                    logger.info(markers.andAppend("interactionWindow", it.interactionWindow.toLog()), "$logName/messageQueuedOnInteractionWindow")
                    return SQSHandlerResponse(true)
                }
            }

            val user = event.toUser()
            markers.andAppend("userId", user.id.value)

            val activeConsents = paymentAdapter.getActiveSweepingConsents(user.id, WalletId(event.walletId)).getOrElse {
                emptyList()
            }
            markers.andAppend("activeConsents", activeConsents)

            val currentState = findCurrentState(user, event, activeConsents)

            val wallet = event.toWalletWithBills(activeConsents = activeConsents, currentBills = currentState.walletWithBills.bills)

            val isComingDueNotification = event.details is BillComingDueRegularDetailsTO || event.details is BillComingDueLastWarnDetailsTO
            markers.andAppend("isComingDueNotification", isComingDueNotification)

            if (isComingDueNotification) {
                onePixPayInstrumentation.requestedToNotifyUser(user, wallet.bills)
            }

            val parsedNotifications = chatbotNotificationBuilder.buildNotifications(
                details = event.details,
                user = user,
                currentState = currentState,
                subscriptionType = event.account.subscriptionType,
                walletProvider = { wallet },
            ).getOrElse {
                markers.andAppend("status", it.name)

                when (it) {
                    NotificationError.NO_BILLS_TO_NOTIFY, NotificationError.USER_ALREADY_NOTIFIED, NotificationError.USER_NOT_ELIGIBLE -> {
                        logger.warn(markers, logName)
                    }

                    NotificationError.TRANSACTION_NOT_FOUND, NotificationError.UNKNOWN_TRANSACTION_TYPE -> {
                        logger.error(markers, logName)
                    }
                }

                return SQSHandlerResponse(true)
            }
            markers.andAppend("fakeNotificationMessage", parsedNotifications.map { it.text })
                .andAppend("notifications", parsedNotifications.map { it.notification })

            parsedNotifications.forEach { notification ->
                saveAndNotify(user, notification)
            }

            if (isComingDueNotification) {
                if (parsedNotifications.any { it.notification != null }) {
                    conversationHistoryService.updateConversationHistoryState<BillComingDueHistoryState>(user.id) { stateToUpdate ->
                        stateToUpdate.copy(
                            user = user,
                            internalStateControl = stateToUpdate.internalStateControl.copy(
                                shouldSynchronizeBeforeCompletion = true,
                                billComingDueNotifiedAt = LocalDateTime.now(),
                            ),
                            walletWithBills = event.toWalletWithBills(stateToUpdate.walletWithBills.bills, activeConsents),
                        )
                    }
                }

                onePixPayInstrumentation.notifiedUser(user = user, bills = wallet.bills)
            }

            logger.info(markers, logName)
            return SQSHandlerResponse(true)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    private fun saveAndNotify(user: User, notification: ParsedNotificationTO) {
        val logName = "ChatBotNotificationGatewayHandler#saveAndNotify"
        val markers = Markers.append("userId", user.id.value)
            .andAppend("notification", notification)

        try {
            createAuxiliaryMessage(user, notification)
            if (notification.notification != null) {
                notificationService.notify(notification.notification, notification.delaySeconds)
            }
            logger.info(markers, logName)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    private fun ChatBotNotificationGatewayTO.toUser(): User {
        return if (account.id.isNotEmpty()) {
            User(
                accountId = AccountId(account.id),
                id = UserId.fromMsisdn(account.msisdn),
                name = account.fullName.split(" ")[0].captilizeFirstLetter(),
                accountGroups = account.accountGroups,
                status = account.status,
                paymentStatus = account.paymentStatus,
            )
        } else {
            buildUnregisteredUserAndWallet(UserId.fromMsisdn(account.msisdn)).user
        }
    }

    private fun findCurrentState(user: User, event: ChatBotNotificationGatewayTO, activeConsents: List<SweepingConsent>): BillComingDueHistoryState {
        return try {
            conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(user.id)
        } catch (e: UserConversationHistoryNotFound) {
            conversationHistoryService.setupEmptyHistoryState(
                userId = user.id,
                initialAssistantMessage = null,
                initialUserAndWallet = UserAndWallet(
                    user = user,
                    wallet = event.toWalletWithBills(
                        activeConsents = activeConsents,
                        currentBills = emptyList(),
                    ),
                ),
            )
        }
    }

    private fun createAuxiliaryMessage(user: User, notification: ParsedNotificationTO) {
        try {
            when (notification.role) {
                ChatMessageRole.ASSISTANT -> conversationHistoryService.createAssistantMessage(user.id, notification.text)
                ChatMessageRole.SYSTEM -> conversationHistoryService.createSystemMessage(user.id, notification.text)
                else -> throw IllegalArgumentException("Parsed notification must be either ASSISTANT or SYSTEM")
            }
        } catch (e: UserConversationHistoryNotFound) {
            throw IllegalStateException("O estado inicial deveria ser garantido no findCurrentState")
        }
    }

    private fun ChatBotNotificationGatewayTO.toWalletWithBills(currentBills: List<BillView>, activeConsents: List<SweepingConsent>): WalletWithBills {
        val (waitingApprovalBills, eventBillsTO) = details.eventBills().partition { it.checkWaitingApproval() }
        val eventBills = eventBillsTO.toBillViews()
        val eventMergeBills = details.eventMergeBills()
        val eventWalletName = details.eventWalletName()

        val activeBills = if (eventMergeBills) {
            val eventBillIds = eventBills.map { it.billId }
            eventBills + (currentBills.filter { it.billId !in eventBillIds })
        } else {
            eventBills
        }

        val orderedBills = activeBills.sortedWith(compareBy<BillView> { it.dueDate }.thenBy { it.billId.value }).mapIndexed { index, billView ->
            val externalBillId = index + 1
            if (billView.externalBillId != externalBillId) {
                billView.copy(externalBillId = externalBillId)
            } else {
                billView
            }
        }

        return WalletWithBills(
            bills = orderedBills,
            totalWaitingApproval = waitingApprovalBills.size,
            walletId = WalletId(walletId),
            walletName = eventWalletName,
            activeConsents = activeConsents,
        )
    }

    private fun String.captilizeFirstLetter(): String = this.lowercase().replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(Markers.append("event", m.body()), "ChatBotNotificationGatewayHandler#handleError", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ChatBotNotificationGatewayHandler::class.java)
    }
}

data class ParsedNotificationTO(
    val role: ChatMessageRole = ChatMessageRole.ASSISTANT,
    val text: String,
    val notification: ChatbotNotification?,
    val delaySeconds: Int? = null,
    val synchronous: Boolean = false,
)

private fun BuildNotificationResult.toParsedNotificationTO(): ParsedNotificationTO? {
    if (!this.shouldSend) {
        return null
    }

    return ParsedNotificationTO(
        text = this.historyMessage,
        notification = this.notification,
    )
}