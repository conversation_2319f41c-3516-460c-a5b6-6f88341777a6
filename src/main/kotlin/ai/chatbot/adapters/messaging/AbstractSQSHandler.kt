package ai.chatbot.adapters.messaging

import ai.chatbot.app.config.TENANT_KEY
import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.andAppend
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import io.micronaut.health.HealthStatus
import io.micronaut.management.health.indicator.HealthIndicator
import io.micronaut.management.health.indicator.HealthResult
import io.micronaut.runtime.event.ApplicationShutdownEvent
import io.micronaut.tracing.annotation.NewSpan
import io.reactivex.Flowable
import jakarta.annotation.PreDestroy
import java.time.Duration
import java.time.Instant
import kotlin.concurrent.thread
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.reactivestreams.Publisher
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.ChangeMessageVisibilityRequest
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest
import software.amazon.awssdk.services.sqs.model.DeleteMessageRequest
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest
import software.amazon.awssdk.services.sqs.model.ListQueuesRequest
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.QueueAttributeName
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest
import software.amazon.awssdk.services.sqs.model.SetQueueAttributesRequest

data class Queue(val url: String, val arn: String)

abstract class AbstractSQSHandler(
    private val tenantPropagator: TenantPropagator,
    private val amazonSQS: SqsClient,
    private val configuration: MessageHandlerConfiguration,
    private val queueName: String,
    private val consumers: Int = 1,
) : ApplicationEventListener<Any>, HealthIndicator {
    var isActive = false
        private set

    private val queueUrls = mutableMapOf<String, String>()
    private val queues = mutableMapOf<String, Queue>()

    private fun getQueueURL(queueName: String) =
        queueUrls.getOrPut(queueName) {
            amazonSQS.getQueueUrl(GetQueueUrlRequest.builder().queueName(queueName).build()).queueUrl()
        }

    private fun getQueue(queueName: String) =
        queues.getOrPut(queueName) {
            val queueUrl = amazonSQS.getQueueUrl { it.queueName(queueName) }.queueUrl()
            val attrs = amazonSQS.getQueueAttributes { it.queueUrl(queueUrl).attributeNames(QueueAttributeName.QUEUE_ARN) }

            return@getOrPut Queue(url = queueUrl, arn = attrs.attributesAsStrings()["QueueArn"]!!)
        }

    open fun receiveMessages(consumerId: Int = 0) {
        val threadName = "${this.javaClass.originalSimpleName()}-$consumerId"
        logger.info(Markers.append("sqsHandlerThreadName", threadName), "SQSHandlerStarting")
        val thread =
            thread(start = false, isDaemon = true, name = threadName, priority = 0) {
                logger.info(Markers.append("sqsHandlerThreadName", threadName), "SQSHandlerStarted")
                receiveMessagesAsync(consumerId)
                logger.info(Markers.append("sqsHandlerThreadName", threadName), "SQSHandlerStopped")
            }

        thread.setUncaughtExceptionHandler { _, exception ->
            logger.error(Markers.append("sqsHandlerThreadName", threadName), "SQSHandlerThreadException", exception)
        }

        thread.start()
    }

    @NewSpan
    open fun receiveMessagesAsync(consumerId: Int = 0) {
        val queueUrl = getQueueURL(queueName)

        val receiveRequest =
            ReceiveMessageRequest.builder().queueUrl(queueUrl)
                .maxNumberOfMessages(configuration.maxNumberOfMessages)
                .waitTimeSeconds(configuration.sqsWaitTime)
                .attributeNames(QueueAttributeName.ALL)
                .messageAttributeNames("All")
                .build()
        while (isActive) {
            val messages = amazonSQS.receiveMessage(receiveRequest).messages()

            if (messages.isNotEmpty()) {
                process(consumerId, messages, queueUrl)
            }

            if (messages.isEmpty()) {
                if (configuration.sqsCoolDownTime > 0) {
                    Thread.sleep(configuration.sqsCoolDownTime * 1000L)
                }
            }
        }
    }

    private fun Instant.elapsedSeconds() = Duration.between(this, getZonedDateTime().toInstant()).toSeconds()

    private fun Instant.elapsedMillis() = Duration.between(this, getZonedDateTime().toInstant()).toMillis()

    open fun process(
        consumerId: Int = 0,
        messages: List<Message>,
        queueUrl: String,
    ) {
        for (message in messages) {
            val tenantId = message.messageAttributes()[TENANT_KEY]?.stringValue()

            tenantPropagator.executeWithTenant(tenantId) {
                tryProcessMessage(message, queueUrl)
            }
        }
    }

    @NewSpan
    open fun tryProcessMessage(
        message: Message,
        queueUrl: String,
    ) {
        val startInstant = getZonedDateTime().toInstant()
        val markers = Markers.append("queueName", queueName)
        try {
            val response = handleMessage(message)
            markers.andAppend("handleSeconds", startInstant.elapsedSeconds())
            if (response.visibilityTimeoutInSeconds != null) {
                changeMessageVisibilityTimeout(queueUrl, message, response.visibilityTimeoutInSeconds)
            }
            if (response.shouldDeleteMessage) {
                message.delete(queueUrl, markers)
            }
        } catch (e: Throwable) {
            val response =
                when (e) {
                    is Exception -> handleError(message, e)
                    else -> handleRuntimeError(message, e)
                }
            if (response.shouldDeleteMessage) {
                message.delete(queueUrl, markers)
            }
        }
        logger.trace(markers.andAppend("elapsedSeconds", startInstant.elapsedSeconds()), "tryProcessMessage")
    }

    private fun Message.delete(
        queueUrl: String,
        markers: LogstashMarker,
    ) {
        val startInstant = getZonedDateTime().toInstant()

        amazonSQS.deleteMessage(
            DeleteMessageRequest.builder().queueUrl(queueUrl).receiptHandle(this.receiptHandle()).build(),
        )

        markers.andAppend("deleteMilliseconds", startInstant.elapsedMillis())
    }

    abstract fun handleMessage(m: Message): SQSHandlerResponse

    abstract fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse

    open fun handleRuntimeError(
        m: Message,
        e: Throwable,
    ): SQSHandlerResponse {
        val markers =
            Markers.append("eventBody", m.body())
                .andAppend("exceptionMessage", e.message)
                .andAppend("implementation", javaClass.simpleName)
        logger.error(markers, "SQSHandlerRuntimeError", e)
        return SQSHandlerResponse(false)
    }

    private fun changeMessageVisibilityTimeout(
        queueUrl: String,
        message: Message,
        visibilityTimeoutInSeconds: Int,
    ) {
        amazonSQS.changeMessageVisibility(
            ChangeMessageVisibilityRequest.builder().queueUrl(queueUrl)
                .receiptHandle(message.receiptHandle())
                .visibilityTimeout(visibilityTimeoutInSeconds).build(),
        )
    }

    override fun onApplicationEvent(event: Any) {
        val markers = Markers.append("queueName", queueName).andAppend("eventName", event.javaClass.simpleName)
        when (event) {
            is ServiceReadyEvent -> {
                logger.info(markers, "AbstractSQSHandlerOnServiceReadyEvent")
                try {
                    createQueueIfNotExists()
                    isActive = true
                    repeat(consumers) { consumerId ->
                        receiveMessages(consumerId)
                    }
                } catch (e: Exception) {
                    logger.error(markers, "AbstractSQSHandlerOnServiceReadyEvent", e)
                    throw e
                }
            }

            is ApplicationShutdownEvent -> {
                isActive = false
                logger.info(
                    markers.andAppend("forceShutDown", event.source.isForceExit),
                    "AbstractSQSHandlerOnApplicationShutdownEvent",
                )
            }
        }
    }

    override fun getResult(): Publisher<HealthResult> {
        val healthStatus = if (isActive) HealthStatus.UP else HealthStatus.DOWN
        return Flowable.just(HealthResult.builder("${this::class.simpleName}HealthIndicator", healthStatus).build())
    }

    private fun createQueueIfNotExists() {
        val listQueuesResult = amazonSQS.listQueues(ListQueuesRequest.builder().queueNamePrefix(queueName).build())
        if (listQueuesResult.queueUrls().isEmpty()) {
            val createQueueResult = amazonSQS.createQueue(CreateQueueRequest.builder().queueName(queueName).build())
            val queueAttributes =
                mutableMapOf(
                    QueueAttributeName.VISIBILITY_TIMEOUT to configuration.visibilityTimeout.toString(),
                )
            if (configuration.dlqEnabled) {
                val dlqQueueName = queueName + "_dlq"
                amazonSQS.createQueue(CreateQueueRequest.builder().queueName(dlqQueueName).build())
                val dlqQueue = getQueue(dlqQueueName)
                queueAttributes[QueueAttributeName.REDRIVE_POLICY] = "{\"maxReceiveCount\":\"3\", \"deadLetterTargetArn\":\"${dlqQueue.arn}\"}"
            }

            val request = SetQueueAttributesRequest.builder().queueUrl(createQueueResult.queueUrl()).attributes(queueAttributes).build()

            amazonSQS.setQueueAttributes(request)
        }
    }

    @PreDestroy
    fun tearDown() {
        isActive = false
    }

    override fun supports(event: Any) = true

    companion object {
        private val logger = LoggerFactory.getLogger(AbstractSQSHandler::class.java)
    }
}

data class SQSHandlerResponse(val shouldDeleteMessage: Boolean, val visibilityTimeoutInSeconds: Int? = null)

fun Class<*>.originalSimpleName(): String = simpleName.split("\$").first { it.isNotBlank() }