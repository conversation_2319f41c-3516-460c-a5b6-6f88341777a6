package ai.chatbot.adapters.messaging

import ai.chatbot.app.NotificationService
import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.encrypt.EncryptService
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.measure
import ai.chatbot.app.utils.parseObjectFrom
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["test"])
open class CognitoTokenHandler(
    tenantPropagator: TenantPropagator,
    @Property(name = "aws.sqs.queues.cognitoTokenMessageQueue") queue: String,
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val notificationService: NotificationService,
    private val encryptService: EncryptService,
    private val buildNotificationService: BuildNotificationService,
) : CoroutineAbstractSQSHandler(tenantPropagator = tenantPropagator, queueName = queue, amazonSQS = amazonSQS, configuration = configuration, consumers = 1) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Trace
    @NewSpan("CognitoTokenHandler#handleMessage")
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val logName = "CognitoTokenHandler#handleMessage"
        val markers = Markers.empty()
        val timeTaken = measure {
            val body = m.body()

            val cognitoTokenMessage = parseObjectFrom<CognitoTokenMessageTO>(body)
            markers.andAppend("cognitoTokenMessage", cognitoTokenMessage.toLog())

            val cognitoToken = cognitoTokenMessage.toCognitoToken()
            markers.andAppend("cognitoToken", cognitoToken.toLog())

            val notification = buildNotificationService.buildTokenNotification(
                accountId = cognitoToken.accountId,
                mobilePhone = cognitoToken.user.value,
                token = cognitoToken.code,
            )

            notificationService.notify(notification = notification)
        }
        logger.info(markers.andAppend("timeElapsed", timeTaken), logName)
        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    private fun CognitoTokenMessageTO.toCognitoToken(): CognitoToken {
        return CognitoToken(
            code = decryptedCode ?: encryptService.decrypt(this.code!!),
            user = UserId(this.phoneNumber!!),
            accountId = AccountId(this.accountId!!),
        )
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(append("event", m.body()).andAppend("ACTION", "VERIFY").andAppend("context", "envio de token do cognito falhou. Não deveria. usuário não vai conseguir validar MFA."), "CognitoTokenHandler#handleError", e)
        return SQSHandlerResponse(false)
    }
}

data class CognitoToken(
    val code: String,
    val user: UserId,
    val accountId: AccountId,
)

data class CognitoTokenMessageTO(
    val code: String?,
    val decryptedCode: String?,
    val source: String?,
    val username: String?,
    val sub: String?,
    val accountId: String?,
    val userPoolId: String?,
    val phoneNumber: String?,
    val phoneNumberVerified: Boolean?,
    val email: String?,
    val emailVerified: Boolean?,
    val userStatus: String?,
    val timestamp: String?,
)

fun CognitoTokenMessageTO.toLog() = this.copy(code = code?.let { it.take(2) + "***" + it.takeLast(2) })
fun CognitoToken.toLog() = this.copy(code = code.let { it.take(1) + "***" + it.takeLast(1) })