package ai.chatbot.adapters.messaging

import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.UserConversationHistoryNotFound
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.parseObjectFrom
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["test"])
open class ChatBotStateHandler(
    tenantPropagator: TenantPropagator,
    @Property(name = "aws.sqs.queues.chatbotStateUpdate") queue: String,
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    val conversationHistoryService: ConversationHistoryService,
) : CoroutineAbstractSQSHandler(tenantPropagator, queueName = queue, amazonSQS = amazonSQS, configuration = configuration, consumers = 1) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Trace
    @NewSpan("ChatBotStateHandler#handleMessage")
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val logName = "ChatBotStateHandler#handleMessage"

        val rawBody = m.body()
        val markers = Markers.append("rawBody", rawBody)
        val parsedBody = parseObjectFrom<UpdateAccountStatusTO>(rawBody)
        val userId = UserId(parsedBody.msisdn)

        markers.andAppend("parsedBody", parsedBody).andAppend("userId", userId)

        val accountState = try {
            conversationHistoryService.historyStateRepository.findByDate(UserId(parsedBody.msisdn), getLocalDate())
        } catch (e: UserConversationHistoryNotFound) {
            return SQSHandlerResponse(shouldDeleteMessage = true)
        } as BillComingDueHistoryState

        conversationHistoryService.saveHistoryState(userId, accountState.copy(user = accountState.user.copy(status = parsedBody.status, paymentStatus = parsedBody.paymentStatus)))

        logger.info(markers, logName)

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    override fun handleError(m: Message, e: Exception): SQSHandlerResponse {
        val logName = "ChatBotStateHandler#handleMessage"
        logger.error(Markers.append("message", m), logName, e)
        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    data class UpdateAccountStatusTO(
        val accountId: String,
        val paymentStatus: AccountPaymentStatus,
        val msisdn: String,
        val status: AccountStatus,
    )
}