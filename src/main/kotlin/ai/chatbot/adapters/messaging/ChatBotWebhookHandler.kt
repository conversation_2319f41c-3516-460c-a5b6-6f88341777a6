package ai.chatbot.adapters.messaging

import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.UserConversationHistoryNotFound
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.parseObjectFrom
import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["test"])
open class ChatBotWebhookHandler(
    tenantPropagator: TenantPropagator,
    @Property(name = "aws.sqs.queues.chatBotWebhook") queue: String,
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    val conversationHistoryService: ConversationHistoryService,
) : CoroutineAbstractSQSHandler(tenantPropagator = tenantPropagator, queueName = queue, amazonSQS = amazonSQS, configuration = configuration, consumers = 1) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Trace
    @NewSpan("ChatBotWebhookHandler#handleMessage")
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val logName = "ChatBotWebhookHandler#handleMessage"

        val rawBody = m.body()
        val parsedBody = parseObjectFrom<BlipChatbotLogTO>(rawBody)

        val phoneNumber = readPhoneNumber(parsedBody)

        if (phoneNumber == null) {
            logger.warn(Markers.append("rawBody", rawBody), "$logName/noPhoneNumber")
            return SQSHandlerResponse(shouldDeleteMessage = true)
        }

        val markers = Markers.append("userId", phoneNumber).andAppend("webhookType", parsedBody.type)

        // Se for uma reação a uma mensagem
        if (parsedBody.type == "application/vnd.lime.reaction+json") {
            val reaction = parseObjectFrom<ReactionMessageBodyTO>(rawBody)

            if (reaction.content.inReactionTo.direction == "sent") {
                val message = reaction.content.emoji.parse()

                markers.andAppend("reaction", message)
                    .andAppend("reactionTo", reaction.content.inReactionTo.id)

                try {
                    conversationHistoryService.createUserReaction(UserId(phoneNumber), message)
                    logger.info(markers, "$logName/userReaction")
                    return SQSHandlerResponse(true)
                } catch (e: UserConversationHistoryNotFound) {
                    logger.warn(markers, "$logName/userReactionWithoutHistory")
                    return SQSHandlerResponse(true)
                }
            }
        }

        logger.info(markers, logName)
        return SQSHandlerResponse(true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(Markers.append("event", m.body()), "ChatBotWebhookHandler#handleError", e)
        return SQSHandlerResponse(false)
    }

    private fun readPhoneNumber(message: BlipChatbotLogTO): String? {
        val fieldValues = listOfNotNull(
            message.from,
            message.to,
            message.metadata?.tunnelOwner,
            message.metadata?.tunnelOriginator,
            message.metadata?.tunnelOriginalFrom,
            message.metadata?.tunnelOriginalTo,
        )

        val foundPhoneNumber = fieldValues.firstOrNull { value ->
            value.matches("[0-9]{12,13}@wa.gw.msging.net".toRegex())
        }?.split("@")?.firstOrNull()

        return foundPhoneNumber
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class ReactionMessageBodyTO(
    val content: ReactionContentTO,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ReactionContentTO(
    val emoji: EmojiTO,
    val inReactionTo: ReactionDetailsTO,
)

data class EmojiTO(
    val values: List<Int>,
) {
    fun parse(): String {
        return values.joinToString("") { Character.toChars(it).joinToString("") }
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class ReactionDetailsTO(
    val direction: String,
    val id: String,
    val type: String,
    val value: Any,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class BlipChatbotLogTO(
    val type: String?,
    val id: String?,
    val from: String?,
    val to: String?,
    val metadata: BlipChatbotMetadataTO?,
    val extras: BlipChatbotMetadataTO?,
    val identity: String?,
    val phoneNumber: String?,
    val source: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class BlipChatbotMetadataTO(
    @JsonProperty("#stateName") val stateName: String?,
    @JsonProperty("#stateId") val stateId: String?,
    @JsonProperty("#messageId") val messageId: String?,
    @JsonProperty("#previousStateId") val previousStateId: String?,
    @JsonProperty("#previousStateName") val previousStateName: String?,
    @JsonProperty("#tunnel.owner")
    @JsonAlias("tunnel.owner")
    val tunnelOwner: String?,
    @JsonProperty("#tunnel.originator")
    @JsonAlias("tunnel.originator")
    val tunnelOriginator: String?,
    @JsonProperty("#tunnel.originalFrom") val tunnelOriginalFrom: String?,
    @JsonProperty("#tunnel.originalTo") val tunnelOriginalTo: String?,
)