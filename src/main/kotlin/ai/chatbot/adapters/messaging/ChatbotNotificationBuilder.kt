package ai.chatbot.adapters.messaging

import ai.chatbot.adapters.billPayment.BillTO
import ai.chatbot.adapters.billPayment.toBillView
import ai.chatbot.adapters.billPayment.toBillViews
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.notification.BillComingDueLastWarnDetailsTO
import ai.chatbot.app.notification.BillComingDueRegularDetailsTO
import ai.chatbot.app.notification.BuildNotificationResult
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatBotNotificationDetailsTO
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.GenericNotificationDetailsTO
import ai.chatbot.app.notification.GenericNotificationRawDetailsTO
import ai.chatbot.app.notification.KnownNotificationTypes
import ai.chatbot.app.notification.KnownTemplateConfigurationKeys
import ai.chatbot.app.notification.NotificationContextTemplatesService
import ai.chatbot.app.notification.NotificationFormatter
import ai.chatbot.app.notification.NotificationMap
import ai.chatbot.app.notification.NotificationParam
import ai.chatbot.app.notification.OnboardingSinglePixNotificationService
import ai.chatbot.app.notification.OpenFinanceIncentiveDetailsTO
import ai.chatbot.app.notification.OpenFinanceIncentiveType
import ai.chatbot.app.notification.RawTemplateNotificationConfig
import ai.chatbot.app.notification.RegisterCompletedTO
import ai.chatbot.app.notification.TestPixReminderType
import ai.chatbot.app.notification.TestPixreminderDetailsTO
import ai.chatbot.app.notification.WelcomeDetailsTO
import ai.chatbot.app.notification.WelcomeMessageType
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.theokanning.openai.completion.chat.ChatMessageRole
import jakarta.inject.Singleton
import java.time.LocalDateTime
import kotlin.collections.listOf
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class ChatbotNotificationBuilder(
    private val buildNotificationService: BuildNotificationService,
    private val notificationContextTemplatesService: NotificationContextTemplatesService,
    private val customNotificationService: CustomNotificationService,
    private val tenantService: TenantService,
    private val onboardingSinglePixNotificationService: OnboardingSinglePixNotificationService,
) {

    fun buildNotifications(
        details: ChatBotNotificationDetailsTO,
        user: User,
        currentState: BillComingDueHistoryState? = null,
        subscriptionType: SubscriptionType = SubscriptionType.PIX,
        walletProvider: (() -> WalletWithBills)? = null,
    ): Either<NotificationError, List<ParsedNotificationTO>> {
        val either =
            when (details) {
                is BillComingDueLastWarnDetailsTO -> details.buildNotifications(user)
                is BillComingDueRegularDetailsTO ->
                    details.buildNotifications(
                        user,
                        currentState ?: createMinimalState(user),
                        subscriptionType,
                    )

                is TestPixreminderDetailsTO -> details.buildNotifications()
                is WelcomeDetailsTO -> details.buildNotifications(user)
                is RegisterCompletedTO -> details.buildNotifications(user)
                is GenericNotificationDetailsTO -> details.buildNotifications(user)
                is GenericNotificationRawDetailsTO -> details.buildNotifications(user)
                is OpenFinanceIncentiveDetailsTO -> {
                    val wallet = walletProvider?.invoke() ?: createMinimalWallet()
                    details.buildNotifications(
                        user,
                        wallet,
                        tenantService.getConfiguration().features.openFinanceIncentive,
                    )
                }
            }
        return either
    }

    private fun RegisterCompletedTO.buildNotifications(
        user: User,
    ): Either<NotificationError, List<ParsedNotificationTO>> {
        val notification =
            buildNotificationService.buildRegisterCompletedNotification(
                accountId = user.accountId,
                mobilePhone = user.id.value,
                userName = user.name,
            )

        return notification.let {
            listOf(
                ParsedNotificationTO(
                    text =
                    notificationContextTemplatesService
                        .getRegisterCompletedMessage(user.name),
                    notification = notification,
                ),
            )
                .right()
        }
    }

    private fun GenericNotificationRawDetailsTO.buildNotifications(
        user: User,
    ): Either<NotificationError, List<ParsedNotificationTO>> {
        val templatedRawNotification = this.toNotification(user.accountId)

        return listOf(
            ParsedNotificationTO(
                notification = templatedRawNotification,
                text =
                customNotificationService.getHistoryMessage(
                    templatedRawNotification,
                ),
            ),
        )
            .right()
    }

    private fun BillComingDueLastWarnDetailsTO.buildNotifications(
        user: User,
    ): Either<NotificationError, List<ParsedNotificationTO>> {
        val paymentLimitTime = bills.first().paymentLimitTime
        val waitingApproveBills = bills.filter { it.checkWaitingApproval() }
        return listOfNotNull(
            ParsedNotificationTO(
                text =
                notificationContextTemplatesService
                    .getBillComingDueLastWarn(
                        user.name,
                        paymentLimitTime,
                    ),
                notification =
                buildNotificationService
                    .buildBillsComingDueLastWarnNotification(
                        accountId = user.accountId,
                        mobilePhone = user.id.value,
                        userName = user.name,
                        paymentLimitTime = paymentLimitTime,
                    ),
            ),
            buildWaitingApprovalBillNotification(user, waitingApproveBills.size),
        )
            .right()
    }

    private fun buildWaitingApprovalBillNotification(
        user: User,
        totalWaitingApproveBills: Int,
    ): ParsedNotificationTO? {
        if (totalWaitingApproveBills <= 0) {
            return null
        }

        val config =
            if (totalWaitingApproveBills == 1) {
                KnownTemplateConfigurationKeys.waitingApprovalBillsSingular
            } else {
                KnownTemplateConfigurationKeys.waitingApprovalBillsPlural
            }

        return customNotificationService
            .buildRawTemplateNotification(
                user = user,
                notificationConfig =
                RawTemplateNotificationConfig(
                    config,
                    KnownNotificationTypes.WAITING_APPROVAL_BILLS,
                ),
                params =
                listOf(
                    NotificationMap(
                        NotificationParam.TOTAL_BILLS,
                        totalWaitingApproveBills.toString(),
                    ),
                ),
            )
            .toParsedNotificationTO()
    }

    private fun BillComingDueRegularDetailsTO.buildNotifications(
        user: User,
        currentState: BillComingDueHistoryState,
        subscriptionType: SubscriptionType,
    ): Either<NotificationError, List<ParsedNotificationTO>> {
        val logName = "BillComingDueRegularDetailsTO#buildNotifications"
        val markers =
            Markers.append("user", user)
                .andAppend("currentStateSubscriptionType", currentState.subscription?.type)
                .andAppend("subscriptionType", subscriptionType)

        try {
            /* desativando a notificação para inadimplentes IN_APP,
             * pois está quebrando a ação do template chatbot_ai_notify_bills_coming_due_simple__1_0_0
             * que esta disponível somente para fluxo PIX.
             */
            val isInApp =
                (currentState.subscription?.type == SubscriptionType.IN_APP) ||
                    (subscriptionType == SubscriptionType.IN_APP)
            markers.andAppend("isInApp", isInApp)

            if (user.paymentStatus == AccountPaymentStatus.Overdue && isInApp) {
                logger.warn(markers, logName)
                return NotificationError.USER_NOT_ELIGIBLE.left()
            }

            val regularBills = bills.filter { bill -> !bill.subscriptionFee }
            markers.andAppend("regularBillsSize", regularBills.size)

            if (regularBills.isEmpty()) {
                logger.warn(markers, logName)
                return NotificationError.NO_BILLS_TO_NOTIFY.left()
            }

            val (waitingApproveBillViews, activeBills) =
                bills.partition { it.checkWaitingApproval() }
            markers.andAppend("waitingApproveBillViewsSize", waitingApproveBillViews.size)
                .andAppend("activeBillsSize", activeBills.size)

            val activeBillViews = activeBills.toBillViews()

            val billComingDueNotification =
                if (shouldNotifyBillComingDue(activeBills, currentState)) {
                    val formattedBills =
                        NotificationFormatter.getFormattedBillInfo(activeBillViews)
                    markers.andAppend("formattedBillsSize", formattedBills.size)

                    val comingDueNotification =
                        customNotificationService.buildBillsComingDueCustom(
                            user,
                            formattedBills,
                        )
                    markers.andAppend("comingDueNotification", comingDueNotification)

                    ParsedNotificationTO(
                        text = comingDueNotification.historyMessage,
                        notification = comingDueNotification.notification,
                    )
                } else {
                    null
                }

            val billsChanged =
                waitingApproveBillViews.size !=
                    currentState
                        .walletWithBills
                        .totalWaitingApproval // TODO - poderiamos salvar os BillIds
            // para verificar com mais exatidão
            markers.andAppend("billsChanged", billsChanged)

            val approveBillNotification =
                if (waitingApproveBillViews.isNotEmpty() &&
                    (billsChanged || forceSendNotification(currentState))
                ) {
                    buildWaitingApprovalBillNotification(user, waitingApproveBillViews.size)
                } else {
                    null
                }
            markers.andAppend("approveBillNotification", approveBillNotification)

            if (billComingDueNotification == null && approveBillNotification == null) {
                logger.warn(markers, logName)
                return NotificationError.USER_ALREADY_NOTIFIED.left()
            }

            logger.info(markers, logName)
            return listOfNotNull(billComingDueNotification, approveBillNotification).right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    private fun GenericNotificationDetailsTO.buildNotifications(
        user: User,
    ): Either<NotificationError, List<ParsedNotificationTO>> {
        val templatedNotification = this.notification.toNotification(user.accountId)

        return listOf(
            ParsedNotificationTO(
                notification = templatedNotification,
                text =
                customNotificationService.getHistoryMessage(
                    templatedNotification,
                ),
            ),
        )
            .right()
    }

    private fun TestPixreminderDetailsTO.buildNotifications():
        Either<NotificationError, List<ParsedNotificationTO>> {
        return when (reminderType) {
            TestPixReminderType.NEXT_DAY ->
                listOf<ParsedNotificationTO>() // TODO gerar notificações
            TestPixReminderType.LAST_DAY ->
                listOf<ParsedNotificationTO>() // TODO gerar notificações
            TestPixReminderType.EXPIRED -> listOf<ParsedNotificationTO>() // TODO gerar notificações
        }.right()
    }

    private fun WelcomeDetailsTO.buildNotifications(
        user: User,
    ): Either<NotificationError, List<ParsedNotificationTO>> {
        val offerNotification =
            customNotificationService.buildRawTemplateNotification(
                user = user,
                params = listOf(),
                notificationConfig =
                RawTemplateNotificationConfig(
                    KnownTemplateConfigurationKeys.onboardingStart,
                    KnownNotificationTypes.ONBOARDING_START,
                ),
            )

        return when (type) {
            WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX ->
                listOf(
                    ParsedNotificationTO(
                        role = ChatMessageRole.SYSTEM,
                        text =
                        onboardingSinglePixNotificationService
                            .getSinglePixStartContextMessage(),
                        notification = null,
                    ),
                    ParsedNotificationTO(
                        text =
                        onboardingSinglePixNotificationService
                            .getSinglePixStartMessage(user.name),
                        notification =
                        buildNotificationService
                            .buildOnboardingSinglePixStartNotification(
                                accountId = user.accountId,
                                mobilePhone = user.id.value,
                                userName = user.name,
                            ),
                        synchronous = true,
                    ),
                    ParsedNotificationTO(
                        text = offerNotification.historyMessage,
                        notification = offerNotification.notification,
                        delaySeconds = 5,
                        synchronous = true,
                    ),
                )
                    .right()
        }
    }

    private fun OpenFinanceIncentiveDetailsTO.buildNotifications(
        user: User,
        wallet: WalletWithBills,
        incentiveEnabled: Boolean = true,
    ): Either<NotificationError, List<ParsedNotificationTO>> {
        fun logAndReturnEmpty(
            reason: String,
        ): Either<NotificationError, List<ParsedNotificationTO>> {
            logger.info(
                Markers.append("userId", user.id.value)
                    .andAppend("details", this)
                    .andAppend("incentiveEnabled", incentiveEnabled)
                    .andAppend("reason", reason),
                "OpenFinanceIncentiveDetails/skip",
            )

            return emptyList<ParsedNotificationTO>().right()
        }

        if (!incentiveEnabled) return logAndReturnEmpty("incentive not enabled")

        if (wallet.hasSweepingAccount) return logAndReturnEmpty("open finance already connected")

        if (userOptedOut) return logAndReturnEmpty("user opted out of incentive")

        val notifications =
            when (type) {
                OpenFinanceIncentiveType.DDA -> {
                    val billInfo =
                        bill?.let {
                            NotificationFormatter.getFormattedBillInfo(
                                listOf(it.toBillView(0)),
                            )[0]
                        }
                            ?: throw IllegalArgumentException(
                                "dda incentive without bill",
                            )

                    listOf(
                        ParsedNotificationTO(
                            text =
                            notificationContextTemplatesService
                                .getPromoteSweepingAccountDDAMessage(
                                    billInfo,
                                ),
                            notification =
                            buildNotificationService
                                .buildPromoteSweepingAccountDDANotification(
                                    accountId = user.accountId,
                                    mobilePhone = user.id.value,
                                    billInfo = billInfo,
                                ),
                            synchronous = true,
                        ),
                    )
                }

                OpenFinanceIncentiveType.CASH_IN ->
                    listOf(
                        ParsedNotificationTO(
                            text =
                            notificationContextTemplatesService
                                .getPromoteSweepingAccountCashInMessage(),
                            notification =
                            buildNotificationService
                                .buildPromoteSweepingAccountCashInNotification(
                                    accountId = user.accountId,
                                    mobilePhone = user.id.value,
                                ),
                            synchronous = true,
                        ),
                    )
            }

        return notifications.right()
    }

    private fun shouldNotifyBillComingDue(
        bills: List<BillTO>,
        currentState: BillComingDueHistoryState,
    ): Boolean {
        val conversationHistoryBillIds =
            currentState.walletWithBills.bills.map { it.billId }.toSet()
        val currentBillIds = bills.map { BillId(it.id) }.toSet()

        val allBillIds = conversationHistoryBillIds.union(currentBillIds)

        val billsChanged = allBillIds.size != bills.size

        return bills.isNotEmpty() && (billsChanged || forceSendNotification(currentState))
    }

    private fun forceSendNotification(currentState: BillComingDueHistoryState) =
        notifiedHoursAgo(currentState.internalStateControl.billComingDueNotifiedAt, 2)

    private fun notifiedHoursAgo(
        lastNotification: LocalDateTime?,
        hours: Long,
    ) = lastNotification == null || lastNotification.isBefore(LocalDateTime.now().minusHours(hours))

    private fun createMinimalState(user: User): BillComingDueHistoryState {
        return BillComingDueHistoryState(
            user = user,
            walletWithBills =
            WalletWithBills(
                bills = emptyList(),
                totalWaitingApproval = 0,
                walletId = WalletId("inspection"),
                walletName = "inspection",
                activeConsents = emptyList(),
            ),
            internalStateControl =
            InternalStateControl(
                shouldSynchronizeBeforeCompletion = false,
                billComingDueNotifiedAt = null,
            ),
            subscription = null,
            alreadyPromotedSweepingAccount = false,
            balance = null,
            contacts = emptyList(),
        )
    }

    private fun createMinimalWallet(): WalletWithBills {
        return WalletWithBills(
            bills = emptyList(),
            totalWaitingApproval = 0,
            walletId = WalletId("inspection"),
            walletName = "inspection",
            activeConsents = emptyList(),
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ChatbotNotificationBuilder::class.java)
    }
}

private fun BuildNotificationResult.toParsedNotificationTO(): ParsedNotificationTO? {
    if (!this.shouldSend) {
        return null
    }

    return ParsedNotificationTO(
        text = this.historyMessage,
        notification = this.notification,
    )
}