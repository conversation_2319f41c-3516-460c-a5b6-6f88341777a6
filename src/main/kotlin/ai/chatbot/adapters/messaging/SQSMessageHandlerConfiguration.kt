package ai.chatbot.adapters.messaging

import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties

@ConfigurationProperties("aws.sqs")
class SQSMessageHandlerConfiguration
@ConfigurationInject
constructor(
    override val sqsWaitTime: Int,
    override val sqsCoolDownTime: Int,
    override val visibilityTimeout: Int,
    override val dlqEnabled: Boolean,
    override val maxNumberOfMessages: Int = 10,
) : MessageHandlerConfiguration

interface MessageHandlerConfiguration {
    val sqsWaitTime: Int
    val sqsCoolDownTime: Int
    val visibilityTimeout: Int
    val maxNumberOfMessages: Int
    val dlqEnabled: Boolean
}