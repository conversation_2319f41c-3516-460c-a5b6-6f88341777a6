package ai.chatbot.adapters.jobs

import ai.chatbot.app.config.EachTenant
import ai.chatbot.app.conversation.InteractionWindowService
import ai.chatbot.app.job.TenantAbstractJob
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires

@EachTenant
@Requirements(Requires(beans = [InteractionWindowService::class]), Requires(notEnv = ["test"]))
open class ExpireInteractionWindowsJob(
    private val interactionWindowService: InteractionWindowService,
) :
    TenantAbstractJob(
        cron = "*/10 * * * *",
    ) {
    override fun execute() {
        interactionWindowService.expireAllDue()
    }
}