package ai.chatbot.adapters.jobs

import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Parameter
import jakarta.inject.Singleton
import java.time.Duration
import java.time.Instant
import net.javacrumbs.shedlock.core.LockConfiguration
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.core.SimpleLock
import net.javacrumbs.shedlock.provider.dynamodb2.DynamoDBLockProvider
import software.amazon.awssdk.services.dynamodb.DynamoDbClient

/*
    Para criar um lock novo é necessario adicionar no application.yml (lock.configuration.***)
 */
const val cashInLockProvider = "cash-in"
const val internalBankServiceLockProvider = "internal-bank-service"
const val updateAccountStatusLockProvider = "update-account-status"
const val walletDailyPaymentLimitLockProvider = "wallet-daily-payment-limit"
const val undoInvoiceLockProvider = "undo-invoice"
const val onePixPayNotification = "one-pix-pay-notification"
const val billComingDueLastWarnNotification = "bill-coming-due-last-warn-notification"
const val billTrackingCalculate = "bill-tracking-calculate"
const val billTrackingQuery = "bill-tracking-query"

@Factory
class LockFactory(
    // @Property(name = "lock.tableName") private val tableName: String,
) {
    @Singleton
    fun lockProvider(dynamoDB: DynamoDbClient): LockProvider = DynamoDBLockProvider(dynamoDB, "Shedlock")
}

interface InternalLock {
    fun acquireLock(lockName: String): SimpleLock?

    fun waitForAcquireLock(lockName: String): SimpleLock?
}

class InternalLockProvider(
    private val lockProvider: LockProvider,
    private val lockConfigurationConfiguration: InternalLockConfiguration,
) : InternalLock {
    override fun acquireLock(lockName: String): SimpleLock? {
        return lockProvider.lock(
            LockConfiguration(
                Instant.now(),
                "${lockConfigurationConfiguration.prefix}$lockName",
                lockConfigurationConfiguration.maxDuration,
                lockConfigurationConfiguration.minDuration,
            ),
        ).orElse(null)
    }

    override fun waitForAcquireLock(lockName: String): SimpleLock? {
        while (true) {
            val lock =
                lockProvider.lock(
                    LockConfiguration(
                        Instant.now(),
                        "${lockConfigurationConfiguration.prefix}$lockName",
                        lockConfigurationConfiguration.maxDuration,
                        lockConfigurationConfiguration.minDuration,
                    ),
                ).orElse(null)
            if (lock != null) return lock
        }
    }
}

@EachProperty("lock.configuration")
class InternalLockConfiguration
@ConfigurationInject
constructor(
    @param:Parameter val name: String,
    val maxDuration: Duration,
    val minDuration: Duration,
    val prefix: String,
)