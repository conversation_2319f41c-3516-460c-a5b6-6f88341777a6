package ai.chatbot.adapters.jobs

import ai.chatbot.app.config.EachTenant
import ai.chatbot.app.job.ChatBotDailyLogService
import ai.chatbot.app.job.TenantAbstractJob
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import java.time.LocalDate

@EachTenant
@Requirements(Requires(beans = [ChatBotDailyLogService::class]), Requires(notEnv = ["test"]))
open class ChatLogJob(
    private val chatBotDailyLogService: ChatBotDailyLogService,
) :
    TenantAbstractJob(
        cron = "0 8 * * *",
    ) {
    override fun execute() {
        chatBotDailyLogService.conversationsReportByDate(LocalDate.now().minusDays(1), forceClassify = true)
    }
}

@EachTenant
@Requirements(Requires(beans = [ChatBotDailyLogService::class]), Requires(notEnv = ["test"]))
open class ChatSummaryJob(
    private val chatBotDailyLogService: ChatBotDailyLogService,
) :
    TenantAbstractJob(
        cron = "0 9 * * MON",
    ) {
    override fun execute() {
        chatBotDailyLogService.sendSummaryByDateRange(LocalDate.now().minusDays(7), LocalDate.now().minusDays(1))
    }
}