package ai.chatbot.app.bill

import ai.chatbot.adapters.billPayment.BillStatus
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.brazilTimeZone
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime

data class BillView(
    val billId: BillId,
    val externalBillId: Int,
    val assignor: String?,
    val recipient: Recipient?,
    val billDescription: String,
    val amount: Long,
    val discount: Long,
    val interest: Long,
    val fine: Long,
    val amountTotal: Long,
    val billType: BillType,
    val scheduledInfo: ScheduledInfo,
    val paymentLimitTime: String,
    val dueDate: LocalDate,
    val subscriptionFee: Boolean,
    val status: BillStatus,
) {
    val isOverdue: Boolean
        get() {
            val time = paymentLimitTime
            val zonedDueDateTime = ZonedDateTime.of(dueDate.atTime(LocalTime.parse(time)), brazilTimeZone)
            return (getZonedDateTime().isAfter(zonedDueDateTime))
        }
}

enum class ScheduledInfo { NOT_SCHEDULED, SCHEDULED, WAITING_FUNDS }

fun List<BillView>.orderByExternalBillId(): List<BillView> {
    return this.sortedBy { it.externalBillId }
}