package ai.chatbot.app.bill

const val FICHA_DIGITABLE_LINE_SIZE = 47
const val CONCESSIONARIA_DIGITABLE_LINE_SIZE = 48

class BarCode(
    val number: String,
    val digitable: String,
) {
    fun formattedDigitable(): String {
        return when (digitable.length) {
            FICHA_DIGITABLE_LINE_SIZE -> "${digitable.substring(0, 5)}.${
                digitable.substring(
                    5,
                    10,
                )
            } ${digitable.substring(10, 15)}.${digitable.substring(15, 21)} ${
                digitable.substring(
                    21,
                    26,
                )
            }.${digitable.substring(26, 32)} ${digitable.substring(32, 33)} ${digitable.substring(33)}"

            CONCESSIONARIA_DIGITABLE_LINE_SIZE -> "${digitable.substring(0, 11)}-${
                digitable.substring(
                    11,
                    12,
                )
            } ${digitable.substring(12, 23)}-${digitable.substring(23, 24)} ${
                digitable.substring(
                    24,
                    35,
                )
            }-${digitable.substring(35, 36)} ${digitable.substring(36, 47)}-${digitable.substring(47)}"

            else -> throw IllegalArgumentException("digitable line should be $FICHA_DIGITABLE_LINE_SIZE or $CONCESSIONARIA_DIGITABLE_LINE_SIZE")
        }
    }

    fun checkIsConcessionaria(): Boolean {
        return digitable.length == CONCESSIONARIA_DIGITABLE_LINE_SIZE
    }

    fun billType(): BillType {
        return if (this.checkIsConcessionaria()) BillType.CONCESSIONARIA else BillType.FICHA_COMPENSACAO
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as BarCode

        if (number != other.number) return false
        if (digitable != other.digitable) return false

        return true
    }

    override fun hashCode(): Int {
        var result = number.hashCode()
        result = 31 * result + digitable.hashCode()
        return result
    }

    companion object {
        fun of(number: String): BarCode {
            return BarCode(number = number, digitable = convertToDigitable(number))
        }

        fun ofDigitable(digitable: String): BarCode {
            return BarCode(number = convertToBarCode(digitable), digitable = digitable)
        }
    }
}

private fun convertToDigitable(number: String): String {
    return when (number.substring(0, 1)) {
        "8" -> convertConcessionariaToDigitable(number)
        else -> convertFichaToDigitable(number)
    }
}

private fun convertFichaToDigitable(number: String): String {
    val fields =
        listOf(
            number.substring(0, 4) + number.substring(19, 24),
            number.substring(24, 34),
            number.substring(34, 44),
            number.substring(4, 19),
        )
    return fields[0] + mod10(fields[0]) +
        fields[1] + mod10(fields[1]) +
        fields[2] + mod10(fields[2]) +
        fields[3]
}

private fun convertConcessionariaToDigitable(number: String): String {
    val fields =
        listOf(number.substring(0, 11), number.substring(11, 22), number.substring(22, 33), number.substring(33, 44))
    return when (number.substring(2, 3)) {
        "6", "7" ->
            fields[0] + mod10(fields[0]) +
                fields[1] + mod10(fields[1]) +
                fields[2] + mod10(fields[2]) +
                fields[3] + mod10(fields[3])

        "8", "9" ->
            fields[0] + mod11(fields[0]) +
                fields[1] + mod11(fields[1]) +
                fields[2] + mod11(fields[2]) +
                fields[3] + mod11(fields[3])

        else -> throw java.lang.IllegalArgumentException("Invalid barcode")
    }
}

private fun convertToBarCode(digitable: String): String {
    return when (digitable.length) {
        FICHA_DIGITABLE_LINE_SIZE ->
            digitable.substring(0, 4) + digitable.substring(32, 47) +
                digitable.substring(
                    4,
                    9,
                ) + digitable.substring(10, 20) + digitable.substring(21, 31)

        CONCESSIONARIA_DIGITABLE_LINE_SIZE ->
            digitable.substring(0, 11) +
                digitable.substring(
                    12,
                    23,
                ) + digitable.substring(24, 35) + digitable.substring(36, 47)

        else -> throw IllegalArgumentException("digitable line should be $FICHA_DIGITABLE_LINE_SIZE or $CONCESSIONARIA_DIGITABLE_LINE_SIZE")
    }
}

private fun mod10(number: String): String {
    val sum =
        number.reversed().asIterable().withIndex().sumOf {
            when {
                it.index % 2 == 1 -> Character.getNumericValue(it.value)
                Character.getNumericValue(it.value) >= 5 -> (Character.getNumericValue(it.value) * 2) - 9
                else -> Character.getNumericValue(it.value) * 2
            }
        }
    return when (val sumResult = 10 - (sum % 10)) {
        10 -> "0"
        else -> sumResult.toString()
    }
}

private fun mod11(number: String): String {
    val multipliers = listOf(2, 3, 4, 5, 6, 7, 8, 9, 2, 3, 4)
    val sum =
        number.reversed().asIterable().withIndex().sumOf {
            Character.getNumericValue(it.value) * multipliers[it.index]
        }
    return when (val sumResult = 11 - (sum % 11)) {
        10, 11 -> "0"
        else -> sumResult.toString()
    }
}

fun String.toBarCode(): BarCode {
    return if (this.length == 44) {
        BarCode.of(this)
    } else {
        BarCode.ofDigitable(this)
    }
}