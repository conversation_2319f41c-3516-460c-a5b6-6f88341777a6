package ai.chatbot.app.bill

import ai.chatbot.app.HistoryStateRepository
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.stream.Collectors.toSet
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class PendingBillsService(
    private val historyStateRepository: HistoryStateRepository,
    private val paymentAdapter: PaymentAdapter,
) {
    open fun updatePendingBills(
        userId: UserId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): Either<PaymentAdapterError, UpdatePendingBillsResult> {
        val markers = Markers.append("userId", userId.value).andAppend("startDate", startDate).andAppend("endDate", endDate)
        val logName = "PendingBillsService#updatePendingBills"
        val result =
            runBlocking {
                paymentAdapter.findPendingBills(userId = userId, startDate = startDate, endDate = endDate)
            }.getOrElse {
                logger.warn(markers.andAppend("error", it), logName)
                return it.left()
            }

        val currentState =
            when (val state = historyStateRepository.findLatest(userId)) {
                is BillComingDueHistoryState -> state
            }
        val currentStateBills = currentState.walletWithBills.bills

        val pendingBillResult = result.wallet.bills

        markers.andAppend("currentStateBills", currentStateBills).andAppend("pendingBillResult", pendingBillResult)

        val billsUpdated = currentStateBills.map { Pair(it.billId.value, it.amountTotal) }.sortedBy { it.first }.toSet() != pendingBillResult.map { Pair(it.billId.value, it.amountTotal) }.sortedBy { it.first }.toSet()
        if (billsUpdated) {
            logger.info(
                Markers
                    .append("userId", userId.value)
                    .andAppend("startDate", startDate)
                    .andAppend("endDate", endDate)
                    .andAppend("oldBills", currentStateBills.map { "${it.billId.value}, ${it.amountTotal}" })
                    .andAppend("newBills", pendingBillResult.map { "${it.billId.value}, ${it.amountTotal}" }),
                "$logName/billsUpdated",
            )

            val walletId = if (result.wallet.walletId?.value.isNullOrEmpty()) {
                currentState.walletWithBills.walletId
            } else {
                result.wallet.walletId
            }

            val walletName = if (result.wallet.walletName.isNullOrEmpty()) {
                currentState.walletWithBills.walletName
            } else {
                result.wallet.walletName
            }

            historyStateRepository.save(
                userId = userId,
                state =
                currentState.copy(
                    user = result.user,
                    lastBillsUpdatedAt = ZonedDateTime.now(),
                    walletWithBills = result.wallet.copy(walletId = walletId, walletName = walletName, bills = pendingBillResult),
                    startDate = startDate,
                    endDate = endDate,
                ),
            )
        }
        logger.info(markers.andAppend("billsUpdated", billsUpdated), logName)
        return UpdatePendingBillsResult(userAndWallet = result, billsUpdated = billsUpdated).right()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(PendingBillsService::class.java)
    }
}

data class UpdatePendingBillsResult(
    val userAndWallet: UserAndWallet,
    val billsUpdated: Boolean,
)