package ai.chatbot.app.featureflag

import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy::class)
data class FeatureFlags(
    val waCommCentre: FeatureFlag,
    val dailyLogS3: FeatureFlag,
    val sendViaWaCommCentre: FeatureFlag,
    val userEventsHttp: FeatureFlag,
)

data class FeatureFlag(
    val enabled: FeatureFlagStatus = FeatureFlagStatus.NO_ONE,
    val whitelist: List<String>? = null,
    val groups: List<String>? = null,
) {
    fun check(userId: UserId = UserId(""), groups: List<String> = emptyList()): Boolean {
        return when (enabled) {
            FeatureFlagStatus.NO_ONE -> false
            FeatureFlagStatus.EVERYONE -> true
            FeatureFlagStatus.SOME -> resolveWhitelist(userId) || resolveGroups(groups)
        }
    }

    fun check(user: User) = check(user.id, user.accountGroups)

    fun check() = enabled == FeatureFlagStatus.EVERYONE

    private fun resolveWhitelist(userId: UserId): Boolean {
        return whitelist?.let {
            userId.value in it
        } ?: false
    }

    private fun resolveGroups(userGroups: List<String>): Boolean {
        return groups?.any { it in userGroups } ?: false
    }
}

enum class FeatureFlagStatus {
    NO_ONE, SOME, EVERYONE
}