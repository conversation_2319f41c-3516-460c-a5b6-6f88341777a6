
@file:Suppress("ktlint:standard:filename")

package ai.chatbot.app

import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(
    include = JsonTypeInfo.As.PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "instanceType",
    visible = true,
)
abstract class PrintableSealedClass {
    override fun toString(): String {
        return this::class.simpleName ?: super.toString()
    }
}