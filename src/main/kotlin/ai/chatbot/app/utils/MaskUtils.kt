package ai.chatbot.app.utils

fun maskConversationMessage(message: String?): String {
    return try {
        maskConversationMessageInternal(message)
    } catch (e: Exception) {
        message ?: ""
    }
}

private fun maskConversationMessageInternal(message: String?): String {
    if (message == null) {
        return ""
    }

    val namesRegex = Regex("(\\p{N})(\\P{N}*)(no valor de)")
    val amountRegex = Regex("(?<=R\\\$[ \\xA0])([0-9.,]+)")

    val withoutNames =
        namesRegex.replace(message) {
            val (_, unicode, recipient, finalText) = it.groupValues
            "$unicode\uFE0F⃣ ${"*".repeat(recipient.length - 4)} $finalText"
        }

    return amountRegex.replace(withoutNames) {
        it.value.replace(Regex("[0-9]"), "*")
    }
}