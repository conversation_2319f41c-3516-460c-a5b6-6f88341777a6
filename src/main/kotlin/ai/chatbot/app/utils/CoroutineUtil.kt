package ai.chatbot.app.utils

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.withContext

suspend fun <A, B> Iterable<A>.parallelMap(f: suspend (A) -> B): List<B> = coroutineScope {
    parallel(f)
}

// to avoid linter problem
// Criação de novo contexto! Não mantém o contexto anterior. Ex: Não propaga a tenant
suspend fun <A, B> Iterable<A>.parallel(f: suspend (A) -> B): List<B> = withContext(Dispatchers.IO) {
    map { async { f(it) } }.awaitAll()
}