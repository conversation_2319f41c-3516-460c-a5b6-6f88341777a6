package ai.chatbot.app.utils

fun isProbablyEmoji(c: Char): <PERSON><PERSON><PERSON> {
    // Contém blocos BASIC_LATIN, LATIN_1_SUPPLEMENT, etc.
    return !Character.UnicodeBlock.of(c).toString().contains("LATIN")
}

// Vai falhar pra mensagens com caracteres em outros scripts como cirílico, kanji etc - Bom o suficiente para a nossa aplicação
fun String.containsOnlyEmojis(): <PERSON><PERSON><PERSON> {
    return this.isNotBlank() && this.replace("\\s+".toRegex(), "").firstOrNull { !isProbablyEmoji(it) } == null
}