package ai.chatbot.app.utils

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper

fun getObjectMapper(): ObjectMapper =
    jacksonObjectMapper()
        .findAndRegisterModules()
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE)
        .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
        .enable(SerializationFeature.WRITE_DATES_WITH_ZONE_ID)

inline fun <reified T> parseObjectFrom(string: String): T {
    return getObjectMapper()
        .readerFor(T::class.java).readValue(string)
}

inline fun <reified T> parseListFrom(string: String): List<T> {
    return getObjectMapper()
        .readerForListOf(T::class.java).readValue(string)
}

inline fun <reified T> parseMapFrom(string: String): Map<String, T> {
    return getObjectMapper()
        .readerForMapOf(T::class.java).readValue(string)
}