package ai.chatbot.app.utils

import ai.chatbot.app.classification.ConversationTag

// Faz a contagem de tags que apareceram junto com uma tag na lista de conversas
// Ex: calculateTagTable(PIX, classifications)[CONTA_CONECTADA] => retorna o número de vezes que CONTA_CONECTADA apareceu em conversas que tiveram a tag PIX
fun calculateTagTable(
    tag: ConversationTag,
    classifications: List<List<ConversationTag>>,
): Map<ConversationTag, Int> {
    return countTags(classifications.filter { tag in it })
}

// Faz a contagem das tags nas conversas
// Ex: countTags(classifications)[PIX] => retorna o número de vezes que a tag PIX apareceu na lista de conversas
fun countTags(
    classifications: List<List<ConversationTag>>,
): Map<ConversationTag, Int> {
    val resultsCount = if (classifications.isNotEmpty()) {
        classifications
            .map { it.associateWith { 1 } }
            .reduce { acc, tags ->
                acc.toMutableMap().apply {
                    tags.forEach { (tag, count) ->
                        this[tag] = (this[tag] ?: 0) + count
                    }
                }
            }
    } else {
        emptyMap()
    }

    return ConversationTag.values()
        .associateWith { resultsCount[it] ?: 0 }
}