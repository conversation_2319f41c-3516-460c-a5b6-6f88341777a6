package ai.chatbot.app.utils

import java.time.Instant
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

val brazilTimeZone: ZoneId = ZoneId.of("Brazil/East")

val dateFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
val brazilDateFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")
val brazilDateFormatWithoutYear: DateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM")
val dateTimeFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
val timestampFormatWithBrazilTimeZone: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS VV").withZone(brazilTimeZone)
val dateFormatWithBrazilTimeZone: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(brazilTimeZone)

object BrazilZonedDateTimeSupplier {
    fun getZonedDateTime(): ZonedDateTime = ZonedDateTime.now(brazilTimeZone)

    fun getLocalDate(): LocalDate = getZonedDateTime().toLocalDate()

    fun getEpochMili(): Long = getZonedDateTime().toInstant().toEpochMilli()

    fun zonedDateTimeFromMillis(epochMillis: Long): ZonedDateTime =
        ZonedDateTime.ofInstant(Instant.ofEpochMilli(epochMillis), brazilTimeZone)

    fun getLocalTime(): LocalTime = getZonedDateTime().toLocalTime()

    fun getCurrentDate(): String = dateFormat.format(getZonedDateTime())

    fun parseDate(dateText: String): LocalDate = LocalDate.parse(dateText, dateFormatWithBrazilTimeZone)

    fun formatWithBrazilTimeZone(dateTime: LocalDate): String = dateFormatWithBrazilTimeZone.format(dateTime)

    fun parseDateTextAsZonedDateTime(dateText: String): ZonedDateTime =
        LocalDate.parse(dateText, dateFormatWithBrazilTimeZone).atStartOfDay().atZone(brazilTimeZone)

    fun parseToZonedDateTime(dateText: String): ZonedDateTime =
        ZonedDateTime.parse(dateText, DateTimeFormatter.ISO_ZONED_DATE_TIME)

    fun getCurrentDayOfWeek(): String = getZonedDateTime().dayOfWeek.getDisplayName(java.time.format.TextStyle.FULL, java.util.Locale("pt", "BR"))
    fun getDayOfWeek(localDate: LocalDate): String = localDate.dayOfWeek.getDisplayName(java.time.format.TextStyle.FULL, java.util.Locale("pt", "BR"))

    fun formatToZonedDateTime(dateTime: ZonedDateTime): String = dateTime.format(DateTimeFormatter.ISO_ZONED_DATE_TIME)
}