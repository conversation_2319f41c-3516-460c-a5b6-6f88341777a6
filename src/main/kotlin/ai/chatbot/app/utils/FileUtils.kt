package ai.chatbot.app.utils

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper

internal class FileUtils {
    companion object {
        fun readLocalFileAsText(path: String) = Thread.currentThread().contextClassLoader.getResource(path)?.readText()

        fun readLocalFileAsStream(path: String) = Thread.currentThread().contextClassLoader.getResourceAsStream(path)

        inline fun <reified T> loadAndParseList(path: String): List<T> =
            runCatching {
                val content = Thread.currentThread().contextClassLoader.getResource(path)
                val converted =
                    jacksonObjectMapper()
                        .readerForListOf(T::class.java)
                        .readValue<List<T>>(content)
                return@runCatching converted
            }.getOrThrow()
    }
}