package ai.chatbot.app.utils

import ai.chatbot.app.NotificationService
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.user.User

fun notifyTransactionProcessing(user: User, notificationService: NotificationService, conversationHistoryService: ConversationHistoryService) {
    val message = "Já estou processando seu pedido. Informarei o resultado em breve."
    notificationService.notify(
        userId = user.id,
        accountId = user.accountId,
        message,
    )
    conversationHistoryService.createAssistantMessage(user.id, message)
}

fun notifyTransactionCanceled(user: User, notificationService: NotificationService, conversationHistoryService: ConversationHistoryService) {
    val message = "Entendi. Se precisar de mais alguma coisa, estou à disposição."
    notificationService.notify(
        userId = user.id,
        accountId = user.accountId,
        message,
    )
    conversationHistoryService.createAssistantMessage(user.id, message)
}