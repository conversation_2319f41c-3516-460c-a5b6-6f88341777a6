package ai.chatbot.app.utils

import ai.chatbot.app.classification.ConversationActionStatus
import ai.chatbot.app.conversation.ChatMessageWrapper
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.job.DailyLogErrorSummary
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.core.util.StringUtils
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.ZonedDateTime
import kotlin.system.measureTimeMillis
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers

fun LogstashMarker.andAppend(
    fieldName: String,
    `object`: Any?,
): LogstashMarker {
    return and(Markers.append(fieldName, `object`))
}

fun getEmojiForNumber(number: String): String {
    return when (number) {
        "0" -> "0\uFE0F⃣" // 0️⃣
        "1" -> "1\uFE0F⃣" // 1️⃣
        "2" -> "2\uFE0F⃣" // 2️⃣
        "3" -> "3\uFE0F⃣" // 3️⃣
        "4" -> "4\uFE0F⃣" // 4️⃣
        "5" -> "5\uFE0F⃣" // 5️⃣
        "6" -> "6\uFE0F⃣" // 6️⃣
        "7" -> "7\uFE0F⃣" // 7️⃣
        "8" -> "8\uFE0F⃣" // 8️⃣
        "9" -> "9\uFE0F⃣" // 9️⃣
        else -> throw IllegalArgumentException("Número deve estar entre 0 e 9")
    }
}

fun maskDocumentSpecialAsterisk(document: String?): String {
    return if (StringUtils.isEmpty(document)) {
        ""
    } else {
        if (isCPF(document!!)) {
            document.replace(
                "([0-9]{3})([0-9]{3})([0-9]{3})([0-9]{2})".toRegex(),
                "∗∗∗\\.$2\\.$3-∗∗",
            )
        } else {
            document.replace("([0-9]{2})([0-9]{3})([0-9]{3})([0-9]{4})([0-9]{2})".toRegex(), "$1\\.$2\\.$3/$4-$5")
        }
    }
}

fun isCPF(document: String): Boolean {
    return StringUtils.isNotEmpty(document) && document.length == 11
}

fun Long?.formattedBalance() = this?.formattedAmount() ?: "Indisponível."

fun Long.formattedAmount(): String {
    val amount = this.toBigDecimal().divide(BigDecimal(100)).setScale(2, RoundingMode.FLOOR).toPlainString().replace(".", ",")
    return "R$ $amount"
}

fun validateCPF(document: String): Boolean {
    if (document.isEmpty()) return false

    val numbers =
        document.filter { it.isDigit() }.map {
            it.toString().toInt()
        }

    if (numbers.size != 11) return false

    if (numbers.all { it == numbers[0] }) return false

    val dv1 =
        ((0..8).sumOf { (it + 1) * numbers[it] }).rem(11).let {
            if (it >= 10) 0 else it
        }

    val dv2 =
        ((0..8).sumOf { it * numbers[it] }.let { (it + (dv1 * 9)).rem(11) }).let {
            if (it >= 10) 0 else it
        }

    return numbers[9] == dv1 && numbers[10] == dv2
}

fun includeConversationInPrompt(prompt: String, messages: List<ChatMessageWrapper>): String {
    val formattedConversation = messages
        .filter { it.message != null }
        .joinToString("\n\n") {
            val sender = when (it.type) {
                MessageType.REACTION,
                MessageType.USER,
                -> "USUÁRIO"

                MessageType.ASSISTANT -> "ASSISTENTE"
                MessageType.SYSTEM -> "SISTEMA"
                MessageType.FUNCTION -> "???"
            }

            "$sender:\n${it.message}"
        }

    return prompt.replace("{{messages}}", formattedConversation)
}

fun includeActionsInPrompt(prompt: String, summaries: List<DailyLogErrorSummary>): String {
    val actions = summaries.flatMap { it.actions }

    val failedActions = actions
        .filter { it.status == ConversationActionStatus.FAILED }
        .joinToString("\n\n") {
            "${it.acao} - ${it.entendimento}"
        }

    val notSupportedActions = actions
        .filter { it.status == ConversationActionStatus.NOT_SUPPORTED }
        .joinToString("\n\n") {
            "${it.acao} - ${it.entendimento}"
        }

    val totalConversations = summaries.sumOf { it.totalConversations }
    val totalActions = summaries.sumOf { it.totalActions }

    return prompt
        .replace("{{notSupported}}", notSupportedActions)
        .replace("{{failed}}", failedActions)
        .replace("{{totalConversations}}", totalConversations.toString())
        .replace("{{totalActions}}", totalActions.toString())
}

inline fun <T> measure(fn: () -> T): Pair<T, Long> {
    val result: T
    val elapsed = measureTimeMillis { result = fn() }
    return result to elapsed
}

fun timeSince(startTime: ZonedDateTime): Long {
    return getZonedDateTime().toInstant().toEpochMilli() - startTime.toInstant().toEpochMilli()
}

fun buildUnregisteredUserAndWallet(userId: UserId): UserAndWallet =
    UserAndWallet(
        user = User(
            accountId = AccountId(""),
            id = userId,
            name = "",
            accountGroups = emptyList(),
            status = AccountStatus.NOT_REGISTERED,
            paymentStatus = AccountPaymentStatus.UpToDate,
        ),
        wallet = WalletWithBills(
            bills = emptyList(),
            walletId = WalletId(""),
            walletName = "Sem carteira",
            activeConsents = emptyList(),
            totalWaitingApproval = 0,
        ),
    )

fun deepMerge(
    source: Map<String, Any>,
    target: Map<String, Any>,
): Map<String, Any> {
    val sourceMMap = source.toMutableMap()

    for ((targetKey, targetValue) in target) {
        val sourceValue = sourceMMap[targetKey]

        sourceMMap[targetKey] = when {
            sourceValue is Map<*, *> && targetValue is Map<*, *> ->
                deepMerge(
                    sourceValue as Map<String, Any>,
                    targetValue as Map<String, Any>,
                )

            else -> targetValue
        }
    }
    return sourceMMap
}