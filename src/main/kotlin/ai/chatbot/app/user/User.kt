@file:Suppress("ktlint:standard:filename")

package ai.chatbot.app.user

import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.SweepingConsentPeriodicLimitUsage
import ai.chatbot.app.SweepingConsentPeriodicUsage
import ai.chatbot.app.bill.BillView

data class UserId(
    val value: String,
) {
    fun toExternalId() = "$<EMAIL>"

    companion object {
        fun fromMsisdn(value: String): UserId {
            val regex = Regex("\\d+")

            val matchResult =
                regex
                    .findAll(value)
                    .map {
                        it.value
                    }.joinToString(separator = "")

            if (matchResult.length < 11) {
                throw Exception("Cannot create user id from string")
            }

            return UserId(matchResult)
        }
    }
}

data class WalletId(
    val value: String,
)

data class AccountId(
    val value: String,
)

enum class AccountStatus {
    ACTIVE,
    CLOSED,
    REGISTER_INCOMPLETE,
    DENIED,
    UNDER_REVIEW,
    UNDER_EXTERNAL_REVIEW,
    APPROVED,
    BLOCKED,
    NOT_REGISTERED, // Usuários que não estão na base
}

enum class AccountPaymentStatus {
    // todas as assinaturas em dia
    UpToDate,

    // assinatura vencida ha menos de 7 dias
    PastDue,

    // assinatura vencida ha 7 dias ou mais
    Overdue,
}

data class
User(
    val accountId: AccountId,
    val id: UserId,
    val name: String,
    val accountGroups: List<String>,
    val status: AccountStatus,
    val paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
) {
    val isEarlyAccess = accountGroups.contains("ALPHA")
    val isInvestmentCampaign = accountGroups.contains("INVESTMENT_CAMPAIGN")
    val isRawTemplateEnabled = accountGroups.contains("DEVELOPER")
}

data class UserAndWallet(
    val user: User,
    val wallet: WalletWithBills,
)

data class WalletWithBills(
    val bills: List<BillView>,
    val walletId: WalletId?,
    val walletName: String?,
    val activeConsents: List<SweepingConsent> = emptyList(),
    val totalWaitingApproval: Int = 0,
) {
    val hasSweepingAccount = activeConsents.isNotEmpty()

    fun hasSweepingAccountFor(amount: Long) = activeConsents.any { it.transactionLimit >= amount }
    fun sweepingConsentsWithTransactionLimitFor(amount: Long) = activeConsents.filter {
        it.transactionLimit >= amount && it.periodicUsage.availableFor(amount)
    }

    private fun SweepingConsentPeriodicUsage?.availableFor(amount: Long): Boolean {
        return if (this == null) {
            true
        } else {
            ((totalLimit - totalUsed) >= amount) &&
                daily.availableFor(amount) &&
                weekly.availableFor(amount) &&
                monthly.availableFor(amount) &&
                yearly.availableFor(amount)
        }
    }

    private fun SweepingConsentPeriodicLimitUsage.availableFor(amount: Long): Boolean {
        return ((quantityLimit - quantityUsed) > 0) && ((amountLimit - amountUsed) >= amount)
    }
}

data class UserActivity(
    val type: UserActivityType,
    val value: Boolean,
)

enum class UserActivityType {
    PromotedSweepingAccountOptOut,
}