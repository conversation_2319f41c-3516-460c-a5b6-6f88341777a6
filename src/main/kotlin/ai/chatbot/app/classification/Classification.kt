package ai.chatbot.app.classification

data class ClassificationResult(val tags: List<ConversationTag>, val entendimento: Map<ConversationTag, String>, val acoes: List<ConversationAction> = listOf())

enum class ConversationTag(val emoji: String, val type: ConversationTagType = ConversationTagType.OTHER) {
    SPAM("🤖"),
    PROBLEMAS_TECNICOS("🚨"),
    CANCELAMENTO_DE_CONTA("📵"),
    FORA_DE_CONTEXTO("🍄"),
    APLICATIVO("📱"),
    JSON_PARSE_ERROR("🆘"),
    REMINDER("⏰"),
    BOTAO_DIA_ANTERIOR("⛓\uFE0F\u200D\uD83D\uDCA5"),
    FEATURE_REQUEST("\uD83E\uDDD0"),
    NON_USER("\uD83D\uDC64"),
    ONBOARDING("\uD83C\uDD95", ConversationTagType.FEATURE),
    PAGAMENTO_CONTAS("\uD83D\uDCB5", ConversationTagType.FEATURE),
    PIX("\uD83C\uDD7F\uFE0F", ConversationTagType.FEATURE),
    MARCAR_COMO_PAGO("✔\uFE0F", ConversationTagType.FEATURE),
    CONTA_CONECTADA("\uD83D\uDD17", ConversationTagType.FEATURE),
    ONE_PIX_PAY("1\uFE0F⃣", ConversationTagType.FEATURE),
    LANCAMENTO_MANUAL("\uD83D\uDCDD", ConversationTagType.FEATURE),
    USUARIO_ATENDIDO("\uD83D\uDFE2", ConversationTagType.SCORE),
    USUARIO_NAO_ATENDIDO("\uD83D\uDD34", ConversationTagType.SCORE),
    OUTROS("🤷"),
}

enum class ConversationTagType {
    FEATURE, SCORE, OTHER,
}

data class ConversationAction(
    val acao: String,
    val fonte: ConversationActionSource,
    val status: ConversationActionStatus,
    val entendimento: String,
)

enum class ConversationActionSource(val emoji: String) {
    USER("\uD83D\uDC64"), ASSISTANT("\uD83E\uDD16"),
}

enum class ConversationActionStatus(val emoji: String) {
    DONE("✅"), FAILED("\uD83D\uDEA8"), IGNORED("❓"), CANCELLED("❌"), NOT_SUPPORTED("\uD83E\uDDD0"),
}