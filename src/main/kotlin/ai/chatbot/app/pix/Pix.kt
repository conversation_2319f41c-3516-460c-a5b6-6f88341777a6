package ai.chatbot.app.pix

import ai.chatbot.app.utils.validateCPF
import java.util.regex.Pattern

const val uuidRegexMatcher = "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\$"

enum class PixKeyType(val regex: String, val displayName: String) {
    CNPJ("^[0-9]{14}\$", "CNPJ"),
    PHONE("^\\+[1-9][0-9]\\d{1,14}\$", "Telefone"),
    EMAIL("^[a-z0-9.!#\$%&‘*+/=?^_`{|}~-]+@[a-z0-9-]+(?:\\.[a-z0-9-]+)*\$", "E-mail"),
    EVP(uuidRegexMatcher, "Chave aleatória"),
    COPY_PASTE("(?i).*br\\.gov\\.bcb\\.pix.*", "Copia e cola"),
    CPF("^[0-9]{11}\$", "CPF"),
}

class PixKey(value: String, val type: PixKeyType) {
    val value: String = value.takeIf { type == PixKeyType.COPY_PASTE } ?: value.lowercase().sanitizeValue(type)

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as PixKey

        if (type != other.type) return false
        if (value != other.value) return false

        return true
    }

    override fun hashCode(): Int {
        var result = type.hashCode()
        result = 31 * result + value.hashCode()
        return result
    }

    private fun String.sanitizeValue(type: PixKeyType): String {
        return when (type) {
            PixKeyType.CNPJ, PixKeyType.CPF -> {
                this
                    .replace("[^A-Za-z0-9]".toRegex(), "")
            }
            PixKeyType.PHONE -> {
                if (this.startsWith("55") && this.length == 13) return "+$this"
                if (this.length == 11) return "+55$this"
                return this
            }
            PixKeyType.EVP, PixKeyType.EMAIL, PixKeyType.COPY_PASTE -> this
        }
    }
}

fun PixKey.isValid() = Pattern.compile(type.regex).matcher(value).matches()

fun String?.toPixKeyType(key: String?): PixKeyType {
    val string = this ?: "ELEVEN_DIGIT"
    val type = if (string == "ELEVEN_DIGIT") {
        val isCPF = validateCPF(key ?: "")
        if (isCPF) "CPF" else "PHONE"
    } else {
        string
    }

    return PixKeyType.valueOf(type)
}

data class PixQRCode(val value: String)

data class AssistantPaymentLimit(
    val amount: Long,
    val enabled: Boolean,
)