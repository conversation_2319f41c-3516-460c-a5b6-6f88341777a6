package ai.chatbot.app.transaction

import ai.chatbot.adapters.api.TransactionPaymentStatus
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.PrintableSealedClass
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.SweepingRequest
import ai.chatbot.app.TransactionRepository
import ai.chatbot.app.TransactionService
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.conversation.SweepingLimitType
import ai.chatbot.app.conversation.SweepingTransferErrorReason
import ai.chatbot.app.media.BoletoInfo
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.pix.PixQRCode
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.utils.andAppend
import arrow.core.getOrElse
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.util.UUID
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class DefaultTransactionService(
    private val transactionRepository: TransactionRepository,
    private val paymentAdapter: PaymentAdapter,
) : TransactionService {
    private val logger = LoggerFactory.getLogger(DefaultTransactionService::class.java)

    override fun create(
        userId: UserId,
        walletId: WalletId,
        details: TransactionDetails,
        transactionGroupId: TransactionGroupId?,
    ): Transaction {
        val markers =
            Markers.append("userId", userId.value)
                .andAppend("walletId", walletId.value)
                .andAppend("details", details)
                .andAppend("transactionGroupId", transactionGroupId?.value)

        val transaction =
            Transaction(
                id = TransactionId(),
                groupId = transactionGroupId ?: TransactionGroupId(),
                userId = userId,
                walletId = walletId,
                status = TransactionStatus.ACTIVE,
                details = details,
                paymentStatus = TransactionPaymentStatus.UNKNOWN,
            )
        markers.andAppend("transaction", transaction)

        val activeTransactions = transactionRepository.find(userId, TransactionStatus.ACTIVE)
        markers.andAppend("activeTransactions", activeTransactions.map { it.id.value })

        activeTransactions.filter {
            it.groupId != transaction.groupId
        }.forEach {
            transactionRepository.save(it.copy(status = TransactionStatus.EXPIRED))
        }

        transactionRepository.save(transaction)

        logger.info(markers, "TransactionService#create")
        return transaction
    }

    override fun create(userId: UserId, details: TransactionDetails, transactionGroupId: TransactionGroupId?): Transaction {
        val balance = runBlocking {
            paymentAdapter.getBalanceAndForecast(userId, null).getOrElse {
                logger.error("Error getting balance and forecast", it)

                throw IllegalStateException("Error getting balance and forecast")
            }
        }

        return create(userId, balance.walletId, details, transactionGroupId)
    }

    override fun cancel(
        transactionId: TransactionId,
        userId: UserId,
    ): TransactionResult {
        val markers =
            Markers.append("transactionId", transactionId.value)
                .andAppend("userId", userId.value)

        val transaction = transactionRepository.find(transactionId)
        markers.andAppend("transaction", transaction)

        if (transaction == null) {
            logger.warn(markers, "TransactionService#cancel")
            return TransactionResult.TransactionNotFound
        }

        return when {
            transaction.userId != userId -> TransactionResult.IllegalUser(transaction.userId)
            transaction.status != TransactionStatus.ACTIVE -> TransactionResult.IllegalState(transaction.status, transaction.paymentStatus)
            else -> {
                transactionRepository.save(transaction.copy(status = TransactionStatus.CANCELED))

                transactionRepository.find(userId, TransactionStatus.ACTIVE).filter {
                    it.groupId == transaction.groupId
                }.forEach {
                    transactionRepository.save(it.copy(status = TransactionStatus.CANCELED))
                }

                TransactionResult.Canceled
            }
        }.also {
            markers.andAppend("result", it)
            logger.info(markers, "TransactionService#cancel")
        }
    }

    override fun confirm(
        transactionId: TransactionId,
        userId: UserId,
        authorizationToken: String?,
    ): TransactionResult {
        val markers = Markers.append("transactionId", transactionId.value)
            .andAppend("userId", userId.value)
            .andAppend("authorizationToken", authorizationToken)

        val transaction = transactionRepository.find(transactionId)
        markers.andAppend("transaction", transaction)

        if (transaction == null) {
            logger.warn(markers, "TransactionService#confirm")
            return TransactionResult.TransactionNotFound
        }

        return when {
            transaction.userId != userId -> TransactionResult.IllegalUser(transaction.userId)
            transaction.status != TransactionStatus.ACTIVE -> TransactionResult.IllegalState(transaction.status, transaction.paymentStatus)
            else -> {
                transaction.copy(
                    authorizationToken = authorizationToken,
                ).also {
                    transactionRepository.save(it)
                }.execute(retry = false)
            }
        }.also {
            markers.andAppend("result", it)
            logger.info(markers, "TransactionService#confirm")
        }
    }

    override fun retry(
        transactionId: TransactionId,
        userId: UserId,
    ): TransactionResult {
        val markers = Markers.append("transactionId", transactionId.value)
            .andAppend("userId", userId.value)

        val transaction = transactionRepository.find(transactionId)
        markers.andAppend("transaction", transaction)

        if (transaction == null) {
            logger.warn(markers, "TransactionService#retry")
            return TransactionResult.TransactionNotFound
        }

        return when {
            transaction.userId != userId -> TransactionResult.IllegalUser(transaction.userId)
            transaction.status in listOf(TransactionStatus.EXPIRED, TransactionStatus.CANCELED) -> TransactionResult.IllegalState(transaction.status, transaction.paymentStatus)
            transaction.paymentStatus != TransactionPaymentStatus.FAILED -> TransactionResult.IllegalState(transaction.status, transaction.paymentStatus)
            else -> transaction.execute(retry = true)
        }.also {
            markers.andAppend("result", it)
            logger.info(markers, "TransactionService#retry")
        }
    }

    private fun Transaction.execute(retry: Boolean): TransactionResult {
        val result = when (details) {
            is PixTransactionDetails -> executePix(
                pixTransactionDetails = details,
                retryTransaction = retry,
            )
            is ScheduleBillsTransactionDetails -> executeScheduleBills(
                bills = details.bills,
                sweepingAmount = null,
                sweepingParticipantId = null,
                authorizationToken = authorizationToken,
                retryTransaction = retry,
            )
            is SweepingTransactionDetails -> executeScheduleBills(
                bills = details.bills,
                sweepingAmount = details.amount,
                sweepingParticipantId = details.sweepingParticipantId,
                authorizationToken = authorizationToken,
                retryTransaction = retry,
            )

            is MarkAsPaidOrIgnoreBillsTransactionDetails, is AddBoletoTransactionDetails, is SendPixCodeTransactionDetails -> TransactionResult.Success(this)
        }

        return if (result is TransactionResult.Success) {
            val completedTransaction = copy(
                status = TransactionStatus.COMPLETED,
                paymentStatus = TransactionPaymentStatus.UNKNOWN,
                authorizationToken = authorizationToken,
            ).also {
                transactionRepository.save(it)
            }

            transactionRepository.find(userId, TransactionStatus.ACTIVE).filter {
                // exclui a propria transação, não é garantido que o indice está atualizado
                it.id != id && it.groupId == groupId
            }.forEach {
                transactionRepository.save(it.copy(status = TransactionStatus.EXPIRED))
            }

            TransactionResult.Success(completedTransaction)
        } else {
            result
        }
    }

    override fun updatePaymentStatus(transactionId: TransactionId, userId: UserId, paymentStatus: TransactionPaymentStatus): TransactionResult {
        val markers =
            Markers.append("transactionId", transactionId.value)
                .andAppend("userId", userId.value)
                .andAppend("paymentStatus", paymentStatus)

        val transaction = transactionRepository.find(transactionId)
        markers.andAppend("transaction", transaction)

        if (transaction == null) {
            logger.warn(markers, "TransactionService#updatePaymentStatus")
            return TransactionResult.TransactionNotFound
        }

        return when {
            transaction.userId != userId -> TransactionResult.IllegalUser(transaction.userId)
            transaction.status in listOf(TransactionStatus.EXPIRED, TransactionStatus.CANCELED) -> TransactionResult.IllegalState(transaction.status, transaction.paymentStatus)
            transaction.paymentStatus != TransactionPaymentStatus.UNKNOWN -> TransactionResult.IllegalState(transaction.status, transaction.paymentStatus)
            else -> {
                TransactionResult.Success(
                    transaction.copy(
                        paymentStatus = paymentStatus,
                    ),
                ).also {
                    transactionRepository.save(it.transaction)
                }
            }
        }.also {
            markers.andAppend("result", it)
            logger.info(markers, "TransactionService#updatePaymentStatus")
        }
    }

    override fun find(transactionId: TransactionId): Transaction? = transactionRepository.find(transactionId)

    override fun find(userId: UserId, transactionGroupId: TransactionGroupId): List<Transaction> {
        return transactionRepository.find(userId, TransactionStatus.ACTIVE).filter {
            it.groupId == transactionGroupId
        }
    }

    private fun PixKey.format(): PixKey {
        if (type == PixKeyType.PHONE) {
            if (value.startsWith("+55")) return this
            return PixKey("+55$value", type)
        }
        return this
    }

    private fun Transaction.executePix(pixTransactionDetails: PixTransactionDetails, retryTransaction: Boolean): TransactionResult {
        val markers = Markers.append("transaction", this)

        return runBlocking {
            paymentAdapter.pixTransaction(
                userId = userId,
                walletId = walletId,
                amount = pixTransactionDetails.amount,
                sweepingRequest = pixTransactionDetails.sweepingAmount?.let {
                    SweepingRequest(
                        transactionId = id,
                        amount = it,
                        participantId = pixTransactionDetails.sweepingParticipantId!!,
                        retry = retryTransaction,
                    )
                },
                key = pixTransactionDetails.pixKey.format(),
                transactionId = id,
                authorizationToken = authorizationToken,
                qrCode = pixTransactionDetails.qrCode?.value,
                retryTransaction = retryTransaction,
            )
        }.map {
            TransactionResult.Success(this)
        }.getOrElse {
            it.toTransactionResult()
        }.also {
            markers.andAppend("result", it)
            logger.info(markers, "TransactionService#execute")
        }
    }

    private fun Transaction.executeScheduleBills(bills: List<BillId>, sweepingAmount: Long?, sweepingParticipantId: SweepingParticipantId?, authorizationToken: String?, retryTransaction: Boolean): TransactionResult {
        val markers = Markers.append("transaction", this)

        return runBlocking {
            paymentAdapter.scheduleBills(
                userId = userId,
                sweepingRequest = sweepingAmount?.let {
                    SweepingRequest(
                        transactionId = id,
                        amount = it,
                        participantId = sweepingParticipantId!!,
                        retry = retryTransaction,
                    )
                },
                walletId = walletId,
                bills = bills,
                authorizationToken = authorizationToken,
            )
        }.map {
            TransactionResult.Success(this)
        }.getOrElse {
            it.toTransactionResult()
        }.also {
            markers.andAppend("result", it)
            logger.info(markers, "TransactionService#execute")
        }
    }

    private fun PaymentAdapterError.toTransactionResult() = when (this) {
        PaymentAdapterError.BillNotActiveError -> TransactionResult.TransactionError("Bill not active")
        PaymentAdapterError.ErrorSchedulingBill -> TransactionResult.TransactionError("Error scheduling bills")
        PaymentAdapterError.InvalidPixKey -> TransactionResult.TransactionError("Invalid Pix Key")
        PaymentAdapterError.PixKeyNotFound -> TransactionResult.TransactionError("Pix Key not found")
        PaymentAdapterError.AssistantLimitExceeded -> TransactionResult.TransactionError("Assistant limit exceeded")
        PaymentAdapterError.PixLimitExceeded -> TransactionResult.TransactionError("Pix daily limit exceeded")
        is PaymentAdapterError.SweepingTransferError -> {
            when (this.reason) {
                is SweepingTransferErrorReason.GenericReason -> {
                    TransactionResult.TransactionError(TransactionErrorReason.GenericReason(this.reason.message), notifyUser = true)
                }
                is SweepingTransferErrorReason.LimitExceeded -> {
                    TransactionResult.TransactionError(TransactionErrorReason.SweepingLimitExceeded(this.reason.limitType), notifyUser = true)
                }
            }
        }
        else -> TransactionResult.TransactionError("Unknown error")
    }
}

data class TransactionId(
    val value: String = "TRANSACTION-${UUID.randomUUID()}",
)

enum class TransactionType {
    PIX, SWEEPING, SCHEDULE_BILLS, MARK_AS_PAID_OR_IGNORE_BILLS, BOLETO, SEND_PIX_CODE
}

enum class TransactionStatus {
    ACTIVE,
    COMPLETED,
    EXPIRED,
    CANCELED,
}

data class Transaction(
    val id: TransactionId,
    val groupId: TransactionGroupId,
    val userId: UserId,
    val walletId: WalletId,
    val status: TransactionStatus,
    val paymentStatus: TransactionPaymentStatus,
    val details: TransactionDetails,
    val authorizationToken: String? = null,
    val createdAt: ZonedDateTime = ZonedDateTime.now(),
    val updatedAt: ZonedDateTime = ZonedDateTime.now(),
)

data class TransactionGroupId(val value: String = "TX_GROUP-${UUID.randomUUID()}")

sealed interface TransactionDetails {
    val type: TransactionType
}

data class SendPixCodeTransactionDetails(
    val bills: List<BillId>,
) : TransactionDetails {
    override val type = TransactionType.SEND_PIX_CODE
}

data class PixTransactionDetails(
    val amount: Long,
    val pixKey: PixKey,
    val sweepingAmount: Long?,
    val sweepingParticipantId: SweepingParticipantId?,
    val recipientName: String,
    val recipientDocument: String,
    val recipientInstitution: String,
    val qrCode: PixQRCode?,
    val hasSubscriptionFee: Boolean = false,
) : TransactionDetails {
    override val type = TransactionType.PIX
}

data class AddBoletoTransactionDetails(
    val barCodes: List<BoletoInfo>,
) : TransactionDetails {
    override val type = TransactionType.BOLETO
}

data class SweepingTransactionDetails(
    val amount: Long,
    val sweepingParticipantId: SweepingParticipantId,
    val bills: List<BillId>,
) : TransactionDetails {
    override val type = TransactionType.SWEEPING
}

data class ScheduleBillsTransactionDetails(
    val bills: List<BillId>,
) : TransactionDetails {
    override val type = TransactionType.SCHEDULE_BILLS
}

data class MarkAsPaidOrIgnoreBillsTransactionDetails(
    val billsToMarkAsPaid: List<BillId>,
    val billsToIgnore: List<BillId>,
) : TransactionDetails {
    override val type = TransactionType.MARK_AS_PAID_OR_IGNORE_BILLS
}

sealed class TransactionErrorReason : PrintableSealedClass() {
    data class GenericReason(val message: String) : TransactionErrorReason()
    data class SweepingLimitExceeded(val limitType: SweepingLimitType) : TransactionErrorReason()
}

sealed class TransactionResult : PrintableSealedClass() {
    object TransactionNotFound : TransactionResult()

    data class TransactionError(val reason: TransactionErrorReason, val notifyUser: Boolean = false) : TransactionResult() {
        constructor (message: String) : this(TransactionErrorReason.GenericReason(message), false)
    }

    data class IllegalState(val status: TransactionStatus, val paymentStatus: TransactionPaymentStatus = TransactionPaymentStatus.UNKNOWN) : TransactionResult()

    data class IllegalUser(val userId: UserId) : TransactionResult()

    data class Success(val transaction: Transaction) : TransactionResult()

    object Canceled : TransactionResult()
}