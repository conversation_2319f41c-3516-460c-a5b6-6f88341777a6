package ai.chatbot.app.event

enum class UserEvent(val eventName: String) {
    PROMOTE_SWEEPING_SENT("of_incentive.incentive_sent"),
    PROMOTE_SWEEPING_CLICK_KNOW_MORE("of_incentive.click_know_more"),
    PROMOTE_SWEEPING_CLICK_OPT_OUT("of_incentive.click_opt_out"),

    PROMOTE_SWEEPING_CLICK_UNKNOWN("of_incentive.click"),
    PROMOTE_SWEEPING_CLICK_DDA("of_incentive.click_dda"),
    PROMOTE_SWEEPING_CLICK_CASH_IN("of_incentive.click_cash_in"),
    PROMOTE_SWEEPING_CLICK_ONBOARDING("of_incentive.click_onboarding"),
    PROMOTE_SWEEPING_CLICK_MARK_AS_PAID("of_incentive.click_mark_as_paid"),
    PROMOTE_SWEEPING_CLICK_BROADCAST("of_incentive.click_broadcast"),

    PROMOTE_INVESTMENT_DISCOUNT_ONBOARDING("investment_discount.click_onboarding"),
}