package ai.chatbot.app.event

import ai.chatbot.adapters.billPayment.BillPaymentAdapter
import ai.chatbot.adapters.notification.MessagePublisher
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.brazilTimeZone
import ai.chatbot.app.utils.dateTimeFormat
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.util.UUID

interface EventService {
    fun send(accountId: AccountId, event: UserEvent, metadata: Map<String, String> = emptyMap())
}

@Singleton
class NoopEventService : EventService {
    override fun send(accountId: AccountId, event: UserEvent, metadata: Map<String, String>) { }
}

@Primary
@Singleton
@Requires(property = "userEvents.enabled", value = "true")
class DefaultEventService(
    private val messagePublisher: MessagePublisher,
    private val billPaymentAdapter: BillPaymentAdapter,
    private val tenantService: TenantService,
    @Property(name = "aws.sqs.queues.eventQueue") private val eventQueueName: String,
) : EventService {
    override fun send(accountId: AccountId, event: UserEvent, metadata: Map<String, String>) {
        if (tenantService.getConfiguration().flags.userEventsHttp.check()) {
            billPaymentAdapter.sendUserEvent(accountId, event, metadata)
        } else {
            messagePublisher.sendMessage(
                eventQueueName,
                UserEventMessage(
                    entityId = accountId.value,
                    event = event.eventName,
                    timestamp = getZonedDateTime().format(dateTimeFormat.withZone(brazilTimeZone)),
                    metadata = metadata,
                ),
            )
        }
    }
}

data class UserEventMessage(
    val entityId: String,
    val entityType: String = "ACCOUNT",
    val source: String = "CHATBOT",
    val event: String,
    val timestamp: String,
    val metadata: Map<String, String>,
    val correlationId: String = UUID.randomUUID().toString(),
)