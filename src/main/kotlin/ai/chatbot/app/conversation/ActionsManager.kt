package ai.chatbot.app.conversation

import ai.chatbot.app.ManualEntryType
import ai.chatbot.app.NotificationService
import ai.chatbot.app.PrintableSealedClass
import ai.chatbot.app.conversation.actions.AddFeatureRequestExecutor
import ai.chatbot.app.conversation.actions.CreateManualEntryExecutor
import ai.chatbot.app.conversation.actions.GetContextExecutor
import ai.chatbot.app.conversation.actions.MakePaymentExecutor
import ai.chatbot.app.conversation.actions.MarkBillsAsPaidExecutor
import ai.chatbot.app.conversation.actions.MarkReminderAsDoneExecutor
import ai.chatbot.app.conversation.actions.MessageActionExecutor
import ai.chatbot.app.conversation.actions.NoopExecutor
import ai.chatbot.app.conversation.actions.NotificationActionExecutor
import ai.chatbot.app.conversation.actions.OnboardingSinglePixExecutor
import ai.chatbot.app.conversation.actions.PixTransactionExecutor
import ai.chatbot.app.conversation.actions.PromoteSweepingAccountExecutor
import ai.chatbot.app.conversation.actions.RefreshBalanceExecutor
import ai.chatbot.app.conversation.actions.RemoveManualEntryExecutor
import ai.chatbot.app.conversation.actions.ScheduleBoletoExecutor
import ai.chatbot.app.conversation.actions.SearchContactsExecutor
import ai.chatbot.app.conversation.actions.SelectSweepingAccountExecutor
import ai.chatbot.app.conversation.actions.SendBillsPeriodExecutor
import ai.chatbot.app.conversation.actions.ValidateBoletoExecutor
import ai.chatbot.app.media.BoletoInfo
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.prompt.DEFAULT_ERROR_MESSAGE
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.user.User
import ai.chatbot.app.utils.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import jakarta.inject.Singleton
import kotlin.time.ExperimentalTime
import kotlin.time.measureTimedValue
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class ActionsManager(
    private val actionExecutorLocator: ActionExecutorLocator,
    private val conversationHistoryService: ConversationHistoryService,
    private val notificationService: NotificationService,
) {
    fun execute(
        actions: List<Action>,
        user: User,
    ): List<ActionResult> {
        return executeActions(actions, user).getOrElse { actionError -> return resolveActionError(actionError, user) }
    }

    fun validate(
        actions: List<Action>,
        user: User,
    ): List<Action> {
        return validateActions(actions, user)
    }

    @OptIn(ExperimentalTime::class)
    private fun validateActions(
        actions: List<Action>,
        user: User,
    ): List<Action> {
        return actions.filter {
            val executor = actionExecutorLocator.locate(it.name)

            val (result, timeTaken) = measureTimedValue {
                executor.validate(it.toCommandExecutor(user))
            }

            logger.info(
                Markers.append("userId", user.id.value)
                    .andAppend("accountId", user.accountId.value)
                    .andAppend("action", it)
                    .andAppend("actionExecutor", executor::class.simpleName)
                    .andAppend("timeTaken", timeTaken.inWholeMilliseconds)
                    .andAppend("result", result),
                "ActionsManager#validateActions",
            )

            result
        }
    }

    private fun resolveActionError(
        actionError: ActionError,
        user: User,
    ): List<ActionResult> {
        return if (actionError.needCompletion) {
            val systemMessage = actionError.message
            listOf(ActionResult.WithCompletion(systemMessage, actionError.actionType))
        } else {
            notificationService.notify(user.id, user.accountId, actionError.message)
            conversationHistoryService.createAssistantMessage(user.id, CompletionMessage.ofMessage(actionError.message))
            listOf(ActionResult.WithoutCompletion(actionError.actionType))
        }
    }

    @OptIn(ExperimentalTime::class)
    private fun executeActions(
        actions: List<Action>,
        user: User,
    ): Either<ActionError, List<ActionResult>> {
        val logName = "ActionsManager#executeActions"
        val markers = Markers.append("accountId", user.accountId.value).andAppend("userId", user.id.value)

        val actionResults =
            actions.sortedBy { it.name.priority }.map { action ->
                markers.andAppend("action", action)

                val actionExecutor = actionExecutorLocator.locate(action.name)
                markers.andAppend("actionExecutor", actionExecutor::class.simpleName)

                try {
                    val (actionResult, timeTaken) = measureTimedValue {
                        actionExecutor.execute(
                            action.toCommandExecutor(user),
                        ).getOrElse {
                            logger.error(
                                markers.andAppend("actionResult", it),
                                logName,
                                it.exception,
                            )
                            return it.left()
                        }
                    }

                    logger.info(markers.andAppend("actionResult", actionResult).andAppend("timeTaken", timeTaken.inWholeMilliseconds), logName)
                    actionResult
                } catch (e: Exception) {
                    val actionResult = ActionError(false, DEFAULT_ERROR_MESSAGE, exception = IllegalStateException("Action está mal implementada. Resultado deveria ser um Either"), actionType = action.name)
                    logger.error(markers.andAppend("actionResult", actionResult), logName, e)
                    return actionResult.left()
                }
            }
        return actionResults.right()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ActionsManager::class.java)
    }
}

interface ActionExecutor<T : ActionExecutorCommand> {
    fun execute(command: T): Either<ActionError, ActionResult>

    fun validate(command: T): Boolean {
        return true
    }
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
sealed interface ActionExecutorCommand {
    @JsonTypeName("makePayment")
    data class MakePaymentActionExecutorCommand(
        val action: Action.MakePayment,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("sendPix")
    data class PixTransactionActionExecutorCommand(
        val action: Action.PixTransaction,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("selectSweepingAccount")
    data class SelectSweepingAccountCommand(
        val action: Action.SelectSweepingAccount,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("createManualEntry")
    data class CreateManualEntryActionExecutorCommand(
        val action: Action.CreateManualEntry,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("removeManualEntry")
    data class RemoveManualEntryActionExecutorCommand(
        val action: Action.RemoveManualEntry,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("markBillsAsPaidOrIgnore")
    data class MarkBillsAsPaidOrIgnoreBillsActionExecutorCommand(
        val action: Action.MarkAsPaidOrIgnore,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("markReminderAsDone")
    data class MarkReminderAsDoneActionExecutorCommand(
        val action: Action.MarkReminderAsDone,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("message")
    data class MessageActionExecutorCommand(
        val action: Action.SendMessage,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("scheduleBoleto")
    data class ScheduleBoletoActionExecutorCommand(
        val action: Action.ScheduleBoleto,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("notification")
    data class NotificationActionExecutorCommand(
        val action: Action.SendNotification,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("searchContacts")
    data class SearchContactsActionExecutorCommand(
        val action: Action.SearchContacts,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("refreshBalance")
    data class RefreshBalanceActionExecutorCommand(
        val action: Action.RefreshBalance,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("sendPendingBillsPeriod")
    data class SendPendingBillsPeriodActionExecutorCommand(
        val action: Action.SendPendingBillsPeriod,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("addFeatureRequest")
    data class AddFeatureRequestActionExecutorCommand(
        val action: Action.NotSupportedFeature,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("noop")
    data class NoopActionExecutorCommand(
        val action: Action.Noop,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("getContext")
    data class GetContextActionExecutorCommand(
        val action: Action.GetContext,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("onboardingTestPix")
    data class OnboardingSinglePixActionExecutorCommand(
        val action: Action.OnboardingSinglePix,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("promoteSweepingAccount")
    data class PromoteSweepingAccountActionExecutorCommand(
        val action: Action.PromoteSweepingAccount,
        val user: User,
    ) : ActionExecutorCommand

    @JsonTypeName("validateBoleto")
    data class ValidateBoletoActionExecutorCommand(
        val action: Action.ValidateBoleto,
        val user: User,
    ) : ActionExecutorCommand
}

@Singleton
open class ActionExecutorLocator(
    private val messageActionExecutor: MessageActionExecutor,
    private val notificationActionExecutor: NotificationActionExecutor,
    private val refreshBalanceExecutor: RefreshBalanceExecutor,
    private val addFeatureRequestExecutor: AddFeatureRequestExecutor,
    private val markBillsAsPaidExecutor: MarkBillsAsPaidExecutor,
    private val markReminderAsDoneExecutor: MarkReminderAsDoneExecutor,
    private val noopExecutor: NoopExecutor,
    private val getContextExecutor: GetContextExecutor,
    private val pixTransactionExecutor: PixTransactionExecutor,
    private val searchContactsExecutor: SearchContactsExecutor,
    private val onboardingSinglePixExecutor: OnboardingSinglePixExecutor,
    private val makePaymentExecutor: MakePaymentExecutor,
    private val promoteSweepingAccount: PromoteSweepingAccountExecutor,
    private val sendBillsPeriodExecutor: SendBillsPeriodExecutor,
    private val validateBoletoExecutor: ValidateBoletoExecutor,
    private val scheduleBoletoExecutor: ScheduleBoletoExecutor,
    private val createManualEntryExecutor: CreateManualEntryExecutor,
    private val removeManualEntryExecutor: RemoveManualEntryExecutor,
    private val selectSweepingAccountExecutor: SelectSweepingAccountExecutor,
) {
    fun locate(actionType: ActionType): ActionExecutor<ActionExecutorCommand> {
        return when (actionType) {
            ActionType.MSG -> messageActionExecutor
            ActionType.NOTIFICATION -> notificationActionExecutor
            ActionType.REFRESH_BALANCE_AND_FORECASTS -> refreshBalanceExecutor
            ActionType.ADD_FEATURE_REQUEST -> addFeatureRequestExecutor
            ActionType.MARK_BILLS_AS_PAID -> markBillsAsPaidExecutor
            ActionType.MARK_REMINDER_AS_DONE -> markReminderAsDoneExecutor
            ActionType.NOOP -> noopExecutor
            ActionType.GET_CONTEXT -> getContextExecutor
            ActionType.PIX_TRANSACTION -> pixTransactionExecutor
            ActionType.SEARCH_CONTACTS -> searchContactsExecutor
            ActionType.ONBOARDING_SINGLE_PIX -> onboardingSinglePixExecutor
            ActionType.MAKE_PAYMENT -> makePaymentExecutor
            ActionType.PROMOTE_SWEEPING_ACCOUNT -> promoteSweepingAccount
            ActionType.SEND_PENDING_BILLS_PERIOD -> sendBillsPeriodExecutor
            ActionType.VALIDATE_BOLETO -> validateBoletoExecutor
            ActionType.SCHEDULE_BOLETO -> scheduleBoletoExecutor
            ActionType.CREATE_MANUAL_ENTRY -> createManualEntryExecutor
            ActionType.REMOVE_MANUAL_ENTRY -> removeManualEntryExecutor
            ActionType.SELECT_SWEEPING_ACCOUNT -> selectSweepingAccountExecutor
        } as ActionExecutor<ActionExecutorCommand>
    }
}

data class ActionMessage(
    val needCompletion: Boolean,
    val message: String,
)

sealed interface ActionResult {
    data class WithCompletion(val systemMessageToCompletion: String, val actionType: ActionType) : ActionResult {
        val needCompletion: Boolean = true
    }

    data class WithoutCompletion(val actionType: ActionType) : ActionResult {
        val needCompletion: Boolean = false
    }
}

open class ActionError(
    open val needCompletion: Boolean,
    open val message: String,
    open val exception: Exception? = null,
    open val actionType: ActionType,
)

enum class SweepingLimitType { DAILY, WEEKLY, MONTHLY, YEARLY, GLOBAL, TRANSACTION, UNKNOWN }

sealed class SweepingTransferErrorReason : PrintableSealedClass() {
    data class GenericReason(val message: String) : SweepingTransferErrorReason()
    data class LimitExceeded(val limitType: SweepingLimitType) : SweepingTransferErrorReason()
}

sealed class PaymentAdapterError : PrintableSealedClass() {
    object NotFoundError : PaymentAdapterError()

    object SufficientBalanceError : PaymentAdapterError()

    object UnknownFinancialInstitution : PaymentAdapterError()

    object AtLeastOneBillRequired : PaymentAdapterError()

    object OnlyActiveOrScheduledBillsAreAllowed : PaymentAdapterError()

    object SingleWalletRequired : PaymentAdapterError()

    object InvalidPixKey : PaymentAdapterError()

    object PixKeyNotFound : PaymentAdapterError()

    object OnboardingSinglePixAlreadyPaid : PaymentAdapterError()

    object OnboardingSinglePixCreateError : PaymentAdapterError()

    object PixFromDifferentOwner : PaymentAdapterError()

    object ReminderError : PaymentAdapterError()

    data class SweepingTransferError(val reason: SweepingTransferErrorReason) : PaymentAdapterError()

    object BillNotActiveError : PaymentAdapterError()

    object ErrorSchedulingBill : PaymentAdapterError()

    object AssistantLimitExceeded : PaymentAdapterError()

    object PixLimitExceeded : PaymentAdapterError()

    object PixQrCodeValidationError : PaymentAdapterError()

    object PixQrCodeInvalid : PaymentAdapterError()

    object PixQrCodeAmountNotFound : PaymentAdapterError()

    object UserNotFound : PaymentAdapterError()

    object BillPaymentLimitExpired : PaymentAdapterError()

    object BillAlreadyPaid : PaymentAdapterError()

    object BillNotPayable : PaymentAdapterError()

    object BillBarcodeNotFound : PaymentAdapterError()

    object BillEmptyAmount : PaymentAdapterError()

    object BillAlreadyExists : PaymentAdapterError()

    object UnableToValidateBill : PaymentAdapterError()
    class ServerError(val exception: Exception) : PaymentAdapterError()
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
sealed interface Action {
    val name: ActionType

    fun toCommandExecutor(user: User): ActionExecutorCommand

    @JsonTypeName("sendMessage")
    data class SendMessage(val content: String) : Action {
        override val name: ActionType = ActionType.MSG

        override fun toCommandExecutor(user: User): ActionExecutorCommand.MessageActionExecutorCommand {
            return ActionExecutorCommand.MessageActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("sendNotification")
    data class SendNotification(val notification: ChatbotRawTemplatedNotification) : Action {
        override val name: ActionType = ActionType.NOTIFICATION

        override fun toCommandExecutor(user: User): ActionExecutorCommand.NotificationActionExecutorCommand {
            return ActionExecutorCommand.NotificationActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("searchContacts")
    data class SearchContacts(val contactId: String?, val message: String?) : Action {
        override val name: ActionType = ActionType.SEARCH_CONTACTS

        override fun toCommandExecutor(user: User): ActionExecutorCommand.SearchContactsActionExecutorCommand {
            return ActionExecutorCommand.SearchContactsActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("notSupportedFeature")
    data class NotSupportedFeature(val content: List<String>) : Action {
        override val name = ActionType.ADD_FEATURE_REQUEST

        override fun toCommandExecutor(user: User): ActionExecutorCommand.AddFeatureRequestActionExecutorCommand {
            return ActionExecutorCommand.AddFeatureRequestActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("refreshBalance")
    object RefreshBalance : Action {
        override val name: ActionType = ActionType.REFRESH_BALANCE_AND_FORECASTS

        override fun toCommandExecutor(user: User): ActionExecutorCommand.RefreshBalanceActionExecutorCommand {
            return ActionExecutorCommand.RefreshBalanceActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("sendPendingBillsPeriod")
    data class SendPendingBillsPeriod(val startDate: String = "", val endDate: String = "", val amount: Boolean) : Action {
        override val name: ActionType = ActionType.SEND_PENDING_BILLS_PERIOD

        override fun toCommandExecutor(user: User): ActionExecutorCommand.SendPendingBillsPeriodActionExecutorCommand {
            return ActionExecutorCommand.SendPendingBillsPeriodActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("makePayment")
    data class MakePayment(val bills: List<Int>, val type: String = "SCHEDULE", val transactionId: TransactionId? = null, val confirmation: Boolean = false) : Action {
        override val name: ActionType = ActionType.MAKE_PAYMENT

        override fun toCommandExecutor(user: User): ActionExecutorCommand.MakePaymentActionExecutorCommand {
            return ActionExecutorCommand.MakePaymentActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("scheduleBills")
    data class ScheduleBoleto(val transactionId: TransactionId, val scheduleToDueDate: Boolean = false) : Action {
        override val name: ActionType = ActionType.SCHEDULE_BOLETO

        override fun toCommandExecutor(user: User): ActionExecutorCommand.ScheduleBoletoActionExecutorCommand {
            return ActionExecutorCommand.ScheduleBoletoActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("pixTransaction")
    data class PixTransaction(val amount: Long, val key: String, val type: String, val ignoreLastUsed: Boolean? = null) : Action {
        override val name: ActionType = ActionType.PIX_TRANSACTION

        override fun toCommandExecutor(user: User): ActionExecutorCommand.PixTransactionActionExecutorCommand {
            return ActionExecutorCommand.PixTransactionActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("selectSweepingAccount")
    data class SelectSweepingAccount(val transactionGroupId: TransactionGroupId) : Action {
        override val name: ActionType = ActionType.SELECT_SWEEPING_ACCOUNT

        override fun toCommandExecutor(user: User): ActionExecutorCommand.SelectSweepingAccountCommand {
            return ActionExecutorCommand.SelectSweepingAccountCommand(this, user)
        }
    }

    @JsonTypeName("createManualEntry")
    data class CreateManualEntry(val title: String, val amount: Long, val type: ManualEntryType, val dueDate: String) : Action {
        override val name: ActionType = ActionType.CREATE_MANUAL_ENTRY

        override fun toCommandExecutor(user: User): ActionExecutorCommand.CreateManualEntryActionExecutorCommand {
            return ActionExecutorCommand.CreateManualEntryActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("removeManualEntry")
    data class RemoveManualEntry(val manualEntryId: String) : Action {
        override val name: ActionType = ActionType.REMOVE_MANUAL_ENTRY

        override fun toCommandExecutor(user: User): ActionExecutorCommand.RemoveManualEntryActionExecutorCommand {
            return ActionExecutorCommand.RemoveManualEntryActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("markAsPaidOrIgnore")
    data class MarkAsPaidOrIgnore(val payload: MarkBillsAsPaidOrIgnoreBillsAction) : Action {
        override val name: ActionType = ActionType.MARK_BILLS_AS_PAID

        override fun toCommandExecutor(user: User): ActionExecutorCommand.MarkBillsAsPaidOrIgnoreBillsActionExecutorCommand {
            return ActionExecutorCommand.MarkBillsAsPaidOrIgnoreBillsActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("markReminderAsDone")
    data class MarkReminderAsDone(val payload: MarkReminderAsDoneAction) : Action {
        override val name: ActionType = ActionType.MARK_REMINDER_AS_DONE

        override fun toCommandExecutor(user: User): ActionExecutorCommand.MarkReminderAsDoneActionExecutorCommand {
            return ActionExecutorCommand.MarkReminderAsDoneActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("noop")
    object Noop : Action {
        override val name: ActionType = ActionType.NOOP

        override fun toCommandExecutor(user: User): ActionExecutorCommand.NoopActionExecutorCommand {
            return ActionExecutorCommand.NoopActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("getContext")
    data class GetContext(val context: AdditionalContext, val subject: String) : Action {
        override val name: ActionType = ActionType.GET_CONTEXT

        override fun toCommandExecutor(user: User): ActionExecutorCommand.GetContextActionExecutorCommand {
            return ActionExecutorCommand.GetContextActionExecutorCommand(GetContext(this.context, subject), user)
        }
    }

    @JsonTypeName("onboardingSinglePix")
    data class OnboardingSinglePix(val payload: OnboardingSinglePixAction) : Action {
        override val name: ActionType = ActionType.ONBOARDING_SINGLE_PIX

        override fun toCommandExecutor(user: User): ActionExecutorCommand.OnboardingSinglePixActionExecutorCommand {
            return ActionExecutorCommand.OnboardingSinglePixActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("promoteSweepingAccount")
    data class PromoteSweepingAccount(val payload: PromoteSweepingAccountAction) : Action {
        override val name: ActionType = ActionType.PROMOTE_SWEEPING_ACCOUNT

        override fun toCommandExecutor(user: User): ActionExecutorCommand.PromoteSweepingAccountActionExecutorCommand {
            return ActionExecutorCommand.PromoteSweepingAccountActionExecutorCommand(this, user)
        }
    }

    @JsonTypeName("validateBoleto")
    data class ValidateBoleto(val barcodes: List<BoletoInfo>) : Action {
        override val name: ActionType = ActionType.VALIDATE_BOLETO

        override fun toCommandExecutor(
            user: User,
        ): ActionExecutorCommand.ValidateBoletoActionExecutorCommand {
            return ActionExecutorCommand.ValidateBoletoActionExecutorCommand(this, user)
        }
    }
}

enum class AdditionalContext {
    ORIGINAL,
    SUBSCRIPTION,
}

enum class ActionType(val priority: Int = 100, val blockMessge: Boolean = true) {
    MSG(0, blockMessge = false),
    NOTIFICATION(0),
    SEND_PENDING_BILLS_PERIOD(1),
    REFRESH_BALANCE_AND_FORECASTS(1),
    ADD_FEATURE_REQUEST(blockMessge = false),
    MARK_BILLS_AS_PAID,
    MARK_REMINDER_AS_DONE,
    NOOP(blockMessge = false),
    GET_CONTEXT,
    PIX_TRANSACTION,
    CREATE_MANUAL_ENTRY,
    REMOVE_MANUAL_ENTRY,
    SEARCH_CONTACTS,
    ONBOARDING_SINGLE_PIX,
    MAKE_PAYMENT,
    PROMOTE_SWEEPING_ACCOUNT,
    VALIDATE_BOLETO,
    SCHEDULE_BOLETO,
    SELECT_SWEEPING_ACCOUNT,
}

data class MarkBillsAsPaidOrIgnoreBillsAction(val billsToMarkAsPaid: List<Int> = emptyList(), val billsToIgnoreOrRemove: List<Int> = emptyList(), val transactionId: TransactionId? = null, val userConfirmed: Boolean = false)

data class MarkReminderAsDoneAction(val reminderId: String)

data class OnboardingSinglePixAction(val action: String, val key: String? = null, val type: String? = null)

data class PromoteSweepingAccountAction(val action: String)

enum class PaymentType {
    PIX,
    SCHEDULE,
}