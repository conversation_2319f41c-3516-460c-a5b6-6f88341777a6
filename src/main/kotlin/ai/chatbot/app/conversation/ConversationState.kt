package ai.chatbot.app.conversation

import ai.chatbot.app.bill.BillView
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.payment.Balance
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime

enum class HistoryStateType {
    BILLS_COMING_DUE,
}

sealed interface HistoryState {
    val type: HistoryStateType

    fun getUserId(): UserId
}

data class BillComingDueHistoryState(
    val walletWithBills: WalletWithBills,
    val user: User,
    val balance: Balance?,
    val internalStateControl: InternalStateControl,
    val startDate: LocalDate = getLocalDate(),
    val endDate: LocalDate = getLocalDate(),
    val contacts: List<Contact>,
    val lastBillsUpdatedAt: ZonedDateTime = getZonedDateTime(),
    val subscription: Subscription?,
    val onboardingState: OnboardingState? = null,
    val alreadyPromotedSweepingAccount: Boolean = false,
) : HistoryState {
    override val type = HistoryStateType.BILLS_COMING_DUE

    override fun getUserId() = user.id
}

data class InternalStateControl(
    val shouldSynchronizeBeforeCompletion: Boolean,
    val billComingDueNotifiedAt: LocalDateTime?,
)

data class Subscription(
    val fee: Long?,
    val dueDate: LocalDate,
    val paymentStatus: SubscriptionPaymentStatus,
    val type: SubscriptionType,
) {
    fun isOverDue(): Boolean {
        return paymentStatus == SubscriptionPaymentStatus.OVERDUE
    }

    fun needToBePaidToday(): Boolean {
        return dueDate == getLocalDate() && type == SubscriptionType.PIX
    }
}

data class OnboardingState(
    val hasAcceptedExamplePayment: Boolean,
    val pixKeyType: PixKeyType? = null,
    val pixKeyValue: String? = null,
    val billView: BillView? = null,
)

enum class SubscriptionPaymentStatus {
    PAID,
    OVERDUE,
}

enum class SubscriptionType {
    PIX, IN_APP
}