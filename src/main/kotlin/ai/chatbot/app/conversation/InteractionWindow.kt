package ai.chatbot.app.conversation

import ai.chatbot.app.notification.ChatBotNotificationGatewayTO
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.timestampFormatWithBrazilTimeZone
import java.time.Duration
import java.time.ZonedDateTime

data class InteractionWindow(
    val userId: UserId,
    val type: InteractionWindowType,
    val state: InteractionWindowState = InteractionWindowState.ACTIVE,
    val expiration: ZonedDateTime,
    val notificationQueue: List<ChatBotNotificationGatewayTO> = listOf(),
) {
    fun toLog() = mapOf(
        "userId" to userId,
        "type" to type,
        "expiration" to expiration.format(timestampFormatWithBrazilTimeZone),
    )
}

enum class InteractionWindowType(val duration: Duration) {
    ONBOARDING_SINGLE_PIX(duration = Duration.ofHours(5)),
}

enum class InteractionWindowState {
    ACTIVE, CLOSED, EXPIRED
}