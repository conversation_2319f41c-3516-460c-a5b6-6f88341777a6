package ai.chatbot.app.conversation

import ai.chatbot.app.classification.ClassificationResult
import ai.chatbot.app.user.UserId
import java.time.LocalDate

data class ConversationHistory(
    val userId: UserId,
    val createdAt: LocalDate,
    val messages: List<ChatMessageWrapper>,
    val classification: ClassificationResult? = null,
)

sealed class UserConversationHistoryNotFound : IllegalStateException() {
    object UserConversationNotFound : UserConversationHistoryNotFound() {
        private fun readResolve(): Any = UserConversationNotFound
    }

    object UserConversationStateNotFound : UserConversationHistoryNotFound() {
        private fun readResolve(): Any = UserConversationStateNotFound
    }
}