package ai.chatbot.app.conversation

import ai.chatbot.app.conversation.actions.OnboardingSinglePixExecutor
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier
import ai.chatbot.app.utils.dateFormat
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.format.DateTimeParseException

@Singleton
class UserMessageValidationService {
    fun validatePayload(
        payload: String?,
        action: InterceptAction?,
    ): UserMessagePayloadValidationResult {
        return when (action?.type) {
            InterceptMessagePayloadType.MARK_REMINDER_AS_DONE -> return validateNotNullOrBlank(payload)
            InterceptMessagePayloadType.ONBOARDING_SINGLE_PIX -> return validateOnboardingSinglePix(payload)
            InterceptMessagePayloadType.PROMOTE_SWEEPING_ACCOUNT -> return validateNotNullOrBlank(payload)
            InterceptMessagePayloadType.ADD_NEW_CONNECTION -> return UserMessagePayloadValidationResult.VALID
            else -> validateDate(payload, action?.type)
        }
    }

    private fun validateDate(payload: String?, payloadType: InterceptMessagePayloadType?): UserMessagePayloadValidationResult {
        if (payload.isNullOrEmpty()) {
            return UserMessagePayloadValidationResult.VALID
        }

        val date =
            try {
                LocalDate.parse(payload, dateFormat)
            } catch (ex: DateTimeParseException) {
                return UserMessagePayloadValidationResult.INVALID
            }

        val allowedOutdated = payloadType?.allowedOutdated ?: false

        if (date != BrazilZonedDateTimeSupplier.getLocalDate() && !allowedOutdated) {
            return UserMessagePayloadValidationResult.OUTDATED
        }

        return UserMessagePayloadValidationResult.VALID
    }

    private fun validateNotNullOrBlank(payload: String?): UserMessagePayloadValidationResult {
        return if (payload.isNullOrBlank()) {
            UserMessagePayloadValidationResult.INVALID
        } else {
            UserMessagePayloadValidationResult.VALID
        }
    }

    private fun validateOnboardingSinglePix(payload: String?): UserMessagePayloadValidationResult {
        if (payload.isNullOrBlank()) {
            return UserMessagePayloadValidationResult.INVALID
        }

        return try {
            OnboardingSinglePixExecutor.OnboardingSinglePixActionEnum.valueOf(payload)

            UserMessagePayloadValidationResult.VALID
        } catch (e: Exception) {
            UserMessagePayloadValidationResult.INVALID
        }
    }
}

enum class UserMessagePayloadValidationResult {
    VALID,
    OUTDATED,
    INVALID,
}