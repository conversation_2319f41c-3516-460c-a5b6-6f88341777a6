package ai.chatbot.app.conversation

import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.HistoryStateRepository
import ai.chatbot.app.IOpenAIAdapter
import ai.chatbot.app.NotificationService
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.PrintableSealedClass
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.UserNotFoundException
import ai.chatbot.app.bill.PendingBillsService
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.payment.Balance
import ai.chatbot.app.prompt.TenantPromptService
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.buildUnregisteredUserAndWallet
import ai.chatbot.app.utils.parallelMap
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.databind.exc.InvalidFormatException
import jakarta.inject.Singleton
import java.time.LocalDate
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Singleton
open class ConversationHistoryService(
    val historyRepository: HistoryRepository,
    val historyStateRepository: HistoryStateRepository,
    private val openAIAdapter: IOpenAIAdapter,
    private val paymentAdapter: PaymentAdapter,
    private val notificationService: NotificationService,
    private val pendingBillsService: PendingBillsService,
    private val promptService: TenantPromptService,
) {
    private val logger: Logger = LoggerFactory.getLogger(ConversationHistoryService::class.java)
    fun createUserMessage(
        userId: UserId,
        content: String,
    ): User {
        try {
            historyRepository.saveUserMessage(userId, message = content)
            return refreshUserState(userId)
        } catch (ex: InvalidFormatException) {
            // apenas para resolver problemas de atualização no banco sem retrocompatibilidade
            notificationService.notify(
                userId,
                null,
                "Devido a uma atualização no sistema, seu histórico foi perdido. Por favor, reinicie a conversa.",
            )
            clearHistory(userId, HistoryStateType.BILLS_COMING_DUE, LocalDate.now())
            val currentState =
                setupBillsComingDueConversationHistory(
                    userId = userId,
                    initialAssistantMessage = null,
                    initialUserMessage = null,
                )
            return currentState.user
        } catch (ex: UserConversationHistoryNotFound) {
            val currentState =
                setupBillsComingDueConversationHistory(
                    userId = userId,
                    initialAssistantMessage = null,
                    initialUserMessage = content,
                )
            return currentState.user
        }
    }

    fun createUserReaction(
        userId: UserId,
        content: String,
    ): User {
        try {
            historyRepository.saveUserReaction(userId, reaction = content)
            return refreshUserState(userId)
        } catch (ex: InvalidFormatException) {
            // apenas para resolver problemas de atualização no banco sem retrocompatibilidade
            notificationService.notify(
                userId,
                null,
                "Devido a uma atualização no sistema, seu histórico foi perdido. Por favor, reinicie a conversa.",
            )
            clearHistory(userId, HistoryStateType.BILLS_COMING_DUE, LocalDate.now())
            val currentState =
                setupBillsComingDueConversationHistory(
                    userId = userId,
                    initialAssistantMessage = null,
                    initialUserMessage = null,
                )
            return currentState.user
        }
    }

    fun refreshPendingBills(userId: UserId, startDate: LocalDate? = null, endDate: LocalDate? = null): Either<RefreshUserStateError, BillComingDueHistoryState> {
        when (val state = historyStateRepository.findLatest(userId)) {
            is BillComingDueHistoryState -> {
                val result =
                    pendingBillsService.updatePendingBills(userId, startDate ?: state.startDate, endDate ?: state.endDate).getOrElse {
                        throw it.toIllegalStateException()
                    }

                if (result.billsUpdated) {
                    return RefreshUserStateError.PendingBillsOutdated.left()
                }

                return state.right()
            }
        }
    }

    private fun getRefreshedStateResult(userId: UserId, walletId: WalletId?) = runBlocking {
        val currentBalance =
            async {
                paymentAdapter.getBalanceAndForecast(userId, walletId).getOrElse {
                    null
                }
            }

        val contacts =
            async {
                paymentAdapter.getContacts(userId, walletId = walletId).getOrElse {
                    if (it is PaymentAdapterError.UserNotFound) {
                        emptyList()
                    } else {
                        throw it.toIllegalStateException()
                    }
                }
            }

        val subscription =
            async {
                paymentAdapter.getSubscription(userId).getOrElse {
                    if (it is PaymentAdapterError.UserNotFound) {
                        null
                    } else {
                        throw it.toIllegalStateException()
                    }
                }
            }

        val activeConsents =
            async {
                paymentAdapter.getActiveSweepingConsents(userId, walletId).getOrElse {
                    if (it is PaymentAdapterError.UserNotFound) {
                        emptyList()
                    } else {
                        throw it.toIllegalStateException()
                    }
                }
            }

        RefreshedStateResult(
            currentBalance = currentBalance.await(),
            contacts = contacts.await(),
            subscription = subscription.await(),
            activeConsents = activeConsents.await(),
        )
    }

    private fun refreshUserState(userId: UserId): User {
        when (val state = historyStateRepository.findLatest(userId)) {
            is BillComingDueHistoryState -> {
                if (state.internalStateControl.shouldSynchronizeBeforeCompletion) {
                    val refreshStateResult = getRefreshedStateResult(userId, state.walletWithBills.walletId)

                    return updateConversationHistoryState<BillComingDueHistoryState>(userId) { currentState ->
                        currentState.copy(
                            internalStateControl = currentState.internalStateControl.copy(
                                shouldSynchronizeBeforeCompletion = false,
                            ),
                            balance = refreshStateResult.currentBalance,
                            contacts = refreshStateResult.contacts,
                            subscription = refreshStateResult.subscription,
                            walletWithBills = currentState.walletWithBills.copy(
                                activeConsents = refreshStateResult.activeConsents,
                            ),
                        )
                    }.map {
                        it.user
                    }.getOrElse {
                        state.user
                    }
                }
                return state.user
            }
        }
    }

    fun createAssistantMessage(
        userId: UserId,
        message: String,
    ) {
        createAssistantMessage(userId, CompletionMessage.ofMessage(message))
    }

    fun createAssistantMessage(
        userId: UserId,
        completionMessage: CompletionMessage,
    ) {
        historyRepository.saveAssistantMessage(userId, completionMessage)
    }

    fun createSystemMessage(
        userId: UserId,
        message: String,
    ) {
        historyRepository.saveSystemMessage(userId, message)
    }

    fun isUserActive(
        userId: UserId,
    ): Boolean {
        return try {
            val history = historyRepository.findLatest(userId)
            history.messages.any {
                it.type == MessageType.USER
            }
        } catch (e: UserConversationHistoryNotFound) {
            false
        }
    }

    fun createCompletionMessage(
        user: User,
        systemMessageToCompletion: List<ActionResult>? = null,
    ): List<CompletionMessage> {
        val userId = user.id
        val history = historyRepository.findLatest(userId)
        val state = historyStateRepository.findLatest(userId) as BillComingDueHistoryState
        val prompt = promptService.getPrompt(state).prompt

        if (systemMessageToCompletion != null) {
            return runBlocking {
                return@runBlocking systemMessageToCompletion.filterIsInstance<ActionResult.WithCompletion>().parallelMap {
                    // Disclaimer: Foi desativado essa busca por contexto pois o fred buscava confusamente o contexto e respondia informações desconexas
//                    val prompt =
//                        when (it.actionType) {
//                            ActionType.GET_CONTEXT -> subscriptionPrompt.replace("{{currentDate}}", BrazilZonedDateTimeSupplier.getCurrentDate())
//                            else -> getPrompt(state.user.status, isEarlyAccess)
//                        }
//                    val historyState =
//                        when (it.actionType) {
//                            ActionType.GET_CONTEXT -> null
//                            else -> state
//                        }
                    return@parallelMap openAIAdapter.createChatCompletion(prompt, state, emptyList(), it.systemMessageToCompletion)
                }
            }
        }
        return listOf(openAIAdapter.createChatCompletion(prompt, state, history.messages, null))
    }

    fun synchronizeBeforeCompletion(userId: UserId) {
        updateConversationHistoryState<BillComingDueHistoryState>(userId = userId) { state ->
            state.copy(internalStateControl = state.internalStateControl.copy(shouldSynchronizeBeforeCompletion = true))
        }
    }

    fun setupEmptyHistoryState(
        userId: UserId,
        initialAssistantMessage: String?,
        initialUserAndWallet: UserAndWallet,
    ): BillComingDueHistoryState {
        try {
            return when (val state = historyStateRepository.findLatest(userId)) {
                is BillComingDueHistoryState -> state
            }
        } catch (e: UserConversationHistoryNotFound) {
            val billComingDueHistoryState = if (initialUserAndWallet.user.accountId.value.isNotEmpty()) {
                BillComingDueHistoryState(
                    walletWithBills = initialUserAndWallet.wallet,
                    balance = null,
                    internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
                    user = initialUserAndWallet.user,
                    contacts = emptyList(),
                    subscription = null,
                )
            } else {
                setupNotRegisteredState(userId)
            }
            historyRepository.create(userId = userId, historyStateType = billComingDueHistoryState.type, null)
            saveHistoryState(userId, billComingDueHistoryState)

            if (initialAssistantMessage != null) {
                createAssistantMessage(userId, CompletionMessage.ofMessage(initialAssistantMessage))
            }

            return billComingDueHistoryState
        }
    }

    private fun setupBillsComingDueConversationHistory(
        userId: UserId,
        initialAssistantMessage: String?,
        initialUserMessage: String?,
        initialUserAndWallet: UserAndWallet? = null,
    ): BillComingDueHistoryState {
        try {
            return when (val state = historyStateRepository.findLatest(userId)) {
                is BillComingDueHistoryState -> state
            }
        } catch (e: UserConversationHistoryNotFound) {
            val billComingDueHistoryState = try {
                setupUserHistoryState(initialUserAndWallet, userId)
            } catch (e: UserNotFoundException) {
                setupNotRegisteredState(userId)
            }

            historyRepository.create(userId = userId, historyStateType = billComingDueHistoryState.type, initialUserMessage)
            saveHistoryState(userId, billComingDueHistoryState)

            if (initialAssistantMessage != null) {
                createAssistantMessage(userId, CompletionMessage.ofMessage(initialAssistantMessage))
            }

            return billComingDueHistoryState
        }
    }

    private fun setupUserHistoryState(
        initialUserAndWallet: UserAndWallet?,
        userId: UserId,
    ): BillComingDueHistoryState {
        val userAndWallet = runBlocking {
            val today = getLocalDate()
            initialUserAndWallet ?: paymentAdapter.findPendingBills(userId, today, today).getOrElse {
                if (it is PaymentAdapterError.UserNotFound) {
                    throw UserNotFoundException(userId.value)
                } else {
                    throw it.toIllegalStateException()
                }
            }
        }

        val contacts =
            paymentAdapter.getContacts(userId, walletId = userAndWallet.wallet.walletId).getOrElse {
                throw it.toIllegalStateException()
            }

        val subscription = paymentAdapter.getSubscription(userId).getOrElse {
            throw it.toIllegalStateException()
        }

        val billComingDueHistoryState =
            BillComingDueHistoryState(
                walletWithBills = userAndWallet.wallet,
                balance = null,
                internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
                user = userAndWallet.user,
                contacts = contacts,
                subscription = subscription,
            )
        return billComingDueHistoryState
    }

    private fun setupNotRegisteredState(userId: UserId): BillComingDueHistoryState {
        val userAndWallet = buildUnregisteredUserAndWallet(userId)
        return BillComingDueHistoryState(
            walletWithBills = userAndWallet.wallet,
            balance = null,
            internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
            user = userAndWallet.user,
            contacts = emptyList(),
            subscription = null,
        )
    }

    fun saveHistoryState(
        userId: UserId,
        historyState: HistoryState,
    ) {
        when (historyState) {
            is BillComingDueHistoryState -> {
                if (historyState.user.accountId.value.isNotEmpty()) {
                    historyStateRepository.save(userId, historyState)
                } else {
                    val markers = Markers.append("historyState", historyState)
                        .andAppend("userId", userId.value)
                        .andAppend("accountId", historyState.user.accountId.value)
                    logger.warn(markers, "ConversationHistoryService#saveHistoryState")
                }
            }
        }
    }

    fun clearHistory(
        userId: UserId,
        template: HistoryStateType,
        localDate: LocalDate,
    ) {
        historyRepository.clearConversationHistory(userId, template, localDate)
        historyStateRepository.delete(userId, localDate)
    }

    inline fun <reified T : HistoryState> updateConversationHistoryState(
        userId: UserId,
        updateFunction: (currentState: T) -> T,
    ): Either<Exception, T> {
        return try {
            val state = findLatestConversationHistoryState<T>(userId)
            val updatedState = updateFunction(state)
            saveHistoryState(userId, updatedState)
            updatedState.right()
        } catch (e: Exception) {
            e.left()
        }
    }

    fun updateBillComingDueHistoryState(
        userId: UserId,
        updateFunction: (currentState: BillComingDueHistoryState) -> BillComingDueHistoryState,
    ): Either<Exception, BillComingDueHistoryState> {
        return try {
            val state = findLatestConversationHistoryState<BillComingDueHistoryState>(userId)
            val updatedState = updateFunction(state)
            historyStateRepository.save(userId, updatedState)
            saveHistoryState(userId, updatedState)
            updatedState.right()
        } catch (e: Exception) {
            e.left()
        }
    }

    inline fun <reified T : HistoryState> findLatestConversationHistoryState(userId: UserId): T {
        val history = historyStateRepository.findLatest(userId)
        if (history is T) {
            return history
        } else {
            throw IllegalArgumentException("History state is not of correct type. Current state: ${history::class.simpleName} - Expected: ${T::class.simpleName}")
        }
    }

    private fun PaymentAdapterError.toIllegalStateException(): IllegalStateException =
        if (this is PaymentAdapterError.ServerError) {
            IllegalStateException(this.exception)
        } else {
            IllegalStateException(this::class.simpleName ?: "")
        }
}

sealed class RefreshUserStateError : PrintableSealedClass() {
    object PendingBillsOutdated : RefreshUserStateError()
}

private data class RefreshedStateResult(
    val currentBalance: Balance?,
    val contacts: List<Contact>,
    val subscription: Subscription?,
    val activeConsents: List<SweepingConsent>,
)