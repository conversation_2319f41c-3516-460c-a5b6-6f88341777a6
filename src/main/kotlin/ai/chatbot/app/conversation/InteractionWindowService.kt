package ai.chatbot.app.conversation

import ai.chatbot.adapters.notification.MessagePublisher
import ai.chatbot.app.InteractionWindowRepository
import ai.chatbot.app.PrintableSealedClass
import ai.chatbot.app.notification.BillComingDueLastWarnDetailsTO
import ai.chatbot.app.notification.BillComingDueRegularDetailsTO
import ai.chatbot.app.notification.ChatBotNotificationGatewayTO
import ai.chatbot.app.notification.GenericNotificationDetailsTO
import ai.chatbot.app.notification.GenericNotificationRawDetailsTO
import ai.chatbot.app.notification.OpenFinanceIncentiveDetailsTO
import ai.chatbot.app.notification.RegisterCompletedTO
import ai.chatbot.app.notification.TestPixreminderDetailsTO
import ai.chatbot.app.notification.WelcomeDetailsTO
import ai.chatbot.app.notification.WelcomeMessageType
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class InteractionWindowService(
    private val repository: InteractionWindowRepository,
    private val messagePublisher: MessagePublisher,
    @Property(name = "aws.sqs.queues.chatBotNotificationGateway") private val inboundNotificationQueue: String,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    fun findActiveWindow(userId: UserId): InteractionWindow? {
        val window = repository.findByUserId(userId)

        return window?.let {
            if (it.expiration.isAfter(getZonedDateTime())) {
                it
            } else {
                close(it)
                null
            }
        }
    }

    fun create(userId: UserId, type: InteractionWindowType): Either<InteractionWindowError, InteractionWindow> {
        findActiveWindow(userId)?.let { return InteractionWindowError.UserAlreadyHasOpenWindow(it).left() }

        val window = InteractionWindow(
            userId = userId,
            type = type,
            expiration = getZonedDateTime().plus(type.duration),
        )

        repository.save(window)
        return window.right()
    }

    fun close(userId: UserId, sendQueuedMessages: Boolean = true) {
        val window = repository.findByUserId(userId)

        window?.let {
            close(it, sendQueuedMessages)
        } ?: throw IllegalArgumentException("user id does not have an open interaction window")
    }

    private fun close(window: InteractionWindow, sendQueuedMessages: Boolean = true) {
        repository.delete(window)

        if (sendQueuedMessages) {
            window.notificationQueue.forEach {
                messagePublisher.sendMessage(inboundNotificationQueue, it, delaySeconds = 60)
            }
        }
    }

    fun expireAllDue() {
        val windows = repository.findAllByStateAndExpirationBefore(InteractionWindowState.ACTIVE, getZonedDateTime())

        windows.forEach {
            close(it, sendQueuedMessages = true)
        }
    }

    private fun queueNotification(window: InteractionWindow, notification: ChatBotNotificationGatewayTO) {
        repository.save(window.copy(notificationQueue = window.notificationQueue + notification))
    }

    fun checkAndCreate(notification: ChatBotNotificationGatewayTO): Either<InteractionWindowError, Unit> {
        val logName = "InteractionWindowService#checkAndCreate"
        val markers = Markers.append("userId", notification.account.msisdn).andAppend("notificationType", notification.javaClass.simpleName)

        val userId = UserId.fromMsisdn(notification.account.msisdn)

        findActiveWindow(userId)?.let { interactionWindow ->
            queueNotification(interactionWindow, notification)
            logger.info(markers.andAppend("interactionWindow", interactionWindow.toLog()), "$logName/messageQueued")
            return InteractionWindowError.UserAlreadyHasOpenWindow(interactionWindow).left()
        }

        val type = getInteractionWindowType(notification)

        if (type != null) {
            val interactionWindow = create(userId, type).getOrElse {
                logger.error(markers.andAppend("error", it).andAppend("created", false), "$logName/errorCreatingInteractionWindow")
                return Unit.right()
            }

            logger.info(markers.andAppend("interactionWindow", interactionWindow.toLog()).andAppend("created", true), logName)
        } else {
            logger.info(markers.andAppend("created", false), logName)
        }

        return Unit.right()
    }

    private fun getInteractionWindowType(
        notification: ChatBotNotificationGatewayTO,
    ) = when (notification.details) {
        is BillComingDueLastWarnDetailsTO,
        is BillComingDueRegularDetailsTO,
        is RegisterCompletedTO,
        is GenericNotificationDetailsTO,
        is TestPixreminderDetailsTO,
        is OpenFinanceIncentiveDetailsTO,
        is GenericNotificationRawDetailsTO,
        -> null

        is WelcomeDetailsTO -> {
            when (notification.details.type) {
                WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX,
                -> InteractionWindowType.ONBOARDING_SINGLE_PIX
            }
        }
    }
}

sealed class InteractionWindowError : PrintableSealedClass() {
    data class UserAlreadyHasOpenWindow(val interactionWindow: InteractionWindow) : InteractionWindowError()
}