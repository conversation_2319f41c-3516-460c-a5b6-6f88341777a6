package ai.chatbot.app.conversation.actions

import ai.chatbot.app.ManualEntryId
import ai.chatbot.app.ManualEntryType
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.conversation.ActionError
import ai.chatbot.app.conversation.ActionExecutor
import ai.chatbot.app.conversation.ActionExecutorCommand
import ai.chatbot.app.conversation.ActionResult
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.NotificationFormatter
import ai.chatbot.app.notification.NotificationMap
import ai.chatbot.app.notification.NotificationParam
import ai.chatbot.app.notification.TextNotificationConfig
import ai.chatbot.app.prompt.DEFAULT_ERROR_MESSAGE
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.brazilDateFormat
import ai.chatbot.app.utils.dateFormat
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import java.time.LocalDate
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class CreateManualEntryExecutor(
    private val paymentAdapter: PaymentAdapter,
    private val customNotificationService: CustomNotificationService,
) : ActionExecutor<ActionExecutorCommand.CreateManualEntryActionExecutorCommand> {
    private val logger = LoggerFactory.getLogger(CreateManualEntryExecutor::class.java)

    override fun validate(command: ActionExecutorCommand.CreateManualEntryActionExecutorCommand): Boolean {
        val logName = "CreateManualEntryExecutor#validate"
        val markers = Markers.append("command", command)

        val validationMessage: Pair<TextNotificationConfig, List<NotificationMap>>? = when (command.action.type) {
            ManualEntryType.EXTERNAL_PAYMENT -> {
                when {
                    command.action.amount <= 0L && command.action.title.isEmpty() -> {
                        customNotificationService.config().createManualEntryErrorNoAmountAndTitle to listOf()
                    }

                    command.action.amount <= 0L -> {
                        customNotificationService.config().createManualEntryErrorNoAmount to listOf(NotificationMap(NotificationParam.TITLE, command.action.title))
                    }

                    command.action.title.isEmpty() -> {
                        customNotificationService.config().createManualEntryErrorNoTitle to listOf(NotificationMap(NotificationParam.AMOUNT, NotificationFormatter.buildFormattedAmount(command.action.amount)))
                    }

                    else -> {
                        null
                    }
                }
            }

            ManualEntryType.REMINDER -> {
                val date = try {
                    LocalDate.parse(command.action.dueDate, dateFormat)
                } catch (e: Exception) {
                    null
                }

                when {
                    date?.isAfter(getLocalDate()) == false -> {
                        customNotificationService.config().createReminderErrorDateInPast to listOf(NotificationMap(NotificationParam.DATE, brazilDateFormat.format(date)))
                    }

                    command.action.dueDate.isEmpty() && command.action.title.isEmpty() -> {
                        customNotificationService.config().createReminderErrorNoTitleAndDate to listOf()
                    }

                    command.action.dueDate.isEmpty() -> {
                        customNotificationService.config().createReminderErrorNoDate to listOf(NotificationMap(NotificationParam.TITLE, command.action.title))
                    }

                    command.action.title.isEmpty() -> {
                        customNotificationService.config().createReminderErrorNoTitle to listOf(NotificationMap(NotificationParam.DATE, brazilDateFormat.format(date)))
                    }

                    // Date is not empty but could not be parsed
                    date == null -> {
                        logger.error(markers, "$logName/parseDate")
                        customNotificationService.send(command.user.id.value, command.user.accountId, DEFAULT_ERROR_MESSAGE)
                        return false
                    }

                    else -> {
                        null
                    }
                }
            }
        }

        if (validationMessage != null) {
            customNotificationService.send(user = command.user, notificationConfig = validationMessage.first, params = validationMessage.second)
            logger.warn(markers, logName)
            return false
        }

        logger.info(markers, logName)
        return true
    }

    override fun execute(command: ActionExecutorCommand.CreateManualEntryActionExecutorCommand): Either<ActionError, ActionResult> {
        val logName = "CreateManualEntryExecutor#execute"
        val markers = Markers.append("command", command)

        try {
            val result = paymentAdapter.createManualEntry(
                userId = command.user.id,
                title = command.action.title,
                description = "",
                amount = command.action.amount,
                type = command.action.type,
                dueDate = LocalDate.parse(command.action.dueDate, dateFormat),
            ).getOrElse {
                val actionMessage = it.toActionMessage()
                return ActionError(needCompletion = actionMessage.needCompletion, message = actionMessage.message, actionType = ActionType.CREATE_MANUAL_ENTRY).left()
            }

            val (notificationConfig, notificationParams) = getSuccessMessage(command, result)

            customNotificationService.send(
                user = command.user,
                notificationConfig = notificationConfig,
                params = notificationParams,
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return ActionError(needCompletion = false, message = DEFAULT_ERROR_MESSAGE, actionType = ActionType.REMOVE_MANUAL_ENTRY).left()
        }

        logger.info(markers, logName)
        return ActionResult.WithoutCompletion(actionType = ActionType.CREATE_MANUAL_ENTRY).right()
    }

    private fun getSuccessMessage(command: ActionExecutorCommand.CreateManualEntryActionExecutorCommand, result: ManualEntryId): Pair<TextNotificationConfig, List<NotificationMap>> {
        return when (command.action.type) {
            ManualEntryType.EXTERNAL_PAYMENT -> {
                customNotificationService.config().createManualEntrySuccess to listOf(
                    NotificationMap(NotificationParam.TITLE, command.action.title),
                    NotificationMap(NotificationParam.AMOUNT, NotificationFormatter.buildFormattedAmount(command.action.amount)),
                    NotificationMap(NotificationParam.MANUAL_ENTRY_ID, result.value),
                )
            }

            ManualEntryType.REMINDER -> {
                val date = LocalDate.parse(command.action.dueDate, dateFormat)

                if (command.action.amount == 0L) {
                    customNotificationService.config().createReminderSuccessNoAmount to listOf(
                        NotificationMap(NotificationParam.TITLE, command.action.title),
                        NotificationMap(NotificationParam.DATE, brazilDateFormat.format(date)),
                        NotificationMap(NotificationParam.MANUAL_ENTRY_ID, result.value),
                    )
                } else {
                    customNotificationService.config().createReminderSuccess to listOf(
                        NotificationMap(NotificationParam.TITLE, command.action.title),
                        NotificationMap(NotificationParam.DATE, brazilDateFormat.format(date)),
                        NotificationMap(NotificationParam.AMOUNT, NotificationFormatter.buildFormattedAmount(command.action.amount)),
                        NotificationMap(NotificationParam.MANUAL_ENTRY_ID, result.value),
                    )
                }
            }
        }
    }
}

@Singleton
class RemoveManualEntryExecutor(
    private val paymentAdapter: PaymentAdapter,
    private val customNotificationService: CustomNotificationService,
) : ActionExecutor<ActionExecutorCommand.RemoveManualEntryActionExecutorCommand> {
    private val logger = LoggerFactory.getLogger(CreateManualEntryExecutor::class.java)

    override fun validate(command: ActionExecutorCommand.RemoveManualEntryActionExecutorCommand): Boolean = true

    override fun execute(command: ActionExecutorCommand.RemoveManualEntryActionExecutorCommand): Either<ActionError, ActionResult> {
        val logName = "RemoveManualEntryExecutor#execute"
        val markers = Markers.append("command", command)

        try {
            paymentAdapter.removeManualEntry(
                userId = command.user.id,
                manualEntryId = ManualEntryId(command.action.manualEntryId),
            ).getOrElse {
                val actionMessage = it.toActionMessage()
                return ActionError(needCompletion = actionMessage.needCompletion, message = actionMessage.message, actionType = ActionType.REMOVE_MANUAL_ENTRY).left()
            }

            customNotificationService.send(
                user = command.user,
                notificationConfig = customNotificationService.config().removeManualEntrySuccess,
                params = listOf(),
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return ActionError(needCompletion = false, message = DEFAULT_ERROR_MESSAGE, actionType = ActionType.REMOVE_MANUAL_ENTRY).left()
        }

        logger.info(markers, logName)
        return ActionResult.WithoutCompletion(actionType = ActionType.REMOVE_MANUAL_ENTRY).right()
    }
}