package ai.chatbot.app.conversation.actions

import ai.chatbot.app.NotificationService
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.PixLimitStatus
import ai.chatbot.app.PixValidationResult
import ai.chatbot.app.SearchElement
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.TextSearchAdapter
import ai.chatbot.app.TransactionService
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.conversation.ActionError
import ai.chatbot.app.conversation.ActionExecutor
import ai.chatbot.app.conversation.ActionExecutorCommand
import ai.chatbot.app.conversation.ActionResult
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.conversation.Subscription
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.KnownNotificationTypes
import ai.chatbot.app.notification.KnownTemplateConfigurationKeys
import ai.chatbot.app.notification.NotificationFormatter
import ai.chatbot.app.notification.NotificationMap
import ai.chatbot.app.notification.NotificationParam
import ai.chatbot.app.notification.RawTemplateNotificationConfig
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.pix.PixQRCode
import ai.chatbot.app.prompt.WALLET_NOT_FOUND
import ai.chatbot.app.transaction.PixTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.formattedAmount
import ai.chatbot.app.utils.formattedBalance
import ai.chatbot.app.utils.maskDocumentSpecialAsterisk
import ai.chatbot.app.utils.validateCPF
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import java.time.LocalDate
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

internal const val subscriptionWarning = " e também pagará a sua assinatura em aberto"

@Singleton
class PixTransactionExecutor(
    private val paymentAdapter: PaymentAdapter,
    private val conversationHistoryService: ConversationHistoryService,
    private val notificationService: NotificationService,
    private val transactionService: TransactionService,
    private val textSearchAdapter: TextSearchAdapter,
    private val buildNotificationService: BuildNotificationService,
    private val customNotificationService: CustomNotificationService,
) : ActionExecutor<ActionExecutorCommand.PixTransactionActionExecutorCommand> {
    private val logger = LoggerFactory.getLogger(PixTransactionExecutor::class.java)

    override fun validate(command: ActionExecutorCommand.PixTransactionActionExecutorCommand): Boolean {
        val logName = "PixTransactionExecutor#validate"
        val markers = Markers.append("command", command)

        if (command.action.amount == 0L && command.action.type != "COPY_PASTE") {
            val message = "Pode me informar qual o valor que você deseja enviar?"
            notificationService.notify(command.user.id, command.user.accountId, message)
            conversationHistoryService.createAssistantMessage(command.user.id, message)
            markers.andAppend("errorMessage", message)
            logger.info(markers, logName)
            return false
        }

        if (parsePixKeyType(command).isEmpty()) {
            val message = "Pode me informar o tipo da chave Pix para qual você deseja efetuar o pagamento?"
            notificationService.notify(command.user.id, command.user.accountId, message)
            conversationHistoryService.createAssistantMessage(command.user.id, message)
            markers.andAppend("errorMessage", message)
            logger.info(markers, logName)
            return false
        }

        if (command.action.key.isEmpty()) {
            val message = "Pode me informar a chave Pix para qual você deseja efetuar o pagamento?"
            notificationService.notify(command.user.id, command.user.accountId, message)
            conversationHistoryService.createAssistantMessage(command.user.id, message)
            markers.andAppend("errorMessage", message)
            logger.info(markers, logName)
            return false
        }

        logger.info(markers, logName)
        return true
    }

    override fun execute(command: ActionExecutorCommand.PixTransactionActionExecutorCommand): Either<ActionError, ActionResult> {
        val logName = "PixTransactionExecutor#execute"
        val markers = Markers.append("command", command)

        var currentState = conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(userId = command.user.id)
        markers.andAppend("walletId", currentState.walletWithBills.walletId?.value)

        if (currentState.walletWithBills.walletId == null) {
            logger.warn(markers, logName)
            return ActionError(
                needCompletion = false,
                message = WALLET_NOT_FOUND,
                actionType = ActionType.PIX_TRANSACTION,
            ).left()
        }

        val walletId = currentState.walletWithBills.walletId!!

        val pixKey = resolvePixKey(command, currentState).getOrElse {
            markers.andAppend("actionResult", it)
            logger.info(markers, logName)
            return it.right()
        }
        val qrCode = if (pixKey.type == PixKeyType.COPY_PASTE) command.action.key else null
        markers.andAppend("pixKey", pixKey)

        val pixTransactionValidationResult = pixValidation(pixKey, command, markers, walletId = walletId).getOrElse {
            markers.andAppend("actionResult", it)
            logger.info(markers, logName)
            return it.left()
        }

        markers.andAppend("pixValidation", pixTransactionValidationResult)

        if (pixTransactionValidationResult.pixValidationResult.pixLimitStatus == PixLimitStatus.EXCEEDS_DAILY_LIMIT) {
            handleDailyLimitExceeded(command)
            logger.info(markers, logName)
            return ActionResult.WithoutCompletion(actionType = ActionType.PIX_TRANSACTION).right()
        }

        val balance =
            runBlocking {
                paymentAdapter.getBalanceAndForecast(
                    userId = command.user.id,
                    walletId = walletId,
                )
            }.getOrElse {
                markers.andAppend("error", it)
                logger.error(markers, logName)
                return it.toActionMessage().toActionErrorLeft(actionType = ActionType.PIX_TRANSACTION)
            }
        markers.andAppend("balance", balance)

        currentState = conversationHistoryService.updateBillComingDueHistoryState(command.user.id) { state ->
            val activeConsents = paymentAdapter.getActiveSweepingConsents(state.user.id, state.walletWithBills.walletId).getOrElse {
                markers.andAppend("activeConsentsError", it.toString())
                state.walletWithBills.activeConsents
            }

            state.copy(
                balance = balance,
                walletWithBills = state.walletWithBills.copy(
                    activeConsents = activeConsents,
                ),
            )
        }.getOrElse {
            logger.error(markers, logName, it)
            return ActionError(
                needCompletion = false,
                message = WALLET_NOT_FOUND,
                actionType = ActionType.PIX_TRANSACTION,
            ).left()
        }
        markers.andAppend("activeConsents", currentState.walletWithBills.activeConsents)

        if (balance.current < pixTransactionValidationResult.amount) {
            if (!currentState.walletWithBills.hasSweepingAccount) {
                logger.info(markers, logName)
                return insufficientBalanceError
            }

            val subscriptionFee = subscriptionFeeFrom(currentState.subscription)
            val sweepingTotal = pixTransactionValidationResult.amount + subscriptionFee - balance.current
            markers.andAppend("sweepingTotal", sweepingTotal)

            // comparamos o limite do consentimento com o sweepingTotal, que leva em consideracao o balance e subscriptionFee
            val availableConsents = currentState.walletWithBills.sweepingConsentsWithTransactionLimitFor(sweepingTotal).sortedByLastCashInAndName()
            markers.andAppend("availableConsents", availableConsents.size)

            if (availableConsents.isEmpty()) {
                val transaction = createPixTransaction(
                    userId = command.user.id,
                    walletId = walletId,
                    pixTransactionValidationResult = pixTransactionValidationResult,
                    hasSubscriptionFee = subscriptionFee > 0,
                    qrCode = qrCode,
                    sweepingAmount = null,
                    sweepingParticipantId = null,
                )
                buildNotificationService.buildPixAmountExceedsSweepingTransactionLimit(
                    accountId = command.user.accountId,
                    mobilePhone = command.user.id.value,
                    transactionId = transaction.id,
                ).let { notificationService.notify(it) }
                conversationHistoryService.createAssistantMessage(command.user.id, "O valor do pix solicitado pelo usuário excede o limite configurado na conta conectada.")
            } else {
                notifyPixWithSweeping(
                    user = command.user,
                    walletId = walletId,
                    pixTransactionValidationResult = pixTransactionValidationResult,
                    availableConsents = availableConsents,
                    sweepingAmount = sweepingTotal,
                    qrCode = qrCode,
                    hasSubscriptionFee = subscriptionFee > 0,
                )
            }
        } else {
            notifyPix(command.user, walletId, pixTransactionValidationResult, qrCode)
        }

        logger.info(markers, logName)
        return ActionResult.WithoutCompletion(actionType = ActionType.PIX_TRANSACTION).right()
    }

    private fun subscriptionFeeFrom(subscription: Subscription?): Long {
        return if (subscription != null &&
            subscription.type == SubscriptionType.PIX &&
            subscription.fee != null &&
            (subscription.isOverDue() || subscription.dueDate == LocalDate.now())
        ) {
            subscription.fee
        } else {
            0
        }
    }

    private fun pixValidation(
        pixKey: PixKey,
        command: ActionExecutorCommand.PixTransactionActionExecutorCommand,
        markers: LogstashMarker,
        walletId: WalletId,
    ): Either<ActionError, PixTransactionValidationResult> {
        val validation = paymentAdapter.run {
            if (pixKey.type == PixKeyType.COPY_PASTE) {
                validatePixQrCode(qrCode = pixKey.value, amount = command.action.amount, userId = command.user.id)
            } else {
                validateWithFallback(pixKey, command, markers, walletId)
            }
        }.getOrElse {
            if (it is PaymentAdapterError.ServerError) {
                logger.error(markers, "PixTransactionExecutor#execute", it.exception)
            } else {
                markers.andAppend("error", it)
                logger.error(markers, "PixTransactionExecutor#execute")
            }
            return it.toActionMessage().toActionErrorLeft(actionType = ActionType.PIX_TRANSACTION)
        }

        val amount = validation.amount ?: command.action.amount

        return PixTransactionValidationResult(validation, amount).right()
    }

    private fun PaymentAdapter.validateWithFallback(
        pixKey: PixKey,
        command: ActionExecutorCommand.PixTransactionActionExecutorCommand,
        markers: LogstashMarker,
        walletId: WalletId,
    ): Either<PaymentAdapterError, PixValidationResult> {
        val validationResult = pixValidation(pixKey, command.user.id, command.action.amount, walletId)

        validationResult.mapLeft { result ->
            val fallbackPixKey = pixKey.getFallback()

            if (result is PaymentAdapterError.PixKeyNotFound && fallbackPixKey != null) {
                val fallbackResult = pixValidation(fallbackPixKey, command.user.id, command.action.amount, walletId)

                if (fallbackResult.isRight()) {
                    logger.warn(markers.andAppend("fallbackPixKey", fallbackPixKey), "PixTransactionExecutor#validateWithFallback/pixKeyFoundOnFallback")
                    return fallbackResult
                }
            }
        }

        return validationResult
    }

    private fun PixKey.getFallback(): PixKey? {
        return when (this.type) {
            PixKeyType.CPF -> PixKey(this.value, PixKeyType.PHONE)
            PixKeyType.PHONE -> PixKey(this.value.removePrefix("+55"), PixKeyType.CPF)
            PixKeyType.CNPJ,
            PixKeyType.EMAIL,
            PixKeyType.EVP,
            PixKeyType.COPY_PASTE,
            -> null
        }
    }

    private val insufficientBalanceError = ActionError(
        needCompletion = false,
        message = "Você não tem saldo suficiente para realizar a transação.",
        actionType = ActionType.PIX_TRANSACTION,
    ).left()

    private fun PixTransactionValidationResult.mustAuthorizeOnApp() = this.pixValidationResult.pixLimitStatus == PixLimitStatus.EXCEEDS_ASSISTANT_LIMIT

    private fun handleDailyLimitExceeded(command: ActionExecutorCommand.PixTransactionActionExecutorCommand) {
        val message = """
                    Desculpe, mas esse valor excede seu limite diário de Pix.
                    
                    Você pode configurar seu limite em Carteira > Limites Transacionais.
        """.trimIndent()

        notificationService.notify(command.user.id, command.user.accountId, message)
        conversationHistoryService.createAssistantMessage(command.user.id, message)
    }

    private fun resolvePixKey(command: ActionExecutorCommand.PixTransactionActionExecutorCommand, state: BillComingDueHistoryState): Either<ActionResult.WithoutCompletion, PixKey> {
        val type = parsePixKeyType(command)

        return if (type == "CONTACT") {
            resolveContactPixKey(command, state)
        } else {
            val keyType = PixKeyType.valueOf(type)

            if (keyType == PixKeyType.PHONE && command.action.key.length == 13) {
                PixKey(value = command.action.key.substring(2), type = keyType)
            } else {
                PixKey(value = command.action.key, type = keyType)
            }.right()
        }
    }

    private fun resolveContactPixKey(command: ActionExecutorCommand.PixTransactionActionExecutorCommand, state: BillComingDueHistoryState): Either<ActionResult.WithoutCompletion, PixKey> {
        val ignoreLastUsedPixKey = command.action.ignoreLastUsed != null && command.action.ignoreLastUsed

        val contacts =
            textSearchAdapter.search(command.action.key, state.contacts.toSearchElements()).mapNotNull { rawId ->
                state.contacts.find { it.id.value == rawId }
            }

        if (contacts.isEmpty()) {
            val message = "Não encontrei nenhum contato procurando por ${command.action.key}. Você tem a chave pix desse contato?"

            notificationService.notify(command.user.id, command.user.accountId, message)
            conversationHistoryService.createAssistantMessage(command.user.id, message)
            return ActionResult.WithoutCompletion(actionType = ActionType.PIX_TRANSACTION).left()
        }

        if (contacts.size > 1) {
            val message =
                """
                    |Encontrei ${contacts.size} contatos procurando por ${command.action.key}.
                    |
                    |${NotificationFormatter.buildContactListMessage(contacts = contacts).trim()}
                    |  
                    |Qual deles você deseja enviar o pix?
                """.trimMargin()

            notificationService.notify(command.user.id, command.user.accountId, message)
            conversationHistoryService.createAssistantMessage(command.user.id, message)
            return ActionResult.WithoutCompletion(actionType = ActionType.PIX_TRANSACTION).left()
        }

        val contact = contacts.first()

        if (contact.pixKeys.size > 1 && contact.lastUsed?.pixKey != null && !ignoreLastUsedPixKey) {
            val message =
                """
                    |A ultima chave pix utilizada para esse contato é:
                    |
                    |${contact.lastUsed.pixKey.type.displayName}: ${contact.lastUsed.pixKey.value}
                    |
                    |Deseja usar esta chave?
                """.trimMargin()
            notificationService.notify(command.user.id, command.user.accountId, message)
            conversationHistoryService.createAssistantMessage(command.user.id, message)
            return ActionResult.WithoutCompletion(actionType = ActionType.PIX_TRANSACTION).left()
        }

        if (contact.pixKeys.size > 1) {
            val message =
                """
                    |O contato possui mais de uma chave pix. Por favor, informe a chave desejada:
                    |${contact.pixKeys.joinToString(separator = "\n") { "${it.type.displayName}: ${it.value}" }}
                """.trimMargin()
            notificationService.notify(command.user.id, command.user.accountId, message)
            conversationHistoryService.createAssistantMessage(command.user.id, message)
            return ActionResult.WithoutCompletion(actionType = ActionType.PIX_TRANSACTION).left()
        }

        if (contact.pixKeys.isEmpty()) {
            val message =
                """
                    |Não encontrei uma chave pix para esse contato. Por favor, informe uma chave pix.
                """.trimMargin()
            notificationService.notify(command.user.id, command.user.accountId, message)
            conversationHistoryService.createAssistantMessage(command.user.id, message)
            return ActionResult.WithoutCompletion(actionType = ActionType.PIX_TRANSACTION).left()
        }

        return contact.pixKeys.first().right()
    }

    private fun createPixTransaction(
        userId: UserId,
        walletId: WalletId,
        pixTransactionValidationResult: PixTransactionValidationResult,
        hasSubscriptionFee: Boolean,
        qrCode: String?,
        sweepingAmount: Long?,
        sweepingParticipantId: SweepingParticipantId?,
        transactionGroupId: TransactionGroupId? = null,
    ) = transactionService.create(
        userId = userId,
        walletId = walletId,
        transactionGroupId = transactionGroupId,
        details =
        PixTransactionDetails(
            amount = pixTransactionValidationResult.amount,
            sweepingAmount = sweepingAmount,
            sweepingParticipantId = sweepingParticipantId,
            pixKey = PixKey(pixTransactionValidationResult.pixValidationResult.keyValue, pixTransactionValidationResult.pixValidationResult.keyType),
            recipientName = pixTransactionValidationResult.pixValidationResult.recipientName,
            recipientDocument = pixTransactionValidationResult.pixValidationResult.recipientDocument,
            recipientInstitution = pixTransactionValidationResult.pixValidationResult.recipientInstitution,
            qrCode = qrCode?.let { PixQRCode(it) },
            hasSubscriptionFee = hasSubscriptionFee,
        ),
    )

    private fun notifyPix(
        user: User,
        walletId: WalletId,
        pixTransactionValidationResult: PixTransactionValidationResult,
        qrCode: String?,
    ) {
        val transaction = createPixTransaction(
            userId = user.id,
            walletId = walletId,
            pixTransactionValidationResult = pixTransactionValidationResult,
            hasSubscriptionFee = false,
            qrCode = qrCode,
            sweepingAmount = null,
            sweepingParticipantId = null,
        )

        val config = if (pixTransactionValidationResult.mustAuthorizeOnApp()) {
            KnownTemplateConfigurationKeys.authorizePix
        } else {
            KnownTemplateConfigurationKeys.pixConfirmation
        }

        customNotificationService.send(
            user = user,
            notificationConfig = RawTemplateNotificationConfig(config, KnownNotificationTypes.PIX_CONFIRMATION),
            params = buildNotificationParams(transaction, pixTransactionValidationResult.pixValidationResult),
        )
    }

    private fun notifyPixWithSweeping(user: User, walletId: WalletId, pixTransactionValidationResult: PixTransactionValidationResult, availableConsents: List<SweepingConsent>, sweepingAmount: Long, qrCode: String?, hasSubscriptionFee: Boolean) {
        val transactionGroupId = TransactionGroupId()
        val transactions = availableConsents.associate {
            it.participant.id to createPixTransaction(
                userId = user.id,
                walletId = walletId,
                transactionGroupId = transactionGroupId,
                pixTransactionValidationResult = pixTransactionValidationResult,
                hasSubscriptionFee = hasSubscriptionFee,
                qrCode = qrCode,
                sweepingAmount = sweepingAmount,
                sweepingParticipantId = it.participant.id,
            )
        }

        val (config, extraParams) = if (pixTransactionValidationResult.mustAuthorizeOnApp()) {
            val primaryConsent = availableConsents.sortedBy { it.lastSuccessfulCashIn }.reversed().first()

            if (hasSubscriptionFee) {
                customNotificationService.config().authorizePixSweeping to listOf(
                    NotificationMap(NotificationParam.TRANSACTION_GROUP_ID, transactions[primaryConsent.participant.id]!!.groupId.value),
                    NotificationMap(NotificationParam.SUBSCRIPTION_WARNING, subscriptionWarning),
                )
            } else {
                customNotificationService.config().authorizePixSweeping to listOf(
                    NotificationMap(NotificationParam.TRANSACTION_GROUP_ID, transactions[primaryConsent.participant.id]!!.groupId.value),
                    NotificationMap(NotificationParam.SUBSCRIPTION_WARNING, ""),
                )
            }
        } else {
            when {
                availableConsents.size == 2 -> {
                    val ofBalances = paymentAdapter.getOpenFinanceBalances(user.id, walletId, availableConsents.map { it.participant.id }).getOrElse {
                        logger.warn(Markers.append("error", it), "PixTransactionExecutor#notifyPixWithSweeping")
                        emptyMap()
                    }

                    customNotificationService.config().pixConfirmationMultipleSweepingConsent to
                        availableConsents[0].buildNotificationParams(
                            NotificationParam.CONTA1_NOME,
                            NotificationParam.CONTA1_NOME_CURTO,
                            NotificationParam.CONTA1_SALDO,
                            NotificationParam.TRANSACTION_ID,
                            ofBalances,
                            transactions,
                        ) + availableConsents[1].buildNotificationParams(
                            NotificationParam.CONTA2_NOME,
                            NotificationParam.CONTA2_NOME_CURTO,
                            NotificationParam.CONTA2_SALDO,
                            NotificationParam.TRANSACTION_ID_2,
                            ofBalances,
                            transactions,
                        )
                }

                availableConsents.size > 2 -> {
                    val primaryConsent = availableConsents.sortedBy { it.lastSuccessfulCashIn }.reversed().first()

                    val ofBalances = paymentAdapter.getOpenFinanceBalances(user.id, walletId, listOf(primaryConsent.participant.id)).getOrElse {
                        logger.warn(Markers.append("error", it), "PixTransactionExecutor#notifyPixWithSweeping")
                        emptyMap()
                    }

                    customNotificationService.config().pixConfirmationSelectSweepingConsent to
                        primaryConsent.buildNotificationParams(
                            NotificationParam.CONTA1_NOME,
                            NotificationParam.CONTA1_NOME_CURTO,
                            NotificationParam.CONTA1_SALDO,
                            NotificationParam.TRANSACTION_ID,
                            ofBalances,
                            transactions,
                        ) + listOf(
                            NotificationMap(NotificationParam.TRANSACTION_GROUP_ID, transactions[primaryConsent.participant.id]!!.groupId.value),
                        )
                }

                else -> {
                    val primaryConsent = availableConsents.sortedBy { it.lastSuccessfulCashIn }.reversed().first()

                    val ofBalances = paymentAdapter.getOpenFinanceBalances(user.id, walletId, listOf(primaryConsent.participant.id)).getOrElse {
                        logger.warn(Markers.append("error", it), "PixTransactionExecutor#notifyPixWithSweeping")
                        emptyMap()
                    }

                    customNotificationService.config().pixConfirmationSingleSweepingConsent to listOf(
                        NotificationMap(NotificationParam.CONTA1_NOME, primaryConsent.participant.name),
                        NotificationMap(NotificationParam.CONTA1_NOME_CURTO, primaryConsent.participant.shortName.take(10)),
                        NotificationMap(NotificationParam.CONTA1_SALDO, ofBalances[primaryConsent.participant.id].formattedBalance()),
                    )
                }
            }
        }

        val subscripitionParams = if (hasSubscriptionFee) {
            NotificationMap(NotificationParam.SUBSCRIPTION_WARNING, subscriptionWarning)
        } else {
            NotificationMap(NotificationParam.SUBSCRIPTION_WARNING, "")
        }

        customNotificationService.send(
            user = user,
            notificationConfig = config,
            params = buildNotificationParams(transactions[availableConsents[0].participant.id]!!, pixTransactionValidationResult.pixValidationResult) + extraParams + subscripitionParams,
        )
    }

    private fun SweepingConsent.buildNotificationParams(name: NotificationParam, shortName: NotificationParam, balance: NotificationParam, transactionId: NotificationParam, balances: Map<SweepingParticipantId, Long?>, transactions: Map<SweepingParticipantId, Transaction>): List<NotificationMap> {
        return listOf(
            NotificationMap(name, participant.name),
            NotificationMap(shortName, participant.shortName.take(10)),
            NotificationMap(balance, balances[participant.id].formattedBalance()),
            NotificationMap(transactionId, transactions[participant.id]!!.id.value),
        )
    }

    private fun buildNotificationParams(transaction: Transaction, pixValidation: PixValidationResult) = listOf(
        NotificationMap(NotificationParam.RECIPIENT_NAME, pixValidation.recipientName),
        NotificationMap(NotificationParam.RECIPIENT_DOCUMENT, maskDocumentSpecialAsterisk(pixValidation.recipientDocument)),
        NotificationMap(NotificationParam.RECIPIENT_INSTITUTION, pixValidation.recipientInstitution),
        NotificationMap(NotificationParam.AMOUNT, (transaction.details as PixTransactionDetails).amount.formattedAmount()),
        NotificationMap(NotificationParam.PIX_KEY, pixValidation.keyValue),
        NotificationMap(NotificationParam.TRANSACTION_ID, transaction.id.value),
    )

    private fun List<Contact>.toSearchElements(): List<SearchElement> {
        return this.map {
            SearchElement(
                id = it.id.value,
                fields =
                buildMap {
                    put("name", it.name)
                    if (it.alias != null) {
                        put("alias", it.alias)
                    }
                },
            )
        }
    }

    private fun parsePixKeyType(command: ActionExecutorCommand.PixTransactionActionExecutorCommand) =
        if (command.action.type == "ELEVEN_DIGIT") {
            val isCPF = validateCPF(command.action.key)
            if (isCPF) "CPF" else "PHONE"
        } else {
            command.action.type
        }

    data class PixTransactionValidationResult(
        val pixValidationResult: PixValidationResult,
        val amount: Long,
    )
}