package ai.chatbot.app.conversation.actions

import ai.chatbot.app.NotificationService
import ai.chatbot.app.OnePixPayInstrumentation
import ai.chatbot.app.TransactionService
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.conversation.ActionError
import ai.chatbot.app.conversation.ActionExecutor
import ai.chatbot.app.conversation.ActionExecutorCommand
import ai.chatbot.app.conversation.ActionResult
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.PaymentType
import ai.chatbot.app.conversation.RefreshUserStateError
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.NotificationFormatter
import ai.chatbot.app.prompt.WALLET_NOT_FOUND
import ai.chatbot.app.transaction.ScheduleBillsTransactionDetails
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.timeSince
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class MakePaymentExecutor(
    private val notificationService: NotificationService,
    private val conversationHistoryService: ConversationHistoryService,
    private val onePixPayInstrumentation: OnePixPayInstrumentation,
    private val buildNotificationService: BuildNotificationService,
    private val actionExecutorsHelpers: ActionExecutorsHelpers,
    private val transactionService: TransactionService,
) : ActionExecutor<ActionExecutorCommand.MakePaymentActionExecutorCommand> {
    private val logger = LoggerFactory.getLogger(MakePaymentExecutor::class.java)

    override fun execute(command: ActionExecutorCommand.MakePaymentActionExecutorCommand): Either<ActionError, ActionResult> {
        val logName = "MakePaymentExecutor#execute"
        val startTime = getZonedDateTime()
        val markers = Markers.append("command", command)

        val billIds = command.action.bills

        val currentState = conversationHistoryService.updateConversationHistoryState<BillComingDueHistoryState>(command.user.id) { state ->
            val (balanceResult, activeConsentsResult) = actionExecutorsHelpers.refreshState(command.user, state)
            val balance = balanceResult.getOrNull()
            val activeConsents = activeConsentsResult.getOrElse { emptyList() }
            state.copy(
                balance = balance,
                walletWithBills = state.walletWithBills.copy(
                    activeConsents = activeConsents,
                ),
            )
        }.getOrElse { conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(userId = command.user.id) }

        val selectedBills =
            currentState.walletWithBills.bills.filter { bill ->
                billIds.find { it == bill.externalBillId } != null
            }
        markers.andAppend("selectedBills", selectedBills)

        val walletId = currentState.walletWithBills.walletId
        if (walletId == null) {
            logger.error(markers, logName)
            return ActionError(
                needCompletion = false,
                message = WALLET_NOT_FOUND,
                actionType = ActionType.MAKE_PAYMENT,
            ).left()
        }

        val currentBalance = currentState.balance?.current ?: 0
        val allBillsAmount = selectedBills.sumOf { it.amountTotal }
        val amountMissing = allBillsAmount - currentBalance
        markers.andAppend("currentBalance", currentBalance)
            .andAppend("allBillsAmount", allBillsAmount)
            .andAppend("amountMissing", amountMissing)

        when (PaymentType.valueOf(command.action.type)) {
            PaymentType.PIX -> {
                logger.info(markers.andAppend("timeTaken", timeSince(startTime)), logName)
                return actionExecutorsHelpers.sendOnePixPay(command.user, selectedBills, walletId, currentState, actionType = ActionType.MAKE_PAYMENT)
            }

            PaymentType.SCHEDULE -> {
                logger.info(markers.andAppend("timeTaken", timeSince(startTime)), logName)

                //
                // Pedir confirmação se o usuário falou por texto em vez de confirmar pelo botão
                if (!command.action.confirmation) {
                    val transaction = transactionService.create(
                        userId = command.user.id,
                        walletId = currentState.walletWithBills.walletId,
                        details = ScheduleBillsTransactionDetails(selectedBills.map { it.billId }),
                    )

                    sendConfirmationMessage(
                        selectedBills,
                        command,
                        transaction.id,
                        InterceptMessagePayloadType.SCHEDULE_BILLS,
                    )

                    logger.info(markers.andAppend("timeTaken", timeSince(startTime)), logName)
                    return ActionResult.WithoutCompletion(actionType = ActionType.MAKE_PAYMENT).right()
                }

                return actionExecutorsHelpers.scheduleBills(
                    user = command.user,
                    selectedBills = selectedBills,
                    amountMissing = amountMissing,
                    walletId = walletId,
                    state = currentState,
                    actionType = ActionType.MAKE_PAYMENT,
                )
            }
        }
    }

    override fun validate(command: ActionExecutorCommand.MakePaymentActionExecutorCommand): Boolean {
        val billIds = command.action.bills
        val currentState =
            conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(userId = command.user.id)
        val hasSubscriptionOverdue =
            currentState.walletWithBills.bills.any { bill ->
                bill.subscriptionFee && (bill.dueDate == getLocalDate() || bill.dueDate.isBefore(getLocalDate()))
            }
        val selectedBills =
            currentState.walletWithBills.bills.filter { bill ->
                hasSubscriptionOverdue && bill.subscriptionFee || billIds.find { it == bill.externalBillId } != null
            }

        // Verifica se a lista de bills está desatualizada
        conversationHistoryService.refreshPendingBills(userId = command.user.id).getOrElse {
            when (it) {
                is RefreshUserStateError.PendingBillsOutdated -> {
                    onePixPayInstrumentation.billsChangedAfterInteraction(command.user, InterceptMessagePayloadType.SCHEDULE_BILLS)
                    sendBillsOutdatedNotification(command)

                    return false
                }
            }
        }

        if (billIds.isEmpty()) {
            actionExecutorsHelpers.assistantNotification(command.user, "Você deve selecionar ao menos uma conta para pagamento.")
            return false
        }

        if (selectedBills.isEmpty()) {
            actionExecutorsHelpers.assistantNotification(command.user, "A conta selecionada não foi encontrada ou já está paga, por favor tente novamente.")
            conversationHistoryService.synchronizeBeforeCompletion(userId = command.user.id)
            return false
        }

        val walletId = currentState.walletWithBills.walletId
        if (walletId == null) {
            actionExecutorsHelpers.assistantNotification(command.user, "Carteira não encontrada.")
            return false
        }

        try {
            PaymentType.valueOf(command.action.type)
        } catch (e: Exception) {
            actionExecutorsHelpers.assistantNotification(command.user, "Desculpe não entendi qual tipo de pagamento você deseja utilizar? As opções são Pix, agendamento com saldo ou agendamento com saldo da conta open finance.")
            return false
        }

        if (currentState.walletWithBills.bills.size > 1 && hasSubscriptionOverdue) {
            actionExecutorsHelpers.assistantNotification(command.user, "Sua assinatura está vencendo ou já está vencida, vou incluir o valor da assinatura na transação.")
        }

        val (balanceResult, activeConsentsResult) = actionExecutorsHelpers.refreshState(command.user, currentState)

        val balance = balanceResult.getOrElse { return true }
        val activeConsents = activeConsentsResult.getOrElse { return true }

        conversationHistoryService.updateConversationHistoryState<BillComingDueHistoryState>(command.user.id) { state ->
            state.copy(
                balance = balance,
                walletWithBills = state.walletWithBills.copy(
                    activeConsents = activeConsents,
                ),
            )
        }

        return true
    }

    private fun sendBillsOutdatedNotification(command: ActionExecutorCommand.MakePaymentActionExecutorCommand) {
        val state = when (val state = conversationHistoryService.historyStateRepository.findLatest(command.user.id)) {
            is BillComingDueHistoryState -> state
        }

        if (state.walletWithBills.bills.isEmpty()) {
            actionExecutorsHelpers.assistantNotification(command.user, "Você não tem nenhuma conta pendente para hoje")
        } else {
            val formattedBillsMessage = NotificationFormatter.buildBillNotificationMessage(state.walletWithBills.bills)

            val notification = buildNotificationService.buildOutdatedPendingBillsNotification(
                user = state.user,
                formattedBillsMessage = formattedBillsMessage,
            )

            notificationService.notify(
                userId = state.user.id,
                accountId = state.user.accountId,
                message = notification.message,
                quickReplyButtons = notification.quickReplyButtons,
            )

            conversationHistoryService.createAssistantMessage(
                userId = command.user.id,
                message = notification.message,
            )
        }
    }

    private fun sendConfirmationMessage(selectedBills: List<BillView>, command: ActionExecutorCommand.MakePaymentActionExecutorCommand, transactionId: TransactionId, action: InterceptMessagePayloadType) {
        val formattedBillsMessage = NotificationFormatter.buildBillNotificationMessage(selectedBills)

        val notification = buildNotificationService.buildSweepingAccountBillsScheduleConfirmation(
            formattedBillsMessage = formattedBillsMessage,
            user = command.user,
            transactionId = transactionId,
            action = action,
        )

        notificationService.notify(
            userId = command.user.id,
            accountId = command.user.accountId,
            message = notification.message,
            quickReplyButtons = notification.quickReplyButtons,
        )

        conversationHistoryService.createAssistantMessage(command.user.id, notification.message)
    }
}