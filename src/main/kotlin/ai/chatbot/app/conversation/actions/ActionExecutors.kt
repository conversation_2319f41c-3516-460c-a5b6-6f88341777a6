package ai.chatbot.app.conversation.actions

import ai.chatbot.app.AvailableLimitResponse
import ai.chatbot.app.AvailableLimitType
import ai.chatbot.app.NotificationService
import ai.chatbot.app.OnePixPayInstrumentation
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.TransactionService
import ai.chatbot.app.bill.BarCode
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.bill.PendingBillsService
import ai.chatbot.app.bill.Recipient
import ai.chatbot.app.bill.ScheduledInfo
import ai.chatbot.app.bill.toBarCode
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.ActionError
import ai.chatbot.app.conversation.ActionExecutor
import ai.chatbot.app.conversation.ActionExecutorCommand
import ai.chatbot.app.conversation.ActionMessage
import ai.chatbot.app.conversation.ActionResult
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InteractionWindowService
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.OnboardingState
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.event.EventService
import ai.chatbot.app.event.UserEvent
import ai.chatbot.app.media.BoletoInfo
import ai.chatbot.app.notification.BlipPayload
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ButtonWhatsAppDeeplinkParameter
import ai.chatbot.app.notification.ButtonWhatsAppTrackedDeeplinkParameter
import ai.chatbot.app.notification.CTALink
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.KnownNotificationTypes
import ai.chatbot.app.notification.KnownTemplateConfigurationKeys
import ai.chatbot.app.notification.NotificationContextTemplatesService
import ai.chatbot.app.notification.NotificationFormatter
import ai.chatbot.app.notification.NotificationMap
import ai.chatbot.app.notification.NotificationParam
import ai.chatbot.app.notification.OnboardingSinglePixNotificationService
import ai.chatbot.app.notification.QuickReplyButton
import ai.chatbot.app.notification.RawTemplateNotificationConfig
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.pix.isValid
import ai.chatbot.app.pix.toPixKeyType
import ai.chatbot.app.prompt.DEFAULT_ERROR_MESSAGE
import ai.chatbot.app.prompt.MARK_AS_PAID_EMPTY_BILLS_ERROR_PROMPT
import ai.chatbot.app.prompt.NO_BILLS_ERROR_PROMPT
import ai.chatbot.app.prompt.REFRESH_BALANCE_SYSTEM_PROMPT
import ai.chatbot.app.prompt.WALLET_NOT_FOUND
import ai.chatbot.app.prompt.billPaymentIntegrationErrors
import ai.chatbot.app.transaction.AddBoletoTransactionDetails
import ai.chatbot.app.transaction.MarkAsPaidOrIgnoreBillsTransactionDetails
import ai.chatbot.app.transaction.ScheduleBillsTransactionDetails
import ai.chatbot.app.transaction.SendPixCodeTransactionDetails
import ai.chatbot.app.transaction.SweepingTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserActivity
import ai.chatbot.app.user.UserActivityType
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.brazilDateFormat
import ai.chatbot.app.utils.brazilDateFormatWithoutYear
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.formattedAmount
import ai.chatbot.app.utils.formattedBalance
import ai.chatbot.app.utils.getObjectMapper
import ai.chatbot.app.utils.parseObjectFrom
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.format.TextStyle
import java.util.Locale
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
class MessageActionExecutor(
    private val notificationService: NotificationService,
) : ActionExecutor<ActionExecutorCommand.MessageActionExecutorCommand> {
    override fun execute(command: ActionExecutorCommand.MessageActionExecutorCommand): Either<ActionError, ActionResult> {
        notificationService.notify(command.user.id, command.user.accountId, command.action.content)
        return ActionResult.WithoutCompletion(actionType = ActionType.MSG).right()
    }

    override fun validate(command: ActionExecutorCommand.MessageActionExecutorCommand): Boolean {
        return command.action.content.isNotEmpty()
    }
}

@Singleton
class NotificationActionExecutor(
    private val notificationService: NotificationService,
) : ActionExecutor<ActionExecutorCommand.NotificationActionExecutorCommand> {
    override fun execute(command: ActionExecutorCommand.NotificationActionExecutorCommand): Either<ActionError, ActionResult> {
        notificationService.notify(command.action.notification)
        return ActionResult.WithoutCompletion(actionType = ActionType.NOTIFICATION).right()
    }

    override fun validate(command: ActionExecutorCommand.NotificationActionExecutorCommand): Boolean {
        return command.action.notification.let { it.configurationKey.value.isNotEmpty() && it.mobilePhone.isNotEmpty() }
    }
}

@Singleton
class AddFeatureRequestExecutor : ActionExecutor<ActionExecutorCommand.AddFeatureRequestActionExecutorCommand> {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun execute(command: ActionExecutorCommand.AddFeatureRequestActionExecutorCommand): Either<ActionError, ActionResult> {
        logger.info(
            append("command", command)
                .andAppend("userId", command.user.id.value)
                .andAppend("feature", command.action.content.firstOrNull()),
            "AddFeatureRequestExecutor#execute",
        )
        return ActionResult.WithoutCompletion(actionType = ActionType.ADD_FEATURE_REQUEST).right()
    }
}

@Singleton
class NoopExecutor : ActionExecutor<ActionExecutorCommand.NoopActionExecutorCommand> {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun execute(command: ActionExecutorCommand.NoopActionExecutorCommand): Either<ActionError, ActionResult> {
        logger.info(append("command", command), "NoopExecutor#execute")
        return ActionResult.WithoutCompletion(actionType = ActionType.NOOP).right()
    }
}

@Singleton
class GetContextExecutor : ActionExecutor<ActionExecutorCommand.GetContextActionExecutorCommand> {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun execute(command: ActionExecutorCommand.GetContextActionExecutorCommand): Either<ActionError, ActionResult> {
        logger.info(append("command", command), "GetContextExecutor#execute")

        return ActionResult.WithCompletion(systemMessageToCompletion = "responda sobre: ${command.action.subject}", actionType = ActionType.GET_CONTEXT).right()
    }
}

@Singleton
class RefreshBalanceExecutor(
    private val paymentAdapter: PaymentAdapter,
    private val conversationHistoryService: ConversationHistoryService,
) : ActionExecutor<ActionExecutorCommand.RefreshBalanceActionExecutorCommand> {
    override fun execute(command: ActionExecutorCommand.RefreshBalanceActionExecutorCommand): Either<ActionError, ActionResult> {
        val walletId = conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(userId = command.user.id).walletWithBills.walletId
        val balance =
            runBlocking { paymentAdapter.getBalanceAndForecast(command.user.id, walletId) }.getOrElse {
                val actionError = it.toActionMessage()
                return ActionError(needCompletion = actionError.needCompletion, message = actionError.message, actionType = ActionType.REFRESH_BALANCE_AND_FORECASTS).left()
            }
        conversationHistoryService.updateConversationHistoryState<BillComingDueHistoryState>(command.user.id) { currentState ->
            currentState.copy(balance = balance, internalStateControl = currentState.internalStateControl.copy(shouldSynchronizeBeforeCompletion = true))
        }
        val currentBalance = NotificationFormatter.buildFormattedAmount(balance.current)
        return ActionResult.WithCompletion(systemMessageToCompletion = REFRESH_BALANCE_SYSTEM_PROMPT.replace("{saldo}", currentBalance), actionType = ActionType.REFRESH_BALANCE_AND_FORECASTS).right()
    }
}

@Singleton
class SendBillsPeriodExecutor(
    private val pendingBillsService: PendingBillsService,
    private val buildNotificationService: BuildNotificationService,
    private val notificationService: NotificationService,
    private val conversationHistoryService: ConversationHistoryService,
    private val customNotificationService: CustomNotificationService,
    private val tenantService: TenantService,
) : ActionExecutor<ActionExecutorCommand.SendPendingBillsPeriodActionExecutorCommand> {

    private val logger = LoggerFactory.getLogger(SendBillsPeriodExecutor::class.java)

    private fun sendPendingBills(user: User, bills: List<BillView>, includeDueDates: Boolean) {
        val billsMessage = NotificationFormatter.buildBillNotificationMessage(bills, includeDueDate = includeDueDates)

        val notifications = buildNotificationService.buildBillsComingDueUserRequestedNotification(user, billsMessage)

        notifications.map { notification ->
            notificationService.notify(
                userId = user.id,
                accountId = user.accountId,
                message = notification.message,
                quickReplyButtons = notification.quickReplyButtons,
            )
            conversationHistoryService.createAssistantMessage(user.id, notification.message)
        }
    }

    private fun createBillsContext(user: User, bills: List<BillView>, includeDueDates: Boolean = true) {
        val billsMessage = NotificationFormatter.buildBillNotificationMessage(bills, includeDueDate = includeDueDates)
        val message = """
            Contas usadas no último envio de soma de contas:
            ${billsMessage.joinToString(separator = "\n")}
        """.trimMargin()
        conversationHistoryService.createSystemMessage(user.id, message)
    }

    private fun sendWaitingApprovalBills(
        totalWaitingApproval: Int,
        user: User,
    ): Boolean {
        if (totalWaitingApproval == 0) {
            return false
        }

        val notificationConfig = if (totalWaitingApproval == 1) {
            KnownTemplateConfigurationKeys.waitingApprovalBillsSingular
        } else {
            KnownTemplateConfigurationKeys.waitingApprovalBillsPlural
        }

        val buildNotificationResult = customNotificationService.buildRawTemplateNotification(
            user = user,
            notificationConfig = RawTemplateNotificationConfig(notificationConfig, KnownNotificationTypes.WAITING_APPROVAL_BILLS),
            params = listOf(NotificationMap(NotificationParam.TOTAL_BILLS, totalWaitingApproval.toString())),
        )
        notificationService.notify(notification = buildNotificationResult.notification)
        conversationHistoryService.createAssistantMessage(userId = user.id, message = buildNotificationResult.historyMessage)
        return true
    }

    data class PendingBillsPeriodAmounts(
        val open: Long,
        val scheduled: Long,
        val overdue: Long,
    )

    private fun sumAmounts(bills: List<BillView>): PendingBillsPeriodAmounts {
        val open = bills.filter { !it.isOverdue && it.scheduledInfo == ScheduledInfo.NOT_SCHEDULED }.sumOf { it.amountTotal }
        val scheduled = bills.filter { !it.isOverdue && it.scheduledInfo == ScheduledInfo.SCHEDULED }.sumOf { it.amountTotal }
        val overdue = bills.filter { it.isOverdue }.sumOf { it.amountTotal }

        return PendingBillsPeriodAmounts(open = open, scheduled = scheduled, overdue = overdue)
    }

    private fun pendingAmountsMessage(startDate: LocalDate, endDate: LocalDate, amounts: PendingBillsPeriodAmounts): String {
        val period = when {
            startDate == getLocalDate() && endDate == getLocalDate() -> "hoje"
            startDate == getLocalDate().plusDays(1) && endDate == getLocalDate().plusDays(1) -> "amanhã"
            startDate == endDate -> startDate.dayOfWeek.getDisplayName(TextStyle.FULL, Locale("pt", "BR"))
            else -> "${startDate.format(brazilDateFormatWithoutYear)} a ${endDate.format(brazilDateFormatWithoutYear)}"
        }

        return """
            |Oi, suas contas de $period estão assim:
            |Contas em aberto: ${NotificationFormatter.buildFormattedAmount(amounts.open)}
            |Contas agendadas: ${NotificationFormatter.buildFormattedAmount(amounts.scheduled)}
            |Contas atrasadas: ${NotificationFormatter.buildFormattedAmount(amounts.overdue)}
            |
            |Total: ${NotificationFormatter.buildFormattedAmount(amounts.open + amounts.scheduled + amounts.overdue)}
            |
            |Eu posso te enviar quais são as contas, quer?
        """.trimMargin()
    }

    override fun execute(command: ActionExecutorCommand.SendPendingBillsPeriodActionExecutorCommand): Either<ActionError, ActionResult> {
        val startDate = LocalDate.parse(command.action.startDate, dateFormat)
        val endDate = LocalDate.parse(command.action.endDate, dateFormat)

        val result =
            pendingBillsService.updatePendingBills(
                command.user.id,
                startDate,
                endDate,
            ).getOrElse { return it.toActionMessage().toActionErrorLeft(actionType = ActionType.SEND_PENDING_BILLS_PERIOD) }

        if (result.userAndWallet.wallet.bills.isEmpty()) {
            customNotificationService.send(
                user = command.user,
                notificationConfig = customNotificationService.config().noBillsFoundForPeriod,
                params = emptyList(),
            )
            sendWaitingApprovalBills(result.userAndWallet.wallet.totalWaitingApproval, command.user)
            return ActionResult.WithoutCompletion(actionType = ActionType.SEND_PENDING_BILLS_PERIOD).right()
        }

        if (command.action.amount) {
            val bills = result.userAndWallet.wallet.bills
            val amounts = sumAmounts(bills)
            val message = pendingAmountsMessage(startDate, endDate, amounts)
            notificationService.notify(
                userId = command.user.id,
                accountId = command.user.accountId,
                message = message,
            )
            conversationHistoryService.createAssistantMessage(command.user.id, message)
            createBillsContext(command.user, bills, includeDueDates = true)
            sendWaitingApprovalBills(result.userAndWallet.wallet.totalWaitingApproval, command.user)
            return ActionResult.WithoutCompletion(actionType = ActionType.SEND_PENDING_BILLS_PERIOD).right()
        }
        sendPendingBills(command.user, result.userAndWallet.wallet.bills, includeDueDates = true)
        sendWaitingApprovalBills(result.userAndWallet.wallet.totalWaitingApproval, command.user)
        return ActionResult.WithoutCompletion(actionType = ActionType.SEND_PENDING_BILLS_PERIOD).right()
    }
}

@Singleton
class MarkBillsAsPaidExecutor(
    private val paymentAdapter: PaymentAdapter,
    private val conversationHistoryService: ConversationHistoryService,
    private val notificationService: NotificationService,
    private val onePixPayInstrumentation: OnePixPayInstrumentation,
    private val buildNotificationService: BuildNotificationService,
    private val notificationContextTemplatesService: NotificationContextTemplatesService,
    private val transactionService: TransactionService,
    private val eventService: EventService,
    private val tenantService: TenantService,
) : ActionExecutor<ActionExecutorCommand.MarkBillsAsPaidOrIgnoreBillsActionExecutorCommand> {
    private val logger = LoggerFactory.getLogger(MarkBillsAsPaidExecutor::class.java)

    override fun execute(command: ActionExecutorCommand.MarkBillsAsPaidOrIgnoreBillsActionExecutorCommand): Either<ActionError, ActionResult> {
        val logName = "MarkBillsAsPaidExecutor#execute"
        val markers = append("command", command)

        val userConfirmation = command.action.payload.userConfirmed
        val billsToMarkAsPaidIds = command.action.payload.billsToMarkAsPaid
        val billsToIgnoreIds = command.action.payload.billsToIgnoreOrRemove

        if (billsToMarkAsPaidIds.isEmpty() && billsToIgnoreIds.isEmpty()) {
            // FIXME: Refatorar para mandar uma mensagem elaborada pela Friday
            return ActionError(needCompletion = true, message = MARK_AS_PAID_EMPTY_BILLS_ERROR_PROMPT, actionType = ActionType.MARK_BILLS_AS_PAID).left()
        }

        val currentState =
            conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(userId = command.user.id)

        val selectedBillsToMarkAsPaid =
            currentState.walletWithBills.bills.filter { bill ->
                billsToMarkAsPaidIds.find { it == bill.externalBillId } != null
            }

        val selectedBillsToIgnore =
            currentState.walletWithBills.bills.filter { bill ->
                billsToIgnoreIds.find { it == bill.externalBillId } != null
            }

        if (selectedBillsToMarkAsPaid.isEmpty() && selectedBillsToIgnore.isEmpty()) {
            // FIXME: Refatorar para mandar uma mensagem elaborada pela Friday
            return ActionError(needCompletion = true, message = NO_BILLS_ERROR_PROMPT, actionType = ActionType.MARK_BILLS_AS_PAID).left()
        }

        val walletId = currentState.walletWithBills.walletId
        if (walletId == null) {
            logger.error(markers, logName)
            return ActionError(
                needCompletion = false,
                message = WALLET_NOT_FOUND,
                actionType = ActionType.MARK_BILLS_AS_PAID,
            ).left()
        }

        if (!userConfirmation) {
            val transaction = transactionService.create(
                userId = command.user.id,
                walletId = walletId,
                details = MarkAsPaidOrIgnoreBillsTransactionDetails(
                    billsToMarkAsPaid = selectedBillsToMarkAsPaid.map { it.billId },
                    billsToIgnore = selectedBillsToIgnore.map { it.billId },
                ),
            )

            if (selectedBillsToMarkAsPaid.isNotEmpty() && selectedBillsToIgnore.isNotEmpty()) {
                val formattedBillsMessageToMarkAsPaid = NotificationFormatter.buildBillNotificationMessage(selectedBillsToMarkAsPaid)
                val formattedBillsMessageToIgnore = NotificationFormatter.buildBillNotificationMessage(selectedBillsToIgnore)
                val notification = buildNotificationService.buildMarkAsPaidAndIgnoreConfirmation(
                    user = command.user,
                    formattedBillsMessageToMarkAsPaid = formattedBillsMessageToMarkAsPaid,
                    formattedBillsMessageToIgnore = formattedBillsMessageToIgnore,
                    transactionId = transaction.id,
                )
                notificationService.notify(
                    userId = command.user.id,
                    accountId = command.user.accountId,
                    message = notification.message,
                    quickReplyButtons = notification.quickReplyButtons,
                )

                conversationHistoryService.createAssistantMessage(command.user.id, notification.message)
                return ActionResult.WithoutCompletion(actionType = ActionType.MARK_BILLS_AS_PAID).right()
            }

            when {
                selectedBillsToMarkAsPaid.isNotEmpty() -> {
                    val formattedBillsMessage = NotificationFormatter.buildBillNotificationMessage(selectedBillsToMarkAsPaid)
                    val notification = buildNotificationService.buildMarkAsPaidConfirmation(
                        user = command.user,
                        formattedBillsMessage = formattedBillsMessage,
                        transactionId = transaction.id,
                    )
                    notificationService.notify(
                        userId = command.user.id,
                        accountId = command.user.accountId,
                        message = notification.message,
                        quickReplyButtons = notification.quickReplyButtons,
                    )

                    conversationHistoryService.createAssistantMessage(command.user.id, notification.message)
                    return ActionResult.WithoutCompletion(actionType = ActionType.MARK_BILLS_AS_PAID).right()
                }

                selectedBillsToIgnore.isNotEmpty() -> {
                    val formattedBillsMessage = NotificationFormatter.buildBillNotificationMessage(selectedBillsToIgnore)
                    val notification = buildNotificationService.buildIgnoreBillsConfirmation(
                        user = command.user,
                        formattedBillsMessage = formattedBillsMessage,
                        transactionId = transaction.id,
                    )
                    notificationService.notify(
                        userId = command.user.id,
                        accountId = command.user.accountId,
                        message = notification.message,
                        quickReplyButtons = notification.quickReplyButtons,
                    )

                    conversationHistoryService.createAssistantMessage(command.user.id, notification.message)
                    return ActionResult.WithoutCompletion(actionType = ActionType.MARK_BILLS_AS_PAID).right()
                }
            }
        }

        if (selectedBillsToMarkAsPaid.isNotEmpty()) {
            onePixPayInstrumentation.requestedToMarkAsPaid(user = command.user, selected = selectedBillsToMarkAsPaid)

            runBlocking {
                paymentAdapter.markBillsAsPaid(userId = command.user.id, bills = selectedBillsToMarkAsPaid.map { it.billId }, walletId = walletId)
            }.getOrElse {
                conversationHistoryService.synchronizeBeforeCompletion(userId = command.user.id)
                return it.toActionMessage().toActionErrorLeft(actionType = ActionType.MARK_BILLS_AS_PAID)
            }
        }

        if (selectedBillsToIgnore.isNotEmpty()) {
            runBlocking {
                paymentAdapter.ignoreBills(userId = command.user.id, bills = selectedBillsToIgnore.map { it.billId }, walletId = walletId)
            }.getOrElse {
                conversationHistoryService.synchronizeBeforeCompletion(userId = command.user.id)
                return it.toActionMessage().toActionErrorLeft(actionType = ActionType.MARK_BILLS_AS_PAID)
            }
        }

        val notificationMessage = markBillsAsPaidOrIgnoreNotificationMessage(selectedBillsToMarkAsPaid, selectedBillsToIgnore)

        notificationService.notify(command.user.id, command.user.accountId, notificationMessage)

        conversationHistoryService.createAssistantMessage(command.user.id, notificationMessage)
        conversationHistoryService.synchronizeBeforeCompletion(userId = command.user.id)

        promoteSweepingAccount(currentState, command.user, selectedBillsToMarkAsPaid)

        return ActionResult.WithoutCompletion(actionType = ActionType.MARK_BILLS_AS_PAID).right()
    }

    fun promoteSweepingAccount(
        state: BillComingDueHistoryState,
        user: User,
        selectedBillsToMarkAsPaid: List<BillView>,
    ) {
        if (!tenantService.getConfiguration().features.openFinanceIncentive) return
        if (state.walletWithBills.hasSweepingAccount) return
        if (state.alreadyPromotedSweepingAccount) return

        val promotedSweepingAccountOptOut =
            paymentAdapter.getUserActivities(user.id, UserActivityType.PromotedSweepingAccountOptOut).fold(
                ifLeft = { true },
                ifRight = { it.firstOrNull()?.value ?: false },
            )

        if (promotedSweepingAccountOptOut) return

        if (selectedBillsToMarkAsPaid.isNotEmpty()) {
            notificationService.notify(
                notification = buildNotificationService.buildPromoteSweepingAccountMarkAsPaidNotification(
                    accountId = user.accountId,
                    mobilePhone = user.id.value,
                ),
                delaySeconds = 60,
            )

            val message = notificationContextTemplatesService.getPromoteSweepingAccountMarkAsPaidMessage()
            conversationHistoryService.createAssistantMessage(user.id, message)

            conversationHistoryService.updateConversationHistoryState<BillComingDueHistoryState>(user.id) { currentState ->
                currentState.copy(alreadyPromotedSweepingAccount = true)
            }

            eventService.send(user.accountId, UserEvent.PROMOTE_SWEEPING_SENT, metadata = mapOf("source" to "mark_as_paid"))
        }
    }

    override fun validate(command: ActionExecutorCommand.MarkBillsAsPaidOrIgnoreBillsActionExecutorCommand): Boolean {
        val currentState =
            conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(userId = command.user.id)
        val billIdsToMarkAsPaid = command.action.payload.billsToMarkAsPaid

        val selectedBillsToMarkAsPaid =
            currentState.walletWithBills.bills.filter { bill ->
                billIdsToMarkAsPaid.find { it == bill.externalBillId } != null
            }

        val hasSubscription =
            selectedBillsToMarkAsPaid.any { bill ->
                bill.subscriptionFee
            }

        if (hasSubscription) {
            val message = "Não é possível marcar a assinatura como paga."
            notificationService.notify(command.user.id, command.user.accountId, message)
            conversationHistoryService.createAssistantMessage(command.user.id, message)
            return false
        }

        if (currentState.walletWithBills.bills.isEmpty()) {
            return false
        }
        return true
    }

    private fun markBillsAsPaidOrIgnoreNotificationMessage(
        selectedBillsToMarkAsPaid: List<BillView>,
        selectedBillsToIgnore: List<BillView>,
    ): String {
        if (selectedBillsToIgnore.isNotEmpty() && selectedBillsToMarkAsPaid.isNotEmpty()) {
            val formattedBillsMessageToIgnore = NotificationFormatter.buildBillNotificationMessage(selectedBillsToIgnore)
            val formattedBillsMessageToMarkAsPaid = NotificationFormatter.buildBillNotificationMessage(selectedBillsToMarkAsPaid)

            return """
                   |Marquei as seguintes contas como pagas:
                   |${formattedBillsMessageToMarkAsPaid.joinToString(separator = "\n")}
                   |
                   |E removi as seguintes contas da sua timeline:
                   |${formattedBillsMessageToIgnore.joinToString(separator = "\n")}
            """.trimMargin()
        }

        return when {
            selectedBillsToMarkAsPaid.size == 1 -> "Ok, marquei a conta como paga."
            selectedBillsToMarkAsPaid.size > 1 -> "Ok, marquei as contas como pagas."
            selectedBillsToIgnore.size == 1 -> "Ok, removi a conta da timeline para você."
            selectedBillsToIgnore.size > 1 -> "Ok, removi as contas da timeline para você."
            else -> ""
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(MarkBillsAsPaidExecutor::class.java)
    }
}

@Singleton
class MarkReminderAsDoneExecutor(
    private val paymentAdapter: PaymentAdapter,
    private val notificationService: NotificationService,
    private val buildNotificationService: BuildNotificationService,
) : ActionExecutor<ActionExecutorCommand.MarkReminderAsDoneActionExecutorCommand> {
    override fun execute(command: ActionExecutorCommand.MarkReminderAsDoneActionExecutorCommand): Either<ActionError, ActionResult> {
        runBlocking {
            paymentAdapter.markReminderAsDone(
                userId = command.user.id,
                reminderId = command.action.payload.reminderId,
            )
        }.mapLeft {
            val notification =
                buildNotificationService.buildReminderResponseErrorNotification(
                    accountId = command.user.accountId,
                    mobilePhone = command.user.id.value,
                )
            notificationService.notify(notification)
            return it.toActionMessage().toActionErrorLeft(actionType = ActionType.MARK_REMINDER_AS_DONE)
        }

        val notification =
            buildNotificationService.buildReminderResponseSuccessNotification(
                accountId = command.user.accountId,
                mobilePhone = command.user.id.value,
            )

        notificationService.notify(notification)

        return ActionResult.WithoutCompletion(actionType = ActionType.MARK_REMINDER_AS_DONE).right()
    }

    override fun validate(command: ActionExecutorCommand.MarkReminderAsDoneActionExecutorCommand): Boolean {
        return true
    }
}

@Singleton
class SearchContactsExecutor : ActionExecutor<ActionExecutorCommand.SearchContactsActionExecutorCommand> {
    override fun execute(command: ActionExecutorCommand.SearchContactsActionExecutorCommand): Either<ActionError, ActionResult> {
        return ActionResult.WithoutCompletion(actionType = ActionType.SEARCH_CONTACTS).right()
    }
}

@Singleton
class OnboardingSinglePixExecutor(
    private val notificationService: NotificationService,
    private val conversationHistoryService: ConversationHistoryService,
    private val buildNotificationService: BuildNotificationService,
    private val onboardingSinglePixNotificationService: OnboardingSinglePixNotificationService,
    private val paymentAdapter: PaymentAdapter,
    private val interactionWindowService: InteractionWindowService,
    private val customNotificationService: CustomNotificationService,
    private val tenantService: TenantService,
) : ActionExecutor<ActionExecutorCommand.OnboardingSinglePixActionExecutorCommand> {
    private val logger = LoggerFactory.getLogger(this::class.java)
    private val logName = "OnboardingSinglePixExecutor"

    enum class OnboardingSinglePixActionEnum {
        ACCEPT, CONFIRM, SKIP, PIX,
    }

    private fun sendReply(user: User, message: String, link: CTALink? = null) {
        conversationHistoryService.createAssistantMessage(user.id, message)

        notificationService.notify(
            userId = user.id,
            accountId = user.accountId,
            message = message,
            ctaLink = link,
        )
    }

    private fun sendTryTheAppMessage(command: ActionExecutorCommand.OnboardingSinglePixActionExecutorCommand) {
        val linkFinishedContext = CTALink(
            displayText = "Abrir no App",
            url = ButtonWhatsAppDeeplinkParameter("contas").value,
        )
        sendReply(user = command.user, message = onboardingSinglePixNotificationService.getSinglePixFinishedContextMessage(), linkFinishedContext)
        try {
            interactionWindowService.close(command.user.id)
        } catch (e: IllegalArgumentException) {
            logger.warn(append("command", command), "OnboardingSinglePixExecutor#userWithoutInteractionWindow")
        }
    }

    private fun sendNewDayError(user: User) {
        val openApp = CTALink(
            displayText = "Abrir no App",
            url = ButtonWhatsAppDeeplinkParameter("contas").value,
        )

        sendReply(user = user, message = onboardingSinglePixNotificationService.getSinglePixNewDayError(), openApp)
    }

    private fun sendPixAlreadyPaidMessage(user: User) {
        val openApp = CTALink(
            displayText = "Abrir no App",
            url = ButtonWhatsAppDeeplinkParameter("contas").value,
        )
        sendReply(user = user, message = onboardingSinglePixNotificationService.getSinglePixAlreadyPaid(), openApp)
    }

    private fun sendPixCallSupportErrorMessage(user: User) {
        val callSupport = CTALink(
            displayText = "Falar com Atendimento",
            url = "https://wa.me/5521997151483",
        )
        sendReply(user = user, message = onboardingSinglePixNotificationService.getSinglePixCallSupportError(), callSupport)
    }

    private fun sendSkippedFinalizedMessages(command: ActionExecutorCommand.OnboardingSinglePixActionExecutorCommand) {
        val linkFinished = CTALink(
            displayText = "Adicionar no App",
            url = ButtonWhatsAppDeeplinkParameter("contas").value,
        )
        sendReply(user = command.user, message = onboardingSinglePixNotificationService.getSinglePixSkippedFinishedMessage(), linkFinished)

        sendTryTheAppMessage(command)
    }

    private fun sendFinalizedMessage(command: ActionExecutorCommand.OnboardingSinglePixActionExecutorCommand, skippedFlow: Boolean = false) {
        val linkFinished = CTALink(
            displayText = "Adicionar no App",
            url = ButtonWhatsAppDeeplinkParameter("contas").value,
        )

        val message = onboardingSinglePixNotificationService.getSinglePixFinishedMessage()

        sendReply(user = command.user, message = message, linkFinished)
    }

    override fun execute(command: ActionExecutorCommand.OnboardingSinglePixActionExecutorCommand): Either<ActionError, ActionResult> {
        val markers = append("userId", command.user.id.value)
        val openFinanceIncentiveEnabled = tenantService.getConfiguration().features.openFinanceIncentive

        val action = try {
            logger.info(markers.andAppend("action", OnboardingSinglePixActionEnum.valueOf(command.action.payload.action)), logName)
            OnboardingSinglePixActionEnum.valueOf(command.action.payload.action)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }

        try {
            when (action) {
                OnboardingSinglePixActionEnum.ACCEPT -> {
                    runBlocking { paymentAdapter.createOnboardingSinglePix(userId = command.user.id) }.fold(
                        ifLeft = {
                            when (it) {
                                PaymentAdapterError.OnboardingSinglePixAlreadyPaid -> {
                                    logger.error(markers.andAppend("error", it), logName)
                                    sendPixAlreadyPaidMessage(command.user)
                                    sendSkippedFinalizedMessages(command)
                                    return@fold
                                }

                                is PaymentAdapterError.ServerError -> logger.error(markers, logName, it.exception)
                                else -> logger.error(markers.andAppend("error", it), logName)
                            }
                            conversationHistoryService.updateConversationHistoryState<BillComingDueHistoryState>(command.user.id) { currentState ->
                                currentState.copy(onboardingState = OnboardingState(hasAcceptedExamplePayment = true))
                            }
                            sendReply(user = command.user, message = onboardingSinglePixNotificationService.getSinglePixNotFoundPixKey())
                            sendReply(user = command.user, message = onboardingSinglePixNotificationService.getSinglePixAskForPixKey())
                        },
                        ifRight = {
                            onboardingSinglePixCreated(command, it, markers)
                        },
                    )
                }

                OnboardingSinglePixActionEnum.CONFIRM -> {
                    val currentState = conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(command.user.id)

                    // No onboarding podemos assumir que walletId == accountId, porque o usuário ainda não teve chance de criar outra carteira.
                    val walletId = currentState.walletWithBills.walletId ?: WalletId(command.user.accountId.value)

                    var billView = currentState.onboardingState?.billView

                    if (billView == null) {
                        val keyType = command.action.payload.type
                        val keyValue = command.action.payload.key

                        billView = runBlocking { paymentAdapter.createOnboardingSinglePix(userId = command.user.id, keyValue = keyValue, keyType = keyType) }.getOrElse {
                            logger.error(markers.andAppend("error", it), logName)

                            sendNewDayError(command.user)
                            sendSkippedFinalizedMessages(command)

                            return ActionResult.WithoutCompletion(actionType = ActionType.ONBOARDING_SINGLE_PIX).right()
                        }
                    }

                    runBlocking { paymentAdapter.scheduleBills(userId = command.user.id, bills = listOf(billView.billId), sweepingRequest = null, walletId = walletId) }.fold(
                        ifLeft = {
                            when (it) {
                                is PaymentAdapterError.ServerError -> logger.error(markers, logName, it.exception)
                                else -> logger.error(markers.andAppend("error", it), logName)
                            }

                            return it.toActionMessage().toActionErrorLeft(actionType = ActionType.ONBOARDING_SINGLE_PIX)
                        },
                        ifRight = {
                            logger.info(markers.andAppend("scheduleBillsResult", it), logName)

                            if (openFinanceIncentiveEnabled) {
                                customNotificationService.config().onboardingPromoteSweepingAccount?.let { onboardingPromoteSweepingAccount ->
                                    customNotificationService.send(command.user, emptyList(), onboardingPromoteSweepingAccount)
                                } ?: run {
                                    logger.error(append("ACTION", "VERIFY").andAppend("context", "onboardingPromoteSweepingAccount está null no YML mas o openFinanceIncentiveEnabled está ativo. Notificação não foi enviada"), logName)
                                }
                            } else {
                                sendFinalizedMessage(command)
                            }

                            sendTryTheAppMessage(command)
                        },
                    )
                }

                OnboardingSinglePixActionEnum.SKIP -> {
                    sendReply(user = command.user, message = onboardingSinglePixNotificationService.getSinglePixRemindMeLaterMessage())
                    sendSkippedFinalizedMessages(command)
                }

                OnboardingSinglePixActionEnum.PIX -> {
                    val keyValue = command.action.payload.key
                    val keyType = command.action.payload.type.toPixKeyType(keyValue)

                    markers.andAppend("pixKey", mapOf("type" to keyType, "value" to keyValue))

                    val pixKey = PixKey(keyValue ?: "", keyType)

                    if (!pixKey.isValid()) {
                        logger.error(markers, logName)

                        return ActionMessage(
                            needCompletion = false,
                            message = onboardingSinglePixNotificationService.getSinglePixIncorrectPixKey(),
                        ).toActionErrorLeft(actionType = ActionType.ONBOARDING_SINGLE_PIX)
                    }

                    runBlocking { paymentAdapter.createOnboardingSinglePix(keyType = keyType.name, keyValue = keyValue, userId = command.user.id) }.fold(
                        ifLeft = {
                            when (it) {
                                is PaymentAdapterError.ServerError -> logger.error(markers, logName, it.exception)
                                else -> logger.error(markers.andAppend("error", it), logName)
                            }

                            conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(command.user.id).onboardingState?.let { state ->
                                if (state.hasAcceptedExamplePayment && !state.pixKeyValue.isNullOrEmpty()) {
                                    val linkFinished = CTALink(
                                        displayText = "Adicionar no App",
                                        url = ButtonWhatsAppDeeplinkParameter("contas").value,
                                    )
                                    sendReply(user = command.user, message = onboardingSinglePixNotificationService.getSinglePixNotFoundPixKeyInputted(), linkFinished)
                                    sendTryTheAppMessage(command)
                                    return@fold
                                }
                            }
                            when (it) {
                                PaymentAdapterError.PixFromDifferentOwner -> sendReply(user = command.user, message = onboardingSinglePixNotificationService.getSinglePixOtherOwnerPixKey())
                                PaymentAdapterError.PixKeyNotFound -> sendReply(user = command.user, message = onboardingSinglePixNotificationService.getSinglePixIncorrectPixKey())
                                PaymentAdapterError.OnboardingSinglePixAlreadyPaid -> {
                                    sendPixAlreadyPaidMessage(command.user)
                                    sendSkippedFinalizedMessages(command)
                                }

                                PaymentAdapterError.OnboardingSinglePixCreateError -> sendReply(user = command.user, message = onboardingSinglePixNotificationService.getSinglePixNotFoundPixKeyInputted())
                                else -> {
                                    sendPixCallSupportErrorMessage(command.user)
                                    sendSkippedFinalizedMessages(command)
                                }
                            }

                            conversationHistoryService.updateConversationHistoryState<BillComingDueHistoryState>(command.user.id) { currentState ->
                                currentState.copy(
                                    onboardingState = OnboardingState(
                                        hasAcceptedExamplePayment = currentState.onboardingState?.hasAcceptedExamplePayment ?: true,
                                        pixKeyType = keyType,
                                        pixKeyValue = keyValue,
                                    ),
                                )
                            }
                        },
                        ifRight = {
                            onboardingSinglePixCreated(command, it, markers, true)
                        },
                    )
                }
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return ActionError(
                needCompletion = false,
                message = DEFAULT_ERROR_MESSAGE,
                exception = e,
                actionType = ActionType.ONBOARDING_SINGLE_PIX,
            ).left()
        }

        return ActionResult.WithoutCompletion(actionType = ActionType.ONBOARDING_SINGLE_PIX).right()
    }

    private fun onboardingSinglePixCreated(
        command: ActionExecutorCommand.OnboardingSinglePixActionExecutorCommand,
        billView: BillView,
        markers: LogstashMarker,
        inputtedPix: Boolean = false,
    ) {
        logger.info(markers.andAppend("billView", billView).andAppend("inputtedPix", inputtedPix), logName)

        conversationHistoryService.updateConversationHistoryState<BillComingDueHistoryState>(command.user.id) { currentState ->
            currentState.copy(
                onboardingState = OnboardingState(
                    hasAcceptedExamplePayment = true,
                    pixKeyType = command.action.payload.type?.let { PixKeyType.valueOf(it) },
                    pixKeyValue = command.action.payload.key,
                    billView = billView,
                ),
            )
        }

        if (inputtedPix) {
            sendReply(user = command.user, message = onboardingSinglePixNotificationService.getSinglePixInputtedPaymentExplanationMessage())
        } else {
            sendReply(user = command.user, message = onboardingSinglePixNotificationService.getSinglePixPaymentExplanationMessage())
        }
        val formattedBillsMessage = NotificationFormatter.buildBillNotificationMessage(listOf(billView.copy(recipient = Recipient("Pix de teste"))))
        val notification =
            buildNotificationService.buildOnboardingPixConfirmationNotification(
                accountId = command.user.accountId,
                mobilePhone = command.user.id.value,
                userName = command.user.name,
                formattedBillsMessage = formattedBillsMessage,
            )
        notificationService.notify(notification)
        conversationHistoryService.createAssistantMessage(
            command.user.id,
            onboardingSinglePixNotificationService.getSinglePixPaymentConfirmMessage(command.user.name, formattedBillsMessage),
        )
    }
}

@Singleton
class PromoteSweepingAccountExecutor(
    private val notificationService: NotificationService,
    private val conversationHistoryService: ConversationHistoryService,
    private val notificationContextTemplatesService: NotificationContextTemplatesService,
    private val paymentAdapter: PaymentAdapter,
) : ActionExecutor<ActionExecutorCommand.PromoteSweepingAccountActionExecutorCommand> {
    private val logger = LoggerFactory.getLogger(this::class.java)
    private val logName = "PromoteSweepingAccountExecutor"

    enum class PromoteSweepingAccountActionEnum {
        KNOW_MORE, OPT_OUT,
    }

    enum class PromoteSweepingAccountSource {
        DDA, ONBOARDING, CASH_IN, MARK_AS_PAID, BROADCAST,
    }

    data class ActionTO(
        val action: PromoteSweepingAccountActionEnum,
        val source: PromoteSweepingAccountSource?,
    ) {
        companion object {
            fun parse(payload: String): ActionTO {
                return parseObjectFrom<ActionTO>(payload)
            }
        }
    }

    private fun sendReply(user: User, message: String, link: CTALink? = null) {
        conversationHistoryService.createAssistantMessage(user.id, message)

        notificationService.notify(
            userId = user.id,
            accountId = user.accountId,
            message = message,
            ctaLink = link,
        )
    }

    override fun execute(command: ActionExecutorCommand.PromoteSweepingAccountActionExecutorCommand): Either<ActionError, ActionResult> {
        val markers = append("userId", command.user.id.value)

        val actionTO = try {
            ActionTO.parse(command.action.payload.action)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }

        logger.info(markers.andAppend("action", actionTO.action), logName)

        when (actionTO.action) {
            PromoteSweepingAccountActionEnum.KNOW_MORE -> {
                val event = when (actionTO.source) {
                    PromoteSweepingAccountSource.DDA -> UserEvent.PROMOTE_SWEEPING_CLICK_DDA
                    PromoteSweepingAccountSource.ONBOARDING -> UserEvent.PROMOTE_SWEEPING_CLICK_ONBOARDING
                    PromoteSweepingAccountSource.CASH_IN -> UserEvent.PROMOTE_SWEEPING_CLICK_CASH_IN
                    PromoteSweepingAccountSource.MARK_AS_PAID -> UserEvent.PROMOTE_SWEEPING_CLICK_MARK_AS_PAID
                    PromoteSweepingAccountSource.BROADCAST -> UserEvent.PROMOTE_SWEEPING_CLICK_BROADCAST
                    null -> UserEvent.PROMOTE_SWEEPING_CLICK_UNKNOWN
                }

                val openApp = CTALink(
                    displayText = "Conectar meu banco",
                    url = ButtonWhatsAppTrackedDeeplinkParameter("open-finance", event).value,
                )
                sendReply(user = command.user, message = notificationContextTemplatesService.getPromoteSweepingAccountKnowMoreMessage(), openApp)
            }

            PromoteSweepingAccountActionEnum.OPT_OUT -> {
                paymentAdapter.setUserActivity(command.user.id, UserActivity(type = UserActivityType.PromotedSweepingAccountOptOut, value = true)).getOrElse {
                    logger.error(markers.andAppend("action", actionTO.action.name), logName)
                    return it.toActionMessage().toActionErrorLeft(actionType = ActionType.PROMOTE_SWEEPING_ACCOUNT)
                }

                sendReply(user = command.user, message = notificationContextTemplatesService.getPromoteSweepingAccountOptOutMessage())
            }
        }

        return ActionResult.WithoutCompletion(actionType = ActionType.PROMOTE_SWEEPING_ACCOUNT).right()
    }
}

@Singleton
class ValidateBoletoExecutor(
    private val paymentAdapter: PaymentAdapter,
    private val customNotificationService: CustomNotificationService,
    private val transactionService: TransactionService,
    private val notificationContextTemplatesService: NotificationContextTemplatesService,
) : ActionExecutor<ActionExecutorCommand.ValidateBoletoActionExecutorCommand> {
    private val logger = LoggerFactory.getLogger(this::class.java)
    private val logName = "ValidateBoletoExecutor"

    override fun execute(
        command: ActionExecutorCommand.ValidateBoletoActionExecutorCommand,
    ): Either<ActionError, ActionResult> {
        val markers = append("userId", command.user.id.value).andAppend("action", command.action)
        val boletoInfos = command.action.barcodes

        if (boletoInfos.isEmpty()) {
            return ActionError(needCompletion = true, message = "Nenhum boleto encontrado", actionType = ActionType.VALIDATE_BOLETO).left()
        }

        if (boletoInfos.size > 1) {
            val validateMessages = mutableListOf<String>()

            boletoInfos.take(3).forEach {
                val barcode = try {
                    if (it.codigo.length == 44) {
                        BarCode.of(it.codigo)
                    } else {
                        BarCode.ofDigitable(it.codigo)
                    }
                } catch (e: Exception) {
                    logger.error(markers.andAppend("error", e.localizedMessage), logName)
                    validateMessages.add("❌ O código de barras ${it.codigo} não é válido.")
                    return@forEach
                }

                val bill = paymentAdapter.addBill(command.user.id, barcode.digitable, it.vencimento, dryRun = true).getOrElse { error ->
                    logger.error(markers.andAppend("error", it), logName)

                    validateMessages.add(
                        """
                        |❌ Não consegui validar o boleto ${barcode.digitable}.
                        |${error.toActionMessage().message}
                        """.trimMargin(),
                    )
                    return@forEach
                }

                val message = """
                    |🧾Conta: ${bill.assignor}
                    |💰 Valor: ${bill.amount.formattedAmount()} 
                    |${bill.dueDate?.let { date -> "📆 Vencimento: ${date.format(brazilDateFormat)}" } ?: ""}
                """.trimMargin()

                validateMessages.add(message)

                markers.andAppend("bill", bill)
            }

            val barCodes = boletoInfos.mapNotNull {
                try {
                    val barCode = it.codigo.toBarCode()
                    BoletoInfo(barCode.number, it.vencimento)
                } catch (e: Exception) {
                    logger.error(markers.andAppend("error", e), logName)
                    null
                }
            }

            val transaction = transactionService.create(
                userId = command.user.id,
                walletId = WalletId(command.user.accountId.value),
                details = AddBoletoTransactionDetails(barCodes),
            )

            val message = """
                |Identifiquei ${boletoInfos.size} boletos
                |
                |Aqui estão alguns deles:
                |${validateMessages.joinToString(separator = "\n\n")}
                |
                |Você deseja adicionar todos os boletos válidos?
            """.trimMargin()

            val quickReplies = listOf(
                QuickReplyButton(
                    text = "Sim",
                    payload = getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = getLocalDate().format(dateFormat),
                            action = InterceptMessagePayloadType.ADD_BOLETO.name,
                            transactionId = transaction.id.value,
                        ),
                    ),
                ),
                QuickReplyButton(
                    text = "Não",
                    payload = getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = getLocalDate().format(dateFormat),
                            action = InterceptMessagePayloadType.TRANSACTION_CANCEL.name,
                            transactionId = transaction.id.value,
                        ),
                    ),
                ),
            )

            customNotificationService.send(command.user.id.value, command.user.accountId, message, quickReplies = quickReplies)

            return ActionResult.WithoutCompletion(actionType = ActionType.VALIDATE_BOLETO).right()
        }
        val barcode = command.action.barcodes.first()

        val bill = paymentAdapter.addBill(command.user.id, barcode.codigo, barcode.vencimento, dryRun = true).getOrElse {
            logger.error(markers.andAppend("error", it), logName)
            customNotificationService.send(
                mobilePhone = command.user.id.value,
                accountId = command.user.accountId,
                message = """
                        |❌ Não consegui validar o boleto ${barcode.codigo}.
                        |${it.toActionMessage().message}
                """.trimMargin(),
            )

            return ActionError(needCompletion = false, message = "Não consegui validar o boleto", actionType = ActionType.VALIDATE_BOLETO).left()
        }

        markers.andAppend("bill", bill)

        val barCode = barcode.codigo.toBarCode()

        val isConcessionaria = barCode.checkIsConcessionaria()

        val transaction = transactionService.create(
            userId = command.user.id,
            walletId = WalletId(command.user.accountId.value),
            details = AddBoletoTransactionDetails(
                barCodes = listOf(BoletoInfo(barCode.number, barcode.vencimento)),
            ),
        )

        if (isConcessionaria) {
            val payloadDate = getLocalDate()

            val message = notificationContextTemplatesService.getAddBoletoConcessionariaMessage(bill.amount, bill.assignor, bill.dueDate)
            val quickReplies = listOf(
                QuickReplyButton(
                    text = "Incluir no App",
                    payload = getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = payloadDate.format(dateFormat),
                            action = InterceptMessagePayloadType.ADD_BOLETO.name,
                            transactionId = transaction.id.value,
                        ),
                    ),
                ),
                QuickReplyButton(
                    text = "Pagar Agora",
                    payload = getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = payloadDate.format(dateFormat),
                            action = InterceptMessagePayloadType.PAY_BOLETO.name,
                            transactionId = transaction.id.value,
                        ),
                    ),
                ),
            )
            customNotificationService.send(mobilePhone = command.user.id.value, accountId = command.user.accountId, message = message, quickReplies = quickReplies)
        } else {
            val payloadDate = getLocalDate()

            val message = notificationContextTemplatesService.getAddBoletoMessage(bill.amount, bill.assignor, bill.dueDate!!)
            val quickReplies = listOf(
                QuickReplyButton(
                    text = "Sim",
                    payload = getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = payloadDate.format(dateFormat),
                            action = InterceptMessagePayloadType.ADD_BOLETO.name,
                            transactionId = transaction.id.value,
                        ),
                    ),
                ),
                QuickReplyButton(
                    text = "Não",
                    payload = getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = payloadDate.format(dateFormat),
                            action = InterceptMessagePayloadType.TRANSACTION_CANCEL.name,
                            transactionId = transaction.id.value,
                        ),
                    ),
                ),
            )

            customNotificationService.send(mobilePhone = command.user.id.value, accountId = command.user.accountId, message = message, quickReplies = quickReplies)
        }

        return ActionResult.WithoutCompletion(actionType = ActionType.VALIDATE_BOLETO).right()
    }
}

@Singleton
class ScheduleBoletoExecutor(
    val actionExecutorsHelpers: ActionExecutorsHelpers,
    val paymentAdapter: PaymentAdapter,
    val transactionService: TransactionService,
    val conversationHistoryService: ConversationHistoryService,
    val customNotificationService: CustomNotificationService,
) : ActionExecutor<ActionExecutorCommand.ScheduleBoletoActionExecutorCommand> {
    override fun execute(command: ActionExecutorCommand.ScheduleBoletoActionExecutorCommand): Either<ActionError, ActionResult> {
        val transaction = transactionService.find(command.action.transactionId)

        if (transaction?.details !is ScheduleBillsTransactionDetails) {
            return ActionError(needCompletion = true, message = "Transação não encontrada", actionType = ActionType.SCHEDULE_BOLETO).left()
        }

        val billIds = (transaction.details as ScheduleBillsTransactionDetails).bills
        val selectedBills = paymentAdapter.checkBills(command.user.id, billIds).getOrElse {
            val message = when (it) {
                is PaymentAdapterError.NotFoundError -> "O pagamento não foi encontrado."
                is PaymentAdapterError.BillNotActiveError -> "Este pagamento não está mais ativo, ele pode já ter sido pago ou removido."
                else -> it.toActionMessage().message
            }

            customNotificationService.send(command.user.id.value, command.user.accountId, message)

            return ActionError(needCompletion = false, message = message, actionType = ActionType.SCHEDULE_BOLETO).left()
        }

        val walletId = WalletId(command.user.accountId.value)

        val currentState = conversationHistoryService.updateConversationHistoryState<BillComingDueHistoryState>(command.user.id) { state ->
            val (balanceResult, activeConsentsResult) = actionExecutorsHelpers.refreshState(command.user, state)
            val balance = balanceResult.getOrNull()
            val activeConsents = activeConsentsResult.getOrElse { emptyList() }
            state.copy(
                balance = balance,
                walletWithBills = state.walletWithBills.copy(
                    activeConsents = activeConsents,
                ),
            )
        }.getOrElse { conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(userId = command.user.id) }
        val currentBalance = currentState.balance?.current ?: 0
        val amountMissing = selectedBills.first().amountTotal - currentBalance

        return actionExecutorsHelpers.scheduleBills(
            user = command.user,
            selectedBills = selectedBills,
            amountMissing = amountMissing,
            walletId = walletId,
            state = currentState,
            actionType = ActionType.SCHEDULE_BOLETO,
            scheduleToDueDate = command.action.scheduleToDueDate,
        )
    }
}

@Singleton
class ActionExecutorsHelpers(
    private val paymentAdapter: PaymentAdapter,
    private val conversationHistoryService: ConversationHistoryService,
    private val notificationService: NotificationService,
    private val onePixPayInstrumentation: OnePixPayInstrumentation,
    private val transactionService: TransactionService,
    private val buildNotificationService: BuildNotificationService,
    private val customNotificationService: CustomNotificationService,
    private val notificationContextTemplatesService: NotificationContextTemplatesService,
) {
    fun refreshState(user: User, currentState: BillComingDueHistoryState) =
        runBlocking {
            val balance =
                async {
                    paymentAdapter.getBalanceAndForecast(user.id, currentState.walletWithBills.walletId)
                }

            val sweepingConsent =
                async {
                    paymentAdapter.getActiveSweepingConsents(user.id, currentState.walletWithBills.walletId)
                }

            Pair(balance.await(), sweepingConsent.await())
        }

    fun sendOnePixPay(
        user: User,
        selectedBills: List<BillView>,
        walletId: WalletId,
        currentState: BillComingDueHistoryState,
        sweepingTransferError: ActionError? = null,
        actionType: ActionType,
    ): Either<ActionError, ActionResult> {
        val pixCode =
            runBlocking {
                paymentAdapter.generateOnePixPay(userId = user.id, bills = selectedBills.map { it.billId }, walletId = walletId)
            }
                .getOrElse {
                    conversationHistoryService.synchronizeBeforeCompletion(userId = user.id)
                    return it.toActionMessage().toActionErrorLeft(actionType = actionType)
                }
        val notificationMessage = if (sweepingTransferError != null) {
            // ex. "O valor excede seu limite de transferência por este canal"
            "${sweepingTransferError.message}. Por não conseguir usar a sua conta conectada, estou gerando o código pix para você pagar suas contas."
        } else {
            val totalAmount = selectedBills.sumOf { it.amount }
            val billsInfo = NotificationFormatter.buildBillNotificationMessage(selectedBills)
            val currentBalance = currentState.balance?.current ?: 0

            generateOnePixPayMessage(billsInfo, totalAmount, currentBalance)
        }

        notificationService.notify(user.id, user.accountId, notificationMessage)
        notificationService.notify(userId = user.id, accountId = user.accountId, pixCode.value, delay = 1)

        onePixPayInstrumentation.sentOnePixPayCodeToUser(
            user = user,
            current = currentState.walletWithBills.bills,
            selected = selectedBills,
        )
        conversationHistoryService.createAssistantMessage(user.id, notificationMessage)
        conversationHistoryService.createAssistantMessage(user.id, "código pix enviado ao usuário.")

        conversationHistoryService.synchronizeBeforeCompletion(userId = user.id)

        return ActionResult.WithoutCompletion(actionType = actionType).right()
    }

    private fun createTransactions(
        user: User,
        selectedBills: List<BillView>,
        amountMissing: Long,
        walletId: WalletId,
        availableConsents: List<SweepingConsent>,
        transactionGroupId: TransactionGroupId,
    ): List<Transaction> {
        return availableConsents.sortedByLastCashInAndName().map { consent ->
            transactionService.create(
                userId = user.id,
                walletId = walletId,
                details =
                SweepingTransactionDetails(
                    amount = amountMissing,
                    bills = selectedBills.map { it.billId },
                    sweepingParticipantId = consent.participant.id,
                ),
                transactionGroupId = transactionGroupId,
            )
        }
    }

    fun scheduleBills(
        user: User,
        selectedBills: List<BillView>,
        amountMissing: Long,
        walletId: WalletId,
        state: BillComingDueHistoryState,
        actionType: ActionType,
        scheduleToDueDate: Boolean = false,
    ): Either<ActionError, ActionResult> {
        val logName = "ActionExecutorsHelpers#scheduleBills"
        val markers = append("userId", user.id.value).andAppend("action", actionType.name).andAppend("amountMissing", amountMissing)

        logger.info(markers.andAppend("selectedBills", selectedBills), logName)
        if (amountMissing > 0 && !scheduleToDueDate) {
            // Se falta saldo e o usuário não tem conta conectada joga para o fluxo de OPP
            if (!state.walletWithBills.hasSweepingAccount) {
                return sendOnePixPay(user, selectedBills, walletId, state, actionType = actionType)
            }

            val transactionGroupId = TransactionGroupId()
            val sendPixCodeTransactionId = transactionService.create(
                userId = user.id,
                walletId = walletId,
                details =
                SendPixCodeTransactionDetails(
                    bills = selectedBills.map { it.billId },
                ),
                transactionGroupId = transactionGroupId,
            ).id

            val availableConsents = state.walletWithBills.sweepingConsentsWithTransactionLimitFor(amountMissing)

            // Se falta saldo e o valor esta acima do limite da conta conectada, pergunta se quer reconectar ou fazer OPP
            if (availableConsents.isEmpty()) {
                buildNotificationService.buildScheduledAmountExceedsSweepingTransactionLimit(
                    accountId = user.accountId,
                    mobilePhone = user.id.value,
                    transactionId = sendPixCodeTransactionId,
                ).let { notificationService.notify(it) }
                conversationHistoryService.createAssistantMessage(user.id, "Você não tem saldo suficiente para realizar a transação e o valor ultrapassa o limite definido na conexão com seu banco. Para pagar ainda hoje, refaça a conexão e coloque um limite mais alto.")
                logger.info(markers, logName)
                return ActionResult.WithoutCompletion(actionType).right()
            }

            val sweepingTransactions = createTransactions(
                user = user,
                selectedBills = selectedBills,
                amountMissing = amountMissing,
                walletId = walletId,
                availableConsents = availableConsents,
                transactionGroupId = transactionGroupId,
            )
            markers.andAppend("sweepingTransactions", sweepingTransactions.map { it.id.value })

            val balance =
                runBlocking { paymentAdapter.getBalanceAndForecast(user.id, walletId) }.getOrElse {
                    val actionError = it.toActionMessage()
                    return ActionError(needCompletion = actionError.needCompletion, message = actionError.message, actionType = ActionType.REFRESH_BALANCE_AND_FORECASTS).left()
                }
            conversationHistoryService.updateConversationHistoryState<BillComingDueHistoryState>(user.id) { currentState ->
                currentState.copy(balance = balance)
            }
            val billsAmount = selectedBills.sumOf { it.amountTotal }
            val missingAmount = billsAmount - balance.current

            val openFinanceBalances = paymentAdapter.getOpenFinanceBalances(user.id, walletId, availableConsents.map { it.participant.id }).getOrElse {
                conversationHistoryService.synchronizeBeforeCompletion(userId = user.id)
                return it.toActionMessage().toActionErrorLeft(actionType = actionType)
            }

            val availableLimit = paymentAdapter.validateAvailableLimit(
                userId = user.id,
                walletId = state.walletWithBills.walletId!!,
                amount = missingAmount,
                type = AvailableLimitType.SCHEDULE_BILLS,
            ).getOrElse {
                conversationHistoryService.synchronizeBeforeCompletion(userId = user.id)
                return it.toActionMessage().toActionErrorLeft(actionType = actionType)
            }

            val billsMessage = NotificationFormatter.buildBillNotificationMessage(selectedBills, includeDueDate = true).joinToString(separator = "\n\n")

            when (availableLimit) {
                AvailableLimitResponse.OK -> {
                    when (sweepingTransactions.size) {
                        1 -> {
                            customNotificationService.send(
                                user = user,
                                notificationConfig = customNotificationService.config().scheduleConfirmationSingleSweepingConsent,
                                params = listOf(
                                    NotificationMap(NotificationParam.TRANSACTION_ID, sweepingTransactions[0].id.value),
                                    NotificationMap(NotificationParam.CONTA1_NOME, availableConsents[0].participant.name),
                                    NotificationMap(NotificationParam.CONTA1_NOME_CURTO, availableConsents[0].participant.shortName),
                                    NotificationMap(NotificationParam.CONTA1_SALDO, openFinanceBalances[availableConsents[0].participant.id].formattedBalance()),
                                ) + NotificationMap(NotificationParam.BILLS_LIST, billsMessage),
                            )
                        }

                        2 -> {
                            customNotificationService.send(
                                user = user,
                                notificationConfig = customNotificationService.config().scheduleConfirmationMultipleSweepingConsent,
                                params = availableConsents[0].buildNotificationMaps(
                                    transaction = NotificationParam.TRANSACTION_ID,
                                    name = NotificationParam.CONTA1_NOME,
                                    shortName = NotificationParam.CONTA1_NOME_CURTO,
                                    balance = NotificationParam.CONTA1_SALDO,
                                    sweepingTransactions = sweepingTransactions,
                                    openFinanceBalances = openFinanceBalances,
                                ) + availableConsents[1].buildNotificationMaps(
                                    transaction = NotificationParam.TRANSACTION_ID_2,
                                    name = NotificationParam.CONTA2_NOME,
                                    shortName = NotificationParam.CONTA2_NOME_CURTO,
                                    balance = NotificationParam.CONTA2_SALDO,
                                    sweepingTransactions = sweepingTransactions,
                                    openFinanceBalances = openFinanceBalances,
                                ) + NotificationMap(NotificationParam.BILLS_LIST, billsMessage),
                            )
                        }

                        else -> {
                            customNotificationService.send(
                                user = user,
                                notificationConfig = customNotificationService.config().scheduleConfirmationSelectSweepingConsent,
                                params = availableConsents[0].buildNotificationMaps(
                                    transaction = NotificationParam.TRANSACTION_ID,
                                    name = NotificationParam.CONTA1_NOME,
                                    shortName = NotificationParam.CONTA1_NOME_CURTO,
                                    balance = NotificationParam.CONTA1_SALDO,
                                    sweepingTransactions = sweepingTransactions,
                                    openFinanceBalances = openFinanceBalances,
                                ) + listOf(
                                    NotificationMap(NotificationParam.TRANSACTION_GROUP_ID, transactionGroupId.value),
                                    NotificationMap(NotificationParam.BILLS_LIST, billsMessage),
                                ),
                            )
                        }
                    }
                }

                AvailableLimitResponse.ASSISTANT_LIMIT_EXCEEDED -> {
                    when (sweepingTransactions.size) {
                        1 -> {
                            customNotificationService.send(
                                user = user,
                                notificationConfig = customNotificationService.config().scheduleAuthorizationSingleSweepingConsent,
                                params = availableConsents[0].buildNotificationMaps(
                                    transaction = NotificationParam.TRANSACTION_ID,
                                    name = NotificationParam.CONTA1_NOME,
                                    shortName = NotificationParam.CONTA1_NOME_CURTO,
                                    balance = NotificationParam.CONTA1_SALDO,
                                    sweepingTransactions = sweepingTransactions,
                                    openFinanceBalances = openFinanceBalances,
                                ) + listOf(
                                    NotificationMap(NotificationParam.TRANSACTION_GROUP_ID, transactionGroupId.value),
                                    NotificationMap(NotificationParam.BILLS_LIST, billsMessage),
                                ),
                            )
                        }

                        2 -> {
                            customNotificationService.send(
                                user = user,
                                notificationConfig = customNotificationService.config().scheduleAuthorizationMultipleSweepingConsent,
                                params = availableConsents[0].buildNotificationMaps(
                                    transaction = NotificationParam.TRANSACTION_ID,
                                    name = NotificationParam.CONTA1_NOME,
                                    shortName = NotificationParam.CONTA1_NOME_CURTO,
                                    balance = NotificationParam.CONTA1_SALDO,
                                    sweepingTransactions = sweepingTransactions,
                                    openFinanceBalances = openFinanceBalances,
                                ) + availableConsents[1].buildNotificationMaps(
                                    transaction = NotificationParam.TRANSACTION_ID_2,
                                    name = NotificationParam.CONTA2_NOME,
                                    shortName = NotificationParam.CONTA2_NOME_CURTO,
                                    balance = NotificationParam.CONTA2_SALDO,
                                    sweepingTransactions = sweepingTransactions,
                                    openFinanceBalances = openFinanceBalances,
                                ) + listOf(
                                    NotificationMap(NotificationParam.TRANSACTION_GROUP_ID, transactionGroupId.value),
                                    NotificationMap(NotificationParam.BILLS_LIST, billsMessage),
                                ),
                            )
                        }

                        else -> {
                            customNotificationService.send(
                                user = user,
                                notificationConfig = customNotificationService.config().scheduleAuthorizationSelectSweepingConsent,
                                params = availableConsents[0].buildNotificationMaps(
                                    transaction = NotificationParam.TRANSACTION_ID,
                                    name = NotificationParam.CONTA1_NOME,
                                    shortName = NotificationParam.CONTA1_NOME_CURTO,
                                    balance = NotificationParam.CONTA1_SALDO,
                                    sweepingTransactions = sweepingTransactions,
                                    openFinanceBalances = openFinanceBalances,
                                ) + availableConsents[1].buildNotificationMaps(
                                    transaction = NotificationParam.TRANSACTION_ID_2,
                                    name = NotificationParam.CONTA2_NOME,
                                    shortName = NotificationParam.CONTA2_NOME_CURTO,
                                    balance = NotificationParam.CONTA2_SALDO,
                                    sweepingTransactions = sweepingTransactions,
                                    openFinanceBalances = openFinanceBalances,
                                ) + availableConsents[2].buildNotificationMaps(
                                    transaction = NotificationParam.TRANSACTION_ID_3,
                                    name = NotificationParam.CONTA3_NOME,
                                    shortName = NotificationParam.CONTA3_NOME_CURTO,
                                    balance = NotificationParam.CONTA3_SALDO,
                                    sweepingTransactions = sweepingTransactions,
                                    openFinanceBalances = openFinanceBalances,
                                ) + listOf(
                                    NotificationMap(NotificationParam.TRANSACTION_GROUP_ID, transactionGroupId.value),
                                    NotificationMap(NotificationParam.BILLS_LIST, billsMessage),
                                ),
                            )
                        }
                    }
                }

                AvailableLimitResponse.PIX_LIMIT_EXCEEDED -> {
                    return ActionError(needCompletion = false, message = "Invalid limit type", actionType = actionType).left()
                }
            }

            logger.info(markers, logName)
            return ActionResult.WithoutCompletion(actionType = actionType).right()
        } else {
            doScheduleBills(user, selectedBills, walletId, scheduleToDueDate).getOrElse {
                logger.info(markers, logName)
                return when (it) {
                    PaymentAdapterError.AssistantLimitExceeded -> {
                        requestScheduleAuthorization(user, walletId, selectedBills)

                        ActionResult.WithoutCompletion(actionType = actionType).right()
                    }

                    else -> {
                        it.toActionMessage().toActionErrorLeft(actionType = actionType)
                    }
                }
            }

            logger.info(markers, logName)
            return ActionResult.WithoutCompletion(actionType = actionType).right()
        }
    }

    private fun List<Transaction>.find(sweepingParticipantId: SweepingParticipantId) = single {
        (it.details as SweepingTransactionDetails).sweepingParticipantId == sweepingParticipantId
    }

    private fun SweepingConsent.buildNotificationMaps(transaction: NotificationParam, name: NotificationParam, shortName: NotificationParam, balance: NotificationParam, sweepingTransactions: List<Transaction>, openFinanceBalances: Map<SweepingParticipantId, Long?>) = listOf(
        NotificationMap(transaction, sweepingTransactions.find(participant.id).id.value),
        NotificationMap(name, participant.name),
        NotificationMap(shortName, participant.shortName),
        NotificationMap(balance, openFinanceBalances[participant.id].formattedBalance()),
    )

    fun generateOnePixPayMessage(
        billsInfo: List<String>,
        totalAmount: Long,
        currentBalance: Long,
    ): String {
        val prefix =
            if (billsInfo.size == 1) {
                "A conta selecionada para pagamento é:"
            } else {
                "As contas selecionadas para pagamento são:"
            }

        val billsMessage = billsInfo.joinToString(separator = """\n""")

        val amountToPay = totalAmount - currentBalance
        val balanceMessage = if (currentBalance > 0) "\uD83C\uDFE6 Saldo em conta: ${NotificationFormatter.buildFormattedAmount(currentBalance)}" else ""
        return """
                    |$prefix
                    |
                    |$billsMessage
                    |
                    |$balanceMessage
                    |💰 Valor da transferência: ${NotificationFormatter.buildFormattedAmount(amountToPay)}
                    |
                    |Agora basta copiar e colar o código no app de seu banco.
        """.trimMargin()
    }

    fun assistantNotification(
        user: User,
        message: String,
    ) {
        notificationService.notify(user.id, user.accountId, message)
        conversationHistoryService.createAssistantMessage(user.id, message)
    }

    private fun doScheduleBills(
        user: User,
        selectedBills: List<BillView>,
        walletId: WalletId,
        scheduleToDueDate: Boolean,
    ): Either<PaymentAdapterError, Unit> {
        runBlocking {
            paymentAdapter.scheduleBills(
                userId = user.id,
                bills = selectedBills.map { it.billId },
                walletId = walletId,
                sweepingRequest = null,
                scheduleToDueDate = scheduleToDueDate,
            )
        }
            .getOrElse {
                conversationHistoryService.synchronizeBeforeCompletion(userId = user.id)
                return it.left()
            }

        val notificationMessage = if (scheduleToDueDate) {
            "Pagamento agendado para o vencimento."
        } else {
            "Ok, estou processando e já volto com o resultado."
        }

        notificationService.notify(user.id, user.accountId, notificationMessage)

        onePixPayInstrumentation.scheduledBills(
            user = user,
            bills = selectedBills,
        )

        conversationHistoryService.createAssistantMessage(user.id, notificationMessage)

        conversationHistoryService.synchronizeBeforeCompletion(userId = user.id)

        return Unit.right()
    }

    private fun requestScheduleAuthorization(
        user: User,
        walletId: WalletId,
        selectedBills: List<BillView>,
    ) {
        val transaction = transactionService.create(
            userId = user.id,
            walletId = walletId,
            details = ScheduleBillsTransactionDetails(bills = selectedBills.map { bill -> bill.billId }),
        )

        notificationService.notify(
            buildNotificationService.buildAuthorizeScheduleBillsNotification(
                mobilePhone = user.id.value,
                accountId = user.accountId,
                bills = selectedBills,
                transactionId = transaction.id,
            ),
        )

        conversationHistoryService.createAssistantMessage(
            user.id,
            notificationContextTemplatesService.getAuthorizeScheduleBillsMessage(
                NotificationFormatter.getFormattedBillInfo(selectedBills),
            ),
        )
    }

    companion object {
        val logger = LoggerFactory.getLogger(ActionExecutorsHelpers::class.java)
    }
}

internal fun PaymentAdapterError.toActionMessage() = billPaymentIntegrationErrors(this)

internal fun ActionMessage.toActionErrorLeft(actionType: ActionType) = ActionError(needCompletion = needCompletion, message = message, actionType = actionType).left()

internal fun ActionMessage.toActionError(actionType: ActionType) = ActionError(needCompletion = needCompletion, message = message, actionType = actionType)

fun List<SweepingConsent>.sortedByLastCashInAndName() = this.sortedWith(
    compareByDescending<SweepingConsent> {
        it.lastSuccessfulCashIn
    }.thenBy {
        it.participant.name
    },
)