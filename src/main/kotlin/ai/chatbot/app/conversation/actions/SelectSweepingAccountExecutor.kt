package ai.chatbot.app.conversation.actions

import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.TransactionService
import ai.chatbot.app.conversation.ActionError
import ai.chatbot.app.conversation.ActionExecutor
import ai.chatbot.app.conversation.ActionExecutorCommand
import ai.chatbot.app.conversation.ActionResult
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.NotificationMap
import ai.chatbot.app.notification.NotificationParam
import ai.chatbot.app.transaction.PixTransactionDetails
import ai.chatbot.app.transaction.SweepingTransactionDetails
import ai.chatbot.app.transaction.TransactionDetails
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.formattedBalance
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class SelectSweepingAccountExecutor(
    private val transactionService: TransactionService,
    private val customNotificationService: CustomNotificationService,
    private val conversationHistoryService: ConversationHistoryService,
    private val paymentAdapter: PaymentAdapter,
) : ActionExecutor<ActionExecutorCommand.SelectSweepingAccountCommand> {
    private val logger = LoggerFactory.getLogger(SelectSweepingAccountExecutor::class.java)

    private fun TransactionDetails.sweepingParticipantId(): SweepingParticipantId? {
        return when (this) {
            is SweepingTransactionDetails -> sweepingParticipantId
            is PixTransactionDetails -> sweepingParticipantId
            else -> null
        }
    }

    private fun SweepingParticipantId.toActiveConsent(currentState: BillComingDueHistoryState) = currentState.walletWithBills.activeConsents.firstOrNull { consent ->
        consent.participant.id == this
    }

    override fun execute(command: ActionExecutorCommand.SelectSweepingAccountCommand): Either<ActionError, ActionResult> {
        val logName = "SelectSweepingAccountExecutor#execute"
        val markers = Markers.append("command", command)

        val transactions = transactionService.find(userId = command.user.id, transactionGroupId = command.action.transactionGroupId).sortedBy {
            it.id.value
        }
        markers.andAppend("transactions", transactions)

        val currentState = conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(command.user.id)

        val transactionParticipants = transactions.associate {
            it.id to it.details.sweepingParticipantId()?.toActiveConsent(currentState)?.participant
        }.filterValues {
            it != null
        }

        if (transactionParticipants.size < 3) {
            logger.warn(markers, logName)
            return ActionError(
                needCompletion = false,
                message = "O estado da transação não é válido",
                actionType = ActionType.SELECT_SWEEPING_ACCOUNT,
            ).left()
        }

        if (currentState.walletWithBills.walletId == null) {
            logger.warn(markers, logName)
            return ActionError(
                needCompletion = false,
                message = "O estado da transação não é válido",
                actionType = ActionType.SELECT_SWEEPING_ACCOUNT,
            ).left()
        }

        val balances = paymentAdapter.getOpenFinanceBalances(command.user.id, currentState.walletWithBills.walletId, transactionParticipants.values.map { it!!.id }).getOrElse {
            emptyMap()
        }

        customNotificationService.send(
            user = command.user,
            notificationConfig = customNotificationService.config().selectSweepingAccount,
            params = listOf(
                NotificationMap(NotificationParam.CONTA1_NOME, transactionParticipants[transactions[0].id]!!.name),
                NotificationMap(NotificationParam.CONTA1_NOME_CURTO, transactionParticipants[transactions[0].id]!!.shortName),
                NotificationMap(NotificationParam.CONTA1_SALDO, balances[transactionParticipants[transactions[0].id]!!.id].formattedBalance()),
                NotificationMap(NotificationParam.TRANSACTION_ID, transactions[0].id.value),

                NotificationMap(NotificationParam.CONTA2_NOME, transactionParticipants[transactions[1].id]!!.name),
                NotificationMap(NotificationParam.CONTA2_NOME_CURTO, transactionParticipants[transactions[1].id]!!.shortName),
                NotificationMap(NotificationParam.CONTA2_SALDO, balances[transactionParticipants[transactions[1].id]!!.id].formattedBalance()),
                NotificationMap(NotificationParam.TRANSACTION_ID_2, transactions[1].id.value),

                NotificationMap(NotificationParam.CONTA3_NOME, transactionParticipants[transactions[2].id]!!.name),
                NotificationMap(NotificationParam.CONTA3_NOME_CURTO, transactionParticipants[transactions[2].id]!!.shortName),
                NotificationMap(NotificationParam.CONTA3_SALDO, balances[transactionParticipants[transactions[2].id]!!.id].formattedBalance()),
                NotificationMap(NotificationParam.TRANSACTION_ID_3, transactions[2].id.value),
            ),
        )

        return ActionResult.WithoutCompletion(ActionType.SELECT_SWEEPING_ACCOUNT).right()
    }
}