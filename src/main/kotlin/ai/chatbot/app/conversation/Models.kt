package ai.chatbot.app.conversation

import com.theokanning.openai.completion.chat.ChatMessageRole
import java.time.ZonedDateTime

data class CompletionMessage(
    val verificacaoDeIntegridade: String,
    val entendimento: String,
    val acoes: List<Action>,
) {
    companion object {
        fun ofMessage(message: String): CompletionMessage {
            return CompletionMessage(
                verificacaoDeIntegridade = "",
                entendimento = "",
                acoes = listOf(Action.SendMessage(content = message)),
            )
        }
    }
}

data class ChatMessageWrapper(
    val type: MessageType,
    val message: String?,
    val completionMessage: CompletionMessage?,
    val timestamp: ZonedDateTime?,
)

enum class MessageType(val role: ChatMessageRole) {
    USER(role = ChatMessageRole.USER),
    ASSISTANT(role = ChatMessageRole.ASSISTANT),
    SYSTEM(role = ChatMessageRole.SYSTEM),
    FUNCTION(role = ChatMessageRole.FUNCTION),
    REACTION(role = ChatMessageRole.USER),
    ;

    companion object {
        fun fromRole(role: ChatMessageRole) {
            when (role) {
                ChatMessageRole.SYSTEM -> SYSTEM
                ChatMessageRole.USER -> USER
                ChatMessageRole.ASSISTANT -> ASSISTANT
                ChatMessageRole.FUNCTION -> FUNCTION
            }
        }
    }
}

data class UserReaction(
    val reaction: String,
    val id: String?,
)