package ai.chatbot.app.instrumentation

import ai.chatbot.app.OnePixPayInstrumentation
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.metrics.CountMetric
import ai.chatbot.app.metrics.push
import ai.chatbot.app.user.User
import jakarta.inject.Singleton

@Singleton
class DefaultOnePixPayInstrumentation : OnePixPayInstrumentation {
    override fun requestedToNotifyUser(
        user: User,
        bills: List<BillView>,
    ) {
        OnePixPayMetric.RequestedToNotifyUser.push()
    }

    override fun notifiedUser(
        user: User,
        bills: List<BillView>,
    ) {
        OnePixPayMetric.NotifiedUser.push()
    }

    override fun userQuickRepliedPayAll(user: User) {
        OnePixPayMetric.UserQuickRepliedPayAll.push()
    }

    override fun userQuickRepliedMarkAllAsPaid(user: User) {
        OnePixPayMetric.UserQuickRepliedMarkAllAsPaid.push()
    }

    override fun userRequestedToPayBills(
        user: User,
        current: List<BillView>,
        selected: List<BillView>,
    ) {
        if (current.size == selected.size) {
            OnePixPayMetric.UserRequestedToPayBills.All.push()
        } else {
            OnePixPayMetric.UserRequestedToPayBills.Some.push()
        }
    }

    override fun billsChangedAfterInteraction(
        user: User,
        reply: InterceptMessagePayloadType,
    ) {
        when (reply) {
            InterceptMessagePayloadType.MARK_AS_PAID -> OnePixPayMetric.BillsChangedAfterInteraction.MarkAsPaid
            InterceptMessagePayloadType.SEND_PIX_CODE -> OnePixPayMetric.BillsChangedAfterInteraction.SendPixCode
            InterceptMessagePayloadType.SEND_SUBSCRIPTION_PIX_CODE -> OnePixPayMetric.BillsChangedAfterInteraction.SendSubscriptionPixCode
            InterceptMessagePayloadType.TRANSACTION_CONFIRM -> OnePixPayMetric.BillsChangedAfterInteraction.TransactionConfirm
            InterceptMessagePayloadType.TRANSACTION_RETRY -> OnePixPayMetric.BillsChangedAfterInteraction.TransactionRetry
            InterceptMessagePayloadType.TRANSACTION_CANCEL -> OnePixPayMetric.BillsChangedAfterInteraction.TransactionCancel
            InterceptMessagePayloadType.MARK_REMINDER_AS_DONE -> OnePixPayMetric.BillsChangedAfterInteraction.MarkReminderAsDone
            InterceptMessagePayloadType.ADD_NEW_CONNECTION -> OnePixPayMetric.BillsChangedAfterInteraction.AddNewConnection
            InterceptMessagePayloadType.LIST_BILLS -> OnePixPayMetric.BillsChangedAfterInteraction.ListBills
            InterceptMessagePayloadType.ONBOARDING_SINGLE_PIX -> OnePixPayMetric.BillsChangedAfterInteraction.OnboardingSinglePix
            InterceptMessagePayloadType.SCHEDULE_BILLS -> OnePixPayMetric.BillsChangedAfterInteraction.ScheduleBills
            InterceptMessagePayloadType.SCHEDULE_CONFIRM -> OnePixPayMetric.BillsChangedAfterInteraction.ScheduleBills
            InterceptMessagePayloadType.PROMOTE_SWEEPING_ACCOUNT -> OnePixPayMetric.BillsChangedAfterInteraction.PromoteSweepingAccount
            InterceptMessagePayloadType.PAY_SOME -> OnePixPayMetric.BillsChangedAfterInteraction.PaySome
            InterceptMessagePayloadType.RETRY_SWEEPING -> OnePixPayMetric.BillsChangedAfterInteraction.RetrySweepingTransfer
            InterceptMessagePayloadType.ADD_BOLETO -> OnePixPayMetric.BillsChangedAfterInteraction.AddBoleto
            InterceptMessagePayloadType.PAY_BOLETO -> OnePixPayMetric.BillsChangedAfterInteraction.PayBoleto
            InterceptMessagePayloadType.SCHEDULE_BOLETO -> OnePixPayMetric.BillsChangedAfterInteraction.ScheduleBoleto
            InterceptMessagePayloadType.VALIDATE_BOLETO -> OnePixPayMetric.BillsChangedAfterInteraction.ValidateBoleto
            InterceptMessagePayloadType.REMOVE_MANUAL_ENTRY -> OnePixPayMetric.BillsChangedAfterInteraction.RemoveManualEntry
            InterceptMessagePayloadType.SELECT_SWEEPING_ACCOUNT -> OnePixPayMetric.BillsChangedAfterInteraction.SweepingTransfer
        }.push()
    }

    override fun sentOnePixPayCodeToUser(
        user: User,
        current: List<BillView>,
        selected: List<BillView>,
    ) {
        OnePixPayMetric.SentOnePixPayCodeToUser.push()
    }

    override fun requestedToMarkAsPaid(
        user: User,
        selected: List<BillView>,
    ) {
        OnePixPayMetric.RequestedToMarkAsPaid.push()
    }

    override fun scheduledBills(user: User, bills: List<BillView>) {
        OnePixPayMetric.ScheduledBills.push()
    }

    override fun requestedSweepingTransfer(user: User, amount: Long, bills: List<BillView>) {
        OnePixPayMetric.RequestedSweepingTransfer.push()
    }
}

sealed class OnePixPayMetric {
    object RequestedToNotifyUser : CountMetric(name = "one_pix_pay.requested_to_notify_user")

    object NotifiedUser : CountMetric(name = "one_pix_pay.notified_user")

    object UserQuickRepliedPayAll : CountMetric(name = "one_pix_pay.user_quick_replied_pay_all")

    object UserQuickRepliedMarkAllAsPaid : CountMetric(name = "one_pix_pay.user_quick_replied_mark_all_as_paid")

    object UserRequestedToPayBills {
        object All : CountMetric(name = "one_pix_pay.user_requested_to_pay_bills.all")

        object Some : CountMetric(name = "one_pix_pay.user_requested_to_pay_bills.some")
    }

    object BillsChangedAfterInteraction {
        object RetrySweepingTransfer : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.retry_sweeping_transfer")

        object MarkAsPaid : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.mark_as_paid")

        object SendPixCode : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.send_pix_code")

        object SendSubscriptionPixCode : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.send_subscription_pix_code")

        object TransactionConfirm : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.transaction_confirm")

        object TransactionRetry : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.transaction_retry")

        object TransactionCancel : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.transaction_cancel")

        object MarkReminderAsDone : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.mark_reminder_as_done")

        object AddNewConnection : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.add_new_connection")

        object RemoveManualEntry : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.remove_manual_entry")

        object ListBills : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.list_bills")
        object OnboardingSinglePix : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.onboarding_single_pix")
        object SweepingTransfer : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.sweeping_pix")
        object ScheduleBills : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.schedule_bills")
        object PromoteSweepingAccount : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.promote_sweeping_account")
        object PaySome : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.pay_some")
        object AddBoleto : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.add_boleto")
        object PayBoleto : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.pay_boleto")
        object ScheduleBoleto : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.schedule_boleto")
        object ValidateBoleto : CountMetric(name = "one_pix_pay.bills_changed_after_interaction.validate_boleto")
    }

    object SentOnePixPayCodeToUser : CountMetric(name = "one_pix_pay.sent_one_pix_pay_code_to_user")

    object RequestedToMarkAsPaid : CountMetric(name = "one_pix_pay.requested_to_mark_as_paid")

    object ScheduledBills : CountMetric(name = "one_pix_pay.scheduled_bills")

    object RequestedSweepingTransfer : CountMetric(name = "one_pix_pay.requested_sweeping_pix")
}