package ai.chatbot.app.file

import java.io.InputStream

interface ObjectRepository {
    fun listObjectKeys(
        bucketName: String,
        directoryKey: String,
    ): List<String>

    fun region(): String

    fun loadObject(
        bucketName: String,
        key: String,
    ): InputStream

    fun loadObjectWithMetadata(
        bucketName: String,
        key: String,
    ): ObjectWithMetadata

    fun moveObject(
        fromBucket: String,
        fromKey: String,
        toBucket: String = fromBucket,
        toKey: String,
    )

    fun copyObject(
        fromBucket: String,
        fromKey: String,
        toBucket: String = fromBucket,
        toKey: String,
    )

    fun putObject(storedObject: StoredObject, fileData: ByteArray, mediaType: String)
    fun findKeysByPrefix(storedObject: StoredObject): List<StoredObject>
    fun hasKeyPrefix(storedObject: StoredObject): Boolean
}

data class StoredObject(
    val region: String?,
    val bucket: String,
    val key: String,
) {

    fun toS3Path(): String {
        return "s3://$bucket/$key"
    }

    companion object {
        fun fromS3Path(region: String, s3Path: String): StoredObject {
            s3Path.removePrefix("s3://").split("/", limit = 2).let { (bucket, key) ->
                return StoredObject(region = region, bucket = bucket, key = key)
            }
        }
    }
}

data class ObjectWithMetadata(
    val data: InputStream,
    val contentLength: Long,
)