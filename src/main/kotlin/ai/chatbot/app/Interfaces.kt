package ai.chatbot.app

import ai.chatbot.adapters.api.TransactionPaymentStatus
import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.BillType
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.classification.ClassificationResult
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ChatMessageWrapper
import ai.chatbot.app.conversation.CompletionMessage
import ai.chatbot.app.conversation.ConversationHistory
import ai.chatbot.app.conversation.HistoryState
import ai.chatbot.app.conversation.HistoryStateType
import ai.chatbot.app.conversation.InteractionWindow
import ai.chatbot.app.conversation.InteractionWindowState
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.conversation.PaymentAdapterError
import ai.chatbot.app.conversation.Subscription
import ai.chatbot.app.conversation.SweepingTransferErrorReason
import ai.chatbot.app.job.DailyLogErrorSummary
import ai.chatbot.app.notification.CTALink
import ai.chatbot.app.notification.ChatbotNotification
import ai.chatbot.app.notification.NotificationMedia
import ai.chatbot.app.notification.QuickReplyButton
import ai.chatbot.app.payment.Balance
import ai.chatbot.app.pix.AssistantPaymentLimit
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.pix.PixQRCode
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionDetails
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.transaction.TransactionResult
import ai.chatbot.app.transaction.TransactionStatus
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserActivity
import ai.chatbot.app.user.UserActivityType
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import arrow.core.Either
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID

interface IOpenAIAdapter {
    fun createChatCompletion(
        prompt: String,
        historyState: HistoryState,
        messages: List<ChatMessageWrapper>,
        systemMessageToCompletion: String? = null,
        retry: Boolean = true,
    ): CompletionMessage

    fun createLogCompletion(
        messages: List<ChatMessageWrapper>,
        retry: Boolean = true,
        state: BillComingDueHistoryState,
    ): OpenAIAdapter.LogCompletionResult

    fun createSummaryCompletion(
        summaries: List<DailyLogErrorSummary>,
        retry: Boolean = true,
    ): OpenAIAdapter.SummaryCompletionResult
}

interface NotificationService {
    fun notify(
        notification: ChatbotNotification,
        delaySeconds: Int? = null,
        retry: Boolean = true,
    )

    fun notify(
        userId: UserId,
        accountId: AccountId?,
        message: String?,
        quickReplyButtons: List<QuickReplyButton>? = null,
        ctaLink: CTALink? = null,
        media: NotificationMedia? = null,
        delay: Int? = null,
        retry: Boolean = true,
    )
}

interface TransactionRepository {
    fun save(transaction: Transaction)

    fun find(userId: UserId): List<Transaction>

    fun find(
        userId: UserId,
        status: TransactionStatus,
    ): List<Transaction>

    fun find(transactionId: TransactionId): Transaction?
}

interface DailyLogSummaryRepository {
    fun save(summary: DailyLogErrorSummary)

    fun find(startDate: LocalDate, endDate: LocalDate): List<DailyLogErrorSummary>
}

interface HistoryRepository {
    fun saveUserMessage(
        userId: UserId,
        message: String,
    )

    fun saveUserReaction(
        userId: UserId,
        reaction: String,
    )

    fun saveAssistantMessage(
        userId: UserId,
        message: String,
    )

    fun saveAssistantMessage(
        userId: UserId,
        completionMessage: CompletionMessage,
    )

    fun saveSystemMessage(
        userId: UserId,
        message: String,
    )

    fun saveClassification(
        userId: UserId,
        date: LocalDate,
        classificationResult: ClassificationResult,
    )

    fun clearConversationHistory(
        userId: UserId,
        template: HistoryStateType,
        date: LocalDate,
    )

    fun findLatest(userId: UserId): ConversationHistory

    fun find(
        userId: UserId,
        date: LocalDate,
        template: HistoryStateType,
    ): ConversationHistory

    fun create(
        userId: UserId,
        historyStateType: HistoryStateType,
        initialUserMessage: String?,
    )

    fun findByDate(date: LocalDate): List<ConversationHistory>
}

interface HistoryStateRepository {
    fun save(
        userId: UserId,
        state: HistoryState,
    )

    fun findLatest(userId: UserId): HistoryState
    fun findByDate(userId: UserId, date: LocalDate): HistoryState

    fun delete(
        userId: UserId,
        date: LocalDate,
    )
}

interface InteractionWindowRepository {
    fun findByUserId(userId: UserId): InteractionWindow?
    fun findAllByStateAndExpirationBefore(state: InteractionWindowState, expiration: ZonedDateTime): List<InteractionWindow>
    fun save(window: InteractionWindow)
    fun delete(window: InteractionWindow)
}

interface PaymentAdapter {
    suspend fun createOnboardingSinglePix(
        keyType: String? = null,
        keyValue: String? = null,
        userId: UserId,
    ): Either<PaymentAdapterError, BillView>

    fun addBill(
        userId: UserId,
        digitable: String,
        dueDate: String? = null,
        dryRun: Boolean = false,
    ): Either<PaymentAdapterError, CreateBillResult>
    suspend fun generatePixQRCode(
        userId: UserId,
        amount: Long,
        walletId: WalletId,
        message: String,
    ): Either<PaymentAdapterError, PixQRCode>

    suspend fun generateOnePixPay(
        userId: UserId,
        bills: List<BillId>,
        walletId: WalletId,
    ): Either<PaymentAdapterError, PixQRCode>

    fun checkBills(
        userId: UserId,
        billId: List<BillId>,
    ): Either<PaymentAdapterError, List<BillView>>

    suspend fun scheduleBills(
        userId: UserId,
        sweepingRequest: SweepingRequest?,
        walletId: WalletId,
        bills: List<BillId>,
        authorizationToken: String? = null,
        scheduleToDueDate: Boolean = false,
    ): Either<PaymentAdapterError, Unit>

    suspend fun findPendingBills(
        userId: UserId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): Either<PaymentAdapterError, UserAndWallet>

    suspend fun getBalanceAndForecast(
        userId: UserId,
        walletId: WalletId?,
    ): Either<PaymentAdapterError, Balance>

    suspend fun markBillsAsPaid(
        userId: UserId,
        bills: List<BillId>,
        walletId: WalletId?,
    ): Either<PaymentAdapterError, Unit>

    suspend fun markReminderAsDone(
        userId: UserId,
        reminderId: String,
    ): Either<PaymentAdapterError, Unit>

    suspend fun ignoreBills(
        userId: UserId,
        bills: List<BillId>,
        walletId: WalletId?,
    ): Either<PaymentAdapterError, Unit>

    fun pixValidation(
        key: PixKey,
        userId: UserId,
        amount: Long,
        walletId: WalletId,
    ): Either<PaymentAdapterError, PixValidationResult>

    fun retrySweepingTransfer(
        userId: UserId,
        walletId: WalletId,
        amount: Long,
        participantId: SweepingParticipantId?,
    ): Either<SweepingTransferErrorReason, String>

    fun getContacts(
        userId: UserId,
        walletId: WalletId? = null,
    ): Either<PaymentAdapterError, List<Contact>>

    fun getSubscriptionFee(
        userId: UserId,
    ): Either<PaymentAdapterError, Long>

    fun getActiveSweepingConsents(
        userId: UserId,
        walletId: WalletId?,
    ): Either<PaymentAdapterError, List<SweepingConsent>>

    fun validatePixQrCode(
        qrCode: String,
        amount: Long,
        userId: UserId,
    ): Either<PaymentAdapterError, PixValidationResult>

    fun getSubscription(
        userId: UserId,
    ): Either<PaymentAdapterError, Subscription?>

    fun createManualEntry(
        userId: UserId,
        title: String,
        description: String,
        amount: Long,
        type: ManualEntryType,
        dueDate: LocalDate,
    ): Either<PaymentAdapterError, ManualEntryId>

    fun removeManualEntry(
        userId: UserId,
        manualEntryId: ManualEntryId,
    ): Either<PaymentAdapterError, Unit>

    suspend fun pixTransaction(
        userId: UserId,
        walletId: WalletId,
        amount: Long,
        sweepingRequest: SweepingRequest?,
        key: PixKey,
        transactionId: TransactionId,
        authorizationToken: String? = null,
        qrCode: String? = null,
        retryTransaction: Boolean = false,
    ): Either<PaymentAdapterError, Unit>

    fun getAssistantLimit(
        userId: UserId,
        walletId: WalletId,
    ): Either<PaymentAdapterError, AssistantPaymentLimit>

    fun getUserActivities(
        userId: UserId,
        vararg activities: UserActivityType,
    ): Either<PaymentAdapterError, List<UserActivity>>

    fun setUserActivity(
        userId: UserId,
        userActivity: UserActivity,
    ): Either<PaymentAdapterError, Unit>

    fun getOpenFinanceBalances(userId: UserId, walletId: WalletId, participantIds: List<SweepingParticipantId>): Either<PaymentAdapterError, Map<SweepingParticipantId, Long?>>
    fun validateAvailableLimit(userId: UserId, walletId: WalletId, amount: Long, type: AvailableLimitType): Either<PaymentAdapterError, AvailableLimitResponse>
}

enum class AvailableLimitType {
    PIX, SCHEDULE_BILLS
}

enum class AvailableLimitResponse {
    OK, ASSISTANT_LIMIT_EXCEEDED, PIX_LIMIT_EXCEEDED
}

interface TextSearchAdapter {
    fun search(
        text: String,
        elements: List<SearchElement>,
    ): List<String>
}

data class SearchElement(
    val id: String,
    val fields: Map<String, String>,
)

data class SweepingParticipantId(
    val value: String = UUID.randomUUID().toString(),
)

data class SweepingParticipant(
    val id: SweepingParticipantId,
    val name: String,
    val shortName: String,
)

data class SweepingConsent(
    val participant: SweepingParticipant,
    val transactionLimit: Long,
    val lastSuccessfulCashIn: ZonedDateTime?,
    val periodicUsage: SweepingConsentPeriodicUsage? = null,
)

data class SweepingConsentPeriodicUsage(
    val daily: SweepingConsentPeriodicLimitUsage,
    val weekly: SweepingConsentPeriodicLimitUsage,
    val monthly: SweepingConsentPeriodicLimitUsage,
    val yearly: SweepingConsentPeriodicLimitUsage,
    val totalLimit: Long,
    val totalUsed: Long,
)

data class SweepingConsentPeriodicLimitUsage(
    val amountLimit: Long,
    val amountUsed: Long,
    val quantityLimit: Long,
    val quantityUsed: Long,
)

data class CreateBillResult(
    val assignor: String,
    val amount: Long,
    val dueDate: LocalDate?,
    val billId: BillId,
    val billType: BillType,
)

data class PixValidationResult(
    val keyType: PixKeyType,
    val keyValue: String,
    val recipientInstitution: String,
    val recipientDocument: String,
    val recipientName: String,
    val pixLimitStatus: PixLimitStatus,
    val e2e: String? = null,
    val amount: Long? = null,
)

enum class PixLimitStatus {
    AVAILABLE, EXCEEDS_ASSISTANT_LIMIT, EXCEEDS_DAILY_LIMIT
}

interface OnePixPayInstrumentation {
    fun requestedToNotifyUser(
        user: User,
        bills: List<BillView>,
    )

    fun notifiedUser(
        user: User,
        bills: List<BillView>,
    )

    fun userQuickRepliedPayAll(user: User)

    fun userQuickRepliedMarkAllAsPaid(user: User)

    fun userRequestedToPayBills(
        user: User,
        current: List<BillView>,
        selected: List<BillView>,
    )

    fun billsChangedAfterInteraction(
        user: User,
        reply: InterceptMessagePayloadType,
    )

    fun sentOnePixPayCodeToUser(
        user: User,
        current: List<BillView>,
        selected: List<BillView>,
    )

    fun requestedToMarkAsPaid(
        user: User,
        selected: List<BillView>,
    )

    fun scheduledBills(
        user: User,
        bills: List<BillView>,
    )

    fun requestedSweepingTransfer(
        user: User,
        amount: Long,
        bills: List<BillView>,
    )
}

interface TransactionService {
    fun create(userId: UserId, walletId: WalletId, details: TransactionDetails, transactionGroupId: TransactionGroupId? = null): Transaction

    fun create(userId: UserId, details: TransactionDetails, transactionGroupId: TransactionGroupId? = null): Transaction

    fun cancel(transactionId: TransactionId, userId: UserId): TransactionResult

    fun retry(transactionId: TransactionId, userId: UserId): TransactionResult

    fun confirm(transactionId: TransactionId, userId: UserId, authorizationToken: String? = null): TransactionResult

    fun updatePaymentStatus(transactionId: TransactionId, userId: UserId, paymentStatus: TransactionPaymentStatus): TransactionResult

    fun find(transactionId: TransactionId): Transaction?

    fun find(userId: UserId, transactionGroupId: TransactionGroupId): List<Transaction>
}

interface TableConfiguration {
    val tableName: String
}

data class SweepingRequest(
    val transactionId: TransactionId,
    val amount: Long,
    val participantId: SweepingParticipantId,
    val retry: Boolean,
)