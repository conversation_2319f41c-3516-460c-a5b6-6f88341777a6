package ai.chatbot.app.metrics

interface Metric {
    val name: String
    val shouldPrefix: Boolean
    val shouldSuffix: Boolean
}

enum class MetricType {
    COUNTER,
    TIMED,
}

abstract class BaseMetric(
    name: String? = null,
    override val shouldPrefix: <PERSON>olean = name.isNullOrEmpty(),
    override val shouldSuffix: Boolean = name.isNullOrEmpty(),
) :
    Metric {
    final override var name: String =
        if (!name.isNullOrEmpty()) {
            name
        } else {
            val clazzName = this::class.java.simpleName

            clazzName.fold(StringBuilder(clazzName.length)) { acc, c ->
                if (c in 'A'..'Z') {
                    (if (acc.isNotEmpty()) acc.append('.') else acc).append(c + ('a' - 'A'))
                } else {
                    acc.append(c)
                }
            }.toString()
        }
}

abstract class TimeElapsedMetric : BaseMetric() {
    abstract fun elapsed(): Long
}

abstract class CountMetric(name: String? = null) : BaseMetric(name)