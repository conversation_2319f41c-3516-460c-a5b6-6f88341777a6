package ai.chatbot.app.metrics

import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.Meter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Timer
import io.micrometer.core.instrument.config.NamingConvention
import io.micronaut.context.annotation.Context
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.util.concurrent.TimeUnit
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Context
@Singleton
@Requires(property = "micronaut.metrics.enabled", notEquals = "false")
private class Metrifier(
    @Property(name = "micronaut.application.name") applicationName: String,
    @Property(name = "management.metrics.tags") commonTags: Map<String, Any>,
    registry: MeterRegistry,
) {
    init {
        defaultPrefix = applicationName

        val common =
            buildList(commonTags.size * 2) {
                commonTags.forEach { (key, value) ->
                    this.add(key)
                    this.add(value.toString())
                }
            }

        registry.config()
            .namingConvention(NamingConvention.dot)
            .commonTags(*common.toTypedArray())

        Companion.registry = registry
    }

    companion object {
        private val logger = LoggerFactory.getLogger(Metrifier::class.java)

        private lateinit var defaultPrefix: String
        private lateinit var registry: MeterRegistry

        fun isInitialized() = this::defaultPrefix.isInitialized && this::registry.isInitialized

        fun count(
            metric: CountMetric,
            value: Number = 1,
            vararg tags: Pair<String, Any?>,
        ) {
            val markers = Markers.appendEntries(mapOf("metric_name" to metric.name, "tags" to tags))

            try {
                if (!isInitialized()) {
                    return
                }

                val name = buildMetricName(metric)
                val meter = getOrCreateMeter(name, MetricType.COUNTER, *tags) as? Counter

                if (meter == null) {
                    logger.warn(markers, "inc#duplicate_tags")
                    return
                }

                meter.increment(value.toDouble())
            } catch (ex: Exception) {
                logger.error(markers, "inc#error", ex)
            }
        }

        fun time(
            metric: TimeElapsedMetric,
            vararg tags: Pair<String, Any?>,
        ) {
            val markers = Markers.appendEntries(mapOf("metric_name" to metric.name, "tags" to tags))

            try {
                if (!isInitialized()) {
                    return
                }

                val name = buildMetricName(metric)
                val meter = getOrCreateMeter(name, MetricType.TIMED, *tags) as? Timer

                if (meter == null) {
                    logger.warn(markers, "time#duplicate_tags")
                    return
                }

                meter.record(metric.elapsed(), TimeUnit.MILLISECONDS)
            } catch (ex: Exception) {
                logger.error(markers, "inc#error", ex)
            }
        }

        private fun buildMetricName(metric: Metric): String {
            val suffix =
                when (metric) {
                    is CountMetric -> "total"
                    is TimeElapsedMetric -> "duration"
                    else -> null
                }

            return buildString {
                if (metric.shouldPrefix) {
                    append("$defaultPrefix.")
                }

                append(metric.name)

                if (metric.shouldSuffix && suffix != null) {
                    append(".$suffix")
                }
            }
        }

        private fun validate(vararg tags: Pair<String, Any?>) =
            tags.groupingBy { it.first }.eachCount().count { it.value > 1 } == 0

        private fun getOrCreateMeter(
            name: String,
            type: MetricType,
            vararg possibleTags: Pair<String, Any?>,
        ): Meter? {
            if (!validate(*possibleTags)) return null

            val tags = possibleTags.map { if (it.second == null) it.first to "undefined" else it.first to it.second }
            val tagsArray = ArrayList<String>(tags.size * 2)

            tags.forEach { tagsArray.addAll(arrayOf(it.first, it.second.toString().lowercase())) }

            return when (type) {
                MetricType.COUNTER -> registry.counter(name, *tagsArray.toTypedArray())
                MetricType.TIMED -> registry.timer(name, *tagsArray.toTypedArray())
            }
        }
    }
}

fun CountMetric.push(value: Number = 1, vararg tags: Pair<String, Any?>) {
    if (Metrifier.isInitialized()) {
        Metrifier.count(this, value, *tags)
    }
}