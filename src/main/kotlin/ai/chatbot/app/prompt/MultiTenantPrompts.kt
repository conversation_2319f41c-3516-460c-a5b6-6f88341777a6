package ai.chatbot.app.prompt

import ai.chatbot.app.config.TenantService
import ai.chatbot.app.prompt.promptModules.PartialAccountPrompts
import ai.chatbot.app.prompt.promptModules.actions.MakePaymentPrompt
import ai.chatbot.app.prompt.promptModules.actions.ManualEntryPrompt
import ai.chatbot.app.prompt.promptModules.actions.SendPixPrompt
import ai.chatbot.app.prompt.promptModules.actions.addBoletoPrompt
import ai.chatbot.app.prompt.promptModules.actions.ignoreOrRemoveBillsPrompt
import ai.chatbot.app.prompt.promptModules.actions.markBillsAsPaidPrompt
import ai.chatbot.app.prompt.promptModules.actions.sendMessagePrompt
import ai.chatbot.app.prompt.promptModules.actions.sendPendingBillsPrompt
import ai.chatbot.app.prompt.promptModules.actions.singlePixPrompt
import ai.chatbot.app.prompt.promptModules.context.GeneralRulesPrompt
import ai.chatbot.app.prompt.promptModules.context.OperationPrompt
import ai.chatbot.app.prompt.promptModules.context.SubscriptionPrompt
import ai.chatbot.app.prompt.promptModules.context.botDetectionPrompt
import ai.chatbot.app.prompt.promptModules.context.mainFeaturesPrompt
import ai.chatbot.app.prompt.promptModules.context.paymentProblemsPrompt
import jakarta.inject.Singleton

@Singleton
class MultiTenantPrompts(
    private val tenantService: TenantService,
    private val makePaymentPrompt: MakePaymentPrompt,
    private val sendPixPrompt: SendPixPrompt,
    private val generalRulesPrompt: GeneralRulesPrompt,
    private val subscriptionPrompt: SubscriptionPrompt,
    private val operationPrompt: OperationPrompt,
    private val partialAccountPrompts: PartialAccountPrompts,
    private val manualEntryPrompts: ManualEntryPrompt,
) {
    private val configuration by lazy { tenantService.getConfiguration().prompt }

    fun getGuestPrompt(): String {
        return partialAccountPrompts.getGuestPrompt(
            appName = configuration.appName,
            hasMailBox = configuration.hasMailBox,
            supportLink = configuration.supportLink,
            appLink = configuration.appLink,
            appDomain = configuration.appDomain,
            hasWalletShare = configuration.hasWalletShare,
            hasInvestments = configuration.hasInvestment,
            personality = configuration.personality,
        )
    }

    fun getNotRegisteredPrompt(): String {
        return partialAccountPrompts.getNotRegisteredPrompt(
            appName = configuration.appName,
            hasMailBox = configuration.hasMailBox,
            supportLink = configuration.supportLink,
            appLink = configuration.appLink,
            appDomain = configuration.appDomain,
            hasWalletShare = configuration.hasWalletShare,
            hasInvestments = configuration.hasInvestment,
            personality = configuration.personality,
        )
    }

    fun getDefaultPrompt(hasInvestmentCampaignText: Boolean = false): String {
        return """
            ${configuration.personality}

            ${configuration.overview}
            
            $sendMessagePrompt

            $mainFeaturesPrompt

            Você deve se apresentar como "${configuration.presentation}" apenas uma vez na mesma conversa.

            $markBillsAsPaidPrompt
            
            $ignoreOrRemoveBillsPrompt

            $singlePixPrompt

            $sendPendingBillsPrompt

            ${makePaymentPrompt.getPrompt(configuration.appName)}
            
            $addBoletoPrompt

            ${sendPixPrompt.getPrompt(configuration.appName)}
            
            ${manualEntryPrompts.getManualEntryPrompt(configuration.appName)}
            
            ${manualEntryPrompts.getReminderPrompt(configuration.appName)}

            ${generalRulesPrompt.getPrompt(configuration.appName, configuration.appDomain)}

            $botDetectionPrompt

            $paymentProblemsPrompt

            ${subscriptionPrompt.getSubscriptionPrompt(
            appName = configuration.appName,
            hasInAppPayment = configuration.hasInAppSubscriptionPayment,
            supportLink = configuration.supportLink,
        )}
            
            ${if (hasInvestmentCampaignText) mePoupeInvestmentPrompt else ""}

            ${operationPrompt.getPrompt(configuration.appName)}
        """.trimIndent()
    }
}