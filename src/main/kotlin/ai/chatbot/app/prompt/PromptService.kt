package ai.chatbot.app.prompt

import ai.chatbot.adapters.dynamodb.PromptTenant
import ai.chatbot.adapters.dynamodb.StagingPromptTenantRepository
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.Friday
import ai.chatbot.app.ME_POUPE_ENV
import ai.chatbot.app.MOTOROLA_ENV
import ai.chatbot.app.MePoupe
import ai.chatbot.app.Motorola
import ai.chatbot.app.Staging
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getCurrentDate
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getCurrentDayOfWeek
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getDayOfWeek
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.dateFormat
import io.micronaut.context.annotation.Primary
import jakarta.inject.Singleton
import java.time.DayOfWeek
import java.time.temporal.TemporalAdjusters
import net.logstash.logback.marker.Markers
import org.slf4j.Logger
import org.slf4j.LoggerFactory

interface PromptService {
    fun buildDefaultPrompt(promptConfiguration: PromptConfiguration): String
    fun buildEarlyAccessPrompt(promptConfiguration: PromptConfiguration): String
    fun buildGuestPrompt(promptConfiguration: PromptConfiguration): String
    fun buildNotRegisteredPrompt(promptConfiguration: PromptConfiguration): String

    fun getPrompt(
        state: BillComingDueHistoryState,
    ): Prompt {
        val promptConfiguration = PromptConfiguration.build(state)
        return getPrompt(promptConfiguration)
    }

    fun getPrompt(promptConfiguration: PromptConfiguration): Prompt {
        val logName = "PromptService#getPrompt"
        val markers = Markers.append("promptConfiguration", promptConfiguration)
        logger.info(markers, logName)

        val promptText = when (promptConfiguration.promptType) {
            PromptType.DEFAULT -> buildDefaultPrompt(promptConfiguration)
            PromptType.EARLY_ACCESS -> buildEarlyAccessPrompt(promptConfiguration)
            PromptType.GUEST -> buildGuestPrompt(promptConfiguration)
            PromptType.NOT_REGISTERED -> buildNotRegisteredPrompt(promptConfiguration)
        }

        return Prompt(prompt = insertCurrentDate(promptText), type = promptConfiguration.promptType)
    }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(this::class.java)
    }
}

@Singleton
class TenantPromptService(
    private val prompts: Map<String, PromptService>,
    private val tenantService: TenantService,
    private val multiTenantPromptService: MultiTenantPromptService,
) {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    fun getPrompt(state: BillComingDueHistoryState): Prompt {
        val logName = "TenantPromptService#getPrompt"
        val tenantName = tenantService.getTenantName().lowercase()
        val markers = Markers.append("tenantName", tenantName).andAppend("prompts", prompts).andAppend("tenantService", prompts[tenantName] ?: "Not found")

        logger.info(markers, logName)

        return prompts[tenantName]?.getPrompt(state) ?: multiTenantPromptService.getPrompt(state)
    }
}

@Singleton
@Primary
@Staging
class StagingPromptService(
    private val repository: StagingPromptTenantRepository,
) : PromptService {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    override fun buildDefaultPrompt(promptConfiguration: PromptConfiguration) = fridayDefaultPrompt
    override fun buildEarlyAccessPrompt(promptConfiguration: PromptConfiguration) = fridayDefaultPrompt
    override fun buildGuestPrompt(promptConfiguration: PromptConfiguration) = fridayGuestPrompt
    override fun buildNotRegisteredPrompt(promptConfiguration: PromptConfiguration) = fridayNotRegisteredPrompt

    private fun getTenant(msisdn: String): PromptTenant? {
        return repository.find(msisdn)
    }

    private fun getPromptService(promptTenant: PromptTenant): PromptService {
        val logName = "StagingPromptService#getPromptService"
        val markers = Markers.append("promptTenant", promptTenant)
        logger.info(markers, logName)

        return when (promptTenant.tenant.lowercase()) {
            FRIDAY_ENV -> FridayPromptService()
            ME_POUPE_ENV -> MePoupePromptService()
            MOTOROLA_ENV -> MotorolaPromptService()
            else -> FridayPromptService()
        }
    }

    override fun getPrompt(
        state: BillComingDueHistoryState,
    ): Prompt {
        val promptTenant = getTenant(state.user.id.value)
        if (promptTenant == null) {
            return FridayPromptService().getPrompt(state)
        } else {
            val promptConfiguration = buildPromptConfiguration(promptTenant)
            val promptService = getPromptService(promptTenant)
            return promptService.getPrompt(promptConfiguration)
        }
    }

    private fun buildPromptConfiguration(promptTenant: PromptTenant): PromptConfiguration {
        val logName = "StagingPromptService#buildPromptConfiguration"

        val type = promptTenant.type ?: PromptType.DEFAULT
        val hasInvestmentCampaignText = promptTenant.hasInvestmentCampaign ?: false
        val promptConfiguration = PromptConfiguration(type, hasInvestmentCampaignText)

        val markers = Markers.append("promptConfiguration", promptConfiguration)
        logger.info(markers, logName)

        return promptConfiguration
    }
}

@Singleton
class MultiTenantPromptService(private val multiTenantPrompts: MultiTenantPrompts) : PromptService {
    override fun buildDefaultPrompt(promptConfiguration: PromptConfiguration) = multiTenantPrompts.getDefaultPrompt()

    override fun buildEarlyAccessPrompt(promptConfiguration: PromptConfiguration) = multiTenantPrompts.getDefaultPrompt()

    override fun buildGuestPrompt(promptConfiguration: PromptConfiguration) = multiTenantPrompts.getGuestPrompt()

    override fun buildNotRegisteredPrompt(promptConfiguration: PromptConfiguration) = multiTenantPrompts.getNotRegisteredPrompt()

    override fun getPrompt(
        state: BillComingDueHistoryState,
    ): Prompt {
        val promptConfiguration = PromptConfiguration.build(state)
        return getPrompt(promptConfiguration)
    }
}

@Friday
@Singleton
class FridayPromptService : PromptService {
    override fun buildDefaultPrompt(promptConfiguration: PromptConfiguration) = fridayDefaultPrompt
    override fun buildEarlyAccessPrompt(promptConfiguration: PromptConfiguration) = fridayEarlyAccessPrompt
    override fun buildGuestPrompt(promptConfiguration: PromptConfiguration) = fridayGuestPrompt
    override fun buildNotRegisteredPrompt(promptConfiguration: PromptConfiguration) = fridayNotRegisteredPrompt
}

@MePoupe
@Singleton
class MePoupePromptService : PromptService {
    override fun buildDefaultPrompt(promptConfiguration: PromptConfiguration): String {
        return if (promptConfiguration.hasInvestmentCampaignText) {
            mePoupeDefaultPrompt.replace("{{mePoupeInvestmentPrompt}}", mePoupeInvestmentPrompt)
        } else {
            mePoupeDefaultPrompt.replace("{{mePoupeInvestmentPrompt}}", "")
        }
    }

    override fun buildEarlyAccessPrompt(promptConfiguration: PromptConfiguration) = buildDefaultPrompt(promptConfiguration)

    override fun buildGuestPrompt(promptConfiguration: PromptConfiguration): String {
        return if (promptConfiguration.hasInvestmentCampaignText) {
            mePoupeGuestPrompt.replace("{{mePoupeInvestmentPrompt}}", mePoupeInvestmentPrompt)
        } else {
            mePoupeGuestPrompt.replace("{{mePoupeInvestmentPrompt}}", "")
        }
    }

    override fun buildNotRegisteredPrompt(promptConfiguration: PromptConfiguration) = mePoupeNotRegisteredPrompt
}

@Motorola
@Singleton
class MotorolaPromptService : PromptService {
    override fun buildDefaultPrompt(promptConfiguration: PromptConfiguration) = motorolaDefaultPrompt
    override fun buildEarlyAccessPrompt(promptConfiguration: PromptConfiguration) = motorolaDefaultPrompt
    override fun buildGuestPrompt(promptConfiguration: PromptConfiguration) = motorolaGuestPrompt
    override fun buildNotRegisteredPrompt(promptConfiguration: PromptConfiguration) = motorolaNotRegisteredPrompt
}

private fun insertCurrentDate(prompt: String): String = prompt.replace("{{currentDate}}", getCurrentDate())
    .replace("{{currentDay}}", getCurrentDayOfWeek())
    .replace("{{tomorrowDay}}", getDayOfWeek(getLocalDate().plusDays(1)))
    .replace("{{tomorrowDate}}", dateFormat.format(getLocalDate().plusDays(1)))
    .replace("{{daysOfMonth}}", getLocalDate().with(TemporalAdjusters.lastDayOfMonth()).dayOfMonth.toString())
    .replace("{{startDayOfWeek}}", getDayOfWeek(getLocalDate().with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY))))
    .replace("{{endDayOfWeek}}", getDayOfWeek(getLocalDate().with(TemporalAdjusters.nextOrSame(DayOfWeek.SATURDAY))))
    .replace("{{startOfWeek}}", dateFormat.format(getLocalDate().with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY))))
    .replace("{{endOfWeek}}", dateFormat.format(getLocalDate().with(TemporalAdjusters.nextOrSame(DayOfWeek.SATURDAY))))
    .replace("{{endOfMonth}}", dateFormat.format(getLocalDate().with(TemporalAdjusters.lastDayOfMonth())))
    .replace("{{overDueStart}}", dateFormat.format(getLocalDate().minusDays(1).minusWeeks(1)))
    .replace("{{overDueEnd}}", dateFormat.format(getLocalDate().minusDays(1)))

data class Prompt(
    val prompt: String,
    val type: PromptType,
)

enum class PromptType {
    DEFAULT,
    EARLY_ACCESS,
    GUEST,
    NOT_REGISTERED,
}

data class PromptConfiguration(
    val promptType: PromptType,
    val hasInvestmentCampaignText: Boolean,
) {
    companion object {
        fun build(
            state: BillComingDueHistoryState,
        ): PromptConfiguration {
            val promptType = when (state.user.status) {
                AccountStatus.BLOCKED,
                AccountStatus.ACTIVE,
                -> {
                    if (state.user.isEarlyAccess) {
                        PromptType.EARLY_ACCESS
                    } else {
                        PromptType.DEFAULT
                    }
                }

                AccountStatus.UNDER_REVIEW,
                AccountStatus.UNDER_EXTERNAL_REVIEW,
                AccountStatus.APPROVED,
                AccountStatus.REGISTER_INCOMPLETE, // Reaberto
                -> PromptType.GUEST

                AccountStatus.NOT_REGISTERED -> PromptType.NOT_REGISTERED

                AccountStatus.DENIED,
                AccountStatus.CLOSED,
                -> throw IllegalStateException("Account status not allowed")
            }

            return PromptConfiguration(
                promptType = promptType,
                hasInvestmentCampaignText = state.user.isInvestmentCampaign,
            )
        }
    }
}