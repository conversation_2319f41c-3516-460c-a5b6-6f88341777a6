package ai.chatbot.app.prompt.promptModules.actions

const val singlePixPrompt = """
[BEGIN - Fluxo de boas vindas single pix]
Esse bloco só deve ser utilizado caso exista uma mensagem de sistema informando que o usuário está realizando o fluxo de boas vindas single pix.

Quando um usuário entrar no fluxo de boas vindas single pix, ele deve realizar as seguintes ações, nessa ordem:
1 - Confirmar que quer fazer o pagamento de teste. Nesse passo a [action] é [ACCEPT] se o usuário concordar e [SKIP] se o usuário negar realizar o pagamento de teste.
2 - Caso seja pedida uma chave pix ao usuário, ele deve informar. Nesse passo a [action] é [PIX].
3 - Confirmar que quer realizar o pagamento de teste. Nesse passo a [action] é [CONFIRM].
4 - Se não receber uma mensagem clara quer de utilização de outro fluxo, deve utilizar sempre [ACCEPT], [SKIP], [PIX] e [CONFIRM], até finalizar o fluxo.

Você deve utilizar a ação [onboarding_single_pix] toda vez que o usuário responder um passo do fluxo de boas vindas, passando o parâmetro:
- [action] com o nome ação correspondente aos passos acima

Quando a [action] for [PIX], você também deve passar, caso informado pelo usuário
- [type] com o tipo da chave pix
- [key] com o valor da chave pix

Os campos [type] e [key] devem ser passados somente se a [action] for [PIX], e somente se o usuário tiver informado uma chave própria. Caso contrário esses campos devem ser [null].
Os valores dos campos [key] e [type] devem ser obtidos através do bloco [Identificação de pix].
[END - Fluxo de boas vindas single pix]
"""