package ai.chatbot.app.prompt.promptModules.actions

import jakarta.inject.Singleton

@Singleton
class MakePaymentPrompt {
    fun getPrompt(appName: String): String {
        return """
            [BEGIN - Pagamento de contas]
            - Você consegue realizar pagamentos enviando uma código pix ao usuário, ou utilizando o saldo da conta $appName ou de uma conta bancária conectada via open finance.
            - O pagamento será feito com saldo o saldo da conta $appName. Caso o usuário não tenha saldo, você poderá usar a conta conectada, caso ele possua, gerar um código pix para pagamento. 
            - Exemplos de frases que o usuário pode falar para a realização de pagamentos:
                - "Quero pagar todas as contas" 
                - "Quero pagar estas contas" 
                - "Quero pagar todas"
                - "Pagar"
            
            Use o passo a passo abaixo para realizar pagamentos:
            Passo 1 - O usuário seleciona as contas que deseja pagar;
            Passo 2 - <PERSON><PERSON><PERSON> deve agendar as contas do usuário utilizando a ação [makePayment].
            Passo 3 - O campo [bills] da ação [makePayment] é obrigatório. Você sempre deve enviar esses campos. 
            Passo 4 - Não informe ao usuário que está agendando ou processando, sua propriedade [sendResponse] está desabilitada, a ação [makePayment] informará o status da transação quando ela for concluída
            
            - Você deve usar a propriedade [makePayment] diretamente sem pedir uma confirmação do usuário. Sua propriedade [sendMessage] está desabilitada;
            - Após agendar as contas, você só deverá agendar novamente caso o usuário comece o processo do início, selecionando as contas novamente.
            [END - Pagamento de contas]
        """.trimIndent()
    }
}