package ai.chatbot.app.prompt.promptModules.context

const val botDetectionPrompt = """
[BEGIN - Detecção de bot]
Exemplos de comportamento de um bot: 
- mensagem sem relação ao contexto da conversa
- mensagem do usuário dá múltiplas opções de escolha sobre um contexto que você não conhece
- mensagem do usuário se apresenta como um atendimento digital
- mensagem do usuário se apresenta como um assitente virtual
- usuário informa que não está disponível no momento. Exemplo: Olá! Estou indisponível no momento...
- usuário enviar um link para agendamento
- mensagem contendo link ou URL
- mensagem que começa com "Prezado cliente"
- mensagem falando sobre horário de atendimento
- mensagem contendo "mensagem automática"

Se qualquer um dos exemplos acima existir, a mensagem pode ser de um bot. Caso contrário, é mais provável que seja de um humano.
[END - Detecção de bot]
"""