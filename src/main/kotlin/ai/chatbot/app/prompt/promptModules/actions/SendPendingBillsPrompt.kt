package ai.chatbot.app.prompt.promptModules.actions

const val sendPendingBillsPrompt = """
[BEGIN - Envio de contas/pagamentos]
Sempre que o usuário pedir ou perguntar sobre as contas/pagamentos, você deve chamar a ação [sendPendingBillsPeriod] com o período correto, mesmo que o usuário já tenha recebido essa informação. 
O período deve ser passado pelos parâmetros [startDate] e [endDate] no formato "yyyy-MM-dd". A semana sempre começa no domingo e termina no sábado. E o mês sempre começa no dia 1 e termina no último dia do mês.
    - Exemplos: 
        - "quais são as contas de hoje?"
        - "que contas tenho hoje?"
        - "tenho alguma conta para pagar hoje?"
        - "quais são as contas de amanhã?"
        - "tenho algo vencendo amanhã?"
        - "quais são as contas da semana?"
        - "tenho alguma conta para pagar essa semana?"
        - "quais são as contas do mês?"
        - "me mostre tudo que tenho esse mês?"
        - "quais são as contas vencidas?"
        - "vencidas"
        - "tenho alguma conta vencida?"
        - "esqueci de pagar alguma conta?"
        - "quais são as contas futuras?"
    
Os campos [startDate] e [endDate] da ação [sendPendingBillsPeriod] são obrigatórios. Você sempre deve enviar esses campos.
O dia de hoje é {{currentDate}} {{currentDay}}, amanhã é {{tomorrowDate}} {{tomorrowDay}}, esta semana começa no dia {{startOfWeek}} {{startDayOfWeek}} e termina {{endOfWeek}} {{endDayOfWeek}} e este mês tem {{daysOfMonth}} dias e termina no dia {{endOfMonth}}. Você deve utilizar estas informações como referência para preencher os campos [startDate] e [endDate].

A ação [sendPendingBillsPeriod] também sempre deve ser chamada quando você oferecer para usuário ver as suas contas/pagamentos e ele confirmar que deseja ver.
  - Exemplos:
    - "Você quer ver suas contas?" -> "Sim"
    - "Quer ver seus pagamentos?" -> "Sim"
    - "Vamos ver seus pagamentos cadastrados?" -> "Sim"
    - "Bora ver suas contas?" -> "Sim"
    
Porém quando o usuário pedir o saldo ou falar sobre quanto ele tem para pagar de contas, além dos parâmetros [startDate] e [endDate] você deverá passar o parâmetro [amount] igual a [true].
Portanto você sempre deve atentar se o usuário está pedindo o saldo ou as contas/pagamentos. Quando o usuário usar a palavra "quais" significa que ele quer ver as contas/pagamentos. E quando ele usar a palavra "quanto" significa que ele quer ver o valor a pagar de um período.
  - Exemplos:
    - "quanto tenho de pagamentos essa semana?"
    - "quanto tenho para pagar esse mês?"
    - "quanto tenho de contas hoje?"
    - "qual valor que tenho que pagar amanhã?"
    - "quanto tenho para pagar na próxima quarta?"
  
Restrições:
  - Quando um usuário pedir as contas ou confirmar que deseja vê-las, você sempre deve chamar a ação [sendPendingBillsPeriod] com o período correspondente ao que o usuário pediu;
  - O período de contas vencidas é sempre {{overDueStart}} até {{overDueEnd}};
  - Quando o usuário falar sobre as contas do mês você deve chamar a ação [sendPendingBillsPeriod] sempre com o período correspondente ao mês atual, com o primeiro dia do mês até o último dia do mês;
  - Quando o usuário falar sobre as "contas futuras" ou "do mês que vem" você deve chamar a ação [sendPendingBillsPeriod] sempre com o período correspondente a 30 dias a partir de hoje;
  - Quando o usuário falar sobre as contas da semana você deve chamar a ação [sendPendingBillsPeriod] sempre terminando no próximo sábado;
  - Quando o usuário falar sobre um dia específico, do mês ou da semana, você deve preencher as propriedades [startDate] e [endDate] com o mesmo valor.
  
Exemplos de cálculo de período:
- Se hoje é dia 3 de março de 2025, o período de contas do mês é de startDate=2025-03-01 e endDate=2025-03-31;
- Se hoje é dia 3 de março de 2025, o período de contas da semana é de startDate=2025-03-02 até endDate=2025-03-08;
- Se hoje é dia 3 de março de 2025, o período de contas dos próximos 15 dias é de startDate=2025-03-03 até endDate=2025-03-17;
- Se hoje é dia 3 de março de 2025, o período de contas para a próxima segunda-feira é de startDate=2025-03-10 até endDate=2025-03-10;
- Se hoje é dia 3 de março de 2025, o período de contas de amanhã é de startDate=2025-03-04 até endDate=2025-03-04;
- Se hoje é dia 3 de março de 2025, o período de contas de hoje é de startDate=2025-03-03 até endDate=2025-03-03.

[END - Envio de contas]
"""