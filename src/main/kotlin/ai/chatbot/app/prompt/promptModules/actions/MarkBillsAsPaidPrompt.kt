package ai.chatbot.app.prompt.promptModules.actions

const val markBillsAsPaidPrompt = """
[BEGIN - Marcar contas como pagas]
Use o passo a passo abaixo para marcar contas como pagas:
Passo 1 - O usuário solicita marcar uma ou mais contas como paga. Alguns exemplos de solicitação:
    - Marcar a conta de luz como paga
    - Marcar a conta de água como paga
    - Marcar a conta de telefone como paga
    - <PERSON><PERSON> todas as contas como pagas
    - Marcar a conta de energia e água como pagas
    - Marcar como paga
    - Acabei de pagar
    - J<PERSON> paguei
    - Já paguei essa conta
    - Já fiz o pix
    - Acabei de fazer o pix
    - ...
    
Passo 2 - Deve usar a ação [markBillsAsPaidOrIgnoreBills] para marcar as contas como pagas.
    - Você deve usar a propriedade [markBillsAsPaidOrIgnoreBills] diretamente sem pedir uma confirmação do usuário. Sua propriedade [sendMessage] está desabilitada;
    - O campo [billsToMarkAsPaid] da propriedade [markBillsAsPaidOrIgnoreBills] serve para enviar a lista de contas que o usuário deseja marcar como pagas;
    - Exemplo:
        - O usuário tem 3 contas: 1. conta de luz, 2. conta de água e 3. Um pix para uma pessoa.
            - Se o usuário pedir para marcar a conta de luz como paga, você deve preencher o campo [billsToMarkAsPaid] com o id 1, que é o id da conta de luz.
            - Se o usuário pedir para marcar todas as contas como pagas, você deve preencher o campo [billsToMarkAsPaid] com os ids [1,2,3], que são todos os ids de contas do usuário.

Passo 3 - O usuário pode aceitar ou negar a confirmação de marcar as contas como paga. Você não deve chamar a ação [markBillsAsPaidOrIgnoreBills] novamente caso o usuário negue a confirmação.
    - Alguns exemplos de negar a confirmação:
        - Não quero marcar como paga
        - Não quero marcar essa conta como paga
        - Não quero
        - Não
        - ...

[END - Marcar contas como pagas]
"""