package ai.chatbot.app.prompt.promptModules.actions

import jakarta.inject.Singleton

@Singleton
class ManualEntryPrompt {
    fun getManualEntryPrompt(appName: String): String {
        return """
            [BEGIN - Lançamento manual]
            O usuário pode querer registrar um gasto feito por fora da $appName, para fins de controle de gastos, através de um lançamento manual.
            Um lançamento manual deve ser feito quando o usuário já realizou o gasto, e quer apenas registrar. Um lançamento manual não gera um pagamento ou debita do saldo do usuário no momento que é feito.

            Exemplos de mensagem de um usuário que quer fazer um lançamento manual:
            - Anotar mercado 15 reais
            - Transferi 20 reais para o Fulano
            - Anota pra mim 34 reais posto de gasolina
            - Registrar gasto 12 reais amendoim

            Quando um usuário desejar fazer um lançamento manual, utilize a ação [createManualEntry], passando:
            - [title]: o título que define o que foi gasto.
                - Exemplo: "Anotar mercado 15 reais" -> "Mercado"
                - Exemplo: "Transferi 20 reais para o Fulano" -> "Fulano"
                - Exemplo: "Registrar gasto 12 reais amendoim" -> "Amendoim".
            - [amount]: o valor gasto em Double.
                - Exemplo: "Anotar mercado 15 reais" -> 15.0
                - Exemplo: "Anota pra mim 34 bar" -> 34.0
                - Exemplo: "Registrar R${'$'} 12,40 amendoim" -> 12.4
            [END - Lançamento manual]
        """.trimIndent()
    }

    fun getReminderPrompt(appName: String): String {
        return """
            [BEGIN - Lembrete]
            O usuário pode querer registrar um lembrete de pagamento a ser feito por fora do app $appName, do qual ele será avisado no dia.
            Um lembrete não debita do saldo ou realiza o pagamento, é apenas um aviso que o usuário receberá na manhã do dia requisitado. O valor do pagamento no lembrete é opcional.
            
            Exemplos de mensagem de um usuário que quer fazer um lançamento manual:
            - Criar lembrete da aula de música 70 reais quinta que vem 
            - Me lembra de pagar a diarista segunda-feira
            - Lembrete de pagamento natação dia 20/06
            
            Quando um usuário desejar fazer um lançamento manual, utilize a ação [createReminder], passando:
            - [title]: o título que define o pagamento que será lembrado.
                - O título deve ser o que vai ser pago, sem verbos. (Ex: "Me lembra de pagar a prefeitura" -> "Prefeitura", e não "Pagar a prefeitura")
                - Exemplo: "Criar lembrete da aula de música 70 reais quinta que vem" -> "Aula de música"
                - Exemplo: "Me lembra de pagar a diarista segunda-feira" -> "Diarista"
                - Exemplo: "Lembrete de pagamento natação dia 20/06" -> "Natação".
            - [amount]: o valor opcional do pagamento em Double. Se o usuário não informar um valor, não passe essa informação.
                - Exemplo: "Criar lembrete da aula de música 70 reais quinta que vem" -> 70.0
                - Exemplo: "Lembrete de pagamento natação dia 20/06 R${'$'}20,40" -> 20.4
                - Exemplo: "Me lembra de pagar a diarista segunda-feira" -> 0
            - [date]: a data que o usuário será lembrado. É apenas uma data, sem horário. Formate sempre no formato yyyy-MM-dd.
                - Exemplo: "Criar lembrete da aula de música 70 reais quinta que vem" -> 2025-09-26 (se a data atual for quinta-feira 2025-06-19)
                - Exemplo: "Lembrete de pagamento natação dia 20/06 R${'$'}20" -> 2025-06-20 (se o ano atual for 2025)
            [END - Lembrete]
        """.trimIndent()
    }
}