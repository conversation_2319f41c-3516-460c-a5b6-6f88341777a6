package ai.chatbot.app.prompt.promptModules.actions

const val ignoreOrRemoveBillsPrompt = """
[BEGIN - Ignorar ou remover contas]
Use o passo a passo abaixo para ignorar ou remover contas:
Passo 1 - O usuário solicita ignorar ou remover uma ou mais contas. Alguns exemplos de solicitação:
    - Ignorar a conta de luz
    - Remover a conta de água
    - Ignorar a conta de telefone
    - Ignorar todas as contas
    - <PERSON>mover todas as contas
    - Remover as contas de energia e água
    - Ignorar
    - Remover
    - Não conheço essa conta
    - Remover essa conta
    - ...
    
Passo 2 - Deve usar a ação [markBillsAsPaidOrIgnoreBills] para ignorar ou remover contas.
    - Você deve usar a propriedade [markBillsAsPaidOrIgnoreBills] diretamente sem pedir uma confirmação do usuário. Sua propriedade [sendMessage] está desabilitada;
    - O campo [billsToIgnoreOrRemove] da propriedade [markBillsAsPaidOrIgnoreBills] serve para enviar a lista de contas que o usuário deseja ignorar ou remover;
    - Exemplo:
        - O usuário tem 3 contas: 1. conta de luz, 2. conta de água e 3. Um pix para uma pessoa.
            - Se o usuário pedir para ignorar a conta de luz, você deve preencher o campo [billsToIgnoreOrRemove] com o id 1, que é o id da conta de luz.
            - Se o usuário pedir para remover todas as contas, você deve preencher o campo [billsToIgnoreOrRemove] com os ids [1,2,3], que são todos os ids de contas do usuário.

Passo 3 - O usuário pode aceitar ou negar a confirmação de ignorar ou remover as contas. Você não deve chamar a ação [markBillsAsPaidOrIgnoreBills] novamente caso o usuário negue a confirmação.
    - Alguns exemplos de negar a confirmação:
        - Não quero ignorar
        - Não quero remover essa conta
        - Não quero
        - Não
        - ...
        
[END - Ignorar ou remover contas] 
"""