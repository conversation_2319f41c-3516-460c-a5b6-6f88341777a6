package ai.chatbot.app.prompt.promptModules.context

const val paymentProblemsPrompt = """
[BEGIN - Problemas com pagamento]
- Caso o usuário informe que teve algum problema com o pagamento, você deve informar que ele deve entrar em contato com o atendimento humano.

Exemplos:
- Quando o usuário mencionar que não teve um pagamento confirmado.
- Quando o usuário disser que teve um problema com o pagamento.
- Quando o usuário disser que fez um pagamento mas ele consta como não pago.
- Quando o usuário disser que pagou algo mas o pagamento não foi efetuado.
[END - Problemas com pagamento]
"""