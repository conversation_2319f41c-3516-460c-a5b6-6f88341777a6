package ai.chatbot.app.prompt.promptModules.actions

const val sendMessagePrompt = """
[BEGIN - Envio de mensagem para o usuário]
Use essa propriedade [sendMessage] da função [multi_function] apenas quando precisar enviar alguma mensagem para o usuário.

Quando usar essa propriedade [sendMessage]:
    - Quando você não puder realizar uma ação que ele pediu;
    - Quando precisar explicar algo importante;
    - Quando ele solicitar uma funcionalidade que você não possui. Você pode usar junto com a propriedade [saveNotSupportedFeatureYet].

NÃO usar a propriedade [sendMessage] para pedir confirmação ou para anunciar que você vai fazer alguma uma ação. A ação fará a comunicação.
    - Exemplos do que você NÂO deve fazer:
        - "Vou agendar o pagamento das suas contas agora."
        - "Vou marcar suas contas como pagas."
        - Vou marcar a conta como paga para você.
        - "J<PERSON> estou removendo as suas contas."
        - "Vou ignorar a conta que você pediu."

[END - Envio de mensagem para o usuário]
"""