package ai.chatbot.app.prompt.promptModules.actions

import jakarta.inject.Singleton

@Singleton
class SendPixPrompt {
    fun getPrompt(appName: String): String {
        return """
            [BEGIN - Envio de pix]
            O envio de valores via pix pode ser feito através de uma chave pix válida, de um contato já salvo na agenda do usuário ou de um código pix copia e cola. Esse valor é enviado usando exclusivamente do saldo da conta $appName.
            Você deve identificar se o usuário deseja enviar o pix para uma chave, para um contato ou se é um pix copia e cola seguindo os passos da seção 'Identificação de pix'. Não pergunte ao usuário qual é o tipo da chave pix, você deve identificar a chave pix obrigatoriamente a partir da mensagem do usuário.
            Você pode receber mensagens de áudio, texto, PDF ou imagens com o código pix ou QR code.

            IMPORTANTE: Cada solicitação de PIX deve ser tratada como uma transação independente. Você NUNCA deve reutilizar valores de transações anteriores. Se o usuário solicitar um novo PIX, você deve sempre pedir o valor novamente, mesmo que ele tenha acabado de fazer um PIX para a mesma chave.
            
            Caso o usuário envie apenas um e-mail, CPF, CNPJ, telefone, nome do contato ou qualquer outra chave pix, mas sem a palavra pix, você deve identificar se é uma chave pix usando a seção 'Identificação de pix' e perguntar se ele deseja fazer um pix.
            Caso o usuário informe um código pix copia e cola você deve seguir os passos da seção 'Checagem de Pix copia e cola'.
            Caso o usuário deseje enviar o pix para uma chave, você deve seguir os passos da seção 'Checagem de pix para chave'.
            Caso o usuário deseje enviar o pix para um contato, falando o nome ou apelido de uma pessoa, você deve seguir os passos da seção 'Checagem de pix para contato'.
            
            Se o usuário não especificar que o valor é em centavos ou real, entenda que é em reais.
            
            - O valor do pix [amount] deve ser informado em Double.
            - Sempre que o usuário quiser fazer um pix, use a função [pixTransaction].
            - Nunca verifique o saldo antes de fazer um pix. Você deve sempre tentar fazer o pix diretamente.
            [END - Envio de pix]
            
            $pixKeyCheckPrompt
            
            $pixContactCheckPrompt
            
            $pixCopyPasteCheckPrompt
            
            $pixIdentificationPrompt
        """.trimIndent()
    }
}

const val pixKeyCheckPrompt = """
[BEGIN - Checagem de pix para chave]
Use o passo a passo abaixo para realizar um pagamento via pix para uma chave:
Passo 1 - O usuário informa o valor e a chave pix que deseja pagar;
Passo 2 - Você deve identificar o tipo da chave pix. Siga os passos da seção 'Identificação de chave pix' para mais detalhes.
Passo 3 - Você deve enviar o pix para a chave utilizando a ação [pixTransaction].

IMPORTANTE: Se o usuário solicitar um novo PIX para a mesma chave, você DEVE pedir o valor novamente. Nunca reutilize valores de transações anteriores.
[END - Checagem de pix para chave]
"""

const val pixContactCheckPrompt = """
[BEGIN - Checagem de pix para contato]
Use o passo a passo abaixo para realizar um pagamento via pix para um contato:
    - Exemplos de contatos:
        - João;
        - Dr. Luiz;
        - Maria;
        - Sr. José;
        - Ana;
        - Tia Maria da Silva.
        
Passo 1 - O usuário informa o valor e nome ou apelido que deseja pagar;
Passo 2 - Você deve utilizar a ação [pixTransaction], com o campo [type] igual a CONTACT e o campo [key] com o nome ou apelido desejado.

IMPORTANTE: Se o usuário solicitar um novo PIX para o mesmo contato, você DEVE pedir o valor novamente. Nunca reutilize valores de transações anteriores.
[END - Checagem de pix para contato]
"""

const val pixCopyPasteCheckPrompt = """
[BEGIN - Checagem de Pix copia e cola]
Passo 1 - O usuário informa um pix copia e cola;
Passo 2 - Você deve utilizar a ação [pixTransaction], com o campo [type] igual a [COPY_PASTE] e o campo [key] com o código pix copia e cola informado pelo usuário e o campo [amount] com o valor. Caso o usuário não passe o valor, deixe o [amount] como 0.

- A construção de cada link/código pode variar conforme a instituição bancária onde o Pix Copia e Cola foi gerado. Mas sempre seguindo o padrão abaixo:
- Exemplos de Pix Copia e Cola:
    - 00020126330014br.gov.bcb.pix01111335366962052040000530398654040.805802BR5919NOME6014CIDADE
    - 00020126580014BR.GOV.BCB.PIX0136904af616-e175-4acc-a4b2-a5f0ba6cac5152040000530398654040.015802BR5923Nome Completo6009CIDADE621405104PrJuBCz6a630470AD
    - 00020126360014BR.GOV.BCB.PIX0114+5553981083254520400005303986540525.005802BR5920Nome6009CIDADE61080540900062240520JSPvpLbB3No0M431d2fd63043E25
    - 00020126940014br.gov.bcb.pix013686b26441-94eb-4523-9e8a-79120d007b940232VENC. 01/04/25 | FAT. 542634446452040000530398654100000057.995802BR5915TIM BRASIL S.A.6009Sao Paulo62170513B0154263444646304C902
[END - Checagem Pix copia e cola]
"""

const val pixIdentificationPrompt = """
[BEGIN - Identificação de chave pix]
- As chaves pix podem ser dos tipos [ELEVEN_DIGIT], [CNPJ], [EMAIL], [EVP], [CPF], [PHONE], [CONTACT], [COPY_PASTE].
    - As chaves pix copia e cola sempre terão 'BR.GOV.BCB.PIX' no meio.
      - Exemplos de pix copa e cola:
        - 00020126330014br.gov.bcb.pix01111335366962052040000530398654040.805802BR5919NOME6014CIDADE
        - 00020126580014BR.GOV.BCB.PIX0136904af616-e175-4acc-a4b2-a5f0ba6cac5152040000530398654040.015802BR5923Nome Completo6009CIDADE621405104PrJuBCz6a630470AD
        - 00020126940014br.gov.bcb.pix013686b26441-94eb-4523-9e8a-79120d007b940232VENC. 01/04/25 | FAT. 542634446452040000530398654100000057.995802BR5915TIM BRASIL S.A.6009Sao Paulo62170513B0154263444646304C902
      - Você nunca deve extrair a chave pix ou o valor de um código pix copia e cola, você deve utilizar TODO o código pix copia e cola como chave.
      - Você não deve perguntar o valor do pix copia e cola, você deve considerar que o valor é 0 caso o usuário não passe.
    - A chave [CNPJ] tem apenas 14 dígitos numéricos com o formato 99.999.999/0001-99.
        - Deve ser convertido para o formato 99999999000199.
        - Exemplos de CNPJ: 12.345.678/0001-95, 12345678000196, 123-456-780-00197.
    - As chaves [ELEVEN_DIGIT] são compostas por 11 dígitos numéricos, a não ser que o usuário explicite que é [CPF] ou [PHONE]. Quando os 11 dígitos numéricos foram escritos sem nenhum tipo de espaçamento ou formatação, sempre devem ser considerados uma chave [ELEVEN_DIGITS].
        - Deve ser convertido para o formato 99999999999.
        - Exemplos de [ELEVEN_DIGIT]: 999.999.99999, 999.999.999-99, 999-999-99999, 9-9-9-9-9-9-9-9-9-9-9.
    - As chaves [PHONE] representam um número de telefone brasileiro com ou sem código do país. Você só deve entender uma chave como [PHONE] se o usuário especificar explicitamente que se trata de um telefone, ou se estiver formatada como um telefone. 
        - Caso o usuário inclua o código do país, por exemplo, +55 21 99999-9999, deve ser convertido para o formato 21999999999.
        - Exemplos de formato de telefone: (99) 99999-9999, 99 99999 9999, 99 999999999.
        - Você nunca deve entender como telefone uma chave que sejam apenas onze dígitos sem formatação. Ex: 09834716238.
    - As chaves [EMAIL], vão ter o formato padrão de email com um @ indicando o domínio.
        - Exemplos de [EMAIL]:
            - <EMAIL>
            - <EMAIL>
    - As chaves [EVP] vão seguir um padrão formado por uma sequência aleatória de 32 caracteres entre números e letras.
        - Um EVP é composto por 32 dígitos alfanuméricos, ou seja, letras e números.
        - Exemplos de [EVP]:
            - beb21af5-d976-4a71-8e38-10791657db30
            - d107fa18-1c21-4eb7-9569-872f69c743be
            - b1201075-2030-44c5-8136-4b8f0f21bf22
    - As chaves [CONTACT] devem parecer nomes de pessoas ou apelidos, apenas se nenhum outro tipo mais específico for identificado.
        - Não inclua prefixos, títulos ou informações que não sejam parte do nome de uma pessoa.
        - Exemplos:
            - "Dr. Luiz" deve ser transformado em "Luiz".
            - "Prof Maria" deve ser transformado em "Maria".
            - "Sr. José" deve ser transformado em "José".
            - "Dona Ana" deve ser transformado em "Ana".
            - "Tia Maria da Silva" deve ser transformado em "Maria da Silva".
    - Se não identificar nenhuma das chaves acima, ou se o usuário não especificar uma chave, considere que o usuário informou um contato [CONTACT].
        - Exemplos de pix para contato:
            - "Quero fazer um pix para o João"
            - "Quero fazer um pix para a Dona Ana"
            - "Faça um pix para o Dr. Mario"
            - "pix para o Sr. José"
[END - Identificação de chave pix]
"""