package ai.chatbot.app.job

import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier
import ai.chatbot.app.utils.andAppend
import ai.chatbot.billpayment.app.job.AbstractJob
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.discovery.event.ServiceReadyEvent
import io.micronaut.runtime.event.ApplicationShutdownEvent
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.Duration
import java.time.Instant
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Requires(notEnv = ["test"])
@Singleton
open class JobManager(val jobs: List<AbstractJob>) : ApplicationEventListener<Any> {
    @field:Property(name = "shedlock.defaults.lock-at-least-for", defaultValue = "PT0S")
    private lateinit var defaultLockAtLeastFor: String

    @field:Property(name = "shedlock.defaults.lock-at-most-for")
    private lateinit var defaultLockAtMostFor: String

    private val shoutDownPoolingInterval = 10000L

    fun listJobs(): List<AbstractJob> {
        return jobs.sortedBy { it.jobName }
    }

    @NewSpan
    override fun onApplicationEvent(event: Any) {
        when (event) {
            is ServiceReadyEvent -> onServiceReady()
            is ApplicationShutdownEvent -> onApplicationShutdown()
        }
    }

    private fun onServiceReady() {
        val markers =
            Markers.append("jobs", jobs.size)
                .andAppend("defaultLockAtLeastFor", defaultLockAtLeastFor)
                .andAppend("defaultLockAtMostFor", defaultLockAtMostFor)
        LOG.info(markers, "JobManager#onServiceReady")

        jobs.forEach {
            it.initialize(
                defaultLockAtLeastFor = defaultLockAtLeastFor,
                defaultLockAtMostFor = defaultLockAtMostFor,
            )
        }
    }

    private fun onApplicationShutdown() {
        LOG.info("JobManager#onApplicationShutdown")

        val start = BrazilZonedDateTimeSupplier.getZonedDateTime().toInstant()

        jobs.forEach {
            it.beginShutdown()
        }

        var waitTime = calcShutdownElapsedTime(start)

        jobs.forEach {
            while (it.running && it.shutdownGracefully && it.shutdownGracefullyMaxWaitTime > waitTime) {
                LOG.warn(Markers.append("jobName", it.jobName), "JobManager#onApplicationShutdown")
                Thread.sleep(shoutDownPoolingInterval)

                waitTime = calcShutdownElapsedTime(start)
            }
        }

        jobs.forEach {
            val markers = Markers.append("jobName", it.jobName)
            if (it.running && it.shutdownGracefully && waitTime >= it.shutdownGracefullyMaxWaitTime) {
                LOG.error(markers, "JobManager#onApplicationShutdown")
            } else {
                LOG.info(markers, "JobManager#onApplicationShutdown")
            }
            it.unlock()
        }
    }

    private fun calcShutdownElapsedTime(start: Instant): Long {
        val now = BrazilZonedDateTimeSupplier.getZonedDateTime().toInstant()
        return Duration.between(start, now).toMinutes()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(JobManager::class.java)
    }
}

data class Job(
    val name: String,
    val crons: List<String>,
    val fixedDelay: Duration?,
    val running: Boolean,
    val lastStartTime: ZonedDateTime?,
    val lastElapsedMinutes: Long?,
    val shouldLock: Boolean,
    val lockAtLeastFor: Duration,
    val lockAtMostFor: Duration,
    val shutdownGracefully: Boolean,
    val shutdownGracefullyMaxWaitTime: Int,
)