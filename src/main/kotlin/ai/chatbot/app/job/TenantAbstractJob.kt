package ai.chatbot.app.job

import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.billpayment.app.job.AbstractJob
import ai.chatbot.billpayment.app.job.originalSimpleName
import jakarta.inject.Inject
import java.time.Duration

abstract class TenantAbstractJob(
    cron: String,
    fixedDelay: Duration? = null,
    lockAtLeastFor: String? = null,
    lockAtMostFor: String? = null,
    shutdownGracefully: Boolean = true,
    shutdownGracefullyMaxWaitTime: Int = 2,
    shouldLock: Boolean = true,
) : AbstractJob(
    listOf(cron),
    fixedDelay,
    lockAtLeastFor,
    lockAtMostFor,
    shutdownGracefully,
    shutdownGracefullyMaxWaitTime,
    shouldLock,
) {
    @Inject
    private lateinit var tenantPropagator: TenantPropagator

    @Inject
    private lateinit var tenantConfiguration: TenantConfiguration

    override val jobName: String by lazy { "${tenantConfiguration.tenantName}_${this.javaClass.originalSimpleName()}" }

    override fun run() {
        tenantPropagator.executeWithTenant(tenantConfiguration.tenantName) {
            super.run()
        }
    }
}