package ai.chatbot.app.job

import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.DailyLogSummaryRepository
import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.HistoryStateRepository
import ai.chatbot.app.classification.ClassificationResult
import ai.chatbot.app.classification.ConversationAction
import ai.chatbot.app.classification.ConversationActionStatus
import ai.chatbot.app.classification.ConversationTag
import ai.chatbot.app.classification.ConversationTagType
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistory
import ai.chatbot.app.conversation.HistoryStateType
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.file.ObjectRepository
import ai.chatbot.app.file.StoredObject
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.formatWithBrazilTimeZone
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.brazilTimeZone
import ai.chatbot.app.utils.calculateTagTable
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.dateTimeFormat
import ai.chatbot.app.utils.getObjectMapper
import ai.chatbot.app.utils.maskConversationMessage
import com.fasterxml.jackson.module.kotlin.readValue
import io.micronaut.context.annotation.Property
import io.micronaut.http.MediaType
import io.via1.communicationcentre.app.integrations.EmailSenderService
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class ChatBotDailyLogService(
    private val historyDbRepository: HistoryRepository,
    private val historyStateRepository: HistoryStateRepository,
    private val emailSenderService: EmailSenderService,
    private val openAIAdapter: OpenAIAdapter,
    private val dailyLogSummaryRepository: DailyLogSummaryRepository,
    private val objectRepository: ObjectRepository,
    @Property(name = "aws.region") private val awsRegion: String,
    private val tenantService: TenantService,
) {
    fun conversationsReportByDate(date: LocalDate, forceClassify: Boolean = false) {
        val logs = historyDbRepository.findByDate(date)

        val filteredMessages = logs.filter { it.messages.any { message -> message.type == MessageType.USER } }

        val executions = mutableListOf<DailyLogExecution>()

        filteredMessages.forEach { conversation ->
            try {
                val classification = processConversation(
                    date = date,
                    conversation = conversation,
                    forceClassify = forceClassify,
                    shouldSend = true,
                ).classification

                executions += DailyLogExecution(history = conversation, result = classification)

                logConversationTimeTaken(conversation, classification)

                LOG.info(
                    Markers.append("userId", conversation.userId.value)
                        .andAppend("historySize", conversation.messages.size)
                        .andAppend("tags", classification.tags)
                        .andAppend("date", date.format(dateFormat)),
                    "ChatBotDailyLogService/conversationLogClassification",
                )
            } catch (e: Exception) {
                LOG.error("ChatBotDailyLogService/errorAnalyzingLog", e)
            }
        }

        logTags(date, executions.map { it.result })
        LOG.info(Markers.append("date", date), "ChatBotDailyLogService#conversationsReportByDate")

        if (tenantService.getConfiguration().flags.dailyLogS3.check()) {
            saveResult(executions, date)
        }

        try {
            val summary = generateSummary(date, executions)
            dailyLogSummaryRepository.save(summary)

            val summaryResult = openAIAdapter.createSummaryCompletion(listOf(summary), true)

            LOG.info(Markers.append("startDate", date).andAppend("endDate", date).andAppend("summary", summaryResult), "ChatBotDailyLogService/createSummary")

            sendSummary(summaryResult.summaryResult, date, date, summary.totalConversations, summary.totalActions)
        } catch (e: Exception) {
            LOG.error("ChatBotDailyLogService/errorCreatingSummary", e)
        }
    }

    fun sendSummaryByDateRange(startDate: LocalDate, endDate: LocalDate) {
        val markers = Markers.append("startDate", startDate).andAppend("endDate", endDate)

        try {
            val summaries = dailyLogSummaryRepository.find(startDate, endDate)

            val summaryResult = openAIAdapter.createSummaryCompletion(summaries, true)

            LOG.info(markers.andAppend("summary", summaryResult), "ChatBotDailyLogService/createSummary")

            val totalConversations = summaries.sumOf { it.totalConversations }
            val totalActions = summaries.sumOf { it.totalActions }
            sendSummary(summaryResult.summaryResult, startDate, endDate, totalConversations, totalActions)
        } catch (e: Exception) {
            LOG.error(markers, "ChatBotDailyLogService/errorCreatingSummary", e)
        }
    }

    private fun processConversation(
        date: LocalDate,
        conversation: ConversationHistory,
        prefix: String? = null,
        forceClassify: Boolean = false,
        shouldSend: Boolean = false,
    ): ProcessConversationResult {
        val state = findState(conversation.userId, date)

        val classification = if (forceClassify || conversation.classification == null) {
            val completion = openAIAdapter.createLogCompletion(conversation.messages, state = state)
            historyDbRepository.saveClassification(conversation.userId, date, completion.classificationResult)
            completion.classificationResult
        } else {
            conversation.classification
        }

        val reactions = conversation.messages.filter { it.type == MessageType.REACTION }.mapNotNull { it.message }
        val groups = findUserGroups(state)

        val (subject, body) = buildConversationLog(conversation, date, classification, reactions, groups, prefix = prefix)

        if (shouldSend) {
            sendConversationLog(subject, body)
        }

        return ProcessConversationResult(classification, subject, body)
    }

    fun processConversation(
        date: LocalDate,
        userId: UserId,
        prefix: String? = null,
        forceClassify: Boolean = false,
        shouldSend: Boolean = false,
    ): ProcessConversationResult {
        val history = historyDbRepository.find(userId, date, HistoryStateType.BILLS_COMING_DUE)
        return processConversation(date, history, prefix, forceClassify, shouldSend)
    }

    fun processQuery(
        query: DailyLogQuery,
        prefix: String? = null,
        forceClassify: Boolean = false,
        shouldSend: Boolean = false,
    ): List<ProcessConversationResult> {
        val histories = historyDbRepository.findByDate(query.date)

        val results = histories.filter { it.classification != null }
            .map { it to processConversation(query.date, it, prefix, forceClassify, shouldSend) }
            .filter { (history, result) ->
                query.check(DailyLogExecution(history = history, result = result.classification))
            }
            .map { (history, result) -> result.copy(subject = "${history.userId.value} | ${result.subject}") }

        return if (query.limit == null) { results } else { results.take(query.limit) }
    }

    private fun findState(userId: UserId, date: LocalDate): BillComingDueHistoryState {
        return when (val state = historyStateRepository.findByDate(userId, date)) {
            is BillComingDueHistoryState -> state
        }
    }

    private fun findUserGroups(state: BillComingDueHistoryState): List<String> {
        return state.user.accountGroups.filter { it in listOf("ALPHA", "BETA") } + listOfNotNull(if (state.walletWithBills.hasSweepingAccount) "SWEEPING_ACCOUNT" else null)
    }

    private fun generateSummary(date: LocalDate, dailyLogs: List<DailyLogExecution>): DailyLogErrorSummary {
        val errorActions = dailyLogs
            .flatMap { it.result.acoes }
            .filter { it.status in listOf(ConversationActionStatus.FAILED, ConversationActionStatus.NOT_SUPPORTED) }

        return DailyLogErrorSummary(
            date = date,
            actions = errorActions,
            totalConversations = dailyLogs.size,
            totalActions = dailyLogs.sumOf { it.result.acoes.size },
        )
    }

    private fun logConversationTimeTaken(
        conversationHistory: ConversationHistory,
        classification: ClassificationResult,
    ) {
        val conversationTimeTaken = getConversationTimeTaken(conversationHistory, classification)
        conversationTimeTaken.forEach {
            LOG.info(
                Markers.append("userId", it.userId)
                    .andAppend("historyDate", it.historyDate)
                    .andAppend("userMessage", it.userMessage)
                    .andAppend("userMessageDate", it.userMessageDate)
                    .andAppend("userMessageAction", it.userMessageAction)
                    .andAppend("assistantMessage", it.assistantMessage)
                    .andAppend("assistantMessageDate", it.assistantMessageDate)
                    .andAppend("assistantMessageAction", it.assistantMessageAction)
                    .andAppend("timeBetweenMessages", it.timeBetweenMessages)
                    .andAppend("classification", it.classification),
                "ChatBotDailyLogService#logConversationTimeTaken",
            )
        }
    }

    fun logTags(
        date: LocalDate,
        classifications: List<ClassificationResult>,
    ): Map<ConversationTag, Int> {
        val fullCount = ConversationTag.values().associateWith { tag ->
            // Quantidade de vezes que cada tag apareceu junto dessa tag
            val countTable = calculateTagTable(tag, classifications.map { it.tags })

            // A quantidade de vezes que a tag apareceu com ela mesma = a quantidade de vezes que a tag apareceu nas conversas ;)
            val count = countTable[tag] ?: 0

            LOG.info(
                Markers.append("date", date.format(dateFormat))
                    .andAppend("tagType", tag.type.name)
                    .andAppend("tag", tag.name)
                    .andAppend("count", count)
                    .andAppend("totalConversations", classifications.size)
                    .andAppend("countTable", countTable),
                "ChatBotDailyLogService#logTags",
            )

            count
        }

        return fullCount
    }

    data class ConversationTimeTakenTO(
        val userId: String,
        val historyDate: String,
        val userMessage: String?,
        val userMessageDate: String,
        val userMessageAction: String?,
        val assistantMessage: String?,
        val assistantMessageDate: String,
        val assistantMessageAction: String?,
        val timeBetweenMessages: Long,
        val classification: String,
    )

    fun getConversationTimeTaken(
        conversationHistory: ConversationHistory,
        classification: ClassificationResult,
    ): List<ConversationTimeTakenTO> {
        if (ConversationTag.SPAM in classification.tags) {
            return emptyList()
        }

        val userId = conversationHistory.userId.value
        val historyDate = formatWithBrazilTimeZone(conversationHistory.createdAt)

        val filteredConversation = conversationHistory.messages.filter { chatMessageWrapper -> chatMessageWrapper.message != null }

        return filteredConversation.mapIndexedNotNull { index, userMessage ->
            if (userMessage.type == MessageType.USER) {
                val assistantMessage = filteredConversation.getOrNull(index + 1)

                if (assistantMessage != null && assistantMessage.type == MessageType.ASSISTANT) {
                    if (assistantMessage.timestamp != null && userMessage.timestamp != null) {
                        return@mapIndexedNotNull ConversationTimeTakenTO(
                            userId = userId,
                            historyDate = historyDate,
                            userMessage = userMessage.message,
                            userMessageDate = formatDateTime(userMessage.timestamp),
                            userMessageAction = userMessage.completionMessage?.acoes?.firstOrNull()?.name?.name,
                            assistantMessage = assistantMessage.message,
                            assistantMessageDate = formatDateTime(assistantMessage.timestamp),
                            assistantMessageAction = assistantMessage.completionMessage?.acoes?.firstOrNull()?.name?.name,
                            timeBetweenMessages = assistantMessage.timestamp.toEpochSecond() - userMessage.timestamp.toEpochSecond(),
                            classification = classification.tags.joinToString(","),
                        )
                    }
                }
            }

            return@mapIndexedNotNull null
        }
    }

    private fun buildConversationLog(
        it: ConversationHistory,
        date: LocalDate,
        classification: ClassificationResult? = null,
        reactions: List<String> = emptyList(),
        groups: List<String> = emptyList(),
        prefix: String? = null,
    ): Pair<String, String> {
        try {
            val messages =
                it.messages.filter { chatMessageWrapper -> chatMessageWrapper.message != null }.mapIndexed { index, message ->
                    val background = if (index % 2 == 0) "background-color: #f2f2f2;" else ""
                    """<tr style="$background">
                    <td style="border: 1px solid #000; padding: 8px;">${formatDateTime(message.timestamp!!)}</td>
                    <td style="border: 1px solid #000; padding: 8px;">${message.type}</td>
                    <td style="border: 1px solid #000; padding: 8px;">${maskConversationMessage(message.message)}</td>
                </tr>"""
                }.joinToString("")

            val actions = classification?.acoes?.joinToString("\n") {
                "<p>${it.fonte.emoji} ${it.acao}: ${it.status.emoji} ${it.entendimento}<p>"
            } ?: ""

            val reactionsSubject = if (reactions.isNotEmpty()) "(${reactions.joinToString(" / ")})" else ""

            val score = classification?.tags
                ?.filter { it.type == ConversationTagType.SCORE }
                ?.joinToString { it.emoji }
                ?.let { if (it.isNotEmpty()) "[$it]" else null }

            val tags = classification?.tags
                ?.filter { it.type != ConversationTagType.SCORE }
                ?.joinToString(" | ") { "${it.emoji} ${it.name}" } ?: "-"

            val subject = "${listOfNotNull(prefix, score, tags).joinToString(" ")} $reactionsSubject"

            val template =
                """
                |<html>
                |    <body>
                |        <h2>Chat Log</h2>
                |        <p>Usuário: ${it.userId.value}</p>
                |        <p>Grupos: ${groups.joinToString(", ")}</p>
                |        <p>Tags: $subject</p>
                |        <h3>Conversa</h3>
                |        <table style="width: 100%; border-collapse: collapse; border: 1px solid #000;">
                |            <tr>
                |                <th style="border: 1px solid #000; padding: 8px; text-align: left;">Horário</th>
                |                <th style="border: 1px solid #000; padding: 8px; text-align: left;">Tipo</th>
                |                <th style="border: 1px solid #000; padding: 8px; text-align: left;">Mensagem</th>
                |            </tr>
                |            $messages
                |        </table>
                |        <h3>Ações</h3>
                |        $actions
                |    </body>
                |</html>
                """.trimMargin()

            return subject to template
        } catch (e: Exception) {
            LOG.error(Markers.append("errorMessage", e.message), "ChatBotDailyLogService", e)
            throw e
        }
    }

    private fun sendConversationLog(
        subject: String,
        template: String,
    ) {
        try {
            val email = tenantService.getConfiguration().communicationCentre.email.dailyLogAiEmail

            emailSenderService.sendRawEmail(
                tenantService.getConfiguration().communicationCentre.email.notification.email,
                subject,
                template,
                email,
                emptyList(),
                MediaType.TEXT_HTML,
            )
        } catch (e: Exception) {
            LOG.error(Markers.append("errorMessage", e.message), "ChatBotDailyLogService", e)
        }
    }

    private fun sendSummary(summary: OpenAIAdapter.SummaryResult, fromDate: LocalDate, toDate: LocalDate, totalConversations: Int, totalActions: Int) {
        try {
            val count = "${ConversationActionStatus.FAILED.emoji} ${summary.naoAtendidas.size} / ${ConversationActionStatus.NOT_SUPPORTED.emoji} ${summary.naoImplementadas.size}"

            val subject = if (fromDate == toDate) {
                "Resumo ${fromDate.format(dateFormat)} ($count)"
            } else {
                "Resumo ${fromDate.format(dateFormat)} - ${toDate.format(dateFormat)} ($count)"
            }

            val heading = if (fromDate == toDate) {
                "Resumo de ${fromDate.format(dateFormat)}"
            } else {
                "Resumo de ${fromDate.format(dateFormat)} a ${toDate.format(dateFormat)}"
            }

            val failures = summary.naoAtendidas.joinToString { "<li>$it</li>" }
            val notImplemented = summary.naoImplementadas.joinToString { "<li>$it</li>" }

            val template =
                """
                |<html>
                |    <body>
                |        <h2>$heading</h2>
                |        <p>$totalActions ações em $totalConversations conversas</p>
                |        <h3>${ConversationActionStatus.FAILED.emoji} Falhas</h3>
                |        <ul>
                |           $failures
                |        </ul>
                |        <ṕ>${summary.entendimentoNaoAtendidas}</p>
                |        <h3>${ConversationActionStatus.NOT_SUPPORTED.emoji} Features não implementadas</h3>
                |        <ul>
                |           $notImplemented
                |        </ul>
                |        <ṕ>${summary.entendimentoNaoImplementadas}</p>
                |    </body>
                |</html>
                """.trimMargin()

            val email = tenantService.getConfiguration().communicationCentre.email.dailyLogAiEmail

            emailSenderService.sendRawEmail(
                tenantService.getConfiguration().communicationCentre.email.notification.email,
                subject,
                template,
                email,
                emptyList(),
                MediaType.TEXT_HTML,
            )
        } catch (e: Exception) {
            LOG.error(Markers.append("errorMessage", e.message), "ChatBotDailyLogService/sendSummary", e)
        }
    }

    private fun saveResult(executions: List<DailyLogExecution>, date: LocalDate) {
        try {
            val content = executions.map {
                DailyLogResultTO(
                    userId = it.history.userId.value,
                    date = date.format(dateFormat),
                    tenant = tenantService.getConfiguration().dailyLog.tenant,
                    score = it.result.tags.firstOrNull { tag -> tag.type == ConversationTagType.SCORE }?.name,
                    features = it.result.tags.filter { tag -> tag.type == ConversationTagType.FEATURE }.map { tag -> tag.name },
                    tags = it.result.tags.filter { tag -> tag.type == ConversationTagType.OTHER }.map { tag -> tag.name },
                )
            }.joinToString("\n") {
                getObjectMapper().writeValueAsString(it)
            }

            val datePath = date.format(DateTimeFormatter.ofPattern("yyyy/MM/dd/yyyy-MM-dd"))

            // "assunto/YYYY/MM/DD/YYYY-MM-DD/dados/"
            objectRepository.putObject(
                storedObject = StoredObject(
                    bucket = tenantService.getConfiguration().dailyLog.bucket,
                    key = "chatbot-classification/$datePath/dados/${tenantService.getConfiguration().dailyLog.tenant}",
                    region = awsRegion,
                ),
                fileData = content.toByteArray(),
                mediaType = "text/plain",
            )
        } catch (e: Exception) {
            LOG.error(Markers.append("executions", executions.size), "ChatBotDailyLogService/errorStoringResults", e)
        }
    }

    data class ProcessConversationResult(
        val classification: ClassificationResult,
        val subject: String,
        val emailBody: String,
    )

    data class DailyLogExecution(
        val history: ConversationHistory,
        val result: ClassificationResult,
    )

    private data class DailyLogResultTO(
        val date: String,
        val tenant: String,
        val userId: String,
        val score: String?,
        val features: List<String>,
        val tags: List<String>,
    )

    companion object {
        private val LOG = LoggerFactory.getLogger(ChatBotDailyLogService::class.java)
    }
}

private fun formatDateTime(timestamp: ZonedDateTime): String =
    LocalDateTime.parse(timestamp.format(dateTimeFormat), dateTimeFormat).atZone(ZoneId.of("UTC")).format(dateTimeFormat.withZone(brazilTimeZone))

data class DailyLogErrorSummary(
    val actions: List<ConversationAction>,
    val date: LocalDate = LocalDate.now(),
    val totalConversations: Int,
    val totalActions: Int,
    val version: String = "1.0.0",
)

data class DailyLogQuery(
    val date: LocalDate,
    val limit: Int? = null,
    val queries: List<List<Rule>>,
) {

    fun check(execution: ChatBotDailyLogService.DailyLogExecution): Boolean {
        return queries.any { rules ->
            rules.all { it.check(execution) }
        }
    }

    data class Rule(
        val tag: String? = null,
        val text: String? = null,
        val not: Map<String, Any>? = null,
    ) {
        private val parsedNot: Rule? = not?.let { parse(it) }

        fun check(execution: ChatBotDailyLogService.DailyLogExecution): Boolean {
            return (tag?.let { execution.result.tags.map { it.name }.contains(tag.uppercase()) } ?: true) &&
                (text?.let { execution.history.messages.any { it.message?.lowercase()?.contains(text.lowercase()) ?: false } } ?: true) &&
                (parsedNot?.let { !it.check(execution) } ?: true)
        }

        companion object {
            fun parse(json: Map<String, Any>): Rule = parse(getObjectMapper().writeValueAsString(json))
            fun parse(json: String): Rule = getObjectMapper().readValue<Rule>(json)
        }
    }
}

/*
{
    "rules": [ { "text": "..." }, {"tag": "..."}, {"not": {"tag", "..."}}],
}
 */