package ai.chatbot.app.notification

import ai.chatbot.app.MePoupe
import ai.chatbot.app.utils.brazilDateFormat
import ai.chatbot.app.utils.formattedAmount
import ai.chatbot.app.utils.maskDocumentSpecialAsterisk
import jakarta.inject.Singleton
import java.math.RoundingMode
import java.time.LocalDate

@MePoupe
@Singleton
open class MePoupeNotificationContextTemplatesService : NotificationContextTemplatesService {
    override fun getBillsComingDueLegacy(
        userName: String,
        billsContent: List<String>,
    ) =
        """
            |🔔 O-lá $userName, olha o balangodango da Na_th tocando pra lembrar que você tem contas vencendo hoje:
            |
            |Durante a minha vigília, não vamos deixar nenhuma conta vencer em seu nome! 🫡
            |
            |Veja aqui quais são 👇
            |${billsContent.joinToString(separator = "\n")}"
        """.trimMargin()

    override fun getBillsComingDueSweepingAccount(userName: String, billsContent: List<String>) =
        if (billsContent.size > 1) {
            """
                |🔔 O-lá $userName, olha o balangodango da Na_th tocando pra lembrar que você tem ${billsContent.size} contas vencendo hoje:
                |
                |Durante a minha vigília, não vamos deixar nenhuma conta vencer em seu nome! 🫡
                |
                |Bora fazer estes pagamentos por aqui?
            """.trimMargin()
        } else {
            """
                |Oi $userName. Vi que você tem o seguinte pagamento vencendo hoje:
                |
                |${billsContent.joinToString(separator = "\n")}
                |
                |Bora fazer este pagamento por aqui?
            """.trimMargin()
        }

    override fun getBillsComingDue(userName: String, billsContent: List<FormattedBillInfo>): String {
        return if (billsContent.size > 10) {
            """
                |🔔 O-lá $userName, olha o balangodango da Na_th tocando pra lembrar que você tem ${billsContent.size} contas vencendo hoje:
                |
                |Durante a minha vigília, não vamos deixar nenhuma conta vencer em seu nome! 🫡
                |
                |Bora fazer estes pagamentos por aqui?
            """.trimMargin()
        } else {
            """
            |Oi $userName. Vi que você tem os seguintes pagamentos vencendo hoje:
            |
            |${billsContent.mapIndexed { i, bill -> "${i + 1}. ${bill.description} no valor de ${bill.amount}"}.joinToString("\n")}
            |
            |Você quer fazer estes pagamentos?
            """.trimMargin()
        }
    }

    override fun getBillsComingUserRequested(
        billsContent: List<String>,
    ): List<String> {
        val fullListResponse =
            """
            |Aqui estão suas contas em aberto:
            |
            |${billsContent.joinToString(separator = "\n\n")}
            |
            |Você quer fazer estes pagamentos?
            """.trimMargin()

        if (fullListResponse.length < 1024) {
            return listOf(fullListResponse)
        }

        val billsGroups = billsContent.chunked((fullListResponse.length / 1000.0).toBigDecimal().setScale(0, RoundingMode.CEILING).toInt())

        return billsGroups.mapIndexed { index, billsGroup ->

            if (index == 0) {
                """
                    |Aqui estão suas contas em aberto:
                    |
                    |${billsGroup.joinToString(separator = "\n\n")}
                    |
                    |Continua...
                """.trimMargin()
            } else if (index == billsGroups.lastIndex) {
                """
                    |${billsGroup.joinToString(separator = "\n\n")}
                    |
                    |Você quer fazer estes pagamentos?
                """.trimMargin()
            } else {
                """
                    |${billsGroup.joinToString(separator = "\n\n")}
                    |
                    |Continua...
                """.trimMargin()
            }
        }
    }

    override fun getBillsComingUserRequestedSingular(
        billsContent: List<String>,
    ) = """
        |Você tem a seguinte conta em aberto:
        |
        |${billsContent.joinToString(separator = "\n\n")}
        |
        |Você quer fazer estes pagamentos?
    """.trimMargin()

    override fun getSweepingAccountBillsScheduleConfirmation(billsContent: List<String>): String =
        """
        |Estes são os pagamentos selecionados:
        |
        |${billsContent.joinToString(separator = "\n\n")}
        |
        |Posso prosseguir?
        """.trimMargin()

    override fun getSweepingAccountWarnConfirmation(): String =
        """
        |Vi que você não tem saldo na Me Poupe!. Posso usar o saldo da sua conta conectada?
        
        |Se essa conta também estiver sem saldo, a operação pode usar seu limite de crédito.
        """.trimIndent()

    override fun getUserConfirmationNotificationSweepingMessage(userName: String, amount: Long, key: String, recipientName: String, recipientDocument: String, recipientInstitution: String): String {
        return """
                |Você confirma os dados abaixo?
                |👤Nome: $recipientName
                |🪪Documento: ${maskDocumentSpecialAsterisk(recipientDocument)}
                |🏦Instituição: $recipientInstitution
                |💵Valor: ${amount.formattedAmount()}
                |🔑Chave: $key
                |
                |Esta transferência usará saldo da sua conta conectada
        """.trimMargin()
    }

    override fun getPixAuthorizationNotificationSweepingMessage(userName: String, amount: Long, key: String, recipientName: String, recipientDocument: String, recipientInstitution: String): String {
        return """
                |O valor do Pix excede o seu limite transacional por WhatsApp, mas você pode autorizar o pagamento se os dados estiverem corretos:
                |👤Nome: $recipientName
                |🪪Documento: ${maskDocumentSpecialAsterisk(recipientDocument)}
                |🏦Instituição: $recipientInstitution
                |💵Valor: ${amount.formattedAmount()}
                |🔑Chave: $key
                |
                |Esta transferência usará saldo da sua conta conectada
        """.trimMargin()
    }

    override fun getWelcomeAccountCreatedMessage() =
        """
            |🙂👋O-lá!
            |
            |Eu sou a Na_th, a *copilota da sua vida financeira*.
            |
            |Olha só o que faço por você:
            |
            |👀 Busco automaticamente qualquer tipo de conta em seu nome.
            |
            |🔔 Aviso pelo WhatsApp sobre o vencimento de todas as suas contas! Adeus pagamento de multas por atraso.
            |
            |✅ Você autoriza e eu pago as contas com 1 único Pix. É mais rápido que dizer _o rato roeu a roupa do..._ pronto, paguei!
            |
            |✉️ Você escolhe o envelope de gastos e eu organizo sua vida financeira no único app que tira de você o trabalho de pagar contas.
            |
            |Para experimentar meus poderes de copilota financeira, é só criar a sua senha lá no app.
            |
            |E se tiver alguma dúvida, é só me perguntar. Estou aqui para conversar com você. 💜
        """.trimMargin()

    override fun getBillComingDueLastWarn(
        userName: String,
        paymentLimitTime: String,
    ) =
        """
            |Oi *$userName*, aqui é a Na_th.
            |
            |⏰ Tem conta *vencendo hoje às $paymentLimitTime*.
            |
            |Pague agora para não perder o vencimento."
        """.trimMargin()

    override fun getMarkAsPaidConfirmation(billsContent: List<String>) = // TODO - está igual ao da Friday
        if (billsContent.size == 1) {
            """
            |Entendi, você deseja informar que efetuou o pagamento da conta a seguir em outra instituição. Posso marcar ela como paga?
            |
            ${billsContent.joinToString(separator = "\n")}
            """
        } else {
            """
            |Entendi, você deseja informar que efetuou o pagamento das contas a seguir em outra instituição. Posso marcar elas como pagas?
            |
            ${billsContent.joinToString(separator = "\n")}
            """
        }.trimMargin()

    override fun getMarkAsPaidAndIgnoreConfirmation(
// TODO - está igual ao da Friday
        billsContentMarkAsPaid: List<String>,
        billsContentIgnore: List<String>,
    ) =
        """
        |Entendi, você deseja informar que efetuou o pagamento das contas a seguir em outra instituição:
        |${billsContentMarkAsPaid.joinToString(separator = "\n")}
        |
        |E deseja ignorar as contas a seguir:
        |${billsContentIgnore.joinToString(separator = "\n")}
        |
        |É isso mesmo? Posso prosseguir?
        """.trimMargin()

    override fun getIgnoreBillsConfirmation(billsContent: List<String>) = // TODO - está igual ao da Friday. Troquei apenas o nome
        if (billsContent.size == 1) {
            """
            |Entendi, você deseja ignorar a seguinte conta:
            |
            |${billsContent.joinToString(separator = "\n")}
            |
            |Ela será removida da sua timeline, se desejar retornar esse pagamento ele estará disponível na aba Removidos do App Me Poupe.
            |Posso prosseguir?
            """
        } else {
            """
            |Entendi, você deseja ignorar as seguintes contas:
            |
            |${billsContent.joinToString(separator = "\n")}
            |
            |Elas serão removidas da sua timeline, se desejar retornar esses pagamentos eles estarão disponíveis na aba Removidos do App Me Poupe.
            |Posso prosseguir?
            """
        }.trimMargin()

    override fun getSufficientBalance() =
        """
        |Ops, vi que você tem saldo na sua carteira Me Poupe! Por motivos de segurança, só autorizo pagamentos utilizando o saldo da carteira pelo aplicativo, ok? 🤜🤛
        """.trimMargin()

    override fun getAddBoletoConcessionariaMessage(amount: Long, assignor: String, dueDate: LocalDate?) =
        """
            |🧾Conta: $assignor
            |💰 Valor: ${amount.formattedAmount()} 
            |${dueDate?.let { date -> "📆 Vencimento: ${date.format(brazilDateFormat)}" } ?: ""}
            |
            |O que você deseja fazer?
        """.trimMargin()

    override fun addMultipleBoletoSuccessMessage() = "Beleza! Vou adicionar os boletos e em instantes eles estarão na sua agenda."

    override fun getAddBoletoMessage(amount: Long, assignor: String, dueDate: LocalDate): String {
        return """
            |🧾 Conta: $assignor
            |💰 Valor: ${amount.formattedAmount()}
            |📅 Vencimento: ${dueDate.format(brazilDateFormat)}
            |
            |Você quer incluir este boleto na sua agenda de pagamentos? 
        """.trimMargin()
    }

    override fun getOutdatedAction() =
        """
        |Botões e ações sobre notificações só são validos no mesmo dia em que a notificação é enviada. Você pode ver quais são as opções disponíveis para contas de dias anteriores no App.
        """.trimMargin()

    override fun getUserConfirmationNotificationMessage(
        userName: String,
        amount: Long,
        key: String,
        recipientName: String,
        recipientDocument: String,
        recipientInstitution: String,
    ): String {
        return """
                |Você confirma os dados abaixo?
                |👤Nome: $recipientName
                |🪪Documento: ${maskDocumentSpecialAsterisk(recipientDocument)}
                |🏦Instituição: $recipientInstitution
                |💵Valor: ${amount.formattedAmount()}
                |🔑Chave: $key
        """.trimMargin()
    }

    override fun getPixAuthorizationNotificationMessage(
        userName: String,
        amount: Long,
        key: String,
        recipientName: String,
        recipientDocument: String,
        recipientInstitution: String,
    ): String {
        return """
                |O valor do Pix excede o seu limite transacional por WhatsApp, mas você pode autorizar o pagamento se os dados estiverem corretos:
                |👤Nome: $recipientName
                |🪪Documento: ${maskDocumentSpecialAsterisk(recipientDocument)}
                |🏦Instituição: $recipientInstitution
                |💵Valor: ${amount.formattedAmount()}
                |🔑Chave: $key
        """.trimMargin()
    }

    override fun getOutdatedPendingBillsNotificationMessage(
        billsContent: List<String>,
    ): String {
        val s = if (billsContent.size > 1) "s" else ""

        return """
        |${billsContent.joinToString(separator = "\n\n")}
        |
        |*A lista de contas mudou desde a nossa ultima mensagem*. Por isso, estamos confirmando novamente. Você quer fazer este$s pagamento$s?
        """.trimMargin()
    }

    override fun getRegisterCompletedMessage(name: String) = """
        O-lá, $name. Aqui é a Na_th!

        Você acredita em amor à primeira mensagem? Ou vou ter que te escrever mais vezes? 💜

        Já salva meu contato porque serei sua copilota financeira e, daqui pra frente, vamos conversar por aqui.

        Enquanto te escrevo minha equipe está analisando sua conta. Assim que ela for aprovada, enviarei uma mensagem com os próximos passos.

        Ah! E sempre que quiser avaliar minhas mensagens, você pode reagir com 👍 ou 👎 para que eu fique cada vez mais inteligente.
        
        Quer tentar agora me dar um 👍 ou 👎?
    """.trimIndent()

    // FIXME: Atualizar mensagem do Me Poupe antes de liberar essa feature
    override fun getAuthorizeScheduleBillsMessage(billsContent: List<FormattedBillInfo>): String =
        """
            |Tudo pronto, mas a soma dos pagamentos excede o limite dos pagamentos permitidos por WhatsApp.
            |
            |${billsContent.mapIndexed { i, bill -> "${i + 1}. ${bill.description} no valor de ${bill.amount}" }.joinToString("\n")}
        """.trimMargin()

    override fun getPromoteSweepingAccountMarkAsPaidMessage(): String = """
        Ainda pagando contas pelo banco? 
        Você pode conectá-lo aqui e pagar tudo só digitando “sim” no WhatsApp!
    """.trimIndent()

    override fun getPromoteSweepingAccountKnowMoreMessage(): String = """
        As transferências inteligentes entre suas contas é uma função do Open Finance para que você não se restrinja aos serviços do lugar onde está o seu dinheiro. 

        Funciona assim: você pode manter o seu dinheiro no banco que escolheu. Quando eu te mandar os lembretes de vencimento das contas, você só precisa responder “sim” para que eu traga seu dinheiro de forma automática e totalmente segura!

        Ou seja, você vai literalmente responder “sim” e ver suas contas pagas! Um trabalho a menos para os seus dias ;)
    """.trimIndent()

    override fun getPromoteSweepingAccountOptOutMessage(): String = """
        Ok, você pode conectar seu banco a qualquer momento em carteira > open finance
    """.trimIndent()

    override fun getPromoteSweepingAccountCashInMessage(): String = """
        Vi que você adicionou saldo na carteira e, primeiramente, parabéns!

        Segundamente: você pode me conectar com seu banco e não ter mais esse trabalho.
    """.trimIndent()

    override fun getPromoteSweepingAccountDDAMessage(billInfo: FormattedBillInfo): String = """
        ☑️ Conta Paga Sem Friday

        Detectamos o pagamento da conta ${billInfo.description} fora da Friday no valor de ${billInfo.amount}.
        
        Conecte a Friday com seu banco para fazer seus proximos pagamentos digitando "Sim" no Whatsapp.
    """.trimIndent()

    override fun getPaySomeReplyMessage(): String = """
        |Claro! Você pode me dizer quais das contas você gostaria de pagar?
    """.trimMargin()
}