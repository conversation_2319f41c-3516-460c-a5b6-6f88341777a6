package ai.chatbot.app.notification

import ai.chatbot.app.Friday
import jakarta.inject.Singleton

interface OnboardingSinglePixNotificationService {

    fun getSinglePixStartContextMessage(): String
    fun getSinglePixStartMessage(userName: String): String

    fun getSinglePixPaymentOfferMessage(): String
    fun getSinglePixPaymentExplanationMessage(): String
    fun getSinglePixInputtedPaymentExplanationMessage(): String
    fun getSinglePixPaymentConfirmMessage(userName: String, bills: List<String>): String
    fun getSinglePixNotFoundPixKey(): String
    fun getSinglePixNotFoundPixKeyInputted(): String
    fun getSinglePixAskForPixKey(): String
    fun getSinglePixIncorrectPixKey(): String
    fun getSinglePixOtherOwnerPixKey(): String
    fun getSinglePixAlreadyPaid(): String
    fun getSinglePixNewDayError(): String
    fun getSinglePixCallSupportError(): String

    fun getSinglePixPromoteSweepingAccountMessage(): String
    fun getSinglePixSkippedFinishedMessage(): String
    fun getSinglePixFinishedMessage(): String
    fun getSinglePixFinishedContextMessage(): String

    fun getSinglePixRemindMeLaterMessage(): String
}

@Friday
@Singleton
open class FridayOnboardingSinglePixNotificationService : OnboardingSinglePixNotificationService {
    override fun getSinglePixStartContextMessage() = """
        O usuário está realizando o fluxo de boas vindas.
    """.trimIndent()

    override fun getSinglePixStartMessage(userName: String) = """
        $userName, trago boas notícias: sua conta já está ativa e estou buscando os boletos em seu nome.
    """.trimIndent()

    override fun getSinglePixPaymentOfferMessage(): String = """
        A partir de agora, pagar contas será tão simples quanto dizer "Sim".
        
        Para você ver como é fácil, vou criar um pagamento de exemplo.
        
        Quer testar pagar com um 'sim'?
    """.trimIndent()

    override fun getSinglePixPaymentExplanationMessage(): String = """
        Funciona assim: no dia de vencimento das suas contas eu entro em contato e sempre confirmo se você quer  que eu faça os pagamentos por você.
        
        Exatamente como a mensagem abaixo:
    """.trimIndent()

    override fun getSinglePixInputtedPaymentExplanationMessage(): String = """
        Boa! Funciona assim: no dia de vencimento das suas contas eu entro em contato e sempre confirmo se você quer  que eu faça os pagamentos por você.

        Exatamente como a mensagem abaixo:
    """.trimIndent()

    override fun getSinglePixPaymentConfirmMessage(userName: String, bills: List<String>): String = """
        Oi $userName. Hoje você tem este pagamento de exemplo vencendo:

        ${bills.joinToString("\n")}
        
        Você quer que eu pague por aqui?
    """.trimIndent()

    override fun getSinglePixIncorrectPixKey(): String = """
        A chave que você digitou está incorreta. Você pode digitar novamente?
    """.trimIndent()

    override fun getSinglePixOtherOwnerPixKey(): String = """
        A chave que você digitou é de outra titularidade. Digite uma chave Pix que pertença a você, tudo bem?
    """.trimIndent()

    override fun getSinglePixNotFoundPixKey(): String = """
        Estou procurando uma chave Pix em seu nome e não encontrei.
    """.trimIndent()

    override fun getSinglePixAlreadyPaid(): String = """
        Eu já te enviei o pagamento de teste e você recebeu o comprovante por aqui. Caso não encontre, é só dar uma olhada em sua timeline do app!
    """.trimIndent()

    override fun getSinglePixNewDayError(): String = """
        Poxa! A nossa conversa foi pausada e, por isso, não consigo te mostrar como o Pix de teste funciona por aqui. Mas você pode acessar o App e terminar o pagamento por lá!
    """.trimIndent()

    override fun getSinglePixCallSupportError(): String = """
        Poxa! Eu não estou conseguindo fazer o Pix de teste e peço desculpas por isso. Se quiser testar de outra forma, entre em contato com o atendimento. Meus colegas humanos respondem super rápido!
    """.trimIndent()

    override fun getSinglePixNotFoundPixKeyInputted(): String = """
        Poxa, não consegui localizar a chave que você digitou. Mas você pode experimentar o App conectando as suas próprias contas:

        - Água, luz, telefone (contas de consumo)

        - Pix recorrentes (diarista, escolinhas, terapia, etc)

        - Contas de outras pessoas da família
        
        Quer adicionar mais contas para pagar com um ‘sim’?
    """.trimIndent()

    override fun getSinglePixAskForPixKey(): String = """
        Você pode digitar a sua chave Pix?
    """.trimIndent()

    override fun getSinglePixRemindMeLaterMessage(): String = """
    Ok!
    
    No dia de vencimento das suas contas eu entro em contato e sempre confirmo se você quer  que eu faça os pagamentos por você.
    
    Se você disser ‘sim’, eu faço os pagamentos em segundos e te envio os comprovantes. 
    """.trimIndent()

    override fun getSinglePixPromoteSweepingAccountMessage() = """
        Prontinho! O comprovante já chegou por aqui ou vai chegar em instantes.

        Agora, conecte a Friday com seu banco para eu trazer seu dinheiro de forma automática e segura. Você vai digitar "sim" e eu cuido de tudo.
    """.trimIndent()

    override fun getSinglePixSkippedFinishedMessage() = """
        Os boletos emitidos em seu nome (DDA) eu já busco automaticamente. 
        
        Outras contas como:
        
        - Água, luz, telefone (contas de consumo)
        
        - Pix recorrentes (diarista, escolinhas, terapia, etc)
        
        - Contas de outras pessoas da família
        
        Precisam ser adicionadas no App para eu receber mensalmente de forma automática. 
        
        Quer adicionar mais contas para pagar com um ‘sim’?
    """.trimIndent()

    override fun getSinglePixFinishedMessage() = """
       Prontinho! O comprovante já chegou por aqui ou vai chegar em instantes.
        
        A partir de agora pagar contas será tão simples quanto dizer “Sim”!
        
        Os boletos emitidos em seu nome (DDA) eu já busco automaticamente. 
        
        Outras contas como:
        
        - Água, luz, telefone (contas de consumo)
        
        - Pix recorrentes (diarista, escolinhas, terapia, etc)
        
        - Contas de outras pessoas da família
        
        Precisam ser adicionadas no App para eu receber mensalmente de forma automática. 
        
        Quer adicionar mais contas para pagar com um ‘sim’?
    """.trimIndent()

    override fun getSinglePixFinishedContextMessage() = """
        📱 Experimente o App
        
        Dá uma olhada em tudo que você pode fazer pelo App:
        
        - Centralizar todos os seus pagamentos em uma timeline
        
        - Categorizar pagamentos e ver relatórios de gastos
        
        - Convidar outras pessoas para juntar contas ou fazer a gestão em conjunto
        
        - Criar outras carteiras e até abrir uma carteira PJ.
    """.trimIndent()
}