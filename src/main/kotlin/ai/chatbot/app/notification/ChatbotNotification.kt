package ai.chatbot.app.notification

import ai.chatbot.adapters.waCommCentre.ClientId
import ai.chatbot.app.user.AccountId
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import java.util.UUID

sealed class ChatbotNotification(
    open val mobilePhone: String,
    open val accountId: AccountId,
)

data class ChatbotWhatsappTemplatedNotification(
    val notificationId: String = UUID.randomUUID().toString(),
    override val mobilePhone: String,
    override val accountId: AccountId,
    val template: NotificationTemplate,
    val configurationKey: String?,
    val parameters: List<String> = listOf(),
    val quickReplyButtonsWhatsAppParameter: List<String> = emptyList(),
    val quickRepliesStartIndex: Int? = null,
    val buttonWhatsAppParameter: ButtonWhatsAppParameter? = null,
    val media: NotificationMedia? = null,
) : ChatbotNotification(mobilePhone, accountId)

data class ChatbotRawTemplatedNotification(
    val notificationId: String = UUID.randomUUID().toString(),
    override val mobilePhone: String,
    override val accountId: AccountId,
    val clientId: ClientId,
    val configurationKey: ConfigurationKey,
    val language: String? = "pt_BR",
    val arguments: Map<String, String> = emptyMap(),
    val media: NotificationMedia? = null,
    val notificationType: NotificationType? = null,
) : ChatbotNotification(mobilePhone, accountId)

data class NotificationTemplate(val value: String)

data class ChatbotWhatsappSimpleNotification(
    override val mobilePhone: String,
    override val accountId: AccountId,
    val message: String,
    val quickReplyButtons: List<QuickReplyButton>? = null,
    val ctaLink: CTALink? = null,
    val media: NotificationMedia? = null,
) : ChatbotNotification(mobilePhone, accountId)

data class QuickReplyButton(
    val text: String,
    val payload: String,
)

data class CTALink(
    val displayText: String,
    val url: String,
) {
    fun withFullUrl(baseUrl: String): CTALink {
        if (this.url.startsWith("http", ignoreCase = true)) {
            return this
        }

        return this.copy(url = "$baseUrl/$url")
    }
}

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type",
    visible = true,
)
@JsonSubTypes(
    JsonSubTypes.Type(value = NotificationMedia.Document::class, name = "DOCUMENT"),
    JsonSubTypes.Type(value = NotificationMedia.Image::class, name = "IMAGE"),
    JsonSubTypes.Type(value = NotificationMedia.Video::class, name = "VIDEO"),
)
sealed class NotificationMedia(val type: NotificationMediaType) {
    data class Document(val url: String, val filename: String, val documentType: String?) : NotificationMedia(NotificationMediaType.DOCUMENT)
    data class Image(val url: String, val imageType: String? = null, val title: String? = null, val text: String? = null) : NotificationMedia(NotificationMediaType.IMAGE)
    data class Video(val url: String, val videoType: String?) : NotificationMedia(NotificationMediaType.VIDEO)
}

enum class NotificationMediaType {
    DOCUMENT, IMAGE, VIDEO
}