package ai.chatbot.app.notification

import ai.chatbot.app.MePoupe
import jakarta.inject.Singleton

@MePoupe
@Singleton
open class MePoupeSinglePixOnboardingNotificationService : OnboardingSinglePixNotificationService {
    override fun getSinglePixStartContextMessage() = """
        O usuário está realizando o fluxo de boas vindas.
    """.trimIndent()

    override fun getSinglePixStartMessage(userName: String) = """
        Oi $userName, bom dia.

        Eu sou a Na_th, sua copilota financeira. Faço análises das suas contas, nunca esqueço um dia de vencimento e pago tudo em dia por você. 🤓
        
        E trago boas notícias: sua conta já está ativa!
    """.trimIndent()

    override fun getSinglePixPaymentOfferMessage(): String = """
        A partir de agora, pagar contas será tão simples quanto dizer "sim" pra mim.
        
        E pra você ver como é fácil, vou criar um pagamento de exemplo.
        
        Quer testar pagar com um 'sim'?
    """.trimIndent()

    override fun getSinglePixPaymentExplanationMessage(): String = """
        Funciona assim: eu vou te chamar por aqui no dia de vencimento das suas contas e perguntar se você quer que eu faça os pagamentos. Você confirma e pronto, tudo resolvido.
        
        Exatamente como a mensagem abaixo:
    """.trimIndent()

    override fun getSinglePixInputtedPaymentExplanationMessage(): String = """
        Boa, deu certo!
        
        Funciona assim: eu vou te chamar por aqui no dia de vencimento das suas contas e perguntar se você quer que eu faça os pagamentos. Você confirma e pronto, tudo resolvido.
        
        Exatamente como a mensagem abaixo:
    """.trimIndent()

    override fun getSinglePixPaymentConfirmMessage(userName: String, bills: List<String>): String = """
        👀 Meu radar encontrou uma conta de exemplo em seu nome:

        ${bills.joinToString("\n")}

        Você quer que eu pague por aqui?
    """.trimIndent()

    override fun getSinglePixNotFoundPixKey(): String = """
        Ué, estou procurando uma chave Pix em seu nome e não encontrei.
    """.trimIndent()

    override fun getSinglePixNotFoundPixKeyInputted(): String = """
        Poxa, não consegui localizar a chave que você digitou. Mas você pode experimentar o App conectando as suas próprias contas:
        
        - Água, luz, telefone (contas de consumo)

        - Pix recorrentes (diarista, escolinhas, terapia, etc)

        - Contas de outras pessoas da família

        Você adiciona pelo App. Aí vou receber mensalmente de forma automática e aí você já sabe: diz “sim” e PA-PUM. Pago!
    """.trimIndent()

    override fun getSinglePixAskForPixKey(): String = """
        Você pode fazer a graça de digitar sua chave? Agradecida 😉
    """.trimIndent()

    override fun getSinglePixIncorrectPixKey(): String = """
        A chave que você digitou está incorreta. Pode digitar de novo?
    """.trimIndent()

    override fun getSinglePixOtherOwnerPixKey(): String = """
        A chave precisa ser sua, tá? Essa que você digitou não é de sua titularidade. Pode digitar outra?
    """.trimIndent()

    override fun getSinglePixAlreadyPaid(): String = """
        Eu já te enviei o pagamento de teste e você recebeu o comprovante por aqui. Caso não encontre, é só dar uma olhada em sua timeline do app!
    """.trimIndent()

    override fun getSinglePixNewDayError(): String = """
        Poxa! A nossa conversa foi pausada e, por isso, não consigo te mostrar como o Pix de teste funciona por aqui. Mas você pode acessar o App e terminar o pagamento por lá! 💔
    """.trimIndent()

    override fun getSinglePixCallSupportError(): String = """
        Poxa! Falhei na missão e não estou conseguindo fazer o pagamento de teste 😵

        Se quiser testar de outra forma, entre em contato com o atendimento. Meus colegas humanos respondem super rápido!
    """.trimIndent()

    override fun getSinglePixPromoteSweepingAccountMessage(): String = """
        Olha issooooooo! O comprovante já chegou por aqui ou vai chegar em instantes. E você não tem mais desculpa pra vacilar com seus pagamentos. De nada ;)))

        E você pode me conectar com seu banco. Eu trago pra cá, de forma automática e segura, somente o valor das suas contas. Vou te mandar tudo por aqui, você diz “sim” e PÁ-PUM. Pago!
    """.trimIndent()

    override fun getSinglePixSkippedFinishedMessage(): String = """
        Os boletos emitidos em seu nome (DDA) eu já busco automaticamente. 

        Agora, outras contas como:

        - Água, luz, telefone (contas de consumo)

        - Pix recorrentes (diarista, escolinhas, terapia, etc)

        - Contas de outras pessoas da família

         Você adiciona pelo App. Aí vou receber mensalmente de forma automática e aí você já sabe: diz “sim” e PA-PUM. Pago!
    """.trimIndent()

    override fun getSinglePixFinishedMessage() = """
        Olha issooooooo! O comprovante já chegou por aqui ou vai chegar em instantes. E você não tem mais desculpa pra vacilar com seus pagamentos. De nada ;)))

        Os boletos emitidos em seu nome (DDA) eu já busco automaticamente. 

        - Agora, outras contas como:

        - Água, luz, telefone (contas de consumo)

        - Pix recorrentes (diarista, escolinhas, terapia, etc)

        Contas de outras pessoas da família

        Você adiciona pelo App. Aí vou receber mensalmente de forma automática e você já sabe: diz “sim” e PA-PUM. Pago!
    """.trimIndent()

    override fun getSinglePixFinishedContextMessage() = """
        📱 Bora experimentar sua nova vida financeira?

        📆 Complete sua agenda de pagamentos

        ✉️ Use envelopes pra saber pra onde está indo seu dinheiro

        💜 Convide seu mozão pra juntar as contas e acabar com as DRs por dinheiro

        🚀 Crie metas: eu faço as previsões e deixo rendendo até 120% do CDI
    """.trimIndent()

    override fun getSinglePixRemindMeLaterMessage(): String = """
        Tá bom, quem pilota é você 😉
        
        Eu vou te chamar por aqui no dia de vencimento das suas contas e perguntar se você quer que eu faça os pagamentos. Você confirma e pronto, tudo resolvido.
    """.trimIndent()
}