package ai.chatbot.app.notification

import ai.chatbot.app.bill.BillType
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.bill.orderByExternalBillId
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.utils.brazilDateFormat
import ai.chatbot.app.utils.getEmojiForNumber
import java.text.NumberFormat
import java.util.Locale

class NotificationFormatter {
    companion object {
        fun buildFormattedAmount(amount: Long): String {
            val n = NumberFormat.getCurrencyInstance(Locale.forLanguageTag("pt-BR"))
            return n.format(amount.toDouble() / 100.0)
        }

        fun buildBillNotificationMessage(bills: List<BillView>, useEmoji: Boolean = true, includeDueDate: Boolean = false): List<String> {
            return bills.orderByExternalBillId().mapIndexed { index, value -> buildBillNotificationMessage(value, index.plus(1), useEmoji, includeDueDate) }
        }

        fun getFormattedBillInfo(bills: List<BillView>): List<FormattedBillInfo> {
            return bills.sortedBy { it.externalBillId }.map { bill ->
                val recipient =
                    if (bill.billType == BillType.CONCESSIONARIA || bill.billType == BillType.INVESTMENT) {
                        bill.assignor
                    } else {
                        bill.recipient?.name ?: ""
                    }?.trim()

                FormattedBillInfo(
                    description = if (bill.billDescription != "") "$recipient (${bill.billDescription.trim()})" else recipient ?: "",
                    amount = buildFormattedAmount(bill.amountTotal),
                )
            }
        }

        fun buildBillNotificationMessage(
            bill: BillView,
            index: Int? = null,
            useEmoji: Boolean = true,
            includeDueDate: Boolean = false,
        ): String {
            val recipient =
                if (bill.billType == BillType.CONCESSIONARIA || bill.billType == BillType.INVESTMENT) {
                    bill.assignor
                } else {
                    bill.recipient?.name ?: ""
                }?.trim()

            val billDescription = if (bill.billDescription != "") "$recipient (${bill.billDescription.trim()})" else recipient
            val billIndex = index?.toString()?.map { if (useEmoji) getEmojiForNumber(it.toString()) else "$it." }?.joinToString("") ?: ""
            val billDueDate = if (includeDueDate) "- Vencimento em ${bill.dueDate.format(brazilDateFormat)}" else ""
            return "$billIndex $billDescription no valor de ${buildFormattedAmount(bill.amountTotal)} $billDueDate".trim()
        }

        fun buildContactListMessage(
            contacts: List<Contact>,
        ): String {
            return contacts.mapIndexed { index, contact -> "${index.plus(1)} - ${contact.name}" }.joinToString(separator = "\n")
        }
    }
}

data class FormattedBillInfo(
    val description: String,
    val amount: String,
)