package ai.chatbot.app.notification

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import io.micronaut.cache.annotation.Cacheable
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

interface TemplateInfoProvider {
    fun fetch(template: String): TemplateInfo

    fun getHistoryMessage(rawNotificationMessage: ChatbotRawTemplatedNotification): String
    fun assertIfMessageContentIsValid(notification: ChatbotRawTemplatedNotification)
}

@Singleton
open class TemplateInfoService(
    private val templateInfoProvider: TemplateInfoProvider,
) {

    @Cacheable("template-info")
    open fun getInfo(template: String): TemplateInfo {
        logger.info(Markers.append("template", template), "TemplateInfoService#getInfo")
        return templateInfoProvider.fetch(template)
    }

    fun getHistoryMessage(rawNotificationMessage: ChatbotRawTemplatedNotification): String {
        return templateInfoProvider.getHistoryMessage(rawNotificationMessage)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(TemplateInfoService::class.java)
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class TemplateInfo(
    val text: String,
    val params: Int,
    val buttons: List<TemplateButton>,
    val category: String,
)

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY)
sealed class TemplateButton {

    @JsonTypeName("QuickReplyButton")
    data class QuickReply(
        val text: String,
    ) : TemplateButton()

    @JsonTypeName("LinkButton")
    data class Link(
        val text: String,
        val url: String,
    ) : TemplateButton()
}