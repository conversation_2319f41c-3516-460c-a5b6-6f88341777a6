package ai.chatbot.app.notification

import ai.chatbot.adapters.billPayment.BillStatus
import ai.chatbot.adapters.waCommCentre.WaCommCentreAdapter
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.BillType
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.bill.Recipient
import ai.chatbot.app.bill.ScheduledInfo
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.andAppend
import jakarta.inject.Singleton
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.full.memberFunctions
import kotlin.reflect.jvm.jvmErasure
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class NotificationValidator(
    private val buildNotificationService: BuildNotificationService,
    private val waCommCentreAdapter: WaCommCentreAdapter,
    private val tenantService: TenantService,
) {
    private val logger = LoggerFactory.getLogger(this::class.java.name)

    fun validateAll() {
        val methods = buildNotificationService::class.memberFunctions
        val validatedMethods = mutableSetOf<String>()
        methods
            .filter { function ->
                val returnType = function.returnType
                val returnClass = returnType.jvmErasure
                returnClass.isSubclassOf(ChatbotRawTemplatedNotification::class)
            }
            .forEach { method ->
                val accountId = AccountId("account-id-check")
                val userId = UserId("user-id-check")
                val phoneNumber = "***********"
                val user = User(
                    accountId = accountId,
                    id = userId,
                    name = "user-name-check",
                    accountGroups = listOf("account-group-check"),
                    status = AccountStatus.ACTIVE,
                    paymentStatus = AccountPaymentStatus.UpToDate,
                )
                val billView =
                    BillView(
                        billId = BillId("BILL_ID_1"),
                        billDescription = "conta da luz",
                        recipient = Recipient(name = "John Doe"),
                        amount = 4846,
                        discount = 4751,
                        interest = 2381,
                        fine = 3696,
                        amountTotal = 9697,
                        billType = BillType.FICHA_COMPENSACAO,
                        assignor = "John Doe",
                        scheduledInfo = ScheduledInfo.NOT_SCHEDULED,
                        paymentLimitTime = "20:00",
                        dueDate = getLocalDate(),
                        externalBillId = 1,
                        subscriptionFee = false,
                        status = BillStatus.ACTIVE,
                    )

                val billView2 =
                    BillView(
                        billId = BillId("BILL_ID_2"),
                        billDescription = "Pix para o enzo",
                        recipient = Recipient(name = "Enzo"),
                        amount = 70_000_00,
                        discount = 0,
                        interest = 0,
                        fine = 0,
                        amountTotal = 70_000_00,
                        billType = BillType.PIX,
                        assignor = "Enzo",
                        scheduledInfo = ScheduledInfo.NOT_SCHEDULED,
                        paymentLimitTime = "20:00",
                        dueDate = getLocalDate(),
                        externalBillId = 2,
                        subscriptionFee = false,
                        status = BillStatus.ACTIVE,
                    )
                val billviews = listOf(billView, billView2)
                val transactionId = TransactionId()
                val formattedBillsMessage = NotificationFormatter.buildBillNotificationMessage(billviews)
                val billInfo = NotificationFormatter.getFormattedBillInfo(billviews)

                with(buildNotificationService) {
                    when (method.name) {
                        "buildBillsComingDueLastWarnNotification" -> {
                            validateMessage(
                                buildBillsComingDueLastWarnNotification(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                    userName = "notification-check",
                                    paymentLimitTime = "2023-12-31T23:59:59Z",
                                ),
                            )
                            validatedMethods += method.name
                        }

                        "buildSufficientBalanceNotification" -> {
                            validateMessage(
                                buildSufficientBalanceNotification(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                ),
                            )
                        }

                        "buildOutdatedAction" -> {
                            validateMessage(
                                buildOutdatedAction(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                ),
                            )
                        }

                        "buildTokenNotification" -> {
                            validateMessage(
                                buildTokenNotification(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                    token = "token-check",
                                ),
                            )
                        }

                        "buildAddNewConnectionNotification" -> {
                            validateMessage(
                                buildAddNewConnectionNotification(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                ),
                            )
                        }

                        "buildAuthorizeScheduleBillsNotification" -> {
                            validateMessage(
                                buildAuthorizeScheduleBillsNotification(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                    bills = billviews,
                                    transactionId = transactionId,
                                ),
                            )
                        }

                        "buildOnboardingPixConfirmationNotification" -> {
                            validateMessage(
                                buildOnboardingPixConfirmationNotification(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                    userName = "user-name",
                                    formattedBillsMessage = formattedBillsMessage,
                                ),
                            )
                        }

                        "buildOnboardingSinglePixStartNotification" -> {
                            validateMessage(
                                buildOnboardingSinglePixStartNotification(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                    userName = "user-name",
                                ),
                            )
                        }

                        "buildPixAmountExceedsSweepingTransactionLimit" -> {
                            validateMessage(
                                buildPixAmountExceedsSweepingTransactionLimit(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                    transactionId = transactionId,
                                ),
                            )
                        }

                        "buildPromoteSweepingAccountCashInNotification" -> {
                            validateMessage(
                                buildPromoteSweepingAccountCashInNotification(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                ),
                            )
                        }

                        "buildPromoteSweepingAccountDDANotification" -> {
                            validateMessage(
                                buildPromoteSweepingAccountDDANotification(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                    billInfo = billInfo[0],
                                ),
                            )
                        }

                        "buildPromoteSweepingAccountMarkAsPaidNotification" -> {
                            validateMessage(
                                buildPromoteSweepingAccountMarkAsPaidNotification(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                ),
                            )
                        }

                        "buildRegisterCompletedNotification" -> {
                            validateMessage(
                                buildRegisterCompletedNotification(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                    userName = "user-name",
                                ),
                            )
                        }

                        "buildReminderResponseErrorNotification" -> {
                            validateMessage(
                                buildReminderResponseErrorNotification(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                ),
                            )
                        }

                        "buildReminderResponseSuccessNotification" -> {
                            validateMessage(
                                buildReminderResponseSuccessNotification(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                ),
                            )
                        }

                        "buildScheduledAmountExceedsSweepingTransactionLimit" -> {
                            validateMessage(
                                buildScheduledAmountExceedsSweepingTransactionLimit(
                                    accountId = accountId,
                                    mobilePhone = phoneNumber,
                                    transactionId = transactionId,
                                ),
                            )
                        }

                        else -> {
                            error("Método ${method.name} do BuildNotificationService não valida a notificação construída!")
                        }
                    }
                }
            }
    }

    fun validateMessage(notification: ChatbotRawTemplatedNotification) {
        runCatching {
            waCommCentreAdapter.assertIfMessageContentIsValid(notification)
        }.onFailure { exception ->
            logger.error(
                Markers.append("tenantName", tenantService.getTenantName())
                    .andAppend("content", notification)
                    .andAppend("errorMessage", exception.message),
                "NotificationValidator#validateMessage",
                exception,
            )
        }
    }
}