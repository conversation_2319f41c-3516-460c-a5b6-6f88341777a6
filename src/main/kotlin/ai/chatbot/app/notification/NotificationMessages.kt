package ai.chatbot.app.notification

import ai.chatbot.app.event.UserEvent
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy::class)
data class NotificationMessages(

    val onboardingPromoteSweepingAccount: TextNotificationConfig?, // Alguns não tem

    val authorizePixSweeping: TextNotificationConfig,
    val pixConfirmationSingleSweepingConsent: TextNotificationConfig,
    val pixConfirmationMultipleSweepingConsent: TextNotificationConfig,
    val pixConfirmationSelectSweepingConsent: TextNotificationConfig,
    val scheduleAuthorizationSingleSweepingConsent: TextNotificationConfig,
    val scheduleAuthorizationMultipleSweepingConsent: TextNotificationConfig,
    val scheduleAuthorizationSelectSweepingConsent: TextNotificationConfig,
    val scheduleConfirmationSingleSweepingConsent: TextNotificationConfig,
    val scheduleConfirmationMultipleSweepingConsent: TextNotificationConfig,
    val scheduleConfirmationSelectSweepingConsent: TextNotificationConfig,
    val selectSweepingAccount: TextNotificationConfig,
    val noBillsFoundForPeriod: TextNotificationConfig,

    val createManualEntryErrorNoTitle: TextNotificationConfig,
    val createManualEntryErrorNoAmount: TextNotificationConfig,
    val createManualEntryErrorNoAmountAndTitle: TextNotificationConfig,
    val createManualEntrySuccess: TextNotificationConfig,
    val removeManualEntrySuccess: TextNotificationConfig,

    val createReminderErrorNoDate: TextNotificationConfig,
    val createReminderErrorNoTitle: TextNotificationConfig,
    val createReminderErrorNoTitleAndDate: TextNotificationConfig,
    val createReminderErrorDateInPast: TextNotificationConfig,
    val createReminderSuccess: TextNotificationConfig,
    val createReminderSuccessNoAmount: TextNotificationConfig,
)

@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy::class)
data class QuickReplyConfig(
    val text: String? = null,
    val action: String? = null,
    val payload: String? = null,
    val transactionId: NotificationParam? = null,
)

@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy::class)
data class LinkConfig(
    val text: String? = null,
    val url: String,
    val event: UserEvent? = null,
)

sealed interface NotificationConfig

@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy::class)
data class TextNotificationConfig(
    val text: String, // Only for simple notifications
    val params: List<String> = emptyList(),
    val link: LinkConfig? = null,
    val quickReplies: List<QuickReplyConfig>? = null,
    val disabled: Boolean? = false,
    val mediaUrl: String? = null,
    val mediaType: NotificationMediaType? = null,
) : NotificationConfig

data class RawTemplateNotificationConfig(
    val configurationKey: ConfigurationKey,
    val notificationType: NotificationType?,
) : NotificationConfig

data class ConfigurationKey(val value: String)

object KnownTemplateConfigurationKeys {
    val chatbotAiNotifyBillsComingDue = ConfigurationKey("chatbot-ai-notify-bills-coming-due")
    val chatbotAiNotifyBillsComingDueMax = ConfigurationKey("chatbot-ai-notify-bills-coming-due-max")
    val chatbotAiNotifyBillsComingDueSingular = ConfigurationKey("chatbot-ai-notify-bills-coming-due-singular")
    val chatbotAiNotifyBillsComingDueSimple = ConfigurationKey("chatbot-ai-notify-bills-coming-due-simple")
    val chatbotAiNotifyBillsComingDueBasic = ConfigurationKey("chatbot-ai-notify-bills-coming-due-basic")
    val authorizePix = ConfigurationKey("authorize-pix")
    val pixConfirmation = ConfigurationKey("pix-confirmation")
    val onboardingStart = ConfigurationKey("onboarding-start")
    val waitingApprovalBillsSingular = ConfigurationKey("waiting-approval-bills-singular")
    val waitingApprovalBillsPlural = ConfigurationKey("waiting-approval-bills-plural")
    val tokenMfa = ConfigurationKey("token-mfa")
    val chatbotAiNotifyBillsComingDueLastWarnEarlyAccess = ConfigurationKey("chatbot-ai-notify-bills-coming-due-last-warn-early-access")
    val chatbotSufficientBalance = ConfigurationKey("chatbot-sufficient-balance")
    val chatbotOutdatedAction = ConfigurationKey("chatbot-outdated-action")
    val reminderNotificationResponseSuccess = ConfigurationKey("reminder-notification-response-success")
    val reminderNotificationResponseError = ConfigurationKey("reminder-notification-response-error")
    val utilityAccountAddNewConnection = ConfigurationKey("utility-account-add-new-connection")
    val onboardingSinglePixStart = ConfigurationKey("onboarding-single-pix-start")
    val onboardingSinglePixExamplePaymentConfirmation = ConfigurationKey("onboarding-single-pix-example-payment-confirmation")
    val registrationCompletion = ConfigurationKey("registration-completion")
    val promoteSweepingAccountMarkAsPaid = ConfigurationKey("promote-sweeping-account-mark-as-paid")
    val promoteSweepingAccountCashIn = ConfigurationKey("promote-sweeping-account-cash-in")
    val promoteSweepingAccountDDA = ConfigurationKey("promote-sweeping-account-dda")
    val sweepingDailyLimitExceed = ConfigurationKey("sweeping-daily-limit-exceed")
    val sweepingLimitExceed = ConfigurationKey("sweeping-limit-exceed")
    val pixAmountExceedsSweepingLimit = ConfigurationKey("pix-amount-exceeds-sweeping-limit")
    val scheduledAmountExceedsSweepingLimit = ConfigurationKey("scheduled-amount-exceeds-sweeping-limit")
    val authorizeScheduleBills = ConfigurationKey("authorize-schedule-bills")
}

data class NotificationType(val value: String)

object KnownNotificationTypes {
    val AUTHORIZE_PIX = NotificationType("AUTHORIZE_PIX")
    val BILLS_COMING_DUE = NotificationType("BILLS_COMING_DUE")
    val ONBOARDING_START = NotificationType("ONBOARDING_START")
    val PIX_CONFIRMATION = NotificationType("PIX_CONFIRMATION")
    val WAITING_APPROVAL_BILLS = NotificationType("WAITING_APPROVAL_BILLS")
    val TOKEN_MFA = NotificationType("TOKEN_MFA")
    val BILLS_COMING_DUE_LAST_WARN_EARLY_ACCESS = NotificationType("BILLS_COMING_DUE_LAST_WARN_EARLY_ACCESS")
    val SUFFICIENT_BALANCE = NotificationType("SUFFICIENT_BALANCE")
    val CHATBOT_OUTDATED_ACTION = NotificationType("CHATBOT_OUTDATED_ACTION")
    val REMINDER_NOTIFICATION_RESPONSE_SUCCESS = NotificationType("REMINDER_NOTIFICATION_RESPONSE_SUCCESS")
    val REMINDER_NOTIFICATION_RESPONSE_ERROR = NotificationType("REMINDER_NOTIFICATION_RESPONSE_ERROR")
    val UTILITY_ACCOUNT_ADD_NEW_CONNECTION = NotificationType("UTILITY_ACCOUNT_ADD_NEW_CONNECTION")
    val ONBOARDING_SINGLE_PIX_START = NotificationType("ONBOARDING_SINGLE_PIX_START")
    val ONBOARDING_SINGLE_PIX_EXAMPLE_PAYMENT_CONFIRMATION = NotificationType("ONBOARDING_SINGLE_PIX_EXAMPLE_PAYMENT_CONFIRMATION")
    val REGISTRATION_COMPLETION = NotificationType("REGISTRATION_COMPLETION")
    val PROMOTE_SWEEPING_ACCOUNT_MARK_AS_PAID = NotificationType("PROMOTE_SWEEPING_ACCOUNT_MARK_AS_PAID")
    val PROMOTE_SWEEPING_ACCOUNT_CASH_IN = NotificationType("PROMOTE_SWEEPING_ACCOUNT_CASH_IN")
    val PROMOTE_SWEEPING_ACCOUNT_DDA = NotificationType("PROMOTE_SWEEPING_ACCOUNT_DDA")
    val SWEEPING_DAILY_LIMIT_EXCEED = NotificationType("SWEEPING_DAILY_LIMIT_EXCEED")
    val SWEEPING_LIMIT_EXCEED = NotificationType("SWEEPING_LIMIT_EXCEED")
    val PIX_AMOUNT_EXCEEDS_SWEEPING_LIMIT = NotificationType("PIX_AMOUNT_EXCEEDS_SWEEPING_LIMIT")
    val SCHEDULED_AMOUNT_EXCEEDS_SWEEPING_LIMIT = NotificationType("SCHEDULED_AMOUNT_EXCEEDS_SWEEPING_LIMIT")
    val AUTHORIZE_SCHEDULE_BILLS = ConfigurationKey("AUTHORIZE_SCHEDULE_BILLS")
}