package ai.chatbot.app.notification

import ai.chatbot.app.NotificationService
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.notification.BuildNotificationService.Companion.MAX_BILLS_IN_NOTIFICATION
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.getObjectMapper
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private const val TEMPLATE_NOT_FOUND_MESSAGE = "(Não foi possível encontrar o conteúdo da mensagem)"

data class NotificationMap(
    val param: NotificationParam,
    val value: String,
)

@Singleton
open class CustomNotificationService(
    private val notificationService: NotificationService,
    private val conversationHistoryService: ConversationHistoryService,
    private val templateInfoService: TemplateInfoService,
    private val tenantService: TenantService,
    private val notificationContextTemplatesService: NotificationContextTemplatesService,
) {
    fun config() = tenantService.getConfiguration().messages

    fun send(
        user: User,
        params: List<NotificationMap>,
        notificationConfig: NotificationConfig,
    ) {
        val logName = "CustomNotificationService#send"

        val markers = append("userId", user.id.value).andAppend("accountId", user.accountId.value).andAppend("notificationConfig", notificationConfig).andAppend("params", params)

        val buildNotificationResult = when (notificationConfig) {
            is RawTemplateNotificationConfig -> {
                buildRawTemplateNotification(user, params, notificationConfig)
            }

            is TextNotificationConfig -> {
                if (notificationConfig.disabled == true) {
                    logger.info(markers, "$logName/skipped")
                    return
                }
                buildFromTextNotificationConfig(user, notificationConfig, params)
            }
        }
        try {
            notificationService.notify(buildNotificationResult.notification)
            conversationHistoryService.createAssistantMessage(user.id, buildNotificationResult.historyMessage)
            logger.info(markers, logName)
        } catch (e: Throwable) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    fun send(
        mobilePhone: String,
        accountId: AccountId,
        message: String,
        link: CTALink? = null,
        quickReplies: List<QuickReplyButton>? = null,
    ) {
        val logName = "CustomNotificationService#send"

        val markers = append("userId", mobilePhone).andAppend("accountId", accountId).andAppend("message", message).andAppend("link", link).andAppend("quickReplies", quickReplies)

        val userId = UserId(mobilePhone)

        try {
            notificationService.notify(userId, accountId, message, quickReplies, link)
            conversationHistoryService.createAssistantMessage(userId, message)
            logger.info(markers, logName)
        } catch (e: Throwable) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    fun send(buildNotificationResult: BuildNotificationResult) {
        val logName = "CustomNotificationService#send"

        val markers = Markers.append("userId", buildNotificationResult.notification.mobilePhone).andAppend("accountId", buildNotificationResult.notification.accountId.value).andAppend("notification", buildNotificationResult.notification)

        try {
            notificationService.notify(buildNotificationResult.notification)
            conversationHistoryService.createAssistantMessage(UserId(buildNotificationResult.notification.mobilePhone), buildNotificationResult.historyMessage)
            logger.info(markers, logName)
        } catch (e: Throwable) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    fun getHistoryMessage(templatedNotification: ChatbotWhatsappTemplatedNotification): String {
        val templateInfo = try {
            templateInfoService.getInfo(templatedNotification.template.value)
        } catch (e: Exception) {
            logger.error(Markers.append("template", templatedNotification.template.value), "CustomNotificationService/couldNotGetHistoryMessage", e)
            TemplateInfo(text = TEMPLATE_NOT_FOUND_MESSAGE, buttons = listOf(), params = 0, category = "UNKNOWN")
        }

        return templateInfo.resolveHistoryMessage(templatedNotification.parameters)
    }

    fun buildFromTextNotificationConfig(
        user: User,
        notificationConfig: TextNotificationConfig,
        params: List<NotificationMap>,
    ): BuildNotificationResult {
        val notification = buildSimpleNotification(notificationConfig, user, params)

        logger.info(
            append("userId", user.id.value).andAppend("accountId", user.accountId.value).andAppend("notificationConfig", notificationConfig).andAppend("params", params).andAppend("notification", notification),
            "CustomNotificationService#build",
        )

        return notification
    }

    fun getHistoryMessage(templatedRawNotification: ChatbotRawTemplatedNotification): String {
        val history = try {
            templateInfoService.getHistoryMessage(templatedRawNotification)
        } catch (e: Exception) {
            logger.error(Markers.append("notificationId", templatedRawNotification.notificationId), "CustomNotificationService/couldNotGetHistoryMessageForRawTemplate", e)
            ""
        }

        return history
    }

    fun buildRawTemplateNotification(
        user: User,
        params: List<NotificationMap>,
        notificationConfig: RawTemplateNotificationConfig,
    ): BuildNotificationResult {
        val currentTenantConfig = tenantService.getConfiguration()
        val parsedArgs = params.resolveToMap()

        val rawNotification = ChatbotRawTemplatedNotification(
            accountId = user.accountId,
            mobilePhone = user.id.value,
            clientId = currentTenantConfig.clientId,
            configurationKey = notificationConfig.configurationKey,
            arguments = parsedArgs,
            notificationType = notificationConfig.notificationType,
        )

        val history = templateInfoService.getHistoryMessage(rawNotification)

        return BuildNotificationResult(
            rawNotification,
            historyMessage = history,
            shouldSend = true,
        )
    }

    private fun buildSimpleNotification(
        notificationConfig: TextNotificationConfig,
        user: User,
        params: List<NotificationMap>,
    ): BuildNotificationResult {
        val message = params.resolve(notificationConfig.text)

        val notification = BuildNotificationResult(
            notification = ChatbotWhatsappSimpleNotification(
                accountId = user.accountId,
                mobilePhone = user.id.value,
                message = message,
                ctaLink = notificationConfig.link?.let {
                    val url = if (it.event != null) {
                        ButtonWhatsAppTrackedDeeplinkParameter(params.resolve(it.url), it.event).value
                    } else {
                        ButtonWhatsAppDeeplinkParameter(params.resolve(it.url)).value
                    }

                    CTALink(
                        displayText = it.text ?: throw IllegalStateException("Simple notification link CTA must always have text"),
                        url = "${tenantService.getConfiguration().appBaseUrl}/$url",
                    )
                },
                quickReplyButtons = notificationConfig.quickReplies?.let {
                    it.map { qr ->
                        QuickReplyButton(
                            text = params.resolve(qr.text ?: throw IllegalStateException("Simple notification quick reply must always have text")),
                            payload = qr.resolvePayload(params),
                        )
                    }
                },
                media = notificationConfig.mediaUrl?.let {
                    mediaFrom(notificationConfig)
                },
            ),
            historyMessage = message,
            shouldSend = notificationConfig.disabled != true,
        )

        return notification
    }

    private fun mediaFrom(notificationConfig: TextNotificationConfig): NotificationMedia {
        if (notificationConfig.mediaUrl == null || notificationConfig.mediaType == null) {
            throw IllegalArgumentException("NotificationConfig does not contain url or mediaType")
        }

        return when (notificationConfig.mediaType) {
            NotificationMediaType.DOCUMENT -> NotificationMedia.Document(
                url = notificationConfig.mediaUrl,
                filename = "no_name", // TODO: implementar nome do arquivo
                documentType = null,
            )

            NotificationMediaType.IMAGE -> NotificationMedia.Image(url = notificationConfig.mediaUrl)
            NotificationMediaType.VIDEO -> NotificationMedia.Video(url = notificationConfig.mediaUrl, null)
        }
    }

    private fun List<NotificationMap>.resolve(template: String): String {
        val paramNames = paramRegex.findAll(template).map { it.value }

        var resolvedString = template
        paramNames.forEach { param ->
            val paramEnum = try {
                NotificationParam.valueOf(param)
            } catch (e: IllegalArgumentException) {
                throw ParamNotFoundException(param)
            }

            resolvedString = resolvedString.replace("{{$param}}", this.find { it.param == paramEnum }?.value ?: throw ParamNotFoundException(param))
        }

        return resolvedString
    }

    private fun List<NotificationMap>.resolveToMap(): Map<String, String> {
        val repeatedParamNames = this.groupBy { it.param.name }.filter { it.value.size > 1 }.keys

        val repeatedParamIndex = mutableMapOf<String, Int>()
        return this.associate {
            val paramName = it.param.name
            if (paramName !in repeatedParamNames) {
                paramName to it.value
            } else {
                val currentIndex = repeatedParamIndex[paramName] ?: 1
                repeatedParamIndex[paramName] = currentIndex + 1
                "${paramName}_$currentIndex" to it.value
            }
        }
    }

    private fun QuickReplyConfig.resolvePayload(params: List<NotificationMap>): String {
        return getObjectMapper().writeValueAsString(
            BlipPayload(
                action = this.action?.let { InterceptMessagePayloadType.valueOf(it).name } ?: "",
                payload = this.payload?.let { p -> params.resolve(p) } ?: getLocalDate().format(dateFormat),
                transactionId = params.find { it.param == (this.transactionId ?: NotificationParam.TRANSACTION_ID) }?.value,
            ),
        )
    }

    fun buildBillsComingDueCustom(user: User, bills: List<FormattedBillInfo>): BuildNotificationResult {
        val billsCount = bills.size
        return when {
            user.paymentStatus == AccountPaymentStatus.Overdue -> {
                buildRawTemplateNotification(
                    user,
                    listOf(
                        NotificationMap(NotificationParam.USER_NAME, user.name),
                    ),
                    RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDueSimple, KnownNotificationTypes.BILLS_COMING_DUE),
                )
            }

            isBlipMessageSizeGreaterThanMax(notificationContextTemplatesService.getBillsComingDue(user.name, bills).length) -> { // TODO - o wacomm consegue retorner o texto que será enviado. Podemos trocar para ele
                buildRawTemplateNotification(
                    user,
                    listOf(
                        NotificationMap(NotificationParam.USER_NAME, user.name),
                        NotificationMap(NotificationParam.TOTAL_BILLS, billsCount.toString()),
                    ),
                    RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDueBasic, KnownNotificationTypes.BILLS_COMING_DUE),
                )
            }

            billsCount == 1 -> {
                buildRawTemplateNotification(
                    user,
                    listOf(
                        NotificationMap(NotificationParam.USER_NAME, user.name),
                        NotificationMap(NotificationParam.BILL_DESCRIPTION, bills.first().description),
                        NotificationMap(NotificationParam.AMOUNT, bills.first().amount),
                    ),
                    RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDueSingular, KnownNotificationTypes.BILLS_COMING_DUE),
                )
            }

            billsCount in 2..MAX_BILLS_IN_NOTIFICATION -> {
                buildRawTemplateNotification(
                    user = user,
                    params = listOf(
                        NotificationMap(NotificationParam.USER_NAME, user.name),
                        NotificationMap(NotificationParam.TEMPLATE_VARIANT, "${bills.size}_bills"),
                    ) + resolveMultipleBillsParams(bills),
                    notificationConfig = RawTemplateNotificationConfig(
                        configurationKey = KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDue,
                        notificationType = KnownNotificationTypes.BILLS_COMING_DUE,
                    ),
                )
            }

            else -> {
                buildRawTemplateNotification(
                    user = user,
                    params = listOf(
                        NotificationMap(NotificationParam.USER_NAME, user.name),
                    ) + resolveMultipleBillsParams(bills),
                    notificationConfig = RawTemplateNotificationConfig(
                        configurationKey = KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDueMax,
                        notificationType = KnownNotificationTypes.BILLS_COMING_DUE,
                    ),
                )
            }
        }
    }

    private fun resolveMultipleBillsParams(
        bills: List<FormattedBillInfo>,
    ): List<NotificationMap> {
        val logName = "CustomNotificationService#resolveMultipleBillsParams"
        val markers = Markers.append("bills", bills).andAppend("billsCount", bills.size)

        if (bills.size < 2) {
            logger.error(markers, "$logName/invalidBillsCount")
            throw IllegalArgumentException("Number of bills must be at least 2")
        }

        if (bills.size > 10) {
            logger.error(markers.andAppend("context", "apenas as $MAX_BILLS_IN_NOTIFICATION serão consideradas"), "$logName/invalidBillsCount")
        }

        return bills.take(
            MAX_BILLS_IN_NOTIFICATION,
        ).map { bill ->
            listOf(
                NotificationMap(NotificationParam.BILL_DESCRIPTION, bill.description),
                NotificationMap(NotificationParam.AMOUNT, bill.amount),
            )
        }.flatten().also { params ->
            logger.info(markers.andAppend("billParams", params), logName)
        }
    }

    private fun TemplateInfo.resolveHistoryMessage(params: List<String>): String {
        var message = text

        params.forEachIndexed { i, value -> message = message.replace("{{${i + 1}}}", value) }

        return message
    }

    companion object {
        val paramRegex = "(?<=\\{\\{)[^\\}]*(?=\\}\\})".toRegex()
        val logger = LoggerFactory.getLogger(CustomNotificationService::class.java)
    }
}

data class BuildNotificationResult(
    val notification: ChatbotNotification,
    val historyMessage: String,
    val shouldSend: Boolean,
)

data class ParamNotFoundException(val param: String) : RuntimeException(param)

enum class NotificationParam {
    USER_NAME,
    AMOUNT,
    PIX_KEY,
    TRANSACTION_ID,
    TRANSACTION_GROUP_ID,
    TRANSACTION_ID_2,
    TRANSACTION_ID_3,
    DATE,
    BILL_DESCRIPTION,
    BILL_AMOUNT,
    RECIPIENT_NAME, RECIPIENT_DOCUMENT, RECIPIENT_INSTITUTION,
    BILLS_LIST,
    TOTAL_BILLS,
    MARK_AS_PAID,
    SCHEDULE_BILLS,
    CONTA1_NOME,
    CONTA1_NOME_CURTO,
    CONTA1_SALDO,
    CONTA2_NOME,
    CONTA2_NOME_CURTO,
    CONTA2_SALDO,
    CONTA3_NOME,
    CONTA3_NOME_CURTO,
    CONTA3_SALDO,
    TITLE,
    MANUAL_ENTRY_ID,
    SUBSCRIPTION_WARNING,
    TEMPLATE_VARIANT,
}