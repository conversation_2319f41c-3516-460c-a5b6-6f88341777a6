package ai.chatbot.app.notification

import ai.chatbot.app.Friday
import ai.chatbot.app.utils.brazilDateFormat
import ai.chatbot.app.utils.formattedAmount
import ai.chatbot.app.utils.maskDocumentSpecialAsterisk
import jakarta.inject.Singleton
import java.math.RoundingMode
import java.time.LocalDate

interface NotificationContextTemplatesService {
    fun getBillsComingDueLegacy(
        userName: String,
        billsContent: List<String>,
    ): String

    fun getBillsComingDueSweepingAccount(
        userName: String,
        billsContent: List<String>,
    ): String

    fun getBillsComingDue(
        userName: String,
        billsContent: List<FormattedBillInfo>,
    ): String

    fun getBillsComingUserRequested(
        billsContent: List<String>,
    ): List<String>

    fun getBillsComingUserRequestedSingular(
        billsContent: List<String>,
    ): String

    fun getSweepingAccountBillsScheduleConfirmation(
        billsContent: List<String>,
    ): String

    fun getSweepingAccountWarnConfirmation(): String

    fun getWelcomeAccountCreatedMessage(): String

    fun getBillComingDueLastWarn(
        userName: String,
        paymentLimitTime: String,
    ): String

    fun getMarkAsPaidConfirmation(billsContent: List<String>): String

    fun getMarkAsPaidAndIgnoreConfirmation(
        billsContentMarkAsPaid: List<String>,
        billsContentIgnore: List<String>,
    ): String

    fun getIgnoreBillsConfirmation(billsContent: List<String>): String

    fun getSufficientBalance(): String

    fun getAddBoletoConcessionariaMessage(amount: Long, assignor: String, dueDate: LocalDate?): String

    fun addMultipleBoletoSuccessMessage(): String

    fun getAddBoletoMessage(amount: Long, assignor: String, dueDate: LocalDate): String

    fun getOutdatedAction(): String

    fun getUserConfirmationNotificationMessage(
        userName: String,
        amount: Long,
        key: String,
        recipientName: String,
        recipientDocument: String,
        recipientInstitution: String,
    ): String

    fun getUserConfirmationNotificationSweepingMessage(
        userName: String,
        amount: Long,
        key: String,
        recipientName: String,
        recipientDocument: String,
        recipientInstitution: String,
    ): String

    fun getPixAuthorizationNotificationSweepingMessage(
        userName: String,
        amount: Long,
        key: String,
        recipientName: String,
        recipientDocument: String,
        recipientInstitution: String,
    ): String

    fun getPixAuthorizationNotificationMessage(
        userName: String,
        amount: Long,
        key: String,
        recipientName: String,
        recipientDocument: String,
        recipientInstitution: String,
    ): String

    fun getOutdatedPendingBillsNotificationMessage(
        billsContent: List<String>,
    ): String

    fun getRegisterCompletedMessage(name: String): String

    fun getAuthorizeScheduleBillsMessage(billsContent: List<FormattedBillInfo>): String

    fun getPromoteSweepingAccountMarkAsPaidMessage(): String

    fun getPromoteSweepingAccountKnowMoreMessage(): String

    fun getPromoteSweepingAccountOptOutMessage(): String

    fun getPromoteSweepingAccountCashInMessage(): String

    fun getPromoteSweepingAccountDDAMessage(billInfo: FormattedBillInfo): String

    fun getPaySomeReplyMessage(): String
}

@Friday
@Singleton
open class FridayNotificationContextTemplatesService : NotificationContextTemplatesService {
    override fun getBillsComingDueLegacy(
        userName: String,
        billsContent: List<String>,
    ) =
        """
        |Oi $userName, aqui é o Fred, assistente pessoal da Friday. Vi que você tem as seguintes contas vencendo hoje:
        |${billsContent.joinToString(separator = "\n")}
        |Você quer que eu te ajude a fazer esses pagamentos por aqui?
        """.trimMargin()

    override fun getBillsComingDueSweepingAccount(userName: String, billsContent: List<String>) =
        if (billsContent.size > 1) {
            """
            |Oi $userName, vi que você tem ${billsContent.size} contas vencendo hoje.
            |
            |Você quer fazer estes pagamentos?
            """.trimMargin()
        } else {
            """
            |Oi $userName, vi que você tem as seguintes contas vencendo hoje:
            |
            |${billsContent.joinToString(separator = "\n")}
            |
            |Você quer fazer estes pagamentos?
            """.trimMargin()
        }

    override fun getBillsComingDue(userName: String, billsContent: List<FormattedBillInfo>): String {
        return if (billsContent.size > 10) {
            """
            |Oi $userName, vi que você tem ${billsContent.size} contas vencendo hoje.
            |
            |Você quer fazer estes pagamentos?
            """.trimMargin()
        } else {
            """
            |Oi $userName, vi que você tem as seguintes contas vencendo hoje:
            |
            |${billsContent.mapIndexed { i, bill -> "${i + 1}. ${bill.description} no valor de ${bill.amount}" }.joinToString("\n")}
            |
            |Você quer fazer estes pagamentos?
            """.trimMargin()
        }
    }

    override fun getBillsComingUserRequested(
        billsContent: List<String>,
    ): List<String> {
        val fullListResponse =
            """
            |Aqui estão suas contas em aberto:
            |
            |${billsContent.joinToString(separator = "\n\n")}
            |
            |Você quer fazer estes pagamentos?
            """.trimMargin()

        if (fullListResponse.length < 1024) {
            return listOf(fullListResponse)
        }

        // Quantas mensagens vamos precisar enviar para não sobrar conta. Usando 950 caracteres pra não ficar no limite.
        val splitSize = (fullListResponse.length / 950.0).toBigDecimal().setScale(0, RoundingMode.CEILING).toInt()

        // Quantidade de contas em cada mensagem
        val billsPerMessage = (billsContent.size.toDouble() / splitSize.toDouble()).toBigDecimal().setScale(0, RoundingMode.UP).toInt()

        val billsChunks = billsContent.chunked(billsPerMessage)

        return billsChunks.mapIndexed { index, chunk ->

            when (index) {
                0 -> {
                    """
                            |Aqui estão suas contas em aberto:
                            |
                            |${chunk.joinToString(separator = "\n\n")}
                            |
                            |Continua...
                    """.trimMargin()
                }
                billsChunks.lastIndex -> {
                    """
                            |${chunk.joinToString(separator = "\n\n")}
                            |
                            |Você quer fazer estes pagamentos?
                    """.trimMargin()
                }
                else -> {
                    """
                            |${chunk.joinToString(separator = "\n\n")}
                            |
                            |Continua...
                    """.trimMargin()
                }
            }
        }
    }

    override fun getBillsComingUserRequestedSingular(
        billsContent: List<String>,
    ) = """
        |Você tem a seguinte conta em aberto:
        |
        |${billsContent.joinToString(separator = "\n\n")}
        |
        |Você quer fazer estes pagamentos?
    """.trimMargin()

    override fun getSweepingAccountBillsScheduleConfirmation(billsContent: List<String>) =
        """
        |Os pagamentos selecionados são:
        |
        |${billsContent.joinToString(separator = "\n\n")}
        |
        |Posso prosseguir?
        """.trimMargin()

    override fun getSweepingAccountWarnConfirmation(): String =
        """
        |Percebi que você não tem saldo na Friday. Posso tentar retirar da sua conta conectada. 
        
        |Se essa conta também estiver sem saldo, a operação pode usar seu limite de crédito.
        """.trimMargin()

    override fun getWelcomeAccountCreatedMessage() =
        """
        |Trago boas notícias: sua conta já está ativa!
        |
        |Já busquei os seus boletos automáticos. Agora, conecte suas contas de consumo e Pix recorrentes no App para ter uma visão centralizada de tudo. Eu vou lembrar o dia de vencimento de todas as contas e fazer os pagamentos por você!
        |
        |Qualquer dúvida, é só me perguntar. A partir de agora estou sempre disponível para conversar.
        """.trimMargin()

    override fun getBillComingDueLastWarn(
        userName: String,
        paymentLimitTime: String,
    ) =
        """
        |Oi $userName, aqui é o Fred. 🚨Importante: algumas de suas contas vão vencer até às $paymentLimitTime.
        """.trimMargin()

    override fun getMarkAsPaidConfirmation(billsContent: List<String>) =
        if (billsContent.size == 1) {
            """
            |Entendi, você deseja informar que efetuou o pagamento da conta a seguir em outra instituição. Posso marcar ela como paga?
            |
            ${billsContent.joinToString(separator = "\n")}
            """
        } else {
            """
            |Entendi, você deseja informar que efetuou o pagamento das contas a seguir em outra instituição. Posso marcar elas como pagas?
            |
            ${billsContent.joinToString(separator = "\n")}
            """
        }
            .trimMargin()

    override fun getMarkAsPaidAndIgnoreConfirmation(
        billsContentMarkAsPaid: List<String>,
        billsContentIgnore: List<String>,
    ) =
        """
        |Entendi, você deseja informar que efetuou o pagamento das contas a seguir em outra instituição:
        |${billsContentMarkAsPaid.joinToString(separator = "\n")}
        |
        |E deseja ignorar as contas a seguir:
        |${billsContentIgnore.joinToString(separator = "\n")}
        |
        |Posso prosseguir?
        """.trimMargin()

    override fun getIgnoreBillsConfirmation(billsContent: List<String>) =
        if (billsContent.size == 1) {
            """
            |Entendi, você deseja ignorar a seguinte conta:
            |
            |${billsContent.joinToString(separator = "\n")}
            |
            |Ela será removida da sua timeline, se desejar retornar esse pagamento ele estará disponível na aba Removidos do App Friday.
            |Posso prosseguir?
            """
        } else {
            """
            |Entendi, você deseja ignorar as seguintes contas:
            |
            |${billsContent.joinToString(separator = "\n")}
            |
            |Elas serão removidas da sua timeline, se desejar retornar esses pagamentos eles estarão disponíveis na aba Removidos do App Friday.
            |Posso prosseguir?
            """
        }.trimMargin()

    override fun getSufficientBalance() =
        """
        |Ops, vi que você tem saldo na sua carteira Friday. Por motivos de segurança, pagamentos utilizando o saldo da carteira só podem ser feitos pelo aplicativo, ok?
        """.trimMargin()

    override fun getAddBoletoConcessionariaMessage(amount: Long, assignor: String, dueDate: LocalDate?) =
        """
            |🧾Conta: $assignor
            |💰 Valor: ${amount.formattedAmount()} 
            |${dueDate?.let { date -> "📆 Vencimento: ${date.format(brazilDateFormat)}" } ?: ""}
            |
            |O que você deseja fazer?
        """.trimMargin()

    override fun addMultipleBoletoSuccessMessage() = "Beleza! Vou adicionar os boletos e em instantes eles estarão na sua timeline."

    override fun getAddBoletoMessage(amount: Long, assignor: String, dueDate: LocalDate): String {
        return """
            |🧾Conta: $assignor
            |💰 Valor: ${amount.formattedAmount()}
            |📅 Vencimento: ${dueDate.format(brazilDateFormat)}
            |
            |Você quer incluir este boleto na sua timeline?
        """.trimMargin()
    }

    override fun getOutdatedAction() =
        """
        |Botões e ações sobre notificações só são validos no mesmo dia em que a notificação é enviada. Você pode ver quais são as opções disponíveis para contas de dias anteriores no App.
        """.trimMargin()

    override fun getUserConfirmationNotificationMessage(
        userName: String,
        amount: Long,
        key: String,
        recipientName: String,
        recipientDocument: String,
        recipientInstitution: String,
    ): String {
        return """
                |Você confirma os dados abaixo?
                |👤Nome: $recipientName
                |🪪Documento: ${maskDocumentSpecialAsterisk(recipientDocument)}
                |🏦Instituição: $recipientInstitution
                |💵Valor: ${amount.formattedAmount()}
                |🔑Chave: $key
        """.trimMargin()
    }

    override fun getUserConfirmationNotificationSweepingMessage(
        userName: String,
        amount: Long,
        key: String,
        recipientName: String,
        recipientDocument: String,
        recipientInstitution: String,
    ): String {
        return """
                |Você confirma os dados abaixo?
                |👤Nome: $recipientName
                |🪪Documento: ${maskDocumentSpecialAsterisk(recipientDocument)}
                |🏦Instituição: $recipientInstitution
                |💵Valor: ${amount.formattedAmount()}
                |🔑Chave: $key
                |
                |Esta transferência usará saldo da sua conta conectada
        """.trimMargin()
    }

    override fun getPixAuthorizationNotificationMessage(
        userName: String,
        amount: Long,
        key: String,
        recipientName: String,
        recipientDocument: String,
        recipientInstitution: String,
    ): String {
        return """
                |O valor do Pix excede o seu limite transacional por WhatsApp, mas você pode autorizar o pagamento se os dados estiverem corretos:
                |👤Nome: $recipientName
                |🪪Documento: ${maskDocumentSpecialAsterisk(recipientDocument)}
                |🏦Instituição: $recipientInstitution
                |💵Valor: ${amount.formattedAmount()}
                |🔑Chave: $key
        """.trimMargin()
    }

    override fun getPixAuthorizationNotificationSweepingMessage(
        userName: String,
        amount: Long,
        key: String,
        recipientName: String,
        recipientDocument: String,
        recipientInstitution: String,
    ): String {
        return """
                |O valor do Pix excede o seu limite transacional por WhatsApp, mas você pode autorizar o pagamento se os dados estiverem corretos:
                |👤Nome: $recipientName
                |🪪Documento: ${maskDocumentSpecialAsterisk(recipientDocument)}
                |🏦Instituição: $recipientInstitution
                |💵Valor: ${amount.formattedAmount()}
                |🔑Chave: $key
                |
                |Esta transferência usará saldo da sua conta conectada
        """.trimMargin()
    }

    override fun getOutdatedPendingBillsNotificationMessage(
        billsContent: List<String>,
    ): String {
        val s = if (billsContent.size > 1) "s" else ""

        return """
        |${billsContent.joinToString(separator = "\n\n")}
        |
        |*A lista de contas mudou desde a nossa ultima mensagem*. Por isso, estamos confirmando novamente. Você quer fazer este$s pagamento$s?
        """.trimMargin()
    }

    override fun getRegisterCompletedMessage(name: String) = """
        Oi, aqui é o Fred!

        Sua conta está sendo criada e, assim que estiver tudo pronto, passo aqui para te contar.
        
        Enquanto isso, já salva o meu contato. Vou te avisar sobre novas contas e lembrar vencimentos por aqui.
        
        E sempre que quiser avaliar minhas mensagens, é só reagir com 👍 ou 👎.
    """.trimIndent()

    override fun getAuthorizeScheduleBillsMessage(billsContent: List<FormattedBillInfo>): String =
        """
            |Tudo pronto, mas a soma dos pagamentos excede o limite dos pagamentos permitidos por WhatsApp.
            |
            |${billsContent.mapIndexed { i, bill -> "${i + 1}. ${bill.description} no valor de ${bill.amount}" }.joinToString("\n")}
        """.trimMargin()

    override fun getPromoteSweepingAccountMarkAsPaidMessage(): String = """
        Contas pagas manualmente?
        
        Conecte a Friday com seu banco para fazer seus próximos pagamentos digitando “sim” no WhatsApp!
    """.trimIndent()

    override fun getPromoteSweepingAccountKnowMoreMessage(): String = """
        As transferências inteligentes entre suas contas é uma função que o Open Finance criou para que você não se restrinja aos serviços do lugar onde está o seu dinheiro.

        Você pode manter o seu saldo no banco que escolheu e, quando receber os lembretes de vencimento por aqui, digitar “sim” no WhatsApp para que eu traga pra cá somente o que eu preciso para pagar suas contas.

        Ou seja, você vai literalmente responder “sim” e ver suas contas pagas! Um trabalho a menos para os seus dias ;)
    """.trimIndent()

    override fun getPromoteSweepingAccountOptOutMessage(): String = """
        Ok, você pode conectar seu banco a qualquer momento em carteira > open finance
    """.trimIndent()

    override fun getPromoteSweepingAccountCashInMessage(): String = """
        Ainda acessando seu banco para colocar saldo na Friday?

        Conecte a Friday com seu banco para trazer saldo em segundos.
    """.trimIndent()

    override fun getPromoteSweepingAccountDDAMessage(billInfo: FormattedBillInfo): String = """
        ☑️ Conta Paga Sem Friday

        Detectamos o pagamento da conta ${billInfo.description} fora da Friday no valor de ${billInfo.amount}.
        
        Conecte a Friday com seu banco para fazer seus proximos pagamentos digitando "Sim" no Whatsapp.
    """.trimIndent()

    override fun getPaySomeReplyMessage(): String = """
        |Claro! Você pode me dizer quais das contas você gostaria de pagar?
    """.trimMargin()
}