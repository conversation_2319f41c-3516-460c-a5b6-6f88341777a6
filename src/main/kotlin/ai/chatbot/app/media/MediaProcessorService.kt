package ai.chatbot.app.media

import ai.chatbot.app.file.ObjectRepository
import ai.chatbot.app.file.StoredObject
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.brazilDateFormat
import jakarta.inject.Singleton
import java.awt.image.BufferedImage
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.time.LocalDate
import javax.imageio.ImageIO
import net.logstash.logback.marker.Markers.append
import org.apache.pdfbox.pdmodel.PDDocument
import org.apache.pdfbox.rendering.PDFRenderer
import org.apache.pdfbox.text.PDFTextStripper
import org.slf4j.Logger
import org.slf4j.LoggerFactory

private fun String.isPixQrCode(): Boolean {
    return contains("BR.GOV.BCB.PIX", ignoreCase = true)
}

private fun String.isConcessionaria(): Boolean {
    return startsWith("8")
}

private fun List<BoletoInfo>.upsert(boletoInfo: BoletoInfo): List<BoletoInfo> {
    val index = indexOfFirst { it.codigo == boletoInfo.codigo }
    return if (index != -1) {
        toMutableList().apply { set(index, boletoInfo) }
    } else {
        this + boletoInfo
    }
}

@Singleton
class MediaProcessorService(
    private val imageProcessor: ImageProcessor,
    private val textProcessor: TextProcessor,
    private val remoteBarCodeMediaProcessor: RemoteMediaProcessor,
    private val objectRepository: ObjectRepository,
) {
    private val name = "MediaProcessorService"

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(MediaProcessorService::class.java)
        private const val PDF_DPI = 400F
        private val SUPPORTED_IMAGE_EXTENSIONS = setOf("jpg", "jpeg", "png")
    }

    fun process(storedObject: StoredObject): MediaAnalysisResult {
        val bytes = objectRepository.loadObject(storedObject.bucket, storedObject.key).use { it.readAllBytes() }
        val fileExtension = storedObject.key.substringAfterLast('.').lowercase()

        val result = ByteArrayInputStream(bytes).use { stream ->
            processMedia(stream, fileExtension)
        }

        logger.info(append("processedMedia", result), "$name#process")

        return MediaAnalysisResult(
            qrCodeValues = result.qrCodeValues.distinct(),
            boletos = result.boletos.distinct().map {
                BoletoInfo(
                    codigo = it.codigo,
                    vencimento = it.vencimento?.let { date ->
                        LocalDate.parse(date, brazilDateFormat).toString()
                    },
                )
            },
            text = result.text?.takeIf { it.isNotBlank() }?.trim(),
        )
    }

    private fun processMedia(stream: InputStream, fileExtension: String): MediaAnalysisResult {
        val markers = append("fileExtension", fileExtension)
        try {
            return when (fileExtension) {
                "pdf" -> processPdf(stream)
                in SUPPORTED_IMAGE_EXTENSIONS -> processImage(stream)
                else -> {
                    logger.warn(markers.andAppend("message", "Unsupported file type"), "$name#processMedia")
                    MediaAnalysisResult()
                }
            }
        } catch (e: Exception) {
            logger.error(markers, "$name#processMedia", e)
            return MediaAnalysisResult()
        }
    }

    private fun processPdf(stream: InputStream): MediaAnalysisResult {
        var result = MediaAnalysisResult()

        stream.use { input ->
            PDDocument.load(input).use { document ->
                val renderer = PDFRenderer(document)
                val textStripper = PDFTextStripper()

                for (pageIndex in 0 until document.numberOfPages) {
                    val image = renderer.renderImageWithDPI(pageIndex, PDF_DPI)
                    textStripper.startPage = pageIndex + 1
                    textStripper.endPage = pageIndex + 1
                    val pageText = textStripper.getText(document)

                    val pageResult = processPage(imageToByteArray(image), pageText)
                    result = combineResults(result, pageResult)
                }
            }
        }

        return result
    }

    private fun processImage(stream: InputStream): MediaAnalysisResult {
        return stream.use { input ->
            processPage(input.readAllBytes())
        }
    }

    private fun processPage(imageBytes: ByteArray, pageText: String? = null): MediaAnalysisResult {
        val qrCodes = imageProcessor.processImage(imageBytes).also { detectedQRCodes ->
            if (detectedQRCodes.isNotEmpty()) {
                logger.info(append("qrCodes", detectedQRCodes), "$name#processPage")
            }
        }

        val barcodes = pageText?.let { text ->
            textProcessor.processText(text).also { detectedBarcodes ->
                if (detectedBarcodes.isNotEmpty()) {
                    logger.info(append("barCodes", detectedBarcodes), "$name#processPage")
                }
            }
        } ?: emptyList()

        val pixQrCodes = qrCodes.filter { it.isPixQrCode() }
        val hasPixQrCodes = pixQrCodes.isNotEmpty()
        val (boletos, text) = normalizePaymentInfo(imageBytes, barcodes, hasPixQrCodes)

        return MediaAnalysisResult(
            qrCodeValues = pixQrCodes,
            boletos = boletos,
            text = text ?: pageText,
        )
    }

    private fun normalizePaymentInfo(bytes: ByteArray, barcodes: List<BoletoInfo>, hasPix: Boolean): Pair<List<BoletoInfo>, String?> {
        val hasConcessionariaBarcode = barcodes.any { it.codigo.isConcessionaria() }

        if (hasPix || (barcodes.isNotEmpty() && !hasConcessionariaBarcode)) {
            return barcodes to null
        }

        val processedBill = remoteBarCodeMediaProcessor.process(bytes)

        if (processedBill == null || processedBill.first == null) {
            logger.warn(append("message", "Failed to process remote barcode"), "$name#processPage")
            return barcodes to null
        }

        val (parsedBill, text) = processedBill

        val boletoInfos = parsedBill?.let {
            val markers = append("remoteBarCode", it).andAppend("text", text)

            when {
                parsedBill.digitableLine?.isNotBlank() == true && parsedBill.dueDate?.isNotBlank() == true -> {
                    barcodes.upsert(BoletoInfo(parsedBill.digitableLine, parsedBill.dueDate))
                }
                parsedBill.digitableLine.isNullOrBlank() && hasConcessionariaBarcode -> {
                    if (parsedBill.dueDate.isNullOrBlank()) {
                        logger.warn(markers.andAppend("message", "Due date is missing"), "$name#processPage")
                    }
                    barcodes.map { item ->
                        if (item.codigo.isConcessionaria()) {
                            BoletoInfo(codigo = item.codigo, vencimento = parsedBill.dueDate ?: item.vencimento)
                        } else {
                            item
                        }
                    }
                }
                else -> barcodes
            }
        } ?: barcodes

        return boletoInfos to text
    }

    private fun imageToByteArray(image: BufferedImage): ByteArray {
        return ByteArrayOutputStream().use { output ->
            ImageIO.write(image, "png", output)
            output.toByteArray()
        }
    }

    private fun combineResults(result1: MediaAnalysisResult, result2: MediaAnalysisResult): MediaAnalysisResult {
        return MediaAnalysisResult(
            qrCodeValues = result1.qrCodeValues + result2.qrCodeValues,
            boletos = result1.boletos + result2.boletos,
            text = result1.text ?: result2.text,
        )
    }
}