package ai.chatbot.app.payment

import ai.chatbot.app.user.WalletId

data class Balance(
    val current: Long,
    val open: Forecast,
    val onlyScheduled: Forecast,
    val walletId: WalletId,
)

data class Forecast(
    val amountToday: Long,
    val amountInSevenDays: Long,
    val amountInFifteenDays: Long,
    val amountMonth: Long,
    val amountForNextMonth: Long,
) {
    operator fun plus(onlyScheduled: Forecast): Forecast {
        return Forecast(
            this.amountToday + onlyScheduled.amountToday,
            this.amountInSevenDays + onlyScheduled.amountInSevenDays,
            this.amountInFifteenDays + onlyScheduled.amountInFifteenDays,
            this.amountMonth + onlyScheduled.amountMonth,
            this.amountForNextMonth + onlyScheduled.amountForNextMonth,
        )
    }
}