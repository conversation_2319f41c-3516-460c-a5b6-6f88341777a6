package ai.chatbot.app

import io.micronaut.context.annotation.Requires
import jakarta.inject.Named

const val FRIDAY_ENV = "friday"

const val ME_POUPE_ENV = "me-poupe"

const val MOTOROLA_ENV = "motorola"

const val STAGING_ENV = "staging"

const val GIGU_ENV = "gigu"

@Requires(env = [FRIDAY_ENV])
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
@Named(FRIDAY_ENV)
annotation class Friday

@Requires(env = [ME_POUPE_ENV])
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
@Named(ME_POUPE_ENV)
annotation class MePoupe

@Requires(env = [MOTOROLA_ENV])
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
@Named(MOTOROLA_ENV)
annotation class Motorola

@Requires(env = [STAGING_ENV])
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class Staging