package ai.chatbot.app.transcription

import ai.chatbot.app.file.StoredObject
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.measure
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.Logger
import org.slf4j.LoggerFactory

interface TranscriptionAdapter {
    val name: String
    suspend fun transcribe(storedObject: StoredObject): String?
}

@Singleton
class TranscriptionService(
    private val transcriptionAdapter: TranscriptionAdapter,
) {
    suspend fun transcribe(storedObject: StoredObject): String? {
        val marker = append("adapter", transcriptionAdapter.name).andAppend("storedObject", storedObject)

        val (transcription, elapsedTime) = measure {
            try {
                transcriptionAdapter.transcribe(storedObject).also { transcription ->
                    marker.andAppend("transcription", transcription)
                }
            } catch (e: Exception) {
                logger.error(
                    append("adapter", transcriptionAdapter.name)
                        .andAppend("storedObject", storedObject),
                    "TranscriptionService#transcribe",
                    e,
                )
                null
            }
        }
        logger.info(
            marker.andAppend("elapsedTime", elapsedTime),
            "TranscriptionService#transcribe",
        )
        return transcription
    }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(TranscriptionService::class.java)
    }
}