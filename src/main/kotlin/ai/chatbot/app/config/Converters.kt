package ai.chatbot.app.config

import ai.chatbot.app.featureflag.FeatureFlags
import ai.chatbot.app.notification.NotificationMessages
import ai.chatbot.app.utils.deepMerge
import ai.chatbot.app.utils.getObjectMapper
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Prototype
import io.micronaut.core.convert.ConversionContext
import io.micronaut.core.convert.TypeConverter
import java.util.Optional
import kotlin.jvm.java

// NOTE: Esses conversores são usados para converter tipos customizados de configuração em objetos específicos.
// O deepMerge é utilizado para combinar a configuração padrão (application.yml -> foo) com a configuração específica do tenant onde existe o prefixo tenants.TENANT_NAME (application-tenant.yml -> tenants.TENANT_NAME.foo).

@Prototype
class MapToNotificationMessagesConverter(
    @Property(name = "tenant-default.notification-config.messages") private val config: Map<String, Any>,
) : TypeConverter<Map<String, Any>, NotificationMessages> {
    override fun convert(propertyMap: Map<String, Any>, targetType: Class<NotificationMessages>, context: ConversionContext): Optional<NotificationMessages> {
        return Optional.of(getObjectMapper().convertValue(deepMerge(config, propertyMap), NotificationMessages::class.java))
    }
}

@Prototype
class MapToFeatureFlagsConverter(
    @Property(name = "tenant-default.feature-flags.values") private val config: Map<String, Any>,
) : TypeConverter<Map<String, Any>, FeatureFlags> {
    override fun convert(propertyMap: Map<String, Any>, targetType: Class<FeatureFlags>, context: ConversionContext): Optional<FeatureFlags> {
        return Optional.of(getObjectMapper().convertValue(deepMerge(config, propertyMap), FeatureFlags::class.java))
    }
}