package ai.chatbot.app.config

import ai.chatbot.adapters.waCommCentre.ClientId
import ai.chatbot.app.featureflag.FeatureFlags
import ai.chatbot.app.notification.NotificationMessages
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.EachBean
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter

@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS, AnnotationTarget.FUNCTION)
@EachBean(TenantConfiguration::class)
annotation class EachTenant

@EachProperty("tenants")
data class TenantConfiguration @ConfigurationInject constructor(
    @param:Parameter private val name: String,
    val integrations: Integrations,
    val communicationCentre: CommunicationCentreConfig,
    val aws: AwsConfig,
    val internalAuth: InternalAuth,
    val blipAuth: BlipAuthConfig,
    val dynamodb: DynamoDbConfig,
    val appBaseUrl: String,
    val features: FeaturesConfig,
    val waCommCentre: WaCommCentreConfig,
    val prompt: PromptConfigurations,
    val dailyLog: DailyLogConfig,
    private val notificationConfig: Map<String, NotificationMessages>,
    private val featureFlags: Map<String, FeatureFlags>,
) {
    val flags: FeatureFlags = featureFlags["values"]!!
    val messages: NotificationMessages = notificationConfig["messages"]!!
    val tenantName: String = name.uppercase()

    val clientId: ClientId = ClientId(waCommCentre.senderId)

    @ConfigurationProperties("daily-log")
    interface DailyLogConfig {
        val tenant: String
        val bucket: String
    }

    @ConfigurationProperties("prompt")
    interface PromptConfigurations {
        val personality: String
        val overview: String
        val presentation: String
        val hasInAppSubscriptionPayment: Boolean
        val hasMailBox: Boolean
        val supportLink: String
        val appLink: String
        val appDomain: String
        val hasWalletShare: Boolean
        val appName: String
        val hasInvestment: Boolean
        val unregisteredOverview: String
    }

    @ConfigurationProperties("dynamodb")
    interface DynamoDbConfig {
        val tableName: String
    }

    @ConfigurationProperties("blip-auth")
    interface BlipAuthConfig {
        val secret: String
    }

    @ConfigurationProperties("aws")
    interface AwsConfig {
        val accountNumber: String
    }

    @ConfigurationProperties("wa-comm-centre")
    interface WaCommCentreConfig {
        val enabled: Boolean
        val senderId: String
        val backofficeSecret: String
        val auth: WaCommCentreAuthConfig

        @ConfigurationProperties("auth")
        interface WaCommCentreAuthConfig {
            val clientId: String
            val secret: String
        }
    }

    @ConfigurationProperties("integrations")
    interface Integrations {
        val whatsapp: WhatsAppConfig
        val billPayment: BillPaymentConfig

        @ConfigurationProperties("whatsapp")
        interface WhatsAppConfig {
            val accountId: String
            val apiToken: String
        }

        @ConfigurationProperties("bill-payment")
        interface BillPaymentConfig {
            val host: String
            val secret: String
        }
    }

    @ConfigurationProperties("features")
    interface FeaturesConfig {
        val openFinanceIncentive: Boolean
    }

    @ConfigurationProperties("internal-auth")
    interface InternalAuth {
        val identity: String
        val secret: String
    }

    @ConfigurationProperties("communication-centre")
    interface CommunicationCentreConfig {
        val email: EmailConfig
        val forward: ForwardConfig

        @ConfigurationProperties("forward")
        interface ForwardConfig {
            val configurationSet: String
            val sender: String
        }

        @ConfigurationProperties("email")
        interface EmailConfig {
            val region: String
            val dailyLogEmail: String
            val dailyLogAiEmail: String
            val displayName: String
            val returnPath: String
            val bucketUnprocessedEmails: String
            val configurationSetName: String
            val receipt: EmailReceiptContactConfig
            val notification: EmailNotificationContactConfig
            val maxAttachments: Int
            val maxPagesPerAttachment: Int
            val virus: VirusConfig

            @ConfigurationProperties("notification")
            interface EmailNotificationContactConfig {
                val email: String
                val displayName: String
            }

            @ConfigurationProperties("virus")
            interface VirusConfig {
                val bucket: String
            }

            @ConfigurationProperties("receipt")
            interface EmailReceiptContactConfig {
                val email: String
                val displayName: String
            }
        }
    }
}

private fun String.transformToKebabCase(): String {
    return this.replace(Regex("([a-z])([A-Z])"), "$1-$2").lowercase()
}

typealias FeaturesConfig = TenantConfiguration.FeaturesConfig