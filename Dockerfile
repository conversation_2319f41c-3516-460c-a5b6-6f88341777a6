ARG DOCKER_IMAGE_MIRROR
FROM ${DOCKER_IMAGE_MIRROR}amazoncorretto:17.0.8-alpine3.18

ARG APP_VERSION
ENV DD_VERSION $APP_VERSION
ENV SERVICE ai-chatbot
LABEL com.datadoghq.tags.version="$APP_VERSION"

COPY dd-java-agent.jar dd-java-agent.jar
COPY dd-java-agent.properties dd-java-agent.properties

COPY target/${SERVICE}-*.jar ${SERVICE}.jar

EXPOSE 8443

CMD java -XX:+UseContainerSupport -javaagent:dd-java-agent.jar -Ddd.trace.config=dd-java-agent.properties -Ddd.profiling.enabled=true -Ddd.profiling.allocation.enabled=true -XX:FlightRecorderOptions=stackdepth=256 -Dcom.sun.management.jmxremote -noverify ${JAVA_OPTS} -jar ${SERVICE}.jar
